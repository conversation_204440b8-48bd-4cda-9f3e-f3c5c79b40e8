import logging
from datetime import <PERSON><PERSON><PERSON>

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from tencentcloud.common.credential import Credential as TencentCloudCredential

logger = logging.getLogger(__name__)


class AppSettings(BaseSettings):
    # 配置当前类的的行为
    # 注：环境变量在载入默认时大小写不敏感的
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')

    # 调试模式开关
    debug: bool = False

    # 对称加密 Key
    aes_key: str

    # MySQL 数据库配置
    db_host: str
    db_port: int = 3306
    db_user: str
    db_name: str
    db_password: str

    # Redis 配置
    redis_host: str
    redis_port: int

    # 钉钉相关配置
    ding_talk_app_id: str = Field(default='', alias='omTools_login_appid')
    ding_talk_app_secret: str = Field(default='', alias='omTools_login_secret')
    ding_talk_osa_auth_url: str = Field(default='', alias='omTools_login_auth_url')
    ding_talk_redirect_uri: str = Field(default='', alias='omTools_login_redirect_uri')

    # 目前用作允许通过钉钉登录的 IP
    allow_ips: str = '127.0.0.1,***********,localhost,************,*************,*************,*************,************,**********'

    # AI 服务器
    ai_server: str = Field(default='', alias='AI-SERVER')
    ai_server_test: str = Field(default='', alias='AI-SERVER-TEST')

    # 音乐生成配置
    inspire_music_api: str = ''

    # 腾讯云相关配置
    tencent_api_key: str = ''

    # 腾讯云 COS 相关配置
    tencent_cos_host: str = ''
    tencent_cos_secret: str = ''
    tencent_cos_region: str = ''
    tencent_cos_bucket: str = ''

    # OpenAI 相关配置
    openai_api_key: str = ''
    openai_api_base_url: str = ''

    # COSY 服务器
    cosy_voice_api: str = ''

    # 火山引擎图片处理相关配置
    volcengine_access_key_id: str = ''
    volcengine_secret_access_key: str = ''

    # Midjourney 服务器
    midjourney_server: str = ''

    # 4090 机器的地址
    cuda: str = ''

    # 火山引擎 TTS 相关配置
    volcengine_api_host: str = ''
    volcengine_appid: str = ''
    volcengine_access_token: str = ''
    volcengine_cluster: str = ''

    # 百度 OCR 相关的配置
    baidu_api_key: str = ''
    baidu_secret_key: str = ''

    # Deepseek 相关的配置
    deepseek_api_key: str = ''

    # Google 存储相关配置
    google_storage_bucket: str = ''

    # 百炼相关配置
    bailian_api_key: str = ''

    # 邮件配置
    email_host: str = ''
    email_port: int = 0
    email_addr: str = ''
    email_pass: str = ''
    email_name: str = ''

    # GPT-SOVIT 相关配置
    gpt_sovits_server: str = Field(default='', alias='GPT-SoVITS-SERVER')

    # CHAT_TTS 相关配置
    chat_tts: str = Field(default='', alias='CHAT_TTS')

    # qwen 相关配置
    qwen_api_key: str = Field(default='', alias='Qwen_API_KEY')

    # 本地缓存相关配置
    local_storage: bool = Field(default=False, alias='Local_Storage')

    # Nginx Name 相关配置
    nginx_name: str = Field(default='', alias='NGINX_NAME')

    # 腾讯云验证码相关配置
    tencent_captcha_appid: int = Field(default=0, alias='TENCENT_CAPTCHA_APPID')
    tencent_captcha_secretkey: str = Field(default='', alias='TENCENT_CAPTCHA_SECRETKEY')

    # BACKEND-SERVER 相关配置
    backend_server: str = Field(default='', alias='BACKEND-SERVER')

    # OPENAI IMAGE 相关配置
    openai_image_api_key: str = Field(default='', alias='OPENAI_IMAGE_API_KEY')
    openai_image_api_base_url: str = Field(default='', alias='OPENAI_IMAGE_API_BASE_URL')

    # MCP 相关配置
    mcp_server: str = Field(default='', alias='MCP_SERVER')
    mcp_max_round: int = Field(default=10, alias='MCP_MAX_ROUND', description="MCP 调用的最大轮次")

    # ZhipuAI 相关配置
    zhipuai_api_key: str = Field(default='', alias='ZHIPUAI_API_KEY')

    # Flux 相关配置
    flux_api_key: str = Field(default='', alias='FLUX_API_KEY')

    # 百度千帆相关配置
    baidu_appbuilder_api_key: str = Field(default='', alias='BAIDU_APPBUILDER_API_KEY')

    # 请求签名相关配置
    sign_key: str = Field(default='37705919d922c1982f5b', alias='SIGN_KEY', description="盐值")
    sign_ttl: float = Field(default=timedelta(seconds=5).total_seconds(), alias="SIGN_TTL", description="签名的有效时长")

    # 积分相关
    daily_gift_credit: int = Field(default="200", alias="DAILY_GIFT_CREDIT", description="每日赠送积分")

    # 腾讯云签名安全凭证
    tencent_secret_id: str = Field(default='', alias='TENCENT_SECRET_ID')
    tencent_secret_key: str = Field(default='', alias='TENCENT_SECRET_KEY')

    # ComfyUI 相关配置
    comfyui_api_url: str = Field(default='', alias='COMFYUI_API_URL')

    @property
    def tencent_cloud_credential(self):
        if not self.tencent_secret_id or not self.tencent_secret_key:
            raise ValueError(f'tencent secret id or secret key is empty.')
        return TencentCloudCredential(self.tencent_secret_id, self.tencent_secret_key)


app_settings = AppSettings() # noqa
"""应用的全局配置对象"""
