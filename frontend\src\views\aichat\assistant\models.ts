const models: { label: string; value: string; desc?: string }[] = [
  {
    label: 'GPT-3.5',
    value: 'gpt-3.5-turbo',
    desc: 'GPT-3.5是OpenAI公司开发的一种自然语言处理模型，它基于人工智能的机器学习技术，通过大规模的语料训练来学习自然语言处理的能力。GPT-3.5是对GPT-3的改进和优化，拥有更高的模型参数数量，具有更强的自适应和泛化能力，可以更好地适应各种自然语言处理任务。'
  },
  {
    label: 'GPT-4',
    value: 'gpt-4-turbo',
    desc: 'GPT-4是由OpenAI开发的一种基于Transformer模型的自然语言处理模型，是GPT-3之后的最新版本。它在诸多方面进行了改进和升级，旨在提供更强大、更智能的自然语言处理能力。'
  },
  {
    label: 'GPT-4o',
    value: 'gpt-4o',
    desc: 'GPT-4是由OpenAI开发的一种基于Transformer模型的自然语言处理模型，是GPT-3之后的最新版本。它在诸多方面进行了改进和升级，旨在提供更强大、更智能的自然语言处理能力。'
  },
  {
    label: 'Gemini-1.5',
    value: 'gemini-1.5-pro',
    desc: 'Gemini-1.5-Pro 是谷歌DeepMind发布的一款人工智能模型。作为Gemini系列中的最新版本，它专为处理多任务而设计，是在各种任务上扩展的最佳模型。'
  },
  {
    label: 'Claude3',
    value: 'claude-3',
    desc: `Claude-3 Sonnet是Anthropic公司开发的Claude-3系列中的一个模型级别。
  在自然语言处理方面，Claude-3 Sonnet展现了强大的能力，它能够理解和生成接近人类水平的自然语言。这得益于其采用的先进的深度学习算法和海量的语料库训练，使其在文本理解、语言生成、情感分析等方面都有卓越的表现。`
  }
];

export default models;
