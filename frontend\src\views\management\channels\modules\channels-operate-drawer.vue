<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { NDrawer, NDrawerContent, NForm, NFormItem, NInput, NRadio, NRadioGroup } from 'naive-ui';

interface Props {
  /** the type of operation */
  operateType: 'add' | 'edit';
  /** the edit row data */
  rowData?: any | null;
}
const props = defineProps<Props>();

// 抽屉可见性 (双向绑定)
const visible = defineModel<boolean>('visible', {
  default: false
});

// 抽屉标题
const title = computed(() => {
  return props.operateType === 'add' ? '新增频道' : '编辑频道';
});

// 表单模型
const formModel = reactive({
  channel_name: '',
  permission: 'public' // public / private
});

// 每次打开抽屉时，根据是否为编辑模式，初始化表单数据
function initForm() {
  if (props.operateType === 'edit' && props.rowData) {
    formModel.channel_name = props.rowData.channel_name || '';
    formModel.permission = props.rowData.permission || 'public';
  } else {
    // 新增模式
    formModel.channel_name = '';
    formModel.permission = 'public';
  }
}

// 监听抽屉是否打开
watch(visible, newVal => {
  if (newVal) {
    initForm();
  }
});

// 向父组件抛出提交事件
const emit = defineEmits<{
  (e: 'submitted', data: { channel_name: string; permission: string }): void;
}>();

function handleSubmit() {
  // 这里可以加入表单验证
  if (!formModel.channel_name) {
    // 简易校验
    window.$message?.error('请输入频道名称');
    return;
  }
  emit('submitted', {
    channel_name: formModel.channel_name,
    permission: formModel.permission
  });
  visible.value = false;
}

// 关闭抽屉
function closeDrawer() {
  visible.value = false;
}
</script>

<template>
  <NDrawer v-model:show="visible" placement="right" size="360">
    <NDrawerContent :title="title">
      <NForm label-placement="left" label-width="80">
        <NFormItem label="频道名称">
          <NInput v-model:value="formModel.channel_name" placeholder="请输入频道名称" />
        </NFormItem>
        <NFormItem label="权限">
          <NRadioGroup v-model:value="formModel.permission">
            <NRadio value="public">公共</NRadio>
            <NRadio value="private">私有</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NForm>

      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
