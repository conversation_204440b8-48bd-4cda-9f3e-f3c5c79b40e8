import { defineStore } from 'pinia';
import { ref } from 'vue';
import { SetupStoreId } from '@/enum';
import { fetchGetUserCreditInfo } from '@/service/api/user';

export const useCreditStore = defineStore(SetupStoreId.Credit, () => {
  const creditInfo = ref({
    total_credit: 0,
    credit_details: []
  });

  async function updateCreditInfo() {
    const { data, error } = await fetchGetUserCreditInfo();
    if (!error) {
      creditInfo.value = data;
    }
    return !error;
  }

  return {
    creditInfo,
    updateCreditInfo
  };
});
