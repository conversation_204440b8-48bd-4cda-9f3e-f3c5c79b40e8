from fastapi import APIRouter
import os
from pydantic import BaseModel
from base64 import b64encode
import httpx

from config import app_settings
from utils.exceptions import ClientVisibleException

router = APIRouter()


class ClearVoiceRequest(BaseModel):
    file_path: str
    model: str

async def process_audio(req: ClearVoiceRequest, endpoint: str):
    """
    通用的音频处理函数，根据不同的 endpoint 调用不同的服务。
    :param req: 请求体，包括 file_path 和 model
    :param endpoint: 指定调用 clearvoice 服务的路径，如 "enhancement" 或 "separation"
    :return: 处理后的结果或错误信息
    """
    # 1. 判断文件是否存在
    if not os.path.exists(req.file_path):
        raise ClientVisibleException("音频文件不存在")

    # 2. 读取文件并转成base64
    with open(req.file_path, "rb") as audio_file:
        audio_base64 = b64encode(audio_file.read()).decode('utf-8')

    # 3. 组装请求体
    data = {
        "audio_base64": audio_base64,
        "model": req.model
    }

    # 4. 调用后端服务
    clearvoice_service_url = f"{app_settings.ai_server}/clearvoice/{endpoint}"
    async with httpx.AsyncClient(timeout=300) as client:
        try:
            response = await client.post(
                url=clearvoice_service_url,
                headers={"Content-Type": "application/json"},
                json=data
            )
            response.raise_for_status()
            response_data = response.json()
            response_data["type"] = 'data:audio/wav;'
            return {"code": "0000", "data": response_data}
        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时") from e
        except httpx.HTTPError as e:
            raise ClientVisibleException("请求失败，请重试") from e


@router.post("/enhancement")
async def enhancement(
        req: ClearVoiceRequest,
):
    """
    语音增强接口
    """
    res = await process_audio(req, "enhancement")
    return res


@router.post("/separation")
async def separation(
        req: ClearVoiceRequest,
):
    """
    语音分离接口
    """
    return await process_audio(req, "separation")
