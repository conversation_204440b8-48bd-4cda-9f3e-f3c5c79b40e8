import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from service.credit import CreditOperator
from utils.database import get_db
from models.tasks import TaskStatus
from models.users import User
from task_queue.task_manager import TaskManager
from utils.asset_storage import store_asset_from_instance
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


async def get_user_by_username(db: AsyncSession, username: str) -> User | None:
    """
    根据用户名查询用户

    参数:
    - db: 数据库会话
    - username: 用户名

    返回:
    - 用户对象或None
    """
    try:
        stmt = select(User).filter(User.username == username)
        result = await db.execute(stmt)
        user = result.scalars().first()
        return user
    except Exception as e:
        logger.error(f"查询用户 {username} 失败: {str(e)}")
        return None


@router.post("/status_update")
async def task_status_update(
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
):
    """
    接收任务状态更新的Webhook回调

    参数:
    - request: 请求对象，包含任务状态数据
    - db: 数据库会话

    返回:
    - 处理结果
    """
    try:
        # 获取请求数据
        data: dict[str, Any] = await request.json()

        taskid = data.get("taskid")
        status = data.get("status")
        message = data.get("message")
        result = data.get("result")
        resource_url = data.get("resource_url")
        task_params: dict[str, Any] = data.get("params", dict())
        credit_operator_id = task_params.get("credit_operator_id")

        # 更新任务的队列位置信息，这里使用后台任务是因为：
        #   1. 更新比较耗时
        #   2. 这个更新并不需要强一致性，可以容忍一定程度的失败，没必要在试图函数里做
        pos_info = data.get("pos_info", dict())
        if pos_info:
            background_tasks.add_task(update_pos, pos_info, db)

        if not taskid or not status:
            raise ClientVisibleException("缺少必要参数: taskid 或 status")

        logger.info(f"收到任务 {taskid} 状态更新: {status}, {message}")

        # 查找任务
        task = await TaskManager.get_task_by_taskid(db, taskid)

        if not task:
            logger.error(f"未找到任务: {taskid}")
            return {"success": False, "message": f"未找到任务: {taskid}"}

        if task.status == TaskStatus.CANCELED.value:
            return {"success": True, "message": f"任务 {taskid} 已经取消，忽略本次操作"}

        # 准备更新数据
        try:
            task_status = TaskStatus(status)
        except ValueError:
            logger.error(f"无效的任务状态: {status}")
            return {"success": False, "message": f"无效的任务状态: {status}"}

        # 处理不同状态的更新
        if task_status == TaskStatus.IN_PROGRESS:
            # 更新为处理中状态
            await TaskManager.update_task_status(
                db=db,
                task_id=task.id,
                status=task_status
            )
        elif task_status == TaskStatus.SUCCESS:
            # 更新为成功状态，并更新结果
            await TaskManager.update_task_status(
                db=db,
                task_id=task.id,
                status=task_status,
                result=result,
                resource_url=resource_url
            )
            # 将预扣的积分实际扣除
            if credit_operator_id:
                operator = await CreditOperator.from_id(credit_operator_id, db)
                if operator:
                    await operator.done()

            # 任务成功时，将数据存储到资产表
            try:
                # 查询用户信息
                user = await get_user_by_username(db, task.username)
                if user:
                    # 存储资产到资产表
                    asset = await store_asset_from_instance(task, user, db)
                    if asset:
                        logger.info(f"成功为任务 {taskid} 创建资产记录，资产ID: {asset.id}")
                    else:
                        logger.warning(f"任务 {taskid} 没有有效的资源URL，跳过资产存储")
                else:
                    logger.error(f"未找到用户 {task.username}，无法存储资产")
            except Exception as e:
                # 资产存储失败不影响任务状态更新，只记录错误
                logger.error(f"任务 {taskid} 资产存储失败: {str(e)}")

        elif task_status == TaskStatus.FAILURE:
            # 更新为失败状态，并记录失败原因
            await TaskManager.update_task_status(
                db=db,
                task_id=task.id,
                status=task_status,
                fail_reason=message
            )
            # 回滚预扣的积分
            if credit_operator_id:
                operator = await CreditOperator.from_id(credit_operator_id, db)
                if operator:
                    await operator.rollback()
        else:
            # 其他状态
            await TaskManager.update_task_status(
                db=db,
                task_id=task.id,
                status=task_status
            )

        logger.info(f"成功更新任务 {taskid} 状态为 {status}")
        return {"success": True, "message": f"成功更新任务状态为 {status}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理任务状态更新时出错: {e}", exc_info=True)
        return {"success": False, "message": f"处理状态更新失败: {str(e)}"}


async def update_pos(pos_info: dict[str, int], db: AsyncSession):
    """更新任务的排队位置信息"""
    for task_id, queue_position in pos_info.items():
        try:
            task = await TaskManager.get_task_by_taskid(db, task_id)
            if task:
                await TaskManager.update_task_pos(db, task.id, queue_position)
                logger.info(f"更新队列位置信息成功，id：{task.id}，taskid：{task.taskid}，type：{task.task_type}，position：{queue_position}")
        except Exception as e:
            logger.error(f"更新队列位置信息失败，task_id：{task_id}，position：{queue_position}，Error: {e}")
            continue
