<script setup lang="ts">
import { $t } from '@/locales';
import Item from './item.vue';

const items = [
  {
    configKey: 'ASSISTANT_MODELS',
    name: $t('page.manage.model.setting.assistant.title')
  }
];
</script>

<template>
  <NFlex vertical class="!gap-2xl">
    <Item v-for="item in items" :key="item.configKey" :name="item.name" :config-key="item.configKey" />
  </NFlex>
</template>

<style scoped></style>
