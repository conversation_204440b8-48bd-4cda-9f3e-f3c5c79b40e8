import logging

from fastapi import APIRouter, Depends, Query, Body, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, delete

from utils.database import get_db
from pydantic import BaseModel
from typing import List, Dict, Sequence
import datetime
from models.users import User, get_request_user, get_roles_checker
from models.user_role import UserRole
from models.user_credit import UserCredit, UserCreditLog
from models.work_teams import WorkTeams
from models.roles import Role
from service.user import getEncryptPassword,getRandomString
from utils.exceptions import ClientVisibleException
from utils.hash.aes import decrypt

router = APIRouter()

logger = logging.getLogger(__name__)


class UserRes(BaseModel):
  id: int
  group_id: int
  role_id: int
  role: list[int]=[]
  username: str
  nickname: str
  email: str
  avatar: str
  gender: int
  status: int
  credit: int = 0
  company: str | None = None
  loginip: str | None = None
  logintime: int | None = None
  updatetime: datetime.datetime
  updateBy: str | None = None
  class Config:
    from_attributes = True


class PaginatedData(BaseModel):
  records: List[UserRes]
  current: int
  size: int
  total: int
  user_ids: list[int]


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str

class SaveUserRequest(BaseModel):
  id: int = 0
  group_id: int =0
  role_id: int =0
  role: list[int]=[]
  username: str =''
  nickname: str =''
  company: str= ''
  password: str =''
  company: str =''
  credit: int =0
  email: str =''
  avatar: str =''
  gender: int =0
  status: int =1

class SaveUserCreditRequest(BaseModel):
  id: int = 0
  credit: int = 0
  detail: str =''

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()

@router.get(
  "/get_all_user_company",
  dependencies=[Depends(get_roles_checker(['super_admin', 'general-admin']))],
  tags=["system"]
)
async def get_all_user_company(
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
  role: str = Query('')
):
  async with db as session:
    # 检查用户权限，超级管理员返回所有公司，管理员返回所属公司
    process_user=user.id
    result = await session.execute(
      select(Role.roleCode)
      .select_from(UserRole)
      .join(Role, UserRole.role_id == Role.id)
      .where(UserRole.user_id == process_user)
    )
    roles = result.scalars().all()
    if 'super_admin' in roles:
      # result = await session.execute(select(User.company).distinct().where(User.company != ''))
      result = await session.execute(select(WorkTeams.team_name.label('company')))
    else:
      result = await session.execute(select(User.company).where(User.id == process_user))
  # result = await session.execute(select(User.company).distinct().where(User.company != ''))
    records = result.scalars().all()
    return {
      "code":"0000",
      "data":{
        "records":records
      },
    }

@router.get(
  "/getUserList",
  response_model=PaginatedResponse,
  dependencies=[Depends(get_roles_checker('super_admin'))],
  tags=["system"]
)
async def get_user(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  username: str = Query(''),
  gender: int = Query(''),
  status: int = Query(''),
  nickname: str = Query(''),
  email: str = Query(''),
  company: str = Query(''),
  db: AsyncSession = Depends(get_db)
):
  """
    获取用户管理数据
    """
  async with db as session:
    # 获取分页数据
    where=[]
    if nickname:
      where.append(User.nickname.like(f'%{nickname}%'))
    if username:
      where.append(User.username.like(f'%{username}%'))
    if email:
      where.append(User.email == email)
    if gender:
      where.append(User.gender == gender)
    if status:
      where.append(User.status == status)
    if company:
      where.append(User.company.like(f'%{company}%'))

    result = await session.execute(
      # select(User).filter(*where).offset((page - 1) * size).limit(size)
      select(User.id, User.group_id, User.role_id, User.username, User.nickname, User.password, User.salt, User.email, User.mobile, User.avatar, User.level,
              User.gender, User.birthday, User.bio, User.successions, User.maxsuccessions, User.prevtime, User.logintime, User.loginip, User.loginfailure,
              User.joinip, User.createtime, User.updatetime, User.createBy, User.updateBy, User.token, User.status, User.verification, User.company,
              func.coalesce(UserCredit.credit, 0).label("credit"))
      .outerjoin(UserCredit, User.id == UserCredit.user_id)  # 左连接
      .filter(*where)
      .offset((page - 1) * size).limit(size)
    )
    # records = result.scalars().all()
    records = result.all()
    user_dict: Dict[int, UserRes] = {user.id: UserRes.model_validate(user) for user in records}
    user_ids:list[int] =  list( map(lambda x: x.id, records) )
    role_result=await session.execute( select(UserRole).where(UserRole.user_id.in_(user_ids)) )
    role: Sequence[UserRole] = role_result.scalars().all()
    # 填充用户的角色ID字段
    for row in role:
        if row.user_id in user_dict:
            user_dict[row.user_id].role.append(row.role_id)
    count_res = await session.execute(select(func.count(User.id)).filter(*where))
    total = count_res.scalar()
    return PaginatedResponse(
      data=PaginatedData(
        records=list(user_dict.values()),
        current=page,
        size=size,
        total=total,
        user_ids=user_ids
      ),
      code="0000"
    )


@router.post("/updateUser", dependencies=[Depends(get_roles_checker('super_admin'))], tags=["system"])
async def update_user(
  save_user_data: SaveUserRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user),
):
  """
    更新用户管理数据
  """
  try:
    async with db as session:
      # token = authorization.split(" ")[1]
      # # 验证 token
      # token_key = get_token_key(token)
      # userid = await redis.get(token_key)

      if save_user_data.id > 0 :
        # print("===============================update user")
        # 更新用户信息
        result = await session.execute( select(User).where(User.id == save_user_data.id) )
        user_in_db = result.scalars().first()
        if not user_in_db:
          raise ClientVisibleException("用户不存在")

        if save_user_data.password:
          try:
            # 先解密前端传来的密码
            decrypted_password = decrypt(save_user_data.password)
            # 更新密码
            salt:str=getRandomString(20)
            password:str=getEncryptPassword(decrypted_password,salt)
            user_in_db.password=password
            user_in_db.salt=salt
          except Exception as e:
            logger.error(f"密码解密失败: {str(e)}")
            raise ClientVisibleException("密码格式错误") from e

        user_in_db.group_id=save_user_data.group_id
        # user_in_db.role_id=save_user_data.role_id
        user_in_db.username=save_user_data.username
        user_in_db.nickname=save_user_data.nickname
        user_in_db.company=save_user_data.company
        user_in_db.email=save_user_data.email
        user_in_db.avatar=save_user_data.avatar
        user_in_db.gender=save_user_data.gender
        user_in_db.status=save_user_data.status
        user_in_db.updateBy=request.state.user.username
        user_in_db.updatetime=datetime.datetime.now()

        try:
          result = await db.execute(
            select(UserCredit).where(UserCredit.user_id == save_user_data.id).with_for_update()
          )
          user_credit = result.scalar_one()
          if not user_credit:
            return False
          current_credit = user_credit.credit if user_credit.credit >= 0 else 0 # 现有积分
          if current_credit != save_user_data.credit:  # 积分有调整
            user_credit.credit =  save_user_data.credit
            exceLog = UserCreditLog(
              user_id              = save_user_data.id,
              credit               = save_user_data.credit - current_credit ,
              after_credit         = save_user_data.credit,
              matter               = '系统调整',
              ip                   = request.state.client_ip,
              createtime           = datetime.datetime.now(),
              editor               = user.username,
            )
            db.add(exceLog)
        except Exception as e:
          logger.debug(f'================================={e}')
      else:
        # 创建新用户
        salt:str=getRandomString(20)
        try:
          # 先解密前端传来的密码
          decrypted_password = decrypt(save_user_data.password)
          # 使用salt加密解密后的密码
          password:str=getEncryptPassword(decrypted_password,salt)
        except Exception as e:
          logger.error(f"密码解密失败: {str(e)}")
          raise ClientVisibleException("密码格式错误") from e

        new_user = User (
          group_id=save_user_data.group_id,
          role_id=save_user_data.role_id,
          username=save_user_data.username,
          nickname=save_user_data.nickname,
          company=save_user_data.company,
          email=save_user_data.email,
          avatar=save_user_data.avatar,
          gender=save_user_data.gender,
          password=password,
          salt=salt,
          createBy=request.state.user.username,
          updateBy=request.state.user.username,
          status=save_user_data.status,
          createtime=datetime.datetime.now(),
          updatetime=datetime.datetime.now(),
        )
        session.add(new_user)
        await session.flush()
        save_user_data.id = new_user.id
        new_usercredit = UserCredit(
          user_id = new_user.id,
          credit = save_user_data.credit,
          updatetime = datetime.datetime.now()
        )
        session.add(new_usercredit)

      await session.execute( delete(UserRole).where( UserRole.user_id == save_user_data.id))
      if len(save_user_data.role)>0:
        user_roles = [UserRole(user_id=save_user_data.id, role_id=role_id) for role_id in save_user_data.role]
        session.add_all(user_roles)

      await session.commit()

      return {"code": "0000", "msg": "提交成功"}

  except Exception as e:
    logger.error(f"Failed to update user: {e}")
    raise ClientVisibleException("提交失败") from e


@router.post("/updateUserCredit", dependencies=[Depends(get_roles_checker('super_admin'))], tags=["system"])
async def update_user_credit(
  save_user_data: SaveUserCreditRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user),
):
  """
  更新用户积分
  """
  async with db as session:
    try:
      result = await db.execute(
        select(UserCredit).where(UserCredit.user_id == save_user_data.id).with_for_update()
      )
      user_credit = result.scalar_one()
      if not user_credit:
        return False
      current_credit = user_credit.credit if user_credit.credit >= 0 else 0 # 现有积分
      if current_credit != save_user_data.credit:  # 积分有调整
        user_credit.credit =  save_user_data.credit
        exceLog = UserCreditLog(
          user_id              = save_user_data.id,
          credit               = save_user_data.credit - current_credit ,
          after_credit         = save_user_data.credit,
          matter               = '系统调整',
          detail               = save_user_data.detail,
          ip                   = request.state.client_ip,
          createtime           = datetime.datetime.now(),
          editor               = user.username,
        )
        db.add(exceLog)
      await session.commit()
      return {"code": "0000", "msg": "提交成功"}
    except Exception as e:
      logger.error(f"Failed to update user: {e}")
      raise ClientVisibleException("提交失败") from e

@router.post("/deleteUser", dependencies=[Depends(get_roles_checker('super_admin'))], tags=["system"])
async def delete_user(
  id: int = Body(0,alias='id',embed=True),
  db: AsyncSession = Depends(get_db)
):
  """
  删除用户数据
  """
  async with db as session:
    # 查找要删除的数据
    if id<=0:
      raise ClientVisibleException("参数错误")
    result = await session.execute(select(User).where(User.id == id).limit(1))
    user = result.scalars().first()
    if not user:
      raise ClientVisibleException("删除失败,用户不存在")
    # 删除数据
    await session.delete(user)
    await session.commit()

    return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})
