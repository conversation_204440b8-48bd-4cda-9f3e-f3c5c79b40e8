<script setup lang="ts">
import { computed, onUnmounted, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import type { UploadCustomRequestOptions, UploadInst } from 'naive-ui';
import WaveSurfer from 'wavesurfer.js';

import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { Lang, type ModifyToneReq, type SaveTone, createTone, updateTone } from '@/service/api/audio';
import { blobToBase64, blobUrlToAudioBuffer, bufferToWave, sliceAudio } from '@/utils/audio';
import { useThemeStore } from '@/store/modules/theme';
import { generateCosKey, uploadToCOS } from '@/utils/tencentCos';

defineOptions({
  name: 'TimbresOperate'
});

// TimbreData接口定义，与表格数据结构匹配
interface TimbreData {
  id: number;
  name: string;
  gender: string;
  language: string;
  description: string;
  addTime: string;
  isCollect: boolean;
  audioUrl: string;
  is_reduction?: boolean; // 新增，兼容后端返回
}

// Props接口定义
interface Props {
  operateType?: 'add' | 'edit';
  rowData?: TimbreData | null;
}

const props = withDefaults(defineProps<Props>(), {
  operateType: 'add',
  rowData: null
});

interface Emits {
  (e: 'submitted', toneData: SaveTone): void;
}

const emit = defineEmits<Emits>();

// Store
const themeStore = useThemeStore();

// 模态框显示状态
const visible = defineModel<boolean>('visible', {
  default: false
});

// 表单管理
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const message = useMessage();

// 加载状态
const loading = ref(false);
const uploadLoading = ref(false);

// 音频上传相关状态
const uploadRef = ref<UploadInst>();
const fileName = ref<string>('');
const promptAudioBlobUrl = ref<string>('');
const tmpPromptAudioBlobUrl = ref<string>('');

// WaveSurfer相关状态
let wavesurfer: WaveSurfer | null = null;
const playing = ref(false);
const audioStartTime = ref<number>(0);
const audioEndTime = ref<number>(0);
const audioDuration = ref<number>(0);

// 时间标签位置状态
const startTimePosition = ref<number>(0);
const endTimePosition = ref<number>(100);

// 展开收起功能状态
const showMoreDetails = ref(true);

// 音频预览状态
const previewAudioRef = ref<HTMLAudioElement>();
const isPreviewPlaying = ref(false);

// 模态框标题 - 支持动态切换
const title = computed(() => {
  const titles = {
    add: '添加音色',
    edit: '编辑音色'
  };
  return titles[props.operateType];
});

// 计算属性：是否已选择音频文件
const hasAudioFile = computed(() => promptAudioBlobUrl.value !== '');

// 计算属性：提交按钮文本
const submitButtonText = computed(() => {
  return props.operateType === 'add' ? '确认添加' : '保存';
});

// 表单数据模型 - 与ModifyToneReq接口完全兼容
type Model = {
  name: string;
  gender: 0 | 1 | null; // 0: 女声, 1: 男声, null: 未选择
  lang: Lang | null; // 语言类型或null未选择
  description: string;
  audio_url: string; // 修正：与API接口保持一致
  is_favorite: boolean; // 新增：收藏状态，0: 不收藏, 1: 收藏
  need_reduction?: boolean; // 修正：音频降噪选项，与API接口保持一致
};

const model: Model = reactive(createDefaultModel());

// 计算属性：收藏状态转换（boolean <-> number）
// const isCollectChecked = computed({
//   get: () => model.is_favorite === 1,
//   set: (value: boolean) => {
//     model.is_favorite = value ? 1 : 0;
//   }
// });

function createDefaultModel(): Model {
  return {
    name: '',
    gender: null,
    lang: null,
    description: '',
    audio_url: '', // 修正字段名
    is_favorite: false, // 新增字段
    need_reduction: false // 修正字段名
  };
}

// 表单验证规则   | 'gender' | 'lang'
type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
  // gender: defaultRequiredRule,
  // lang: defaultRequiredRule
};

// 性别选项
const genderOptions = [
  { label: '女声', value: 0 },
  { label: '男声', value: 1 }
];

// 语言选项
const languageOptions = [
  { label: '中文-普通话', value: Lang.CMN },
  { label: '中文-粤语', value: Lang.YUE },
  { label: '英文', value: Lang.ENG },
  { label: '日文', value: Lang.JPN },
  { label: '韩语', value: Lang.KOR }
];

// 计算时间标签位置
function updateTimePositions() {
  if (!wavesurfer || !audioDuration.value) return;

  // 计算百分比位置（略微缩进起止位置）
  const startPercent = ((audioStartTime.value ?? 0) / (audioDuration.value || 1)) * 100;
  const endPercent = ((audioEndTime.value ?? 0) / (audioDuration.value || 1)) * 100;

  startTimePosition.value = Math.max(0, Math.min(100, startPercent + 3));
  endTimePosition.value = Math.max(0, Math.min(100, endPercent - 3));

  // 更新CSS自定义属性
  const startElement = document.querySelector('.time-label-start');
  const endElement = document.querySelector('.time-label-end');

  if (startElement) {
    (startElement as HTMLElement).style.setProperty('--position', `${startTimePosition.value}%`);
  }
  if (endElement) {
    (endElement as HTMLElement).style.setProperty('--position', `${endTimePosition.value}%`);
  }
}

// 初始化WaveSurfer
function initWaveSurfer() {
  // 先销毁原来的
  wavesurfer?.destroy();
  const regions = RegionsPlugin.create();

  regions.on('region-in', _region => {
    // region进入事件处理
  });

  regions.on('region-out', _region => {
    console.log('region-out', _region);
    // 移除自动播放
    // _region.play();
  });

  regions.on('region-clicked', (_region, e) => {
    console.log('region-clicked');
    e.stopPropagation(); // prevent triggering a click on the waveform
    // 移除自动播放
    // _region.play();
  });

  regions.on('region-updated', region => {
    console.log(region);
    audioStartTime.value = region.start;
    audioEndTime.value = region.end;
    console.log('区间', region.start, region.end);

    // 如果正在播放，停止播放以避免播放范围混乱
    if (playing.value) {
      wavesurfer?.pause();
    }

    // 更新时间标签位置
    updateTimePositions();
  });

  wavesurfer = WaveSurfer.create({
    container: '#waveform-container', // 绑定容器
    url: promptAudioBlobUrl.value,
    autoCenter: false,
    waveColor: themeStore.themeColor,
    progressColor: '#383351',
    barHeight: 0.7,
    barWidth: 3,
    barRadius: 3,
    cursorWidth: 1,
    barGap: 3,
    hideScrollbar: true,
    dragToSeek: false, // 禁用拖拽跳转，避免与region拖拽冲突
    plugins: [regions]
  });

  wavesurfer.on('decode', () => {});

  /** When the audio is both decoded and can play */
  wavesurfer.on('ready', duration => {
    console.log('Ready', `${duration}s`);
    audioDuration.value = duration;
    audioEndTime.value = duration;
    playing.value = false;

    // Regions
    regions.addRegion({
      start: audioStartTime.value,
      end: audioEndTime.value,
      content: '',
      color: 'rgba(13, 208, 61, 0.5)',
      drag: true,
      resize: true,
      minLength: 1
    });

    // 初始化时间标签位置
    updateTimePositions();
  });

  /** When the audio starts playing */
  wavesurfer.on('play', () => {
    console.log('Play');
    playing.value = true;
  });

  /** When the audio pauses */
  wavesurfer.on('pause', () => {
    console.log('Pause');
    playing.value = false;
  });

  // 移除自动播放功能
  // wavesurfer.on('interaction', () => {
  //   wavesurfer?.play();
  // });
}

// 播放/暂停控制 - 只播放选中的剪裁区域
const triggerPlayPause = async () => {
  if (playing.value) {
    wavesurfer?.pause();
  } else {
    // 设置播放起始位置为剪裁区域的开始时间
    wavesurfer?.setTime(audioStartTime.value);
    wavesurfer?.play();

    // 在剪裁区域结束时自动暂停
    setTimeout(
      () => {
        if (playing.value) {
          wavesurfer?.pause();
        }
      },
      (audioEndTime.value - audioStartTime.value) * 1000
    );
  }
};

// 展开收起切换
const toggleMoreDetails = () => {
  showMoreDetails.value = !showMoreDetails.value;
};

// 音频剪辑功能
const clip = async (): Promise<string> => {
  console.log('clip', [audioStartTime.value, audioEndTime.value]);
  const tmpAudioBuffer = await blobUrlToAudioBuffer(promptAudioBlobUrl.value);
  const newAudioBuffer = await sliceAudio(tmpAudioBuffer, audioStartTime.value, audioEndTime.value);

  return new Promise((resolve, reject) => {
    try {
      // 音频文件生成file
      const blob = bufferToWave(newAudioBuffer, newAudioBuffer.sampleRate * newAudioBuffer.duration);

      blobToBase64(blob).then(res => {
        resolve(res);
      });
    } catch (error) {
      reject(error);
      message.error('裁剪出错，请调整裁剪区间');
    }
  });
};

// 将Base64转换为File对象
const base64ToFile = (base64: string, filename: string): File => {
  // 处理data URL格式
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'audio/wav';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
};

// 上传音频到COS
const uploadAudioToCOS = async (audioBase64: string): Promise<string> => {
  message.info('正在上传音频文件...');

  // 将Base64转换为File对象
  const audioFile = base64ToFile(audioBase64, `tone_audio_${Date.now()}.wav`);

  // 生成COS存储路径
  const cosKey = generateCosKey(audioFile);

  // 上传到COS
  const cosUrl = await uploadToCOS(audioFile, cosKey, progress => {
    console.log(`上传进度: ${progress}%`);
  });

  console.log('音频上传成功，COS URL:', cosUrl);
  return cosUrl;
};

// 验证表单和音频数据
const validateSubmission = async (): Promise<void> => {
  // 1. 验证必须上传音频文件
  if (!hasAudioFile.value) {
    throw new Error('请先上传音频文件');
  }

  // 2. 验证音频时长是否合理
  if (audioEndTime.value - audioStartTime.value < 1) {
    throw new Error('音频剪辑片段过短，至少需要1秒');
  }

  if (audioEndTime.value - audioStartTime.value > 30) {
    throw new Error('音频剪辑片段过长，最多支持30秒');
  }

  // 3. 表单验证
  await validate();

  // 4. 验证gender和lang不能为null
  // if (model.gender === null) {
  // throw new Error('请选择音色性别');
  // }
  //
  // if (model.lang === null) {
  // throw new Error('请选择音色语言');
  // }

  if (model.name === '') {
    throw new Error('请输入音色名称');
  }
};

// 验证音频文件的函数
const validateAudioFile = (file: File): { isValid: boolean; errorMessage?: string } => {
  // 支持的音频文件扩展名
  const supportedExtensions = ['.mp3', '.wav', '.flac', '.m4a', '.ogg', '.webm'];

  // 支持的MIME类型
  const supportedMimeTypes = [
    'audio/mpeg', // mp3
    'audio/mp4', // m4a
    'audio/x-m4a', // m4a (alternative)
    'audio/ogg', // ogg
    'audio/wav', // wav
    'audio/webm', // webm
    'audio/flac', // flac
    'audio/x-wav', // wav (alternative)
    'audio/vnd.wave' // wav (alternative)
  ];

  // 获取文件扩展名
  const fileNameLower = file.name.toLowerCase();
  const fileExtension = fileNameLower.substring(fileNameLower.lastIndexOf('.'));

  // 检查扩展名
  const hasValidExtension = supportedExtensions.includes(fileExtension);

  // 检查MIME类型（可能不准确，所以作为辅助验证）
  const hasValidMimeType = file.type && supportedMimeTypes.includes(file.type);

  // 文件大小验证 (限制50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    return { isValid: false, errorMessage: '文件大小不能超过50MB' };
  }

  // 必须有有效的扩展名，MIME类型作为辅助验证
  if (!hasValidExtension) {
    return {
      isValid: false,
      errorMessage: `不支持的文件格式 "${fileExtension}"，请上传音频文件（支持：${supportedExtensions.join(', ')}）`
    };
  }

  // 如果有MIME类型且不在支持列表中，给出警告但允许上传（基于扩展名）
  if (file.type && !hasValidMimeType) {
    console.warn(`文件扩展名 ${fileExtension} 有效，但MIME类型 ${file.type} 可能不准确`);
  }

  return { isValid: true };
};

// 重置上传状态的函数
const resetUploadState = (clearUploadComponent = true) => {
  fileName.value = '';
  tmpPromptAudioBlobUrl.value = '';
  promptAudioBlobUrl.value = '';
  audioStartTime.value = 0;
  audioEndTime.value = 0;
  audioDuration.value = 0;
  startTimePosition.value = 0;
  endTimePosition.value = 100;

  // 销毁WaveSurfer实例
  wavesurfer?.destroy();
  wavesurfer = null;
  playing.value = false;

  // 只在需要时重置上传组件（避免在customRequest处理过程中清空文件列表）
  if (clearUploadComponent) {
    uploadRef.value?.clear();
  }
};

// 音频文件上传处理
const customRequest = async ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  try {
    uploadLoading.value = true;

    // 重置状态，但不清空上传组件的文件列表（避免onFinish时找不到文件ID）
    resetUploadState(false);

    if (!file.file) {
      throw new Error('文件上传失败，请重试');
    }

    // 严格的文件类型验证
    const validation = validateAudioFile(file.file);
    if (!validation.isValid) {
      throw new Error(validation.errorMessage!);
    }

    console.log('上传文件信息:', {
      name: file.name,
      type: file.type,
      size: `${(file.file.size / 1024 / 1024).toFixed(2)}MB`
    });

    // 处理音频文件
    try {
      const blobUrl = URL.createObjectURL(file.file);
      promptAudioBlobUrl.value = blobUrl;
      tmpPromptAudioBlobUrl.value = blobUrl;

      fileName.value = file.name;

      // 自动设置音色名称，仅在添加模式下
      if (props.operateType === 'add' && file.name) {
        // 去除扩展名
        let pureName = file.name.replace(/\.[^/.]+$/, '');
        if (pureName.length > 20) pureName = pureName.slice(0, 20);
        model.name = pureName;
      }

      message.success('音频文件上传成功');

      // 初始化WaveSurfer（异步，不阻塞上传完成流程）
      setTimeout(() => {
        try {
          initWaveSurfer();
        } catch (wavesurferError) {
          console.error('WaveSurfer初始化失败:', wavesurferError);
          message.error('音频波形图初始化失败，但文件已上传成功');
        }
      }, 100);

      // 最后调用onFinish()完成上传流程
      onFinish();
    } catch (audioError) {
      console.error('音频文件处理失败:', audioError);
      throw new Error('音频文件格式不支持或文件已损坏，请尝试其他文件');
    }
  } catch (error: any) {
    console.error('音频上传处理失败:', error);

    // 确保状态完全重置，包括清空上传组件
    resetUploadState(true);

    // 显示具体错误信息
    const errorMessage = error?.message || '音频文件处理失败，请重试';
    message.error(errorMessage);

    // 调用onError通知上传组件
    onError();
  } finally {
    uploadLoading.value = false;
  }
};

// 移除音频文件
const removeAudioFile = () => {
  // 释放Blob URL资源
  if (promptAudioBlobUrl.value) {
    URL.revokeObjectURL(promptAudioBlobUrl.value);
  }
  if (tmpPromptAudioBlobUrl.value) {
    URL.revokeObjectURL(tmpPromptAudioBlobUrl.value);
  }

  // 重置所有状态，包括清空上传组件
  resetUploadState(true);
};

// 初始化模型数据
function handleInitModel() {
  Object.assign(model, createDefaultModel());

  // 编辑模式下的数据预填充
  if (props.operateType === 'edit' && props.rowData) {
    // 字段映射和数据类型转换
    const editData = {
      name: props.rowData.name,
      // 性别字符串转数字：'男声' -> 1, '女声' -> 0
      gender: getGenderNumber(props.rowData.gender),
      // 语言映射到Lang枚举
      lang: mapLanguageToEnum(props.rowData.language),
      description: props.rowData.description,
      audio_url: props.rowData.audioUrl,
      // 收藏状态保持原值
      is_favorite: props.rowData.isCollect,
      need_reduction: false
    };

    Object.assign(model, editData);

    // 编辑模式下默认展开表单详情
    showMoreDetails.value = true;

    // 音频文件初始化
    if (props.rowData.audioUrl) {
      promptAudioBlobUrl.value = props.rowData.audioUrl;
      fileName.value = props.rowData.name || '音频文件';

      // 延迟初始化WaveSurfer，确保DOM已渲染
      setTimeout(() => {
        initWaveSurfer();
      }, 100);
    }
  } else {
    // 添加模式的原有逻辑
    // 清理音频文件
    removeAudioFile();
    // 重置展开收起状态 - 修改为默认展开
    showMoreDetails.value = true;
    // 重置音频预览状态
    isPreviewPlaying.value = false;
  }
}

// 性别字符串转数字的辅助函数
function getGenderNumber(gender: string): 0 | 1 | null {
  if (gender === '男声') return 1;
  if (gender === '女声') return 0;
  return null;
}

// 语言字符串映射到Lang枚举的辅助函数
function mapLanguageToEnum(language: string): Lang | null {
  const languageMap: Record<string, Lang> = {
    '中文-普通话': Lang.CMN,
    '中文-粤语': Lang.YUE,
    英语: Lang.ENG,
    英文: Lang.ENG,
    日语: Lang.JPN,
    日文: Lang.JPN,
    韩语: Lang.KOR
  };

  return languageMap[language] || null;
}

// 关闭模态框
function closeModal() {
  visible.value = false;
}

// 提交音色数据的辅助函数
async function submitToneData(submitData: ModifyToneReq) {
  let response;
  if (props.operateType === 'add') {
    response = await createTone(submitData);
  } else {
    response = await updateTone(props.rowData!.id, submitData);
  }
  console.log('API响应:', response);

  // 检查响应是否成功
  if (response.data) {
    const successMessage = props.operateType === 'add' ? '音色添加成功' : '音色更新成功';
    message.success(successMessage);
    closeModal();
    emit('submitted', response.data);
  } else {
    const errorMessage = props.operateType === 'add' ? '音色添加失败，请重试' : '音色更新失败，请重试';
    message.error(errorMessage);
  }
}

// 提交处理
async function handleSubmit() {
  try {
    loading.value = true;

    // 1. 验证表单和音频数据
    await validateSubmission();

    // 2. 获取剪辑后的音频Base64数据
    const audioBase64 = await clip();

    // 3. 验证音频数据
    if (!audioBase64 || audioBase64.length < 100) {
      throw new Error('音频数据处理失败，请重新上传音频');
    }

    // 4. 上传音频到COS
    const cosUrl = await uploadAudioToCOS(audioBase64);

    // 5. 准备提交数据 - 使用COS URL替代Base64
    const submitData: ModifyToneReq = {
      name: model.name,
      gender: model.gender!, // 此时已确保不为null
      lang: model.lang!, // 此时已确保不为null
      description: model.description,
      audio_url: cosUrl, // 使用COS URL
      is_favorite: model.is_favorite,
      need_reduction: model.need_reduction
    };

    console.log('提交数据:', {
      ...submitData,
      audio_url: cosUrl
    });

    // 6. 调用API并处理响应
    await submitToneData(submitData);
  } catch (error: any) {
    console.error('提交失败:', error);

    // 处理不同类型的错误
    if (error?.message?.includes('上传') || error?.message?.includes('COS') || error?.message?.includes('CORS')) {
      message.error(`音频上传失败`);
    } else if (error?.message) {
      // message.error(`操作失败`);
    } else {
      message.error('添加失败，请重试');
    }
  } finally {
    loading.value = false;
  }
}

// 监听模态框显示状态变化
watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});

// 组件卸载时清理WaveSurfer实例
onUnmounted(() => {
  wavesurfer?.destroy();
});
</script>

<template>
  <NModal
    v-model:show="visible"
    :title="title"
    preset="card"
    class="max-w-40vw w-700px"
    :mask-closable="false"
    :closable="true"
    :loading="loading"
  >
    <NScrollbar class="max-h-70vh">
      <NForm
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <!-- 音频上传区域 -->
        <NFormItem class="mb-6">
          <div class="audio-upload-container w-full">
            <!-- 显示上传组件（未选择文件时） -->
            <NSpin :show="uploadLoading">
              <NUpload
                v-show="!hasAudioFile"
                ref="uploadRef"
                :custom-request="customRequest"
                accept=".flac,.mp3,.m4a,.ogg,.wav,.webm"
                :show-file-list="false"
                :max="1"
              >
                <NUploadDragger class="min-h-120px">
                  <div class="upload-content">
                    <NFlex class="upload-icon mb-3" justify="center">
                      <SvgIcon icon="material-symbols:upload-file" class="text-4xl text-primary" />
                    </NFlex>
                    <NText class="text-base font-medium">点击或拖拽音频文件到此处上传</NText>
                    <NP depth="3" class="mt-1 text-sm">
                      支持格式：MP3、WAV、FLAC、M4A、OGG、WebM
                      <br />
                      音频为纯净人声，建议时长3-10秒，文件大小不超过50MB
                    </NP>
                  </div>
                </NUploadDragger>
              </NUpload>
            </NSpin>

            <!-- 显示音频波形图和剪辑工具 -->
            <div v-show="hasAudioFile" class="audio-waveform-container">
              <NFlex justify="center" class="mb-1">
                <NText>音频剪辑</NText>
              </NFlex>
              <!-- 波形图容器 -->
              <div class="waveform-wrapper">
                <div id="waveform-container" class="min-h-128px"></div>
                <!-- 时间标签 - 跟随手柄位置 -->
                <div class="time-labels">
                  <div class="time-label time-label-start">
                    <NCard size="small" class="time-card">
                      <NFlex align="center" :size="4">
                        <!-- <SvgIcon icon="material-symbols:play-arrow" class="text-xs" /> -->
                        <span class="text-xs">{{ audioStartTime.toFixed(2) }}s</span>
                      </NFlex>
                    </NCard>
                  </div>
                  <div class="time-label time-label-end">
                    <NCard size="small" class="time-card">
                      <NFlex align="center" :size="4">
                        <!-- <SvgIcon icon="material-symbols:stop" class="text-xs" /> -->
                        <span class="text-xs">{{ audioEndTime.toFixed(2) }}s</span>
                      </NFlex>
                    </NCard>
                  </div>
                </div>
              </div>

              <!-- 展开收起按钮 -->

              <NFlex class="mb-4 mt-7 w-full" justify="center">
                <NButton text type="primary" class="w-50" @click="toggleMoreDetails">
                  {{ showMoreDetails ? '收起更多音色详情' : '填写更多音色详情' }}

                  <SvgIcon
                    :icon="showMoreDetails ? 'material-symbols:expand-less' : 'material-symbols:expand-more'"
                    class="text-xl transition-transform duration-200"
                  />
                </NButton>
              </NFlex>

              <!-- 音色详情信息 -->
              <div v-show="showMoreDetails" class="more-details-section">
                <NFlex>
                  <NFormItem path="name" class="flex-1">
                    <NInput v-model:value="model.name" placeholder="请输入音色名称" :maxlength="20" clearable />
                  </NFormItem>

                  <NFormItem path="gender" class="flex-1">
                    <NSelect v-model:value="model.gender" :options="genderOptions" placeholder="请选择音色性别" />
                  </NFormItem>

                  <NFormItem path="lang" class="flex-1">
                    <NSelect v-model:value="model.lang" :options="languageOptions" placeholder="请选择音色语言" />
                  </NFormItem>
                </NFlex>

                <NFormItem class="mt-3">
                  <NInput
                    v-model:value="model.description"
                    type="textarea"
                    placeholder="请输入音色描述..."
                    :rows="3"
                    clearable
                  />
                </NFormItem>
              </div>

              <NFlex class="relative mt-10" justify="space-around">
                <!-- WaveSurfer播放控制 -->
                <NButton type="info" size="large" class="w-40" @click="triggerPlayPause">
                  <template #icon>
                    <SvgIcon :icon="playing ? 'zondicons:pause-outline' : 'zondicons:play-outline'" />
                  </template>
                  {{ playing ? '暂停' : '播放' }}
                </NButton>

                <!-- 音频降噪选项 - 定位在确认添加按钮上方 -->
                <div class="relative">
                  <NCheckbox
                    v-model:checked="model.need_reduction"
                    :disabled="props.operateType === 'edit' && props.rowData && props.rowData.is_reduction === true"
                    class="absolute left-1/2 whitespace-nowrap -top-8 -translate-x-1/2"
                  >
                    音频降噪
                  </NCheckbox>
                  <NButton type="info" size="large" class="w-40" :loading="loading" @click="handleSubmit">
                    {{ submitButtonText }}
                  </NButton>
                </div>
              </NFlex>

              <!-- 音频预览元素 -->
              <audio
                ref="previewAudioRef"
                :src="promptAudioBlobUrl"
                class="hidden"
                @ended="isPreviewPlaying = false"
                @pause="isPreviewPlaying = false"
                @error="isPreviewPlaying = false"
              />
            </div>
          </div>
        </NFormItem>
      </NForm>
    </NScrollbar>

    <!--
 <template #footer>
      <NSpace justify="end">
        <NButton @click="closeModal">取消</NButton>
        <NButton type="primary" :loading="loading" :disabled="!hasAudioFile" @click="handleSubmit">
          {{ loading ? '正在添加音色...' : '确认添加' }}
        </NButton>
      </NSpace>
    </template>
-->
  </NModal>
</template>

<style scoped lang="scss">
// 音色操作模态框样式
.n-modal {
  .n-card {
    .n-card__content {
      padding-top: 16px;
    }
  }
}

// 音频上传区域样式
.audio-upload-container {
  .upload-content {
    text-align: center;
    padding: 20px;

    .upload-icon {
      color: var(--primary-color);
    }
  }

  .audio-waveform-container {
    .waveform-wrapper {
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 12px;
      background-color: var(--body-color);
      position: relative;

      // WaveSurfer容器样式
      :deep(#waveform-container) {
        width: 100%;
        min-height: 100px;
      }

      // 自定义裁剪手柄样式
      // :deep(.wavesurfer-region-handle) {
      //   width: 8px !important;
      //   background: linear-gradient(45deg, #18a058, #36ad6a) !important;
      //   border-radius: 4px !important;
      //   border: 2px solid #fff !important;
      //   box-shadow: 0 2px 8px rgba(24, 160, 88, 0.3) !important;
      //   cursor: col-resize !important;
      //
      //   &:hover {
      //     background: linear-gradient(45deg, #16a085, #27ae60) !important;
      //     box-shadow: 0 4px 12px rgba(24, 160, 88, 0.5) !important;
      //     transform: scaleY(1.1) !important;
      //     transition: all 0.2s ease !important;
      //   }
      //
      //   &:before {
      //     content: '⋮';
      //     position: absolute;
      //     top: 50%;
      //     left: 50%;
      //     transform: translate(-50%, -50%);
      //     color: #fff;
      //     font-size: 12px;
      //     font-weight: bold;
      //     line-height: 1;
      //   }
      // }

      // 自定义region样式
      // :deep(.wavesurfer-region) {
      //   border: 2px solid #18a058 !important;
      //   border-radius: 4px !important;
      //   background: rgba(24, 160, 88, 0.15) !important;
      //
      //   &:hover {
      //     background: rgba(24, 160, 88, 0.25) !important;
      //   }
      // }

      // 时间标签容器
      .time-labels {
        position: relative;
        height: 0;
        width: 100%;

        .time-label {
          position: absolute;
          bottom: -2.5em;
          left: var(--position, 0%);
          transform: translateX(-50%);
          z-index: 10;
          pointer-events: none;
          white-space: nowrap;

          .time-card {
            // background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(4px);
            // border: 1px solid rgba(24, 160, 88, 0.3) !important;
            border-radius: 6px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            padding: 4px 8px !important;
            min-width: auto !important;
            white-space: nowrap;

            :deep(.n-card__content) {
              padding: 0 !important;
            }

            :deep(.n-flex) {
              white-space: nowrap;
            }

            span {
              white-space: nowrap;
            }
          }

          // &.time-label-start .time-card {
          //   border-color: rgba(24, 160, 88, 0.5) !important;

          //   :deep(.n-flex) {
          //     color: #18a058 !important;
          //   }
          // }

          // &.time-label-end .time-card {
          //   border-color: rgba(239, 68, 68, 0.5) !important;

          //   :deep(.n-flex) {
          //     color: #ef4444 !important;
          //   }
          // }
        }
      }
    }

    .audio-controls {
      border: 1px solid var(--border-color);
      border-radius: 6px;

      .audio-info {
        flex: 1;
        color: var(--text-color);

        // 响应式设计：小屏幕时调整布局
        @media (max-width: 768px) {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }

      .control-buttons {
        flex-shrink: 0;

        // 响应式设计：小屏幕时调整间距
        @media (max-width: 768px) {
          gap: 4px;
        }
      }
    }

    .operation-tips {
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

// 展开收起功能样式
// .more-details-toggle {
//   .n-button {
//     border: 1px dashed var(--border-color);
//     border-radius: 6px;
//     min-height: 40px;
//     transition: all 0.3s ease;

//     &:hover {
//       border-color: var(--primary-color);
//       background-color: var(--primary-color-hover);
//     }
//   }
// }

.more-details-section {
  animation: fadeInDown 0.3s ease-out;
}

// 动画效果
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .n-modal {
    .n-card {
      margin: 12px;
      max-width: calc(100vw - 24px);
    }
  }

  .audio-upload-container {
    .upload-content {
      padding: 16px;
    }

    .audio-waveform-container {
      .waveform-wrapper {
        padding: 8px;
      }

      .audio-controls {
        padding: 12px;
        flex-direction: column;
        gap: 12px;

        .audio-info {
          text-align: center;
        }

        .control-buttons {
          justify-content: center;
        }
      }
    }
  }

  .more-details-section {
    padding: 12px;
  }

  // 移动端时间标签适配
  .audio-waveform-container .waveform-wrapper .time-labels .time-label {
    .time-card {
      font-size: 10px !important;
      padding: 2px 4px !important;

      :deep(.n-flex) {
        font-size: 10px !important;
      }
    }
  }
}

// 表单样式优化
:deep(.n-form-item-feedback-wrapper) {
  display: none;
}

// 上传拖拽区域样式
:deep(.n-upload-dragger) {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-color-hover);
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .audio-waveform-container .waveform-wrapper .time-labels .time-label .time-card {
    background: rgba(0, 0, 0, 0.85) !important;
    color: #fff !important;
  }
}
</style>
