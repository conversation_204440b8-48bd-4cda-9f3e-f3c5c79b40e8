import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Query, Request
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.users import User, get_request_user
from models.tasks import Task, TaskStatus, TaskType
from service.credit import CreditOperator
from task_queue.task_manager import TaskManager
from utils.database import get_db
from app.api_request_time import record_time
from service.translate import get_translate
from utils.common import contains_chinese
from service.tencent.cos import move_object
from sqlalchemy import select
from app.video.common import (
    VideoGenerationRequest, VideoTaskResponse, process_video_generation
)

AI_SERVER = app_settings.ai_server
TENCENT_COS_HOST = app_settings.tencent_cos_host

router = APIRouter()
logger = logging.getLogger(__name__)

# 响应模型
class VideoHistoryResponse(BaseModel):
    code: str = "0000"
    data: Optional[List[Dict[str, Any]]] = None
    msg: Optional[str] = None

# 取消任务请求模型
class CancelTaskRequest(BaseModel):
    taskid: str

# 任务取消响应模型
class CancelTaskResponse(BaseModel):
    code: str = "0000"
    msg: Optional[str] = None

@router.post("/generate", tags=["volcengine"], response_model=VideoTaskResponse)
@record_time(api_name="volcengine_generate")
async def generate_video(
    request: Request,
    req: VideoGenerationRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    火山引擎视频生成接口 - 创建生成任务
    """
    operator = CreditOperator(user.id, request.state.client_ip, user.username, db)
    await operator.pre_debit(1, 'video-generation', req.model)
    response = await process_video_generation(req, user, db, api_tag="volcengine", credit_operator_id=operator.id)
    return response
