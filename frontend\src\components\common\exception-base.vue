<script lang="ts" setup>
import { computed } from 'vue';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { fetchLogout } from '@/service/api/auth';
import { DEFAULT_COOKIE_OPTIONS, deleteCookie } from '@/utils/cookie';
defineOptions({ name: 'ExceptionBase' });

type ExceptionType = '403' | '404' | '500';

interface Props {
  /**
   * Exception type
   *
   * - 403: no permission
   * - 404: not found
   * - 500: service error
   */
  type: ExceptionType;
}

const props = defineProps<Props>();

const { routerPushByKey } = useRouterPush();

async function logout() {
  deleteCookie('token', DEFAULT_COOKIE_OPTIONS);
  await fetchLogout();
  window.location.href = '/login';
}

const iconMap: Record<ExceptionType, string> = {
  '403': 'no-permission',
  '404': 'not-found',
  '500': 'service-error'
};

const icon = computed(() => iconMap[props.type]);
</script>

<template>
  <div class="size-full min-h-520px flex-col-center gap-24px overflow-hidden">
    <div class="flex text-400px text-primary">
      <SvgIcon :local-icon="icon" />
    </div>
    <NButton type="primary" @click="routerPushByKey('root')">{{ $t('common.backToHome') }}</NButton>
    <NButton v-if="type == '403'" type="primary" @click="logout">
      {{ $t('common.backToLogin') }}
    </NButton>
  </div>
</template>

<style scoped></style>
