from datetime import datetime
from enum import StrEnum
from typing import Any

from sqlalchemy.dialects.mysql import INTEGER, TEXT, DATETIME, JSON, ENUM, BOOLEAN, VARCHAR
from sqlalchemy.orm import Mapped, mapped_column

from utils.database import Base


class Status(StrEnum):
    SUBMITTED = "SUBMITTED"
    IN_PROGRESS = "IN_PROGRESS"
    CANCELED = "CANCELED"
    FAILURE = "FAILURE"
    SUCCESS = "SUCCESS"


class AudioSynthesisHistory(Base):
    __tablename__ = "audio_synthesis_history"

    id: Mapped[int] = mapped_column(INTEGER, primary_key=True, nullable=False, comment="合成历史标识")
    params: Mapped[dict[str, Any]] = mapped_column(JSON, comment='合成参数，结构：{ "segs": [] }，segs 中每一个元素表示用于生成音频的参数')
    url: Mapped[str] = mapped_column(TEXT, comment="合成结果链接")
    duration: Mapped[int] = mapped_column(INTEGER, nullable=False, default=0, comment="音频时长（秒）")
    create_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, comment='创建时间')
    update_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, onupdate=datetime.now,
                                                  comment='更新时间')
    user_id: Mapped[int] = mapped_column(INTEGER, nullable=False, comment="用户 ID")
    status: Mapped[Status] = mapped_column(ENUM(Status), nullable=False, comment="状态")


class AudioPretrainedTonesStatus(Base):
    __tablename__ = "audio_pretrained_tones_status"

    id: Mapped[int] = mapped_column(INTEGER, primary_key=True, nullable=False, comment="合成历史标识")
    user_id: Mapped[int] = mapped_column(INTEGER, nullable=False, comment="用户 ID")
    tone_id: Mapped[str] = mapped_column(VARCHAR(128), nullable=False, comment="音色标识")
    is_favorite: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否收藏')
    create_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, comment='创建时间')
    update_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, onupdate=datetime.now,
                                                  comment='更新时间')
