import logging

from utils.redis import redis
from service.file import save_image_by_url
from utils.database import get_db
import importlib
from config import endpoint_config
from sqlalchemy.future import select


logger = logging.getLogger(__name__)

aiadmin_app_queue_name = endpoint_config["aiadmin_app_queue"]["queue_name"]  # 队列名称
aiadmin_app_queue_group_name = endpoint_config["aiadmin_app_queue"]["group_name"]  # 消费组名称
aiadmin_app_queue_consumer_name = endpoint_config["aiadmin_app_queue"]["consumer_name"]  # 消费者名称


async def process_task(task_argv):
    """
    task_argv: {
        'image_url': mj图片地址,
        'task_id': 数据库 ID,
        'table_name': 数据库表名
    }

    Args:
        task_argv:

    Returns:

    """
    # task = json.loads(task_argv)s
    image_url = task_argv['image_url']
    task_id = task_argv['id']
    table_name = task_argv['table']

    # 获取模型类
    module_name = f"models.{table_name}"
    class_name = ''.join(word.capitalize() for word in table_name.split('_'))
    Model = getattr(importlib.import_module(module_name), class_name)

    # 测试
    # try:
    #     module_name = f"models.{table_name}"
    #     class_name = ''.join(word.capitalize() for word in table_name.split('_'))
    #     print(f"Importing module: {module_name}, class: {class_name}")
    #     Model = getattr(importlib.import_module(module_name), class_name)
    #     print(f"Model imported: {Model}")
    # except Exception as e:
    #     print(f"Failed to import model: {e}")
    #     return

    loss = 0  # 计数器
    try:
        # 上传图片到 OSS
        cloud_url = await save_image_by_url(image_url)

        async for db in get_db():
            # 数据库退出时自动关闭，无论是否抛出异常。
            try:
                result = await db.execute(select(Model).filter(Model.id == task_id))
                db_task = result.scalars().first()
                if db_task:
                    db_task.image_url = cloud_url
                    await db.commit()
            except Exception as e:
                await db.rollback()
                raise e
    except Exception as e:
        logger.error(f"上传图片到 OSS 失败: {e}")
        loss += 1
        if loss < 3:
            await redis.xadd(aiadmin_app_queue_name, task_argv)
        else:
            await redis.xack(aiadmin_app_queue_name, aiadmin_app_queue_group_name, task_id)
            await redis.delete(task_id)
        raise e
