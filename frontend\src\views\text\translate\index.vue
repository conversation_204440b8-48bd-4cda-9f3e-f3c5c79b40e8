<script setup lang="ts">
import { computed, h, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { NButton, NDataTable, NInput, NTag, useDialog, useMessage } from 'naive-ui';
import { $t } from '@/locales';
import { fetchFilePresets, fetchSupportedLanguages, fetchSupportedModels, translateStream } from '@/service/api/text';
import { postVolcanoTTS } from '@/service/api';
import SvgIcon from '@/components/custom/svg-icon.vue';
import EditDraw from '../presetmange/editDraw.vue';
import BatchTranslation from './modules/batch-translation.vue';

// 消息提示
const message = useMessage();
const dialog = useDialog();
const isformData = ref<boolean>(false);

// 是否显示预设内容
const isdisplaypresets = ref<boolean>(false);

// 批量翻译抽屉
const showBatchTranslation = ref<boolean>(false);

// 用户输入内容
const userInput = ref<string>('');

// 监控userInput的值是否为空
const isUser = ref<boolean>(false);

// 下载翻译的内容，当ai翻译完成并返回内容后，值修改成true
const AIoutput_finally = ref<boolean>(false);

// 翻译显示区域是否处在加载状态
const isfiallyts = ref<boolean>(false);

// 音频生成状态
const audioGenerating = ref<{ [key: string]: boolean }>({});

// AI翻译内容 - 修改结构以支持多目标翻译和音频
const tableData = ref<{ language?: string; text?: string; translation: string; audioUrls?: string[] }[]>([]);

// 使用预设选项
const usePresets = ref<boolean>(false);

// 语言选项和语言信息映射
const languageInfoMap = ref<{ [key: string]: any }>({});

// 翻译类型选择
const type_options = ref<{ label: string; value: string; audio?: number }[]>([]);
const type_selected = ref<string | string[]>([]);

// 预置游戏名词选择
const presetOptions = ref<{ label: string; value: string }[]>([{ label: '默认', value: 'Default' }]);
const presetSelected = ref<string>('Default');

// 模型选择选项
const modelOptions = ref<{ label: string; value: string }[]>([]);
const selectedModel = ref<string>('');

// 抽屉显示控制
const active = ref<boolean>(false);

// 音频URL缓存
const audioUrlCache = ref<{ [key: string]: string }>({});

// 添加全局音频引用
const currentAudio = ref<HTMLAudioElement | null>(null);

// 流式翻译控制
const streamController = ref<{ cancel: () => void } | null>(null);

// 当前正在翻译的语言索引
const currentTranslatingIndex = ref<number>(0);
// 待翻译的语言列表
const pendingLanguages = ref<string[]>([]);

// 添加翻译完成状态跟踪
const translationStatus = ref<{ [key: string]: boolean }>({});

// 添加一个标记，记录用户是否修改了输入内容
const userInputChanged = ref<boolean>(false);

// 监听输入变化
watch(userInput, newValue => {
  isUser.value = newValue.trim() !== '';

  // 检查输入是否真的发生了变化
  if (tableData.value.length > 0) {
    let inputChanged = false;
    for (const row of tableData.value) {
      if (row.text !== newValue) {
        inputChanged = true;
        break;
      }
    }

    // 如果输入确实变化了，设置标记
    if (inputChanged) {
      userInputChanged.value = true;
    }
  }
});

// 监听语言选择，确保至少选择一种语言
watch(
  () => type_selected.value,
  newValue => {
    // 确保至少选择一个语言
    if (Array.isArray(newValue) && newValue.length === 0 && type_options.value.length > 0) {
      type_selected.value = [type_options.value[0].value];
    }

    // 当用户删除语言时，从tableData中移除对应的行
    if (Array.isArray(newValue) && tableData.value.length > 0) {
      // 获取当前选中的语言标签
      const selectedLabels = new Set(
        newValue.map(langCode => {
          const option = type_options.value.find(opt => opt.value === langCode);
          return option?.label || '';
        })
      );

      // 过滤tableData，只保留选中的语言
      tableData.value = tableData.value.filter(row => selectedLabels.has(row.language || ''));
    }

    // 如果用户已修改输入内容，确保新增的语言在翻译时能被识别为未翻译
    if (userInputChanged.value) {
      // 注意：这里不清空翻译结果，只在用户点击翻译按钮时才清空
      // 此处只是确保系统知道有输入变化
    }
  },
  { deep: true }
);

// 获取预设名称
async function getprestname() {
  const response = await fetchFilePresets();
  if (response.data) {
    const presetFiles = response.data.map((file: { gamename: string; gamecode: string }) => ({
      label: file.gamename,
      value: file.gamecode
    }));
    presetOptions.value = [{ label: '默认', value: 'Default' }, ...presetFiles];
  }
}

// 获取支持的语言列表
async function getSupportedLanguages() {
  try {
    const response = await fetchSupportedLanguages();
    if (response.data) {
      type_options.value = response.data;

      // 构建语言信息映射，方便后续使用
      response.data.forEach((lang: any) => {
        languageInfoMap.value[lang.value] = lang;
      });

      if (type_options.value.length > 0) {
        // 初始化为数组，确保默认选中第一个语言
        if (!Array.isArray(type_selected.value) || type_selected.value.length === 0) {
          type_selected.value = [type_options.value[0].value];
        }
      }
    }
  } catch (error) {
    // message.error('获取支持的语言列表失败');
  }
}

// 获取支持的模型列表
async function getSupportedModels() {
  try {
    const response = await fetchSupportedModels();
    if (response.data) {
      modelOptions.value = response.data;
      if (modelOptions.value.length > 0) {
        selectedModel.value = modelOptions.value[0].value;
      }
    }
  } catch (error) {
    // message.error('获取支持的模型列表失败');
  }
}

// 初始化用户输入变化检测
const initUserInputChanged = () => {
  // 重置用户输入变化标记
  userInputChanged.value = false;
};

// 查看预设内容
function handleViewPresets() {
  if (presetSelected.value === '默认') {
    // message.error($t('page.text.pleaseChooseGame'));
    return;
  }
  active.value = true;
}

// 打开批量翻译抽屉
function openBatchTranslation() {
  showBatchTranslation.value = true;
}

// 音频播放函数，只处理单个音频
const playAudio = async (text: string, voiceType: string, langCode: string, rowIndex: number, iconIndex: number) => {
  // 设置音频生成状态
  const audioKey = `${rowIndex}-${iconIndex}`;
  audioGenerating.value[audioKey] = true;

  // 停止当前正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value.currentTime = 0;
    currentAudio.value = null;
  }

  try {
    // 检查缓存
    if (audioUrlCache.value[audioKey]) {
      const audioElement = new Audio(audioUrlCache.value[audioKey]);
      audioElement.play().catch(err => {
        console.error('音频播放失败:', err);
      });
      // 保存当前播放的音频引用
      currentAudio.value = audioElement;
      return;
    }

    // 限制文本长度为300字
    const truncatedText = text.slice(0, 300);

    const params = {
      text: truncatedText,
      voice_type: voiceType,
      encoding: 'wav',
      speed_ratio: 1,
      volume_ratio: 1,
      pitch_ratio: 1,
      silence_duration: 125,
      emotion: '',
      language: langCode
    };

    try {
      const response = await postVolcanoTTS(params);
      if (response.data) {
        const audioUrl = `data:audio/wav;base64,${response.data.data}`;
        // 缓存音频URL
        audioUrlCache.value[audioKey] = audioUrl;
        const audioElement = new Audio(audioUrl);
        audioElement.play().catch(err => {
          console.error('音频播放失败:', err);
        });
        // 保存当前播放的音频引用
        currentAudio.value = audioElement;
      }
    } catch (err) {
      console.error(`音频生成失败 (${voiceType}, ${langCode}):`, err);
    }
  } catch (error) {
    console.error('音频生成失败:', error);
  } finally {
    audioGenerating.value[audioKey] = false;
  }
};

// 音频按钮渲染函数
const renderAudioButtons = (
  row: { language?: string; text?: string; translation: string; audioUrls?: string[] },
  rowIndex: number
) => {
  // 查找对应的语言信息
  const languageInfo = Object.entries(languageInfoMap.value).find(([_, info]) => info.label === row.language)?.[1];

  // 如果不支持音频，返回null
  if (!languageInfo || !languageInfo.audio || languageInfo.audio !== 1) {
    return null;
  }

  // 解析voice_type和langcode
  const voiceTypes = languageInfo.voice_type.split(',');
  const langCodes = languageInfo.langcode.split(',');

  // 如果数组长度不匹配或为空，返回null
  if (!voiceTypes.length || !langCodes.length || voiceTypes.length !== langCodes.length) {
    return null;
  }

  // 创建所有音频按钮
  const audioButtons = [];
  for (let i = 0; i < voiceTypes.length; i++) {
    const voiceType = voiceTypes[i];
    const langCode = langCodes[i];
    const audioKey = `${rowIndex}-${i}`;
    const isGenerating = audioGenerating.value[audioKey];

    // 根据langCode确定显示文本
    let displayText = '';
    if (langCode === 'zh_yueyu') {
      displayText = '粤语';
    } else if (langCode === 'zh_taipu') {
      displayText = '普通话';
    } else {
      // 对于其他语言代码，可以使用默认文本或原始代码
      displayText = langCode;
    }

    // 判断是否是多音频模式
    if (voiceTypes.length > 1) {
      // 多音频模式使用NTag
      audioButtons.push(
        h(
          NTag,
          {
            type: 'default',
            bordered: false,
            class: 'audio-play-tag mr-2 cursor-pointer',
            size: 'small',
            onClick: (e: MouseEvent) => {
              e.stopPropagation();
              playAudio(row.translation.trim(), voiceType, langCode, rowIndex, i);
            },
            title: `点击播放音频`
          },
          {
            default: () =>
              h('div', { class: 'flex' }, [displayText, h(SvgIcon, { icon: 'rivet-icons:audio-solid', class: 'ml-1' })])
          }
        )
      );
    } else {
      // 单音频模式保持使用NButton
      audioButtons.push(
        h(
          NButton,
          {
            text: true,
            class: 'audio-play-button mr-2',
            onClick: (e: MouseEvent) => {
              e.stopPropagation();
              playAudio(row.translation.trim(), voiceType, langCode, rowIndex, i);
            },
            loading: isGenerating,
            title: `点击播放音频`
          },
          { default: () => h(SvgIcon, { icon: 'rivet-icons:audio-solid' }) }
        )
      );
    }
  }

  // 返回一个包含所有音频按钮的div
  return h('div', { class: 'audio-buttons-container flex' }, audioButtons);
};

// 停止翻译
const stopTranslation = () => {
  if (streamController.value) {
    streamController.value.cancel();
    streamController.value = null;
    isfiallyts.value = false;
  }
};

// 在组件销毁前取消流式请求
onBeforeUnmount(() => {
  stopTranslation();
});

// 提前声明函数类型，解决循环引用问题
type HandleStreamMessageFn = (messageData: string) => void;
type TranslateNextLanguageFn = () => Promise<void>;

// 前向声明，避免TypeScript错误
let translateNextLanguage: TranslateNextLanguageFn;

// 处理流式翻译消息
const handleStreamMessage: HandleStreamMessageFn = (messageData: string) => {
  try {
    // 解析JSON消息
    const data = JSON.parse(messageData);

    // 获取目标语言、翻译内容和完成状态
    const { language, text, done, error } = data;

    if (error) {
      message.error(`翻译错误`);
      isfiallyts.value = false;
      return;
    }

    // 查找对应语言的行
    const languageOption = type_options.value.find(opt => opt.value === language);
    const languageLabel = languageOption?.label || language;

    // 查找匹配的行索引
    const rowIndex = tableData.value.findIndex(row => row.language === languageLabel);

    if (rowIndex >= 0) {
      // 更新已存在的行
      tableData.value[rowIndex].translation = text;

      // 如果翻译完成，标记该语言翻译状态为完成
      if (done) {
        translationStatus.value[language] = true;
      }
    }

    // 如果当前语言翻译完成
    if (done) {
      // 检查是否还有待翻译的语言
      if (currentTranslatingIndex.value < pendingLanguages.value.length - 1) {
        // 移动到下一个语言
        currentTranslatingIndex.value++;
        // 开始翻译下一个语言
        translateNextLanguage();
      } else {
        // 所有语言翻译完成
        AIoutput_finally.value = true;
        isfiallyts.value = false;
        message.success($t('page.text.tsSuccess'));
      }
    }
  } catch (error) {
    console.error('处理流式消息失败:', error);
  }
};

// 翻译下一个待翻译的语言
translateNextLanguage = async () => {
  const currentIndex = currentTranslatingIndex.value;
  if (currentIndex >= 0 && currentIndex < pendingLanguages.value.length) {
    const targetLanguage = pendingLanguages.value[currentIndex];
    const gamecode = presetSelected.value;
    const model = selectedModel.value;

    try {
      const controller = await translateStream(
        {
          userinput: userInput.value,
          target_language: targetLanguage,
          gamecode,
          model,
          use_presets: usePresets.value
        },
        handleStreamMessage,
        error => {
          console.error('翻译错误:', error);
          message.error('翻译失败，请重试');
          isfiallyts.value = false;
        },
        () => {
          console.log(`${targetLanguage} 翻译完成`);
        },
        () => {
          console.log(`开始 ${targetLanguage} 翻译`);
        }
      );

      if (controller) {
        streamController.value = controller;
      }
    } catch (error) {
      isfiallyts.value = false;
      message.error('翻译失败，请重试');
    }
  }
};

// 处理重新生成单一语言翻译
const regenerateTranslation = async (languageLabel: string) => {
  // 查找该语言对应的语言代码
  const languageInfo = Object.entries(languageInfoMap.value).find(([_, info]) => info.label === languageLabel)?.[1];
  if (!languageInfo) {
    message.error('无法找到语言信息');
    return;
  }

  const targetLanguage = languageInfo.value;

  // 显示加载状态
  isfiallyts.value = true;

  // 重置该语言的翻译状态
  translationStatus.value[targetLanguage] = false;

  // 设置只翻译这一种语言
  currentTranslatingIndex.value = 0;
  pendingLanguages.value = [targetLanguage];

  // 开始翻译
  const gamecode = presetSelected.value;
  const model = selectedModel.value;

  try {
    const controller = await translateStream(
      {
        userinput: userInput.value,
        target_language: targetLanguage,
        gamecode,
        model,
        use_presets: usePresets.value
      },
      handleStreamMessage,
      error => {
        console.error('重新生成翻译错误:', error);
        // message.error('翻译失败');
        isfiallyts.value = false;
      },
      () => {
        console.log(`翻译完成`);
        // 翻译完成后更新状态
        isfiallyts.value = false;
        // message.success(`翻译完成`);
      },
      () => {
        console.log(`开始重新生成`);
      }
    );

    if (controller) {
      streamController.value = controller;
    }
  } catch (error) {
    isfiallyts.value = false;
    // message.error('翻译失败');
  }
};

// 处理"开始翻译"按钮逻辑
const handleTranslate = async () => {
  if (!isUser.value) {
    message.error('请输入需要翻译的内容');
    return;
  }

  const targetLanguages = Array.isArray(type_selected.value) ? type_selected.value : [type_selected.value];

  // 检查是否选择了目标语言
  if (targetLanguages.length === 0) {
    message.error('请选择目标语言');
    return;
  }

  // 每次开始新翻译时清除音频缓存
  audioUrlCache.value = {};
  audioGenerating.value = {};

  isfiallyts.value = true;
  isdisplaypresets.value = false;

  // 如果用户修改了输入内容，则清空所有翻译结果并重置状态
  if (userInputChanged.value) {
    // 更新所有行的原文并清空翻译结果
    tableData.value.forEach(row => {
      row.text = userInput.value;
      row.translation = '';
    });

    // 重置所有语言的翻译状态
    for (const targetLanguage of targetLanguages) {
      translationStatus.value[targetLanguage] = false;
    }

    // 清除"所有语言已翻译完成"状态
    AIoutput_finally.value = false;

    // 重置输入变化标记
    userInputChanged.value = false;
  }

  // 查找需要翻译的新语言和未完成翻译的语言
  const existingLanguagesMap = new Map();
  tableData.value.forEach(row => {
    // 通过语言标签找到对应的语言代码
    const languageInfo = Object.entries(languageInfoMap.value).find(([_, info]) => info.label === row.language)?.[1];
    const langCode = languageInfo?.value || '';
    if (langCode) {
      existingLanguagesMap.set(langCode, {
        hasTranslation: row.translation.trim() !== '',
        completed: translationStatus.value[langCode] === true
      });
    }
  });

  // 筛选出需要翻译的语言：
  // 1. 新选择的语言
  // 2. 已选择但翻译未完成或为空的语言
  const languagesToTranslate = targetLanguages.filter(lang => {
    const langInfo = existingLanguagesMap.get(lang);
    return !langInfo || !langInfo.hasTranslation || !langInfo.completed;
  });

  // 添加新选择的语言到表格中
  for (const targetLanguage of targetLanguages) {
    if (!existingLanguagesMap.has(targetLanguage)) {
      const languageOption = type_options.value.find(opt => opt.value === targetLanguage);
      tableData.value.push({
        language: languageOption?.label || targetLanguage,
        text: userInput.value,
        translation: '',
        audioUrls: []
      });
    }
    // 重置翻译状态，只对需要翻译的语言
    if (languagesToTranslate.includes(targetLanguage)) {
      translationStatus.value[targetLanguage] = false;
    }
  }

  // 如果所有语言都已翻译，无需再次翻译
  if (languagesToTranslate.length === 0) {
    isfiallyts.value = false;
    message.success('翻译成功');
    return;
  }

  // 重置翻译状态
  currentTranslatingIndex.value = 0;
  pendingLanguages.value = languagesToTranslate;

  // 开始翻译第一个新语言
  translateNextLanguage();
};

// 计算属性：判断是否为英文
const isEnglishTarget = computed(() => {
  if (Array.isArray(type_selected.value)) {
    return type_selected.value.includes('English');
  }
  return type_selected.value === 'English';
});

// 处理单元格内容复制功能
const copyTranslationCell = (text: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      message.success('复制成功');
    },
    () => {
      message.error('复制失败，请重试');
    }
  );
};

// 处理表格中显示的原文内容
const getDisplayText = (row: { text?: string }) => {
  // 如果用户修改了输入内容但尚未点击翻译按钮，仍然显示原始文本
  // 而不是当前输入框中的内容
  if (userInputChanged.value) {
    return row.text || '';
  }
  // 否则显示当前输入框内容
  return userInput.value;
};

// DataTable 列定义
const columns = computed(() => {
  // 基础列配置
  const baseColumns = [
    {
      title: '语言',
      key: 'language',
      width: 120,
      align: 'center' as const
    },
    {
      title: '翻译',
      key: 'translation',
      align: 'center' as const,
      render: (
        row: { language?: string; text?: string; translation: string; audioUrls?: string[] },
        rowIndex: number
      ) => {
        // 获取语言代码
        const languageInfo = Object.entries(languageInfoMap.value).find(
          ([_, info]) => info.label === row.language
        )?.[1];
        const languageCode = languageInfo?.value || '';

        // 使用translationStatus判断翻译是否完成
        const isDone = languageCode && translationStatus.value[languageCode] === true;

        // 创建复制按钮（仅在翻译完成时显示）
        const copyButton = isDone
          ? h(
              NButton,
              {
                text: true,
                class: 'copy-button text-4 mr-4',
                onClick: e => {
                  e.stopPropagation();
                  copyTranslationCell(row.translation.trim());
                },
                title: '复制翻译内容'
              },
              { default: () => h(SvgIcon, { icon: 'ri:file-copy-2-line' }) }
            )
          : null;

        // 创建重新生成按钮（仅在翻译完成时显示）
        const regenerateButton = isDone
          ? h(
              NButton,
              {
                text: true,
                class: 'regenerate-button text-4 mr-4',
                onClick: e => {
                  e.stopPropagation();
                  regenerateTranslation(row.language || '');
                },
                title: '重新翻译',
                disabled: isfiallyts.value
              },
              { default: () => h(SvgIcon, { icon: 'mdi:refresh' }) }
            )
          : null;

        // 如果翻译已完成，才显示音频按钮
        const audioButton = isDone ? renderAudioButtons(row, rowIndex) : null;

        return h('div', { class: 'translation-cell flex justify-start flex-col mb-2' }, [
          h('pre', { style: 'white-space: pre-wrap; word-wrap: break-word; text-align: left;' }, row.translation),
          // 只有在翻译完成时才显示按钮容器
          isDone
            ? h(
                'div',
                { class: 'action-buttons-wrapper flex justify-start  mt-4' },
                [copyButton, regenerateButton, audioButton].filter(Boolean)
              )
            : null
        ]);
      }
    }
  ];

  // 多语言选择模式
  if (Array.isArray(type_selected.value) && type_selected.value.length > 1) {
    return baseColumns;
  }

  // 单目标翻译 - 文件上传模式
  if (isformData.value && tableData.value.length > 0) {
    return [
      {
        title: '原文',
        key: 'text',
        render: (row: { text?: string }) => {
          return h('div', { class: 'original-text' }, getDisplayText(row));
        }
      },
      ...baseColumns
    ];
  }

  // 单目标翻译 - 输入框模式
  return baseColumns;
});

// 导出表格
const outputTableRef = ref<any>(null);

interface downloadRow {
  language?: string;
  text?: string;
  translation: string;
  audioUrls?: string[];
}

const downloadCsv = () => {
  // 导出CSV时根据当前模式确定列
  let exportColumns;

  if (Array.isArray(type_selected.value) && type_selected.value.length > 1) {
    exportColumns = [
      {
        title: '语言',
        key: 'language'
      },
      {
        title: '原文',
        key: 'text'
      },
      {
        title: '翻译',
        key: 'translation'
      }
    ];
  } else {
    exportColumns = [
      {
        title: '原文',
        key: 'text'
      },
      {
        title: '翻译',
        key: 'translation'
      }
    ];
  }

  const csvContent = `data:text/csv;charset=utf-8,\uFEFF${exportColumns.map(col => col.title).join(',')}\n${tableData.value
    .map(row => exportColumns.map(column => row[column.key as keyof downloadRow] || '').join(','))
    .join('\n')}`;
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement('a');
  link.setAttribute('href', encodedUri);
  link.setAttribute('download', 'translated-data.csv');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 复制译文内容
const copyTranslations = async () => {
  try {
    let textToCopy = '';

    if (Array.isArray(type_selected.value) && type_selected.value.length > 1) {
      // 多目标模式：复制所有语言的译文
      textToCopy = tableData.value.map(row => `${row.language}:\n${row.translation}`).join('\n\n');
    } else {
      // 单目标模式：只复制译文
      textToCopy = tableData.value.map(row => row.translation).join('\n');
    }

    await navigator.clipboard.writeText(textToCopy);
    message.success('复制成功');
  } catch (error) {
    message.error('复制失败，请重试');
  }
};

// 获取预置游戏名词和支持的语言列表
onMounted(async () => {
  getprestname();
  getSupportedLanguages();
  getSupportedModels();
  initUserInputChanged();
});
</script>

<template>
  <NGrid cols="1 l:2" responsive="screen" x-gap="12" y-gap="8">
    <NGridItem>
      <NCard class="h-full">
        <!--
 <NAlert type="info" class="mb-3">
          支持不超过2000字的输入内容进翻译成目标语言，并且支持上传Excel表格进行批量翻译
        </NAlert>
-->
        <NFlex gap="2" class="mb-2" vertical>
          <!-- 目标语言选择 - 独占一行 -->
          <NInputGroup class="w-full">
            <NButton type="info" class="nocursor label-button">目标</NButton>
            <NSelect
              v-model:value="type_selected"
              :options="type_options"
              class="flex-select"
              multiple
              max-tag-count="responsive"
              :clearable="Array.isArray(type_selected) && type_selected.length > 1"
            />
          </NInputGroup>

          <!-- 其他控件使用wrap布局 -->
          <NFlex gap="2" wrap>
            <!-- 模型选择 -->
            <NInputGroup class="input-group flex">
              <NButton type="info" class="nocursor label-button">模型</NButton>
              <NSelect v-model:value="selectedModel" :options="modelOptions" class="flex-select" />
            </NInputGroup>

            <!-- 预置游戏名词选择 -->
            <NSelect
              v-if="presetOptions.length > 1"
              v-model:value="presetSelected"
              :options="presetOptions"
              class="preset-select"
            />

            <NDrawer v-model:show="active" placement="right" resizable width="80em">
              <NDrawerContent>
                <EditDraw :selected-game-code="presetSelected" />
              </NDrawerContent>
            </NDrawer>

            <NTooltip trigger="hover">
              <template #trigger>
                <NButton :disabled="!isEnglishTarget" @click="handleViewPresets">预设</NButton>
              </template>
              <span v-if="isEnglishTarget">查看/编辑 预设内容</span>
              <span v-else>目前仅支持英文预设</span>
            </NTooltip>

            <NCheckbox v-model:checked="usePresets" class="ml-2">使用预设</NCheckbox>

            <!-- 批量翻译按钮 -->
            <NFlex class="batch-translation-btn">
              <NButton type="primary" @click="openBatchTranslation">批量翻译</NButton>
              <!-- 下载示例表格按钮 -->
              <!--
 <NTooltip trigger="hover">
                <template #trigger>
                  <NButton text class="help-btn text-4" @click="downloadExampleCsv">
                    <SvgIcon icon="ph:question-bold" />
                  </NButton>
                </template>
                点击展开翻译抽屉
              </NTooltip>
-->
            </NFlex>

            <!-- 批量翻译抽屉 -->
            <NDrawer
              v-model:show="showBatchTranslation"
              placement="right"
              resizable
              width="90em"
              :mask-closable="false"
              :on-mask-click="
                () => {
                  dialog.warning({
                    title: '确认退出',
                    content: '确认退出批量翻译？',
                    positiveText: '确定',
                    negativeText: '取消',
                    onPositiveClick: () => {
                      showBatchTranslation = false;
                    }
                  });
                  return false;
                }
              "
            >
              <NDrawerContent title="批量翻译">
                <BatchTranslation />
              </NDrawerContent>
            </NDrawer>
          </NFlex>
        </NFlex>

        <!-- 用户输入内容，规定两千字上限 -->
        <NInput
          v-model:value="userInput"
          type="textarea"
          placeholder="请输入想要翻译的内容..."
          show-count
          :maxlength="2000"
          class="LeftBody mt-2 h-2/3"
        />

        <NFlex class="mt-2" justify="end">
          <NTooltip trigger="hover">
            <template #trigger>
              <NButton type="primary" class="w-35" :loading="isfiallyts" @click="handleTranslate">开始翻译</NButton>
            </template>
            开始翻译
          </NTooltip>
        </NFlex>
      </NCard>
    </NGridItem>

    <NGridItem>
      <NCard class="h-full">
        <NDataTable
          ref="outputTableRef"
          :columns="columns"
          :data="tableData"
          :style="{ minHeight: `20%` }"
          :max-height="500"
          :single-line="false"
        />
        <!-- AIoutput_finally -->
        <NFlex justify="end" class="mr-8">
          <NButtonGroup v-if="true" class="mt-2 w-1/3">
            <NButton type="primary" class="flex-1" ghost @click="downloadCsv">
              <template #icon>
                <SvgIcon icon="material-symbols:download"></SvgIcon>
              </template>
              下载表格
            </NButton>

            <!-- <NDivider vertical /> -->

            <NButton type="primary" class="flex-1" ghost @click="copyTranslations">
              <template #icon>
                <SvgIcon icon="mynaui:copy-solid"></SvgIcon>
              </template>
              复制
            </NButton>
          </NButtonGroup>
        </NFlex>
      </NCard>
    </NGridItem>
  </NGrid>
</template>

<style scoped>
.LeftHead {
  display: flex;
  gap: 0.5em;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
}

.nocursor {
  cursor: pointer;
}

.LeftBody {
  max-height: 80%;
  width: 100%;
}

/* 预置名词选择 */
.preset-select {
  max-width: 180px;
  width: 100%;
}

:deep(.n-select.flex-select) {
  width: 100%;
}

/* 输入组合样式 */
.input-group {
  max-width: 220px;
  width: 100%;
  display: flex;
}

/* 多目标模式下的输入组样式 */
.input-group-full {
  width: 100%;
  display: flex;
}

/* 标签按钮样式 */
.label-button {
  width: 70px;
  padding: 0 8px;
  flex-shrink: 0;
}

/* 输入组内的选择框样式 */
.flex-select {
  flex: 1;
  min-width: 60px;
}

.fixed-width-btn {
  width: 100% !important;
}

/* 帮助按钮样式 */
.help-btn {
  /* width: 20px; */
  flex-shrink: 0;
  padding: 0;
}

/* 上传容器样式 */
.upload-container {
  display: flex;
  justify-content: space-between;
  align-items: start;
}

/* 确保InputGroup内的Select也受约束 */
:deep(.n-input-group .n-select) {
  width: 100%;
}

.divstyle {
  height: 100%;
}

.right > * {
  overflow: hidden;
}

/* .RightBody {
  max-height: 80%;
  width: 100%;
} */

.RightBody_presets {
  max-height: 75%;
  width: 100%;
  margin-top: 1em;
  margin-bottom: 1em;
}

.presetsdata {
  height: 100%;
  margin-top: 1em;
}

:deep(.uploadBox) .n-button .n-button__content svg {
  font-size: 1.5em;
}

:deep(.uploadBox) .n-button .n-button__content {
  margin-left: 0.1em;
}

:deep(.uploadBox) .n-upload-trigger,
:deep(.uploadBox) .n-upload {
  width: 100% !important;
}

:deep(.batch-translation-btn) {
  gap: 0 !important;
  align-items: flex-start;
}

.n-checkbox {
  align-items: center;
}

/* 音频按钮样式 */
.audio-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
}

/* 新增音频按钮容器样式 */
.audio-buttons-container {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-right: 8px;
}

/* 修改音频按钮样式 */
.audio-play-button,
.copy-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 添加重新生成按钮样式 */
.regenerate-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 修改翻译单元格样式以适应多个按钮 */
.translation-cell {
  position: relative;
  width: 100%;
  min-height: 40px;
  padding-right: 130px; /* 增加右侧填充空间以容纳三个按钮 */
  padding-bottom: 10px; /* 增加底部空间 */
}

/* 按钮容器样式 */
.action-buttons-wrapper {
  position: absolute;
  right: 8px;
  bottom: 4px;
  display: flex;
  flex-direction: row; /* 确保水平布局 */
  flex-wrap: nowrap; /* 防止按钮换行 */
  gap: 8px;
  align-items: center;
}

/* 确保表格内容的响应式布局 */
:deep(.n-data-table-td) {
  padding: 8px !important;
  position: relative;
}

:deep(.n-data-table-tr) {
  height: auto !important;
}

/* :deep(.n-data-table-wrapper) {
  min-height: 200px;
  height: 100%;
} */
</style>
