import { $t } from '@/locales';

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label as App.I18n.I18nKey)
  }));
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className);
  }

  function remove() {
    document.documentElement.classList.remove(className);
  }

  return {
    add,
    remove
  };
}

interface ScaleResult {
  width: number;
  height: number;
  scale: number;
}

export function scaleDimensions(
  originalWidth: number,
  originalHeight: number,
  maxTargetWidth: number,
  maxTargetHeight: number
): ScaleResult {
  const scaleWidth = maxTargetWidth / originalWidth;
  const scaleHeight = maxTargetHeight / originalHeight;

  // 选择最小的缩放比例，以确保不会超过最大目标宽高
  const scale = Math.min(scaleWidth, scaleHeight);

  const finalWidth = originalWidth * scale;
  const finalHeight = originalHeight * scale;

  return {
    width: finalWidth,
    height: finalHeight,
    scale
  };
}
// 返回随机数
export function getRandomValueInRange(min: number, max: number): number {
  // 确保 min 和 max 值为整数
  const n_min = Math.ceil(min);
  const n_max = Math.floor(max);
  // 返回介于 min（包含）和 max（包含）之间的一个随机整数
  return Math.floor(Math.random() * (n_max - n_min + 1)) + n_min;
}
/**
 * 生成一个简单的 UUID（版本 4）
 *
 * @returns UUID 字符串
 */
export function generateUUID(): string {
  // 生成一个随机数并转换为十六进制字符串
  const randomNumber = (c: string) => {
    const random = Math.random() * 16;
    const value = c === 'x' ? random : (random & 0x3) | 0x8;
    return value.toString(16);
  };

  // 替换指定格式字符串中的 'x' 和 'y'
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, randomNumber);
}

/**
 * 通用文件下载函数，使用fetch API下载文件
 *
 * @param url 文件URL
 * @param customFileName 可选的自定义文件名
 * @returns Promise<void>
 */
export async function downloadFile(url: string, customFileName?: string): Promise<void> {
  if (!url) {
    throw new Error('下载地址无效');
  }

  try {
    // 使用fetch API获取文件
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 转换为blob
    const blob = await response.blob();

    // 创建临时URL
    const objectUrl = URL.createObjectURL(blob);

    // 创建下载链接
    const a = document.createElement('a');
    a.href = objectUrl;

    // 设置文件名
    // 如果提供了自定义文件名，则使用它
    if (customFileName) {
      a.download = customFileName;
    } else {
      // 否则从URL中提取文件名
      const urlParts = url.split('/');
      const fileNameWithParams = urlParts[urlParts.length - 1];
      const fileName = fileNameWithParams.split('?')[0]; // 移除URL参数

      // 如果无法从URL提取有效文件名，则根据MIME类型提供默认文件名
      if (fileName && fileName.includes('.')) {
        a.download = fileName;
      } else {
        // 尝试从MIME类型确定文件扩展名
        const contentType = response.headers.get('content-type');
        let ext = 'bin'; // 默认二进制文件

        // 根据MIME类型设置扩展名
        if (contentType) {
          if (contentType.includes('image/')) {
            ext = contentType.split('/')[1] || 'png';
          } else if (contentType.includes('video/')) {
            ext = contentType.split('/')[1] || 'mp4';
          } else if (contentType.includes('audio/')) {
            ext = contentType.split('/')[1] || 'mp3';
          }
        }

        a.download = `file.${ext}`;
      }
    }

    // 触发下载
    document.body.appendChild(a);
    a.click();

    // 清理
    document.body.removeChild(a);
    URL.revokeObjectURL(objectUrl);
  } catch (error) {
    console.error('文件下载失败:', error);
    throw error;
  }
}
