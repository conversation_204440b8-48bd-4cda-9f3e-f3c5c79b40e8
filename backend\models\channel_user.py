from sqlalchemy import Column, Integer, TIMESTAMP, text, UniqueConstraint
from sqlalchemy.dialects.mysql import TINYINT
from utils.database import Base

class ChannelUser(Base):
    __tablename__ = 'channel_user'

    permission_id = Column(Integer, primary_key=True, autoincrement=True, comment='权限id')
    channel_id = Column(Integer, nullable=False, comment='频道id')
    user_id = Column(Integer, nullable=False, comment='用户id')
    can_view = Column(TINYINT(1), nullable=False, comment='允许查看：0 和 1')
    can_send = Column(TINYINT(1), nullable=False, comment='允许发送：0 和 1')
    join_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), nullable=True, comment='加入时间')
    user_status = Column(TINYINT(1), nullable=False, comment='用户状态：0 和 1')

    __table_args__ = (
        UniqueConstraint('channel_id', 'user_id', name='UC_channel_user'),
    )
