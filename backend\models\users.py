import logging

from sqlalchemy import Column, Inte<PERSON>, String, BigInteger, Date, TIMESTAMP, UniqueConstraint
from sqlalchemy.dialects.mysql import TINYINT
from fastapi import Request
from security import is_non_validate_route
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.hash.md5 import md5
from utils.database import Base
import datetime
from fastapi import Depends, Header
import uuid
from utils.redis import redis
from sqlalchemy.ext.asyncio import AsyncSession
from Crypto.Hash import RIPEMD160
import hmac
from config import endpoint_config
from sqlalchemy.future import select
from typing import Annotated
from models.roles import Role
from models.user_role import UserRole
from collections.abc import Sequence, Container


logger = logging.getLogger(__name__)


SECRET_KEY = "09ur293209ur03qefj2gj0t23tejgw904gnih"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_DAYS = 15


class User(Base):
  __tablename__ = "users"

  id = Column(Integer, primary_key=True, autoincrement=True, nullable=True,comment='ID')
  group_id = Column(Integer, default=0, nullable=False, comment='组别ID')
  role_id = Column(Integer, nullable=False, comment='角色ID')
  username = Column(String(32), unique=True, nullable=True, default='', comment='用户名')
  nickname = Column(String(50), nullable=True, default='', comment='昵称')
  password = Column(String(32), nullable=True, default='', comment='密码')
  salt = Column(String(30), nullable=True, default='', comment='密码盐')
  email = Column(String(100), unique=True, nullable=True, default='', comment='电子邮箱')
  mobile = Column(String(11), nullable=True, default='', comment='手机号')
  avatar = Column(String(255), nullable=True, default='', comment='头像')
  level = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='等级')
  gender = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='性别')
  birthday = Column(Date, nullable=True, comment='生日')
  bio = Column(String(100), nullable=True, default='', comment='格言')
  successions = Column(Integer, default=1, nullable=False, comment='连续登录天数')
  maxsuccessions = Column(Integer, default=1, nullable=False, comment='最大连续登录天数')
  prevtime = Column(BigInteger, nullable=True, comment='上次登录时间')
  logintime = Column(BigInteger, nullable=True, comment='登录时间')
  loginip = Column(String(50), nullable=True, default='', comment='登录IP')
  loginfailure = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='失败次数')
  joinip = Column(String(50), nullable=True, default='', comment='加入IP')
  createtime = Column(TIMESTAMP, default=datetime.datetime.now, nullable=True, comment='创建时间')
  updatetime = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True,
                      comment='更新时间')
  createBy = Column(String(50), nullable=True, default='', comment='由哪一位管理员创建')
  updateBy = Column(String(50), nullable=True, default='', comment='由哪一位管理员更新')
  token = Column(String(50), nullable=True, default='', comment='Token')
  status = Column(TINYINT(unsigned=True), default=1, nullable=False, comment='状态（1：启用，2：禁用）')
  verification = Column(String(255), nullable=True, default='', comment='验证')
  company = Column(String(255), nullable=True, default='', comment='用户所属公司')

  __table_args__ = (
    UniqueConstraint('email', name='email'),
    UniqueConstraint('username', name='username'),
  )


def __repr__(self):
  return f"<User(id={self.id}, username='{self.username}', nickname='{self.nickname}', email='{self.email}')>"


def set_password(self, password, salt):
  self.password = md5(password + salt)


def check_password(self, password, salt):
  return self.password == md5(password + salt)


def get_token_key(token: str) -> str:
  h = hmac.new(endpoint_config.get("token").get("key").encode("utf-8"), digestmod=RIPEMD160)
  h.update(token.encode("utf-8"))
  encode_token = h.hexdigest()
  return "tp:%s" % encode_token


async def create_access_token(user: User, user_agent: str):
  """
  生成用户token
  """
  # 保留最新的5个token , 取出第4个后面的token来删掉
  tokens = await redis.exec("lrange", "user:%s" % user.username, 4, -1)
  for token in tokens:
    await redis.exec("delete", token)
  # 调整列表的长度
  await redis.exec("ltrim", "user:%s" % user.username, 0, 3)
  # 生成新的token
  token = str(uuid.uuid4())
  token_key = get_token_key(token)
  ua_hash = md5(user_agent)
  value = f"{user.id}:{ua_hash}"
  await redis.set(token_key, value, ACCESS_TOKEN_EXPIRE_DAYS * 24 * 60 * 60)
  # 将最新的token放在队列的前面
  await redis.exec("lpush", "user:%s" % user.username, token_key)
  return token


async def clear_user_tokens(username: str):
  """
  清除用户的token
  """
  # 获取用户的所有token key
  token_keys = await redis.exec("lrange", f"user:{username}", 0, -1)

  # 删除每个token
  for token_key in token_keys:
    if isinstance(token_key, bytes):
      token_key = token_key.decode("utf-8")
    # 直接删除token key
    await redis.delete(token_key)

  # 清空用户的token列表
  await redis.delete(f"user:{username}")


async def check_user_auth(
  request: Request,
  authorization: str | None = Header(None),
  user_agent: str = Header(None),
  db: AsyncSession = Depends(get_db)
) -> User | None:
  if not authorization:
    return None

  is_non_auth = is_non_validate_route(request.url.path)
  # 跳过不需要验证的路由
  if is_non_auth:
    return None

  credentials_exception = ClientVisibleException("Could not validate credentials")

  token = None

  if authorization:
    parts = authorization.split()
    if len(parts) == 2 and parts[0] == "Bearer":
      token = parts[1]
    logger.info(f"Authorization token found: {token}")

  if not token:
    logger.info("No token found in request headers.")
    raise credentials_exception

  logger.info(f"Token to be validated: {token}")

  token_key = get_token_key(token)
  value: bytes | None = await redis.get(token_key)
  if not value:
    logger.warning(f"Token {token} not found in Redis.")
    raise credentials_exception

  arr = value.decode('utf-8').split(":")
  if not arr or len(arr) != 2:
    logger.warning(f"Token {token} not found in Redis.")
    raise credentials_exception

  userid, ua = arr

  if userid is None:
    logger.info("Can not found user id in redis.")
    raise credentials_exception

  if md5(user_agent) != ua:
    logger.warning("Error user agent was found")
    raise credentials_exception

  # 转换 Redis 返回的字节字符串为整数
  userid = int(userid)

  result = await db.execute(select(User).filter(User.id == int(userid)))
  user = result.scalars().first()
  if user is None or user.status != 1:
    logger.info(f"User ID {userid} not found or not active.")
    raise credentials_exception

  logger.info(f"User authenticated: {user.username}")
  roles = await get_request_role(user, db=db)
  if 'tourist' in roles and len(roles) == 1:  # 只有游客身份
    raise ClientVisibleException("You do not have the necessary permissions.")
  request.state.user = user
  return user


async def get_request_user(
    user: Annotated[User | None, Depends(check_user_auth)],
) -> User:
  if user is None:
    raise ClientVisibleException("Could not validate credentials")
  return user


async def get_request_role(
        user: Annotated[User, Depends(get_request_user)],
        db: AsyncSession = Depends(get_db)
) -> tuple[str, ...]:
  process_user = user.id
  async with db as session:
    result = await session.execute(
      select(Role.roleCode)
      .select_from(UserRole)
      .join(Role, UserRole.role_id == Role.id)
      .where(UserRole.user_id == process_user)
    )
    roles: Sequence[str] = result.scalars().all()
    return tuple(roles)


def get_roles_checker(target_roles: Container[str] | str):
  """
  检查当前用户是否拥有指定的角色，如果权限不匹配，会直接抛出 403。
  >>> @router.get("/admin", dependencies=[Depends(get_roles_checker("super_admin"))])
  ... ...
  """
  if isinstance(target_roles, str):
    target_roles = [target_roles]

  target_roles_set = set(target_roles)

  async def roles_checker(
    roles: Annotated[tuple[str, ...], Depends(get_request_role)],
  ) -> True:
    """
    通过闭包的方式，将用户和角色信息封装到一起，方便后续使用。
    """
    real_roles_set = set(roles)
    # 超级管理员默认有所有权限，所以直接返回 True
    if 'super_admin' in real_roles_set:
      return True
    # 对于其他用户，要求自身有的权限必须将目标权限包含在内
    if not target_roles_set.issubset(real_roles_set):
      raise ClientVisibleException("权限不足")
    return True
  return roles_checker


async def destory_token(token: str):
  await redis.delete(get_token_key(token))


async def extension_token_ttl(token: str):
  await redis.exec(
    "expire", get_token_key(token), ACCESS_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
  )
