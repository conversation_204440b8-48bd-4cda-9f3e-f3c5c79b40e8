<script setup lang="ts">
import { computed, reactive } from 'vue';
import {
  // useFormRules,  输入格式规则验证
  useNaiveForm
} from '@/hooks/common/form';

defineOptions({
  name: 'CreditLogSearch'
});

interface Emits {
  (e: 'search', params: Api.SystemManage.CreditLogSearchParams): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  user_id: '',
  username: '',
  capacity: null,
  model: '',
  matter: ''
});

// type RuleKey = 'gamecode' | 'gamename';

const rules = computed(() => {
  // const { patternRules } = useFormRules();
  return {
    // gamecode: patternRules,
    // gamename: patternRules
    // lang: patternRules.lang
  };
});

async function search() {
  await validate();
  // console.log('gamecode:', model);
  const searchParams = {
    user_id: model.user_id,
    username: model.username,
    capacity: model.capacity || '',
    model: model.model,
    matter: model.matter,
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}

defineProps<{
  capacityOptions: Array<{ label: string; value: string }>;
}>();
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="Userid" path="userid" class="pr-24px">
          <NInput v-model:value="model.user_id" placeholder="请输入userid" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="用户名" path="username" class="pr-24px">
          <NInput v-model:value="model.username" placeholder="请输入用户名" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="AI 能力" path="capacity" class="pr-24px">
          <NSelect v-model:value="model.capacity" :options="capacityOptions" clearable placeholder="请选择能力" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="模型" path="matter" class="pr-24px">
          <NInput v-model:value="model.model" placeholder="请输入模型" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="事项" path="matter" class="pr-24px">
          <NInput v-model:value="model.matter" placeholder="请输入事项" clearable />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
