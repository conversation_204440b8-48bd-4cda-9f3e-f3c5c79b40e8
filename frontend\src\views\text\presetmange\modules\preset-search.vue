<script setup lang="ts">
import { defineProps, ref } from 'vue';
import { NButton, NForm, NFormItem, NInput, NSpace, useMessage } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { searchPresets } from '@/service/api/text';
import { $t } from '@/locales';

interface SearchParams {
  zh: string | null;
  en: string | null;
}

const model = ref<SearchParams>({
  zh: null,
  en: null
});

const emit = defineEmits<{
  (e: 'search', data: { zh?: string; en?: string; current?: number; size?: number }): void;
  (e: 'uploadFile', data: { file: File; gameCode: string }): void;
}>();

const props = defineProps<{ gameCode: string }>();

const message = useMessage();

const beforeUpload = async (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const allowedExtensions = ['.xlsx', '.xls', '.csv'];
  const file = data.file.file as File;
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.slice(fileName.lastIndexOf('.'));

  if (!allowedExtensions.includes(fileExtension)) {
    message.error($t('page.text.onlyExcel'));
    return false;
  }

  return true;
};

const handleChange = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  // console.log('File change triggered', data);
  if (data.file.file) {
    console.log('Emitting uploadFile event');
    emit('uploadFile', { file: data.file.file, gameCode: props.gameCode });
  }
};

async function handleSearch() {
  const params = {
    zh: model.value.zh || undefined,
    en: model.value.en || undefined,
    current: 1,
    size: 10
  };

  try {
    const response = await searchPresets(params);
    if (response.data) {
      emit('search', params);
    }
  } catch (error) {
    // console.error('请求搜索预设时发生错误:', error);
  }
}
</script>

<template>
  <NForm :model="model" inline>
    <NSpace align="center">
      <NFormItem label="原文">
        <NInput v-model:value="model.zh" placeholder="请输入原文" />
      </NFormItem>
      <NFormItem label="翻译">
        <NInput v-model:value="model.en" placeholder="请输入翻译" />
      </NFormItem>
      <NSpace justify="space-between">
        <NButton type="primary" @click="handleSearch">搜索</NButton>
        <NTooltip trigger="hover">
          <template #trigger>
            <NUpload
              :default-upload="false"
              accept=".xlsx,.xls,.csv"
              :show-file-list="false"
              @change="handleChange"
              @before-upload="beforeUpload"
            >
              <NButton type="success">表格导入</NButton>
            </NUpload>
          </template>
          表格导入预设配置,文档 标题列 为`原文`,`翻译`
        </NTooltip>
      </NSpace>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
