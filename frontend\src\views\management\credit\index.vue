<script setup lang="tsx">
import { ref, watch } from 'vue';
import { N<PERSON>utton, NPopconfirm } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import {
  fetchAddModelPrice,
  fetchDelModelPrice,
  fetchModelPrice,
  fetchModelPriceSearch,
  fetchUpdataModelPrice
} from '@/service/api';
import { AiCapacity } from '@/utils/AiCapacity';
import ModelPriceDrawer from './modules/model-price-drawer.vue';
import ModelPriceSearch from './modules/model-price-search.vue';

const appStore = useAppStore();

const capacityOptions = [
  {
    label: '图像生成',
    value: AiCapacity.IMAGE_GENERATION
  },
  {
    label: '视频生成',
    value: AiCapacity.VIDEO_GENERATION
  }
];

interface ModelPriceData extends NaiveUI.TableData {
  id: number;
  capacity: string;
  model: string;
  api_type: number;
  unit: number;
  credit: number;
  updatetime: Date;
  editor: string;
}

// 获取积分配置信息
const fetchGameManagementTyped: NaiveUI.TableApiFn<
  ModelPriceData,
  Api.SystemManage.CommonSearchParams
> = async params => {
  const response = await fetchModelPrice(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<ModelPriceData>>;
};

const searchParams = ref<Api.SystemManage.CommonSearchParams>({
  current: 1,
  size: 10,
  lang: ''
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchGameManagementTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      minWidth: 10
    },
    {
      key: 'capacity',
      title: 'AI 能力',
      align: 'center',
      minWidth: 300,
      render(row) {
        const value = row.capacity;
        for (const c of capacityOptions) {
          if (c.value === value) {
            return c.label;
          }
        }
        return '未知能力';
      }
    },
    {
      key: 'model',
      title: '模型名称',
      align: 'center',
      minWidth: 60
    },
    {
      key: 'api_type',
      title: '计费类型',
      align: 'center',
      minWidth: 20,
      render(row) {
        const statusMap: { [key: number]: string } = {
          1: '积分',
          2: 'Toekn'
        };
        return statusMap[row.api_type] || '未知';
      }
    },
    {
      key: 'credit',
      title: '积分 / 单位',
      align: 'center',
      minwidth: 50,
      render(row) {
        const unit = row.unit.toLocaleString();
        const credit = row.credit.toLocaleString();
        return `${credit} / ${unit}`;
      }
    },
    {
      key: 'editor',
      title: '修改者',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'updatetime',
      title: '更新时间',
      align: 'center',
      width: 220,
      render: row => row.updatetime.replace('T', ' ')
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确认删除？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  // editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  // onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

// 删除
async function handleDelete(id: number) {
  try {
    // console.log(gamecode);
    await fetchDelModelPrice(id);

    // 如果请求成功，调用 onDeleted
    onDeleted();
  } catch {}
}

// 编辑信息表单数据
const editData = ref<any>(null);
function edit(id: number) {
  const rowData = data.value.find(item => item.id === id);
  if (rowData) {
    editData.value = rowData;
  }
  handleEdit(id);
}

// 查询
async function handleSearch(params: Api.SystemManage.CommonSearchParams) {
  searchParams.value = params;
  // const newParams = { ...searchParams.value };
  const response = await fetchModelPriceSearch(searchParams.value);
  data.value = response.data.records; // 更新表格数据
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

// 添加信息
async function handleSubmit(creditData: any) {
  try {
    if (operateType.value === 'add') {
      await fetchAddModelPrice(creditData);
    } else if (operateType.value === 'edit') {
      await fetchUpdataModelPrice(creditData);
    }
    await getData();
  } finally {
    drawerVisible.value = false;
  }
}
type Row = {
  id: string;
};
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ModelPriceSearch v-model:model="searchParams" :capacity-options="capacityOptions" @search="handleSearch" />
    <NCard title="模型积分管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1024"
        :loading="loading"
        remote
        :row-key="(row: Row) => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <ModelPriceDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editData"
        :capacity-options="capacityOptions"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
