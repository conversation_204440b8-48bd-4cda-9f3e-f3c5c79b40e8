import { getSignature } from '@/utils/sign';
import { chatSSEClient, request } from '../request';

// 获取模型列表
export function getModels() {
  return request({
    url: '/assistant/getModels',
    method: 'get'
  });
}

// 获取提示词模板
export function getTemplatePrompt() {
  return request({
    url: '/assistant/get_prompt_templates',
    method: 'get'
  });
}

// 获取用户的会话记录
export function getChatState() {
  return request({
    url: '/assistant/getChatState',
    method: 'get'
  });
}

// 获取指定 chatid 的聊天内容
export function getChat(chatid: number) {
  return request({
    url: `/assistant/getChat`,
    method: 'get',
    params: {
      chatid
    }
  });
}

interface ProcessStreamParams {
  prompt: string;
  chatid: number;
  gpt_model: string;
  files: { type: string; url: string }[];
  qid?: number;
  regenerate?: boolean;
  switchModel?: boolean;
  useWeb?: boolean;
}

export function processStream(
  data: ProcessStreamParams,
  onMessage: (message: string) => void,
  onError?: (error: any) => void,
  onComplete?: () => void,
  onStart?: () => void
) {
  // 根据环境判断使用的URL前缀
  const baseUrl = import.meta.env.PROD ? '/api' : '/proxy-default';
  const sign = getSignature();
  return chatSSEClient({
    url: `${baseUrl}/assistant/process`,
    payload: data,
    onMessage,
    onError,
    onComplete,
    onStart,
    headers: {
      'X-Timestamp': sign.unixTimestamp.toString(),
      'X-Signature': sign.signature
    }
  });
}

// 删除指定 chatid 的会话
export function fetchDeleteChat(chatid: number) {
  return request({
    url: '/assistant/delete',
    method: 'post',
    data: {
      chatid,
      xx: '' // 由于后端需要一个无效参数来避免参数错误，这里传一个空字符串
    }
  });
}

// 更新指定 chatid 的会话标题
export function updateConversationTitle(chatid: number, title: string) {
  return request({
    url: '/assistant/updateConversationTitle',
    method: 'post',
    data: {
      chatid,
      title
    }
  });
}

// meta prompt提示词优化
export function metaPrompt(text: string) {
  return request({
    url: '/assistant/meta_prompt',
    method: 'post',
    data: {
      text
    }
  });
}

// 添加停止生成的接口
export function stopChat() {
  return request({
    url: '/assistant/stop_generation',
    method: 'post'
  });
}
