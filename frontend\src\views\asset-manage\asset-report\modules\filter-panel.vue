<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchAllUserCompany } from '@/service/api';

const range = ref(null);
const companyId = ref(null);
const companyOptions = ref<CommonType.Option[]>([]);

// 获取公司列表数据
onMounted(() => {
  fetchAllUserCompany().then(({ error, data }) => {
    if (!error) {
      const options: CommonType.Option[] = [];
      data.records.forEach(company => {
        options.push({
          value: company,
          label: company
        });
      });

      companyOptions.value = options;
    }
  });
});

// 定义搜索事件
const emit = defineEmits(['search']);

function handleSearch() {
  const params: any = {};

  if (range.value) {
    params.startDate = new Date(range.value[0]).toISOString();
    params.endDate = new Date(range.value[1]).toISOString();
  }

  if (companyId.value) {
    params.company = companyId.value;
  }

  // 触发父组件的搜索事件
  emit('search', params);
}

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};

// 快捷时间选项
const timeOptions = {
  今天: () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    // 结束时间使用当前时间
    const end = new Date(now);
    return [start.getTime(), end.getTime()] as const;
  },
  昨天: () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0);
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    return [start.getTime(), end.getTime()] as const;
  },
  本月: () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
    const end = new Date();
    return [start.getTime(), end.getTime()] as const;
  },
  上月: () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1, 0, 0, 0);
    const end = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
    return [start.getTime(), end.getTime()] as const;
  },
};
</script>

<template>
  <NCard class="flex">
    <NFlex align="center" class="w-full">
      <div class="max-w-150 flex items-center gap-2">
        <NText>选择日期区间</NText>
        <NDatePicker
          v-model:value="range"
          type="datetimerange"
          value-format="yyyy.MM.dd HH:mm:ss"
          clearable
          :is-date-disabled="disablePreviousDate"
          :shortcuts="timeOptions"
          @update:value="handleSearch"
        />
      </div>

      <div class="w-60 flex items-center gap-2">
        <NText class="min-w-15">选择公司</NText>
        <NSelect v-model:value="companyId" :options="companyOptions" clearable @update:value="handleSearch" />
      </div>
    </NFlex>
  </NCard>
</template>

<style scoped lang="scss">
:deep(.n-card__content) {
  display: flex;
  // align-items: center;
  justify-content: space-between;
}

:deep(.n-flex) {
  gap: 3em !important;
}
</style>
