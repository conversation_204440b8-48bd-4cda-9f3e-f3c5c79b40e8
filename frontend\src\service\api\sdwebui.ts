import { request } from '../request';

/** 打开sd */
export function opensd(port: number) {
  return request({
    url: '/sd/start_webui',
    method: 'get',
    params: {
      port
    }
  });
}

/** 查询显存占用 */
export function getMemory() {
  return request({
    url: '/sd/gpu_memory',
    method: 'get'
  });
}

/** 查询服务状态 */
export function getSdStatus() {
  return request({
    url: '/sd/get_servers_status',
    method: 'get'
  });
}

/** 启动固定服务 */
export function startSdService(port: number) {
  return request({
    url: '/sd/start_fixed_webui',
    method: 'get',
    params: {
      port
    }
  });
}

/** 停止服务 */
export function stopSdService(port: number) {
  return request({
    url: '/sd/stop_webui',
    method: 'post',
    data: {
      port
    }
  });
}
