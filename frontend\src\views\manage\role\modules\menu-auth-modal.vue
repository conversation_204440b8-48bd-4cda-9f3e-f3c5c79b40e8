<script setup lang="ts">
import { computed, ref, shallowRef, watch } from 'vue';
import { $t } from '@/locales';
import { fetchGetAllPages, fetchGetMenuTree, fetchGetRoleMenu, postSetRoleMenu } from '@/service/api';

defineOptions({
  name: 'MenuAuthModal'
});

interface Props {
  /** the roleId */
  roleId: number;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});
const menuRef = ref();
const cascade = ref(false);
function closeModal() {
  visible.value = false;
}

const title = computed(() => $t('common.edit') + $t('page.manage.role.menuAuth'));

const home = shallowRef('');

async function getHome() {
  console.log(props.roleId);

  home.value = 'home';
}

// async function updateHome(val: string) {
//   // request

//   home.value = val;
// }

const pages = shallowRef<string[]>([]);

async function getPages() {
  const { error, data } = await fetchGetAllPages();

  if (!error) {
    pages.value = data;
  }
}

// const pageSelectOptions = computed(() => {
//   const opts: CommonType.Option[] = pages.value.map(page => ({
//     label: page,
//     value: page
//   }));

//   return opts;
// });

const tree = shallowRef<Api.SystemManage.MenuTree[]>([]);

async function getTree() {
  const { error, data } = await fetchGetMenuTree();

  if (!error) {
    tree.value = data;
  }
}

const checks = shallowRef<number[]>([]);

// get user menu id
async function getChecks() {
  cascade.value = false;
  checks.value = [];
  console.log(props.roleId);
  // request
  fetchGetRoleMenu(props.roleId).then(({ data }) => {
    console.log(data);
    checks.value = data!.records.map(e => e.id);
  });
  // nextTick(() => {
  //   cascade.value = true;
  // });
}

function handleSubmit() {
  console.log('home page', home.value);
  console.log('checks menu', checks.value);
  console.log('role', props.roleId);
  // request

  // 半选中
  const indeterminate = menuRef.value.getIndeterminateData().keys;

  // 所有选中的
  const chekcoutAll = [...checks.value, ...indeterminate];
  console.log(chekcoutAll);

  postSetRoleMenu(props.roleId, chekcoutAll).then(() => {
    window.$message?.success?.($t('common.modifySuccess'));
    closeModal();
  });
}

function init() {
  getHome();
  getPages();
  getTree();
  getChecks();
}

watch(visible, val => {
  if (val) {
    init();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-480px">
    <!--
 <div class="flex-y-center gap-16px pb-12px">
      <div>{{ $t('page.manage.menu.home') }}</div>
      <NSelect :value="home" :options="pageSelectOptions" size="small" class="w-160px" @update:value="updateHome" />
    </div>
-->
    <NTree
      ref="menuRef"
      v-model:checked-keys="checks"
      :data="tree"
      key-field="id"
      checkable
      :cascade="cascade"
      expand-on-click
      virtual-scroll
      block-line
      class="h-280px"
    />
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" class="mt-16px" @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
