<script setup lang="ts">
import { reactive } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'UserCreditSearch'
});

interface Emits {
  (e: 'search', params: Api.SystemManage.UserCreditSearch): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  user_id: '',
  user_name: '',
  type: 0,
  credit: 0,
  updatetime: ''
});

async function search() {
  await validate();
  const searchParams = {
    user_id: model.user_id,
    user_name: model.user_name,
    type: model.type,
    credit: model.credit,
    updatetime: model.updatetime,
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}

// 多选下拉框的选项
// const langOptions = [
//   { label: '全部', value: 0 },
//   { label: '普通积分', value: 1 },
//   { label: '视频积分', value: 2 },
//   { label: '音乐积分', value: 3 },
//   { label: '赠送积分', value: 4 },
//   { label: '购买积分', value: 5 }
//   // 可以根据需要添加更多选项
// ];
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NForm ref="formRef" :model="model" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="用户 ID" path="userid" class="pr-24px">
          <NInput v-model:value="model.user_id" placeholder="请输入用户 ID" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="用户名" path="username" class="pr-24px">
          <NInput v-model:value="model.user_name" placeholder="请输入用户名" clearable />
        </NFormItemGi>
        <!--
        <NFormItemGi span="24 s:12 m:6" label="类型" path="type" class="pr-24px">
          <NSelect v-model:value="model.type" :options="langOptions" placeholder="请选择积分类型" />
        </NFormItemGi>
        -->
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
