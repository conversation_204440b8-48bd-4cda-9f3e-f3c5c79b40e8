<script lang="ts" setup>
import { computed, nextTick, onUnmounted, ref, watch } from 'vue';
import { useDialog, useMessage } from 'naive-ui';
import { useThemeStore } from '@/store/modules/theme';
import { useScreenDetection } from '@/utils/detectionScreen';
import type { ParamSeg, SynthesisTone } from '@/service/api/audio';
import {
  ModelFunction,
  ToneType,
  createSynthesisTask,
  deleteSynthesisTask,
  getSynthesisTask
} from '@/service/api/audio';
import AudioContent from './modules/audio-content.vue';
import SystemMenu from './modules/system-menu.vue';
import HistoryQueue from './modules/history-queue.vue';
import GeneratePanel from './modules/generate-panel.vue';
import SynthesisSelect from './modules/synthesis-select.vue';
import AudioInfo from './modules/audio-info.vue';

const themeStore = useThemeStore();
const message = useMessage();
const dialog = useDialog();

// 屏幕检测函数
const { isLargeScreen } = useScreenDetection(1500);

// 计算NScrollbar的最大高度class
const scrollbarHeightClass = computed(() => {
  return isLargeScreen.value ? 'max-h-38em' : 'max-h-25em';
});

// AudioContent 组件管理
interface AudioContentItem {
  id: number;
  toneData?: SynthesisTone | null;
  ref?: InstanceType<typeof AudioContent>;
}

const audioContentList = ref<AudioContentItem[]>([{ id: 1 }]);
const audioContentRefs = ref<Record<number, InstanceType<typeof AudioContent>>>({});
let nextId = 5;

// 当前正在编辑音色的AudioContent ID
const currentEditingAudioId = ref<number | null>(null);

// 添加新的 AudioContent 组件
const addAudioContent = () => {
  audioContentList.value.push({ id: nextId++ });
};

// 移除 AudioContent 组件
const removeAudioContent = (id: number) => {
  // 确保至少保留一个 AudioContent 组件
  if (audioContentList.value.length > 1) {
    audioContentList.value = audioContentList.value.filter(item => item.id !== id);
  }
};

// 是否显示音色选择
const showModelSelect = ref(false);

// 是否显示音频信息
const showAudioInfo = ref(false);

// 当前选中的音频信息
const selectedAudioInfo = ref<any>(null);

// 当前激活的标签页
const activeTab = ref('setting');

// 生成音频的loading状态
const generateLoading = ref(false);

// 当前生成任务列表
const currentGeneratingTasks = ref<
  Array<{
    id: number;
    status: 'SUBMITTED' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILURE';
    text: string;
    model: string;
    duration: string;
    createTime: string;
    url: string;
    isPlaying?: boolean;
  }>
>([]);

// 轮询相关变量
const pollingTimer = ref<NodeJS.Timeout | null>(null);
const pollingInterval = 3000; // 3秒轮询间隔

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// SystemMenu组件引用
const systemMenuRef = ref<InstanceType<typeof SystemMenu>>();

// HistoryQueue组件引用
const historyQueueRef = ref<InstanceType<typeof HistoryQueue>>();

// 音频播放状态管理
const audioRef = ref<HTMLAudioElement>();
const isPlaying = ref(false);
const canPlay = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const isLoading = ref(false);
const hasError = ref(false);

// 当前播放的音频信息
const currentPlayingAudio = ref<{
  type: 'history' | 'generating' | 'synthesis-select' | 'system-menu' | null;
  id: number | string | null;
  url: string;
  text?: string; // 添加文本字段
  toneData?: any; // 用于存储音色数据
}>({
  type: null,
  id: null,
  url: '',
  text: ''
});

// 计算当前播放的音色ID（用于synthesis-select组件）
const currentPlayingToneId = computed(() => {
  if (currentPlayingAudio.value.type === 'synthesis-select' && currentPlayingAudio.value.toneData) {
    const tone = currentPlayingAudio.value.toneData;
    return `${tone.tone_type}-${tone.tone_id}`;
  }
  return null;
});

// 计算当前播放的音色ID（用于system-menu组件）
const currentPlayingTimbreId = computed(() => {
  if (currentPlayingAudio.value.type === 'system-menu' && currentPlayingAudio.value.toneData) {
    const timbre = currentPlayingAudio.value.toneData;
    return `timbre-${timbre.id || 'unknown'}`;
  }
  return null;
});

// 计算音色是否正在播放（用于system-menu组件）
const isTimbrePlaying = computed(() => {
  return currentPlayingAudio.value.type === 'system-menu' && isPlaying.value;
});

// 音频URL
const audioUrl = ref('');

// 格式化时间为 mm:ss 格式
const formatTime = (time: number): string => {
  if (Number.isNaN(time) || time < 0) return '00:00';
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 计算属性用于显示
const formattedCurrentTime = computed(() => formatTime(currentTime.value));
const formattedDuration = computed(() => formatTime(duration.value));

// 音频事件处理函数
const updateTime = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime;
  }
};

const updateDuration = () => {
  if (audioRef.value && audioRef.value.readyState >= HTMLMediaElement.HAVE_CURRENT_DATA) {
    duration.value = audioRef.value.duration || 0;
  }
};

// 重置所有音频的播放状态
const resetAllPlayingStates = () => {
  // 重置历史记录中的播放状态
  if (historyQueueRef.value && historyQueueRef.value.resetAllPlayingStates) {
    historyQueueRef.value.resetAllPlayingStates();
  }
  // 重置生成面板中的播放状态
  currentGeneratingTasks.value.forEach(task => {
    task.isPlaying = false;
  });
};

// 更新播放状态
const updatePlayingState = (
  type: 'history' | 'generating' | 'synthesis-select' | 'system-menu',
  id: number | string,
  isPlayingState: boolean
) => {
  if (type === 'history') {
    // 更新历史记录播放状态
    if (historyQueueRef.value && historyQueueRef.value.updatePlayingState) {
      historyQueueRef.value.updatePlayingState(id as number, isPlayingState);
    }

    // 如果当前显示的音频信息是这个历史记录，同步更新播放状态
    if (showAudioInfo.value && selectedAudioInfo.value && selectedAudioInfo.value.id === id) {
      selectedAudioInfo.value.isPlaying = isPlayingState;
    }
  } else if (type === 'generating') {
    // 更新生成任务播放状态
    const task = currentGeneratingTasks.value.find(t => t.id === id);
    if (task) {
      task.isPlaying = isPlayingState;
    }
  } else if (type === 'synthesis-select') {
    // synthesis-select 的播放状态由组件自己管理，这里不需要特殊处理
    // 播放状态通过 currentPlayingToneId 计算属性传递给组件
  } else if (type === 'system-menu') {
    // system-menu 的播放状态由组件自己管理，这里不需要特殊处理
    // 播放状态通过 isTimbrePlaying 计算属性传递给组件
  }
};

const handleEnded = () => {
  isPlaying.value = false;
  currentTime.value = 0;

  // 重置当前播放音频的状态
  if (currentPlayingAudio.value.type && currentPlayingAudio.value.id) {
    updatePlayingState(currentPlayingAudio.value.type, currentPlayingAudio.value.id, false);
  }

  // 播放结束时不清空音频信息，保持URL和文本，这样用户可以重新播放
  // currentPlayingAudio.value 保持不变，只是播放状态变为false
};

const handleCanPlay = () => {
  canPlay.value = true;
  hasError.value = false;
  isLoading.value = false;
};

const handleError = () => {
  hasError.value = true;
  isLoading.value = false;
  isPlaying.value = false;
  // console.error('音频加载失败');
};

// 停止当前播放的音频
const stopCurrentAudio = () => {
  if (audioRef.value && isPlaying.value) {
    audioRef.value.pause();
    isPlaying.value = false;
  }
  // 重置当前播放音频信息
  currentPlayingAudio.value = {
    type: null,
    id: null,
    url: '',
    text: ''
  };
  // 重置所有播放状态
  resetAllPlayingStates();
};

// 播放/暂停控制函数
const togglePlay = () => {
  if (!audioRef.value || hasError.value || !canPlay.value) return;

  if (isPlaying.value) {
    audioRef.value.pause();
    isPlaying.value = false; // 立即更新播放状态
    // 更新当前播放音频的状态
    if (currentPlayingAudio.value.type && currentPlayingAudio.value.id) {
      updatePlayingState(currentPlayingAudio.value.type, currentPlayingAudio.value.id, false);
    }
  } else {
    isLoading.value = true;
    const playPromise = audioRef.value.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          isPlaying.value = true;
          isLoading.value = false;
          // 更新当前播放音频的状态
          if (currentPlayingAudio.value.type && currentPlayingAudio.value.id) {
            updatePlayingState(currentPlayingAudio.value.type, currentPlayingAudio.value.id, true);
          }
        })
        .catch(error => {
          console.error('播放失败:', error);
          isPlaying.value = false; // 立即更新播放状态
          hasError.value = true;
          isLoading.value = false;
          // 更新当前播放音频的状态
          if (currentPlayingAudio.value.type && currentPlayingAudio.value.id) {
            updatePlayingState(currentPlayingAudio.value.type, currentPlayingAudio.value.id, false);
          }
        });
    }
  }
};

// 进度条拖拽控制函数
const handleSeek = (value: number) => {
  if (audioRef.value && canPlay.value) {
    audioRef.value.currentTime = value;
    currentTime.value = value;
  }
};

// 清理函数
const cleanup = () => {
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value.src = '';
    audioRef.value.load();
  }
  isPlaying.value = false;
  currentTime.value = 0;
  duration.value = 0;
  canPlay.value = false;
  hasError.value = false;
};

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
  stopPolling();
});

// 监听音频URL变化
watch(
  () => audioUrl.value,
  () => {
    cleanup();
    if (audioRef.value) {
      audioRef.value.load();
    }
  }
);

// 显示音频信息
const handleShowAudioInfo = (item: any) => {
  selectedAudioInfo.value = item;
  showAudioInfo.value = true;
};

// 处理音色选择
const handleToneSelected = async (tone: SynthesisTone) => {
  // 隐藏音色选择界面
  showModelSelect.value = false;

  // 将SynthesisTone转换为TimbreInfo格式，保持原始tone_id
  const toneData = {
    id: tone.tone_id, // 保持原始ID，可能是string或number
    name: tone.tone_name,
    gender: tone.gender,
    lang: tone.lang,
    description: tone.description,
    audio_url: tone.url,
    is_favorite: tone.is_favorite,
    prompt_text: tone.prompt_text
  };

  if (currentEditingAudioId.value !== null) {
    // 流程2: 更新指定的AudioContent组件
    const targetAudioContent = audioContentList.value.find(item => item.id === currentEditingAudioId.value);
    if (targetAudioContent) {
      targetAudioContent.toneData = tone;
    }

    // 先切换到设置标签页
    activeTab.value = 'setting';

    // 等待DOM更新，确保SystemMenu组件已经渲染
    await nextTick();

    // 调用SystemMenu的setTimbreData方法，传递来源的 audio-content ID
    if (systemMenuRef.value) {
      systemMenuRef.value.setTimbreData(toneData, currentEditingAudioId.value);
    }

    // 清理编辑状态
    currentEditingAudioId.value = null;
  } else {
    // 流程1: 更新最后一个AudioContent组件
    if (audioContentList.value.length > 0) {
      const lastAudioContent = audioContentList.value[audioContentList.value.length - 1];
      lastAudioContent.toneData = tone;
    }

    // 先切换到设置标签页
    activeTab.value = 'setting';

    // 等待DOM更新，确保SystemMenu组件已经渲染
    await nextTick();

    // 调用SystemMenu的setTimbreData方法
    if (systemMenuRef.value) {
      systemMenuRef.value.setTimbreData(toneData);
    }
  }
};

// 返回历史记录
const handleBackToHistory = () => {
  showAudioInfo.value = false;
  activeTab.value = 'history';
};

// 处理复用设置
const handleReuseSettings = async (audioData: any) => {
  if (!audioData || !audioData.params) {
    message.warning('无法获取音频参数信息');
    return;
  }

  try {
    // 清空现有的 AudioContent 组件，重新开始
    audioContentList.value = [];

    // 根据 params.segs 创建对应数量的 AudioContent 组件
    const segs = audioData.params.segs || [];

    for (let i = 0; i < segs.length; i++) {
      const seg = segs[i];
      const audioContentId = nextId++;

      // 创建 AudioContent 项
      const audioContentItem: AudioContentItem = {
        id: audioContentId,
        toneData: null
      };

      // 如果有音色信息，设置完整的音色数据
      if (seg.tone_id) {
        // 构造完整的SynthesisTone数据结构
        const toneData: SynthesisTone = {
          tone_id: seg.tone_id,
          tone_name: seg.tone_name || seg.model_name || '复用音色',
          tone_type: seg.tone_type || 'pretrained',
          model_function: seg.model_function || 'cosy_pretrained',
          gender: null as 0 | 1 | null,
          lang: 'zh',
          description: seg.tone_name ? `复用音色: ${seg.tone_name}` : '从历史记录复用的音色',
          url: '',
          is_favorite: false,
          prompt_text: ''
        };
        audioContentItem.toneData = toneData;
      }

      audioContentList.value.push(audioContentItem);
    }

    // 如果没有 segs 或者 segs 为空，至少创建一个空的 AudioContent
    if (segs.length === 0) {
      audioContentList.value.push({ id: nextId++ });
    }

    // 切换到设置标签页
    activeTab.value = 'setting';
    showAudioInfo.value = false;

    // 等待 DOM 更新
    await nextTick();

    // 设置文本内容到对应的 AudioContent 组件
    for (let i = 0; i < segs.length; i++) {
      const seg = segs[i];
      const audioContentId = audioContentList.value[i]?.id;

      if (audioContentId && audioContentRefs.value[audioContentId]) {
        // 直接设置文本内容
        const audioContentRef = audioContentRefs.value[audioContentId];
        if (audioContentRef && seg.text) {
          // 通过 ref 访问组件内部的 textContent
          (audioContentRef as any).textContent = seg.text;
        }
      }
    }

    message.success('设置已复用，请检查并调整参数');
  } catch (error) {
    console.error('复用设置失败:', error);
    message.error('复用设置失败');
  }
};

// 处理清除 audio-content 音色数据
const handleClearAudioContentTone = (audioId: number | null) => {
  if (audioId === null) return;

  const targetAudioContent = audioContentList.value.find(item => item.id === audioId);
  if (targetAudioContent) {
    targetAudioContent.toneData = null;
  }
};

// 处理 system-menu 中的音色导入
const handleSystemMenuTimbreImported = (toneData: any) => {
  // 将导入的音色数据转换为SynthesisTone格式
  const synthesisTone: SynthesisTone = {
    tone_id: toneData.id,
    tone_name: toneData.name,
    tone_type: ToneType.CUSTOM, // 导入的音色都是自定义音色
    model_function: ModelFunction.FAST_CLONE, // 导入的音色通常使用fast_clone
    gender: toneData.gender,
    lang: toneData.lang || 'zh',
    description: toneData.description,
    url: toneData.audio_url,
    is_favorite: toneData.is_favorite || false,
    prompt_text: toneData.prompt_text || '' // 使用原始数据中的prompt_text，如果没有则为空字符串
  };

  // 将音色应用到最后一个AudioContent组件
  if (audioContentList.value.length > 0) {
    const lastAudioContent = audioContentList.value[audioContentList.value.length - 1];
    lastAudioContent.toneData = synthesisTone;
  }
};

// 处理AudioContent的音色选择请求
const handleAudioContentSelectTone = async (audioId: number, toneData?: SynthesisTone | null) => {
  // 如果当前存在音色数据，则切换到设置面板显示音色信息
  if (toneData) {
    // 将SynthesisTone转换为TimbreInfo格式
    const timbreData = {
      id: toneData.tone_id,
      name: toneData.tone_name,
      gender: toneData.gender,
      lang: toneData.lang,
      description: toneData.description,
      audio_url: toneData.url,
      is_favorite: toneData.is_favorite,
      prompt_text: toneData.prompt_text
    };

    // 切换到设置标签页
    activeTab.value = 'setting';

    // 等待DOM更新，确保SystemMenu组件已经渲染
    await nextTick();

    // 调用SystemMenu的setTimbreData方法，传递来源的 audio-content ID
    if (systemMenuRef.value) {
      systemMenuRef.value.setTimbreData(timbreData, audioId);
    }
  } else {
    // 如果没有音色数据，则打开音色选择界面
    currentEditingAudioId.value = audioId;
    showModelSelect.value = true;
  }
};

// 格式化时长
const formatTaskDuration = (durationValue: number) => {
  if (!durationValue || Number.isNaN(durationValue) || durationValue < 0) return '00:00';
  const minutes = Math.floor(durationValue / 60);
  const seconds = Math.floor(durationValue % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 获取模型显示名称
const getModelDisplayName = (modelFunction: string) => {
  switch (modelFunction) {
    case 'fast_clone':
      return 'GPT-SoVITS 极速复刻';
    case 'cosy_pretrained':
      return 'CosyVoice 预训练音色';
    case 'chat_tts':
      return 'Chat TTS 语音合成';
    case 'gpt_sovits_tts':
      return 'GPT-SoVITS 语音合成';
    case 'volcengine':
      return 'Volcengine 语音合成';
    default:
      return '未知模型';
  }
};

// 将API返回的TaskItem转换为当前生成任务格式
const transformTaskToGeneratingTask = (task: any) => {
  // 提取文本内容（从params.segs中获取）
  const text = task.params?.segs?.map((seg: any) => seg.text).join(' ') || '音频生成任务';

  // 提取模型信息（从第一个seg获取）
  const taskModelName = task.params?.segs?.[0]?.model_name || '';
  const taskModelFunction = task.params?.segs?.[0]?.model_function || '';

  // 确定显示的模型名称
  let displayModel = '处理中';
  if (taskModelName) {
    displayModel = taskModelName;
  } else if (taskModelFunction) {
    displayModel = getModelDisplayName(taskModelFunction);
  }

  return {
    id: task.id,
    status: task.status,
    text,
    model: displayModel,
    duration: formatTaskDuration(task.duration || 0),
    createTime: new Date(task.create_time || Date.now()).toLocaleString(),
    url: task.url || '',
    isPlaying: false
  };
};

// 更新当前生成任务的状态
const updateCurrentGeneratingTask = (taskId: number, updatedTaskData: any) => {
  const taskIndex = currentGeneratingTasks.value.findIndex(task => task.id === taskId);
  if (taskIndex !== -1) {
    // 使用转换函数来正确处理API返回的任务数据
    const updatedTask = transformTaskToGeneratingTask(updatedTaskData);
    currentGeneratingTasks.value[taskIndex] = updatedTask;

    // 如果任务完成或失败，3秒后从当前生成任务列表中移除
    // if (updatedTaskData.status === 'SUCCESS' || updatedTaskData.status === 'FAILURE') {
    //   setTimeout(() => {
    //     const index = currentGeneratingTasks.value.findIndex(t => t.id === taskId);
    //     if (index !== -1) {
    //       currentGeneratingTasks.value.splice(index, 1);
    //     }
    //   }, 3000);
    // }
  }
};

// 检查是否有需要轮询的任务（状态为SUBMITTED或IN_PROGRESS）
const hasPollingTasks = (): boolean => {
  return currentGeneratingTasks.value.some(task => task.status === 'SUBMITTED' || task.status === 'IN_PROGRESS');
};

// 获取需要轮询的任务ID列表
const getPollingTaskIds = (): number[] => {
  return currentGeneratingTasks.value
    .filter(task => task.status === 'SUBMITTED' || task.status === 'IN_PROGRESS')
    .map(task => task.id);
};

// 轮询函数
const pollCurrentGeneratingTasks = async () => {
  const taskIds = getPollingTaskIds();

  if (taskIds.length === 0) {
    stopPolling();
    return;
  }

  try {
    // 并发获取所有需要轮询的任务状态
    const promises = taskIds.map(taskId => getSynthesisTask(taskId));
    const responses = await Promise.allSettled(promises);

    responses.forEach((response, index) => {
      if (response.status === 'fulfilled' && response.value.data) {
        const taskId = taskIds[index];
        updateCurrentGeneratingTask(taskId, response.value.data);

        // 同时更新 HistoryQueue 组件的状态（如果组件存在）
        if (historyQueueRef.value && historyQueueRef.value.updateTaskData) {
          historyQueueRef.value.updateTaskData(taskId, response.value.data);
        }
      } else if (response.status === 'rejected') {
        console.error(`获取任务 ${taskIds[index]} 状态失败:`, response.reason);
      }
    });

    // 检查是否还有需要继续轮询的任务
    if (!hasPollingTasks()) {
      stopPolling();
    }
  } catch (error) {
    console.error('轮询任务状态时出错:', error);
  }
};

// 开始轮询
const startPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  if (hasPollingTasks()) {
    pollingTimer.value = setInterval(pollCurrentGeneratingTasks, pollingInterval);
  }
};

// 处理任务状态更新
const handleTaskUpdated = (taskId: number, updatedTask: any) => {
  updateCurrentGeneratingTask(taskId, updatedTask);

  // 如果当前显示的音频信息是这个任务，同步更新
  if (showAudioInfo.value && selectedAudioInfo.value && selectedAudioInfo.value.id === taskId) {
    selectedAudioInfo.value = updatedTask;
  }
};

// 处理当前生成任务的播放
const handlePlayGeneratingAudio = (task: any) => {
  // 检查是否点击的是当前正在播放的音频
  if (
    currentPlayingAudio.value.type === 'generating' &&
    currentPlayingAudio.value.id === task.id &&
    currentPlayingAudio.value.url === task.url
  ) {
    // 如果是同一个音频，只需要切换播放/暂停状态
    togglePlay();
    return;
  }

  // 停止当前播放的音频
  if (isPlaying.value) {
    stopCurrentAudio();
  }

  // 设置当前播放音频信息
  currentPlayingAudio.value = {
    type: 'generating',
    id: task.id,
    url: task.url,
    text: task.text || '音频生成任务'
  };

  // 设置音频源并播放
  if (task.url && audioRef.value) {
    audioRef.value.src = task.url;
    audioRef.value.load();
    // 等待音频加载完成后播放
    audioRef.value.addEventListener(
      'canplay',
      () => {
        togglePlay();
      },
      { once: true }
    );
  }
};

// 处理当前生成任务的下载
const handleDownloadGeneratingAudio = (task: any) => {
  if (task.url) {
    const link = document.createElement('a');
    link.href = task.url;
    link.download = `audio_${task.id}.wav`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 处理system-menu音频的播放
const handlePlaySystemMenuTimbre = (timbreData: any) => {
  const timbreKey = `timbre-${timbreData.id || 'unknown'}`;

  // 检查是否点击的是当前正在播放的音频
  if (
    currentPlayingAudio.value.type === 'system-menu' &&
    currentPlayingAudio.value.id === timbreKey &&
    currentPlayingAudio.value.url === timbreData.audio_url
  ) {
    // 如果是同一个音频，只需要切换播放/暂停状态
    togglePlay();
    return;
  }

  // 停止当前播放的音频
  if (isPlaying.value) {
    stopCurrentAudio();
  }

  // 设置当前播放音频信息
  currentPlayingAudio.value = {
    type: 'system-menu',
    id: timbreKey,
    url: timbreData.audio_url,
    text: timbreData.description || timbreData.name || '音色试听',
    toneData: timbreData
  };

  // 设置音频源并播放
  if (timbreData.audio_url && audioRef.value) {
    audioRef.value.src = timbreData.audio_url;
    audioRef.value.load();
    // 等待音频加载完成后播放
    audioRef.value.addEventListener(
      'canplay',
      () => {
        togglePlay();
      },
      { once: true }
    );
  }
};

// 处理synthesis-select音频的播放
const handlePlaySynthesisSelectAudio = (tone: any) => {
  const toneKey = `${tone.tone_type}-${tone.tone_id}`;

  // 检查是否点击的是当前正在播放的音频
  if (
    currentPlayingAudio.value.type === 'synthesis-select' &&
    currentPlayingAudio.value.id === toneKey &&
    currentPlayingAudio.value.url === tone.url
  ) {
    // 如果是同一个音频，只需要切换播放/暂停状态
    togglePlay();
    return;
  }

  // 停止当前播放的音频
  if (isPlaying.value) {
    stopCurrentAudio();
  }

  // 设置当前播放音频信息
  currentPlayingAudio.value = {
    type: 'synthesis-select',
    id: toneKey,
    url: tone.url,
    text: tone.description || tone.tone_name || '音色试听',
    toneData: tone
  };

  // 设置音频源并播放
  if (tone.url && audioRef.value) {
    audioRef.value.src = tone.url;
    audioRef.value.load();
    // 等待音频加载完成后播放
    audioRef.value.addEventListener(
      'canplay',
      () => {
        togglePlay();
      },
      { once: true }
    );
  }
};

// 处理历史记录音频的播放
const handlePlayHistoryAudio = (item: any) => {
  // 检查是否点击的是当前正在播放的音频
  if (
    currentPlayingAudio.value.type === 'history' &&
    currentPlayingAudio.value.id === item.id &&
    currentPlayingAudio.value.url === item.url
  ) {
    // 如果是同一个音频，只需要切换播放/暂停状态
    togglePlay();
    return;
  }

  // 停止当前播放的音频
  if (isPlaying.value) {
    stopCurrentAudio();
  }

  // 设置当前播放音频信息
  currentPlayingAudio.value = {
    type: 'history',
    id: item.id,
    url: item.url,
    text: item.text || '历史音频'
  };

  // 设置音频源并播放
  if (item.url && audioRef.value) {
    audioRef.value.src = item.url;
    audioRef.value.load();
    // 等待音频加载完成后播放
    audioRef.value.addEventListener(
      'canplay',
      () => {
        togglePlay();
      },
      { once: true }
    );
  }
};

// 处理历史记录音频的下载
const handleDownloadHistoryAudio = (item: any) => {
  if (item.url) {
    const link = document.createElement('a');
    link.href = item.url;
    link.download = `audio_${item.id}.wav`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 处理当前播放音频的下载
const handleDownloadCurrentAudio = () => {
  if (currentPlayingAudio.value.url) {
    const link = document.createElement('a');
    link.href = currentPlayingAudio.value.url;
    link.download = `audio_${currentPlayingAudio.value.id || 'current'}.wav`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 处理历史记录音频的删除
const handleDeleteAudio = (item: any) => {
  dialog.warning({
    title: '删除确认',
    content: `确认删除这条音频记录吗？删除后将无法恢复。`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await deleteSynthesisTask(item.id);
        message.success('删除成功');

        // 如果删除的是当前正在播放的音频，停止播放
        if (currentPlayingAudio.value.type === 'history' && currentPlayingAudio.value.id === item.id) {
          if (audioRef.value) {
            audioRef.value.pause();
            audioRef.value.src = '';
            audioRef.value.load();
          }
          isPlaying.value = false;
          currentTime.value = 0;
          duration.value = 0;
          canPlay.value = false;
          hasError.value = false;
          currentPlayingAudio.value = {
            type: null,
            id: null,
            url: '',
            toneData: undefined
          };
        }

        // 刷新历史记录列表
        if (historyQueueRef.value) {
          historyQueueRef.value.refreshData();
        }
      } catch (error) {
        console.error('删除音频失败:', error);
        message.error('删除失败，请重试');
      }
    }
  });
};

// 处理历史记录音频的重试
const handleRetryAudio = async (item: any) => {
  try {
    // 检查是否有参数信息
    if (!item.params || !item.params.segs || item.params.segs.length === 0) {
      message.warning('无法获取原始参数信息，无法重试');
      return;
    }

    // 开始loading
    generateLoading.value = true;

    // 从原始任务参数中提取ParamSeg数据
    const paramSegs: ParamSeg[] = item.params.segs.map((seg: any) => ({
      tone_type: seg.tone_type,
      tone_id: seg.tone_id,
      model_function: seg.model_function,
      text: seg.text,
      speed: seg.speed || 1,
      pitch: seg.pitch || 0
    }));

    // 调用createSynthesisTask API重新生成
    const response = await createSynthesisTask({ segs: paramSegs });

    if (response.data && response.data.task_ids) {
      message.success(`音频重新生成任务已创建，共 ${response.data.task_ids.length} 个任务`);

      // 基于返回的 task_ids 创建生成中任务列表
      const newTasks = response.data.task_ids.map(taskId => {
        return {
          id: taskId,
          status: 'SUBMITTED' as const,
          text: item.text || '音频重新生成中...', // 使用原始文本
          model: item.model || '处理中', // 使用原始模型名称
          duration: '00:00',
          createTime: new Date().toLocaleString(),
          url: '',
          isPlaying: false
        };
      });

      // 添加到当前生成任务列表（而不是覆盖）
      currentGeneratingTasks.value.push(...newTasks);

      // 启动轮询来监控任务状态
      startPolling();

      // 切换到历史记录标签页，显示新创建的任务
      activeTab.value = 'history';

      // 等待DOM更新，确保HistoryQueue组件已经渲染
      await nextTick();

      // 通知HistoryQueue组件重新初始化数据
      if (historyQueueRef.value) {
        await historyQueueRef.value.refreshData();
      }
    }
  } catch (error: any) {
    console.error('重新生成音频失败:', error);
    message.error('重新生成音频失败');
  } finally {
    // 结束loading
    generateLoading.value = false;
  }
};

// 处理生成音频请求
const handleGenerateAudio = async () => {
  try {
    // 开始loading
    generateLoading.value = true;

    // 1. 收集所有AudioContent组件的数据
    const paramSegs: ParamSeg[] = [];

    for (const item of audioContentList.value) {
      const audioContentRef = audioContentRefs.value[item.id];
      if (audioContentRef) {
        const paramSeg = audioContentRef.getParamSegData();
        if (paramSeg) {
          paramSegs.push(paramSeg);
        }
      }
    }

    // 检查是否有有效的数据
    if (paramSegs.length === 0) {
      message.warning('请至少添加一个有效的音频内容（需要选择音色并输入文本）');
      return;
    }

    // 2. 从SystemMenu获取speed和pitch并统一应用
    if (systemMenuRef.value) {
      const globalSpeed = systemMenuRef.value.speed;
      const globalPitch = systemMenuRef.value.pitch;

      // 统一设置所有ParamSeg的speed和pitch
      paramSegs.forEach(seg => {
        seg.speed = globalSpeed;
        seg.pitch = globalPitch;
      });
    }

    // 3. 调用createSynthesisTask API
    const response = await createSynthesisTask({ segs: paramSegs });

    if (response.data && response.data.task_ids) {
      message.success(`音频合成任务已创建，共 ${response.data.task_ids.length} 个任务`);

      // 调试信息
      console.log('API 返回的 task_ids:', response.data.task_ids);
      console.log('paramSegs 数量:', paramSegs.length);

      // 4. 基于返回的 task_ids 创建生成中任务列表
      const newTasks = response.data.task_ids.map(taskId => {
        return {
          id: taskId,
          status: 'SUBMITTED' as const,
          text: '音频生成中...', // 使用通用文本，因为无法确定具体对应哪个输入
          model: '处理中', // 使用通用模型名称
          duration: '00:00',
          createTime: new Date().toLocaleString(),
          url: '',
          isPlaying: false
        };
      });

      // 覆盖之前的任务
      currentGeneratingTasks.value = newTasks;

      // 启动轮询来监控任务状态
      startPolling();

      // 切换到历史记录标签页，显示新创建的任务
      activeTab.value = 'history';

      // 等待DOM更新，确保HistoryQueue组件已经渲染
      await nextTick();

      // 通知HistoryQueue组件重新初始化数据
      if (historyQueueRef.value) {
        await historyQueueRef.value.refreshData();
      }
    }
  } catch (error: any) {
    console.error('创建音频合成任务失败:', error);
    message.error('创建音频合成任务失败');
  } finally {
    // 结束loading
    generateLoading.value = false;
  }
};
</script>

<template>
  <div class="h-full w-full p-3">
    <NCard class="main h-full">
      <NFlex class="h-[calc(100%-6em)]" justify="space-between" :wrap="false">
        <!-- 左侧面板 用户输入 -->
        <NFlex vertical class="user_input_panel h-full w-2/3">
          <NScrollbar :class="`textinput_panel ${scrollbarHeightClass}`">
            <AudioContent
              v-for="item in audioContentList"
              :key="item.id"
              :ref="
                (el: any) => {
                  if (el) audioContentRefs[item.id] = el;
                }
              "
              :tone-data="item.toneData"
              @add-more="addAudioContent"
              @remove="() => removeAudioContent(item.id)"
              @select-tone="toneData => handleAudioContentSelectTone(item.id, toneData)"
            />
          </NScrollbar>
          <GeneratePanel
            :loading="generateLoading"
            :current-generating-tasks="currentGeneratingTasks"
            class="flex-1"
            @generate-audio="handleGenerateAudio"
            @play-audio="handlePlayGeneratingAudio"
            @download-audio="handleDownloadGeneratingAudio"
          />
        </NFlex>

        <!-- 右侧面板 参数选择 -->
        <NFlex class="h-full w-1/3">
          <NTabs v-if="!showModelSelect && !showAudioInfo" v-model:value="activeTab" type="line" class="pl-2 pr-5">
            <NTabPane name="setting" tab="设置">
              <SystemMenu
                ref="systemMenuRef"
                :current-playing-timbre-id="currentPlayingTimbreId"
                :is-timbre-playing="isTimbrePlaying"
                @show-model-select="showModelSelect = true"
                @play-timbre="handlePlaySystemMenuTimbre"
                @clear-audio-content-tone="handleClearAudioContentTone"
                @timbre-imported="handleSystemMenuTimbreImported"
              />
            </NTabPane>

            <NTabPane name="history" tab="队列历史">
              <HistoryQueue
                ref="historyQueueRef"
                @show-audio-info="handleShowAudioInfo"
                @task-updated="handleTaskUpdated"
                @play-audio="handlePlayHistoryAudio"
                @download-audio="handleDownloadHistoryAudio"
                @delete-audio="handleDeleteAudio"
                @retry-audio="handleRetryAudio"
              />
            </NTabPane>
          </NTabs>

          <!-- 模型选择 -->
          <SynthesisSelect
            v-else-if="showModelSelect"
            :current-playing-tone-id="currentPlayingToneId"
            @hide-model-select="showModelSelect = false"
            @tone-selected="handleToneSelected"
            @play-audio="handlePlaySynthesisSelectAudio"
          />

          <!-- 音频信息 -->
          <AudioInfo
            v-else-if="showAudioInfo"
            :audio-data="selectedAudioInfo"
            @go-back="handleBackToHistory"
            @play-audio="handlePlayHistoryAudio"
            @reuse-settings="handleReuseSettings"
          />
        </NFlex>
      </NFlex>

      <!-- 隐藏的音频元素 -->
      <audio
        ref="audioRef"
        :src="audioUrl"
        class="hidden"
        @timeupdate="updateTime"
        @loadedmetadata="updateDuration"
        @ended="handleEnded"
        @canplay="handleCanPlay"
        @error="handleError"
      />

      <!-- 音频播放 -->
      <NFlex class="audio_playback h-[6em] w-full" justify="space-between" align="center" :wrap="false">
        <NFlex class="w-1/5 pl-2" align="center" justify="center">
          <!-- 文本 -->
          <NEllipsis :line-clamp="1">
            {{ currentPlayingAudio.text || '请选择音频播放' }}
            <template #tooltip>
              <div class="line-clamp-5 max-w-100 overflow-hidden text-center">
                {{ currentPlayingAudio.text || '请选择音频播放' }}
              </div>
            </template>
          </NEllipsis>
        </NFlex>

        <!-- 播放控制 -->
        <NFlex align="center" justify="center" class="w-3/5">
          <NButton text :disabled="!canPlay || hasError" @click="togglePlay">
            <SvgIcon :icon="isPlaying ? 'gridicons:pause' : 'lsicon:play-filled'" class="text-4xl" />
          </NButton>

          <NFlex align="center" justify="space-between" class="w-full px-3" :wrap="false">
            <NText class="w-10 text-center">{{ formattedCurrentTime }}</NText>
            <NSlider
              v-model:value="currentTime"
              :max="duration"
              :min="0"
              :step="0.1"
              :disabled="!canPlay || hasError"
              class="mx-3 flex-1"
              :tooltip="false"
              @update:value="handleSeek"
            />
            <NText class="w-10 text-center">{{ formattedDuration }}</NText>
          </NFlex>
        </NFlex>

        <!-- 下载 -->
        <div class="w-1/5 flex items-center justify-center">
          <NButton type="info" @click="handleDownloadCurrentAudio">
            <SvgIcon icon="ep:download" class="mr-1 text-xl" />
            下载音频
          </NButton>
        </div>
      </NFlex>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
.main > :deep(.n-card__content) {
  padding: 0px !important;
}

.user_input_panel {
  border-right: 1.5px solid v-bind('themeStore.darkMode ? "rgba(255,255,255,0.09)" : "rgb(239,239,245)"');
}

.audio_playback {
  border-top: 1.5px solid v-bind('themeStore.darkMode ? "rgba(255,255,255,0.09)" : "rgb(239,239,245)"');
}

:deep(.n-tabs-tab__label) {
  font-size: 1.2em !important;
}
</style>

<style></style>
