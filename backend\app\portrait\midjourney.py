import logging

from fastapi import APIRouter, Body, Depends, Request
from typing import Annotated, Literal, Optional
from pydantic import BaseModel, Field

from config import app_settings
from models.users import User, get_request_user
from models.chat_mj_tasks import ChatMjTasks, ChatMjTasksOut, ChatMjTasksChangeAction, WorksTasksOut
from models.response import success
from utils.database import get_db
from utils.common import contains_chinese, timestamp_to_string
from service.translate import get_translate
from service.file import save_image_b64data, save_image_by_url
from utils.asset_storage import store_asset_from_instance, delete_assets_by_taskid_from_manager
from urllib.parse import urljoin
import json
import uuid
from datetime import timedelta, datetime
import asyncio
from task.task import add_task
from sqlalchemy.future import select
from sqlalchemy import text, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
import httpx
from langchain_openai import ChatOpenAI
from models.mj_interactive import MjInteractive
import re
import aiohttp
import base64

from utils.exceptions import ClientVisibleException
from utils.hash.aes import decrypt

router = APIRouter()
router_cb = APIRouter()
logger = logging.getLogger(__name__)


OPENAI_API_KEY = decrypt(app_settings.openai_api_key)
API_BASE_URL = app_settings.openai_api_base_url
chat_model = ChatOpenAI(api_key=OPENAI_API_KEY, model="gpt-4o", temperature=0.8, base_url=API_BASE_URL)

gpt_prompt = """
根据用户提供的提示词，为Midjourney人工智能项目生成详细而富有创意的描述，并确保优化的提示词遵循以下原则：

1. **主体** - 含有至少一个名词，如: 城堡、树木、机器人。
2. **形容词** - 包括多个形容词来增强视觉效果，如: 美丽的、逼真的、多彩的、巨大的。
3. **风格** - 使用具体的视觉风格描述，如: 超现实主义、对称主义、当代主义、极简主义。
4. **渲染** - 指定计算机制图，如: Octane渲染、Cycles、虚幻引擎、光线追踪。
5. **视角与光线** - 描述视角、镜头和光线效果。
6. **空间与距离** - 提供详细的空间和距离描述，如通过低角度视角表现压迫感或远景展示模糊的背景。
7. **明确说明** - 主体应有明确且具体的描述。
8. **艺术风格** - 根据需要和适用性，加入艺术家风格以影响作品风貌。

确保提示词的优化不偏离用户提供的原始描述。

# Output Format

返回仅包含优化后的提示词内容。

# Examples

- 输入: "美丽的森林"
  - 优化后: "巨大的、美丽的森林场景，采用超现实主义风格，通过Octane渲染，广角镜头捕捉阳光透过树冠的效果，前景是一位小女孩仰望高耸的树木，背景隐约可见远处丘陵。"

- 输入: "科幻城市夜景"
  - 优化后: "当代主义科幻城市夜景，中远景展示光线追踪效果下的霓虹灯街景，近景详细描绘机器人巡逻着人烟稀少的巷道，采用虚幻引擎渲染，俯视视角突出城市的宏伟。"

# Notes

- 确保优化后的提示词言简意赅且清晰易懂。
- 提示词中应自然地组合各个要素，保持描述的流畅性。
"""


async def process_input(input_value):
  """
  判断传入内容是 Base64 还是 URL，如果是 URL 则转为 Base64。

  Args:
      input_value (str): 传入的字符串（URL 或 Base64 数据）。

  Returns:
      str: 如果是 Base64 则直接返回；如果是 URL，返回对应的 Base64 数据。
  """
  # Base64 的正则匹配
  base64_pattern = r'^data:image\/[a-zA-Z]+;base64,[a-zA-Z0-9+/]+={0,2}$'

  # 如果是 Base64 数据，直接返回
  if re.match(base64_pattern, input_value):
    return input_value

  # 如果是 URL，尝试将其转换为 Base64 数据
  try:
    async with aiohttp.ClientSession() as session:
      async with session.get(input_value) as response:
        response.raise_for_status()  # 确保请求成功
        content_type = response.headers.get('Content-Type', 'image/png')  # 默认 PNG 类型
        data = await response.read()  # 异步读取内容
        base64_data = base64.b64encode(data).decode('utf-8')
        return f"data:{content_type};base64,{base64_data}"
  except aiohttp.ClientError as e:
    logger.error(f"无法处理 URL: {e}")

async def process_array(input_array):
    """
    处理数组中的每个元素，判断是 Base64 还是 URL，如果是 URL 则转换为 Base64。

    Args:
        input_array (list): 包含 URL 或 Base64 数据的列表。

    Returns:
        list: 转换后的 Base64 数据列表。
    """
    tasks = [process_input(item) for item in input_array]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results


async def upload_base64_to_oss(base64_array):
    """
    将base64数据上传到OSS并返回URL列表

    Args:
        base64_array (list): 包含base64数据的列表

    Returns:
        list: 上传后的OSS URL列表
    """
    if not base64_array:
        return []

    urls = []
    for idx, base64_data in enumerate(base64_array):
        if base64_data:
            try:
                # 使用service.file中的save_image_b64data函数上传图片
                filename = f"mj_ref_{idx}_{uuid.uuid4().hex}.png"
                url = save_image_b64data(base64_data, filename)
                if url:
                    urls.append(url)
            except Exception as e:
                logger.error(f"上传图片到OSS失败: {e}")

    return urls


class getMyTaskResponse(success):
  data: list[ChatMjTasksOut]
  # total: int  # 总页数
  # last_page: int  # 最后一页


@router.get("/get_my_tasks", tags=["midjourney"], response_model=getMyTaskResponse)
async def getMyTask(
  page: int = 1,
  page_size: int = 10,
  channels: int = 0,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  try:
    # 构建基础查询
    stmt = select(ChatMjTasks)

    # 根据 channels 或 username 应用过滤条件
    if channels != 0:
      stmt = stmt.where(ChatMjTasks.channels == channels)
    else:
      stmt = stmt.where(ChatMjTasks.username == user.username)

    # 应用排序、分页
    stmt = stmt.order_by(ChatMjTasks.submit_time.desc()).limit(page_size).offset((page - 1) * page_size)

    # 执行查询获取任务
    result = await db.execute(stmt)
    tasks = result.scalars().all()

    if not tasks:
      return getMyTaskResponse(data=[])

    # 提取任务 ID（保持为整数）
    task_ids = [task.id for task in tasks]

    # 查询 MjInteractive 以获取用户的点赞状态
    interactive_stmt = select(MjInteractive).where(
      and_(
        MjInteractive.taskid.in_(task_ids),
        MjInteractive.user_id == user.id
      )
    )
    interactive_result = await db.execute(interactive_stmt)
    interactions = interactive_result.scalars().all()

    # 创建 taskid 到 is_like 的映射
    is_like_map = {interaction.taskid: interaction.is_like for interaction in interactions}

    # 准备响应数据，包含 is_like 信息
    response_data = []
    for task in tasks:
      task_dict = task.__dict__.copy()
      # 移除 SQLAlchemy 内部状态
      task_dict.pop('_sa_instance_state', None)
      # 根据映射设置 is_like，默认为 0
      task_dict['is_like'] = is_like_map.get(task.id, 0)
      response_data.append(ChatMjTasksOut(**task_dict))

    return getMyTaskResponse(data=response_data)

  except SQLAlchemyError as e:
    logger.error(f"Database error: {e}")
    raise ClientVisibleException("数据库操作失败")
  except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise ClientVisibleException("服务器内部错误")


async def http(type: str = "POST", uri: str = None, **kwargs) -> dict:
  """
  Sends an HTTP request to the Midjourney server asynchronously.

  Args:
      type (str, optional): The HTTP method (e.g., 'POST', 'GET'). Defaults to 'POST'.
      uri (str): The URI endpoint.
      **kwargs: Additional keyword arguments to pass to the `httpx` library.

  Returns:
      dict: The JSON response data.

  Raises:
      Exception: If the request fails or the response data is not valid JSON.
  """
  server_url = app_settings.midjourney_server
  logger.debug(f"mj_server_url: {server_url}")
  url = urljoin(server_url, uri)

  logger.debug(
    f"uri: {uri} params: {json.dumps(kwargs.get('params', {}))}, json: {json.dumps(kwargs.get('json', {}))}"
  )

  raw_data = None  # 初始化 raw_data 变量

  try:
    async with httpx.AsyncClient(timeout=90.0) as client:
      response = await client.request(type, url, **kwargs)
      response.raise_for_status()
      raw_data = response.text
      logger.debug(f"raw_data: {raw_data}")

      if not raw_data:
        raise ClientVisibleException("request failed, return data is empty")
      json_data = json.loads(raw_data)
      return json_data

  except httpx.RequestError as e:
    logger.error(f"request failed\n" + str(e))
    raise e

  except Exception as e:
    logger.error(str(e))
    # 如果 raw_data 不为空，记录它；否则，记录一个空的返回值
    if raw_data is not None:
      logger.error(f"request failed, return data:\n{raw_data}\n")
    else:
      logger.error("request failed, no response data returned.\n")
    raise e


async def get_task(task_id):
  """
  获取任务信息
  """
  return await http("GET", f"/mj/task/{task_id}/fetch")


async def create_task(
  action: str,
  db: AsyncSession,
  user: User,
  request_data: dict,
  taskid: int = None,
  channels: int = 0,
):
  """
  创建任务
  """
  # 使用 select 语句替代 query
  stmt = select(ChatMjTasks).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.status.in_(["NOT_START", "SUBMITTED", "IN_PROGRESS"]),
    text("submit_time > now() - INTERVAL 5 MINUTE")
  )

  result = await db.execute(stmt)
  tasks = result.scalars().all()  # 获取查询结果并转换为列表
  cut = len(tasks)  # 使用 len() 计算数量

  if cut > 3:
    # 不抛出异常，而是返回错误状态
    return None, "任务数超限，请稍后重试"

  # 处理base64Array，上传到OSS
  base64_array = request_data.get("base64Array", [])
  prompt_img_urls = await upload_base64_to_oss(base64_array)

  # 如果有参考图，将图片URL拼接到prompt前面
  original_prompt = request_data.get("prompt", "")
  if prompt_img_urls:
    # 拼接所有图片URL和原始prompt
    img_urls_str = " ".join(prompt_img_urls)
    request_data["prompt"] = f"{img_urls_str} {original_prompt}"

  # 清空base64Array，减少请求数据量
  request_data["base64Array"] = []

  try:
    # 创建任务
    res = await http(
      type="POST",
      uri="/mj/submit/" + action,
      json=request_data,
    )

    logger.info(f"HTTP response: {res}")

    if res["code"] == 1:
      taskid = res.get("result", res.get("taskId", taskid))
      # 获取任务信息
      task_data = await get_task(taskid)
      # 创建数据库记录
      Task = ChatMjTasks(
        username=user.username,
        taskid=taskid,
        action=task_data.get("action"),
        status=task_data.get("status"),
        prompt=task_data.get("prompt"),
        prompt_en=task_data.get("prompt_en"),
        description=task_data.get("description"),
        state=task_data.get("state"),
        submit_time=timestamp_to_string(task_data.get("submitTime")),
        start_time=timestamp_to_string(task_data.get("startTime")),
        finish_time=timestamp_to_string(task_data.get("finishTime")),
        image_url=task_data.get("imageUrl"),
        progress=task_data.get("progress"),
        fail_reason=task_data.get("failReason"),
        uptime=datetime.now(),
        channels=channels,
        like_count=0,
        prompt_img=json.dumps(prompt_img_urls) if prompt_img_urls else None  # 保存图片URL列表
      )
      # 成功时返回任务和None
      return Task, None
    elif res["code"] == 21:
      # 不抛出异常，而是返回错误状态
      return None, "任务已存在"
    elif res["code"] == 22:
      # 不抛出异常，而是返回错误状态
      return None, "任务排队中"
    else:
      # 不抛出异常，而是返回错误状态
      return None, "您输入的提示词存在违禁词,请重新调整提示词后重试."

  except Exception as e:
    logger.error(f"error is: {e}")
    # print(f"Failed request data: {request_data}")
    # 不抛出异常，而是返回错误状态
    return None, "创作失败,请尝试更换提示词或稍后重试."


class imagineResponse(success):
  code: str = "0000"
  data: Optional[ChatMjTasksOut] = None


@router.post("/submit/imagine", tags=["midjourney"], response_model=imagineResponse)
async def imagine(
  prompt: Annotated[str, Body()],
  base64Array: Annotated[list[str], Body()] = [],
  channels: Annotated[int, Body()] = 0,
  taskid: Annotated[int, Body()] = None,  # 新增taskid参数，默认为None
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
  提交绘画任务
  """
  if prompt == "":
    raise ClientVisibleException("提示词不能为空")

  # 存储要删除的任务，初始为None
  task_to_delete = None
  
  # 如果taskid不为空，检查是否存在且属于当前用户
  if taskid is not None:
    # 查询对应的任务
    stmt = select(ChatMjTasks).filter(ChatMjTasks.id == taskid)
    result = await db.execute(stmt)
    task_to_delete = result.scalars().first()
    
    # 如果任务不存在，或者不属于当前用户，则不处理
    if not task_to_delete or task_to_delete.username != user.username:
      task_to_delete = None
      logger.warning(f"删除任务失败: 任务不存在或不属于当前用户, taskid={taskid}, user={user.username}")

  prompt_en = prompt
  # 进行翻译
  if contains_chinese(prompt):
    try:
      prompt_en = await get_translate(prompt, "zh-CN", "en")
    except:
      raise ClientVisibleException("翻译失败")

  data = {
    "prompt": prompt_en,
    "base64Array": base64Array,  # 直接传递base64Array，不再调用process_array
    # "notifyHook": callback_url,
    "state": uuid.uuid4().hex,
  }

  # 使用 await 调用 create_task 函数
  Task, error = await create_task("imagine", db, user, request_data=data, channels=channels)

  if error:
    # 添加空的 data 字段以满足 imagineResponse 的要求
    raise ClientVisibleException(error)

  Task.prompt = prompt
  Task.prompt_en = prompt_en
  db.add(Task)
  
  # 如果存在需要删除的任务且新任务创建成功，删除旧任务
  if task_to_delete:
    # 记录删除操作
    logger.info(f"正在删除旧任务: id={task_to_delete.id}, taskid={task_to_delete.taskid}, user={user.username}")
    await db.delete(task_to_delete)

  await db.commit()  # 异步提交
  await db.refresh(Task)  # 异步刷新

  return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))


@router.post("/submit/change", tags=["midjourney"], response_model=imagineResponse)
async def change(
  taskid: Annotated[int, Body()],
  action: Annotated[ChatMjTasksChangeAction, Body()],
  channels: Annotated[int, Body()] = 0,
  index: Annotated[int, Body(ge=1, le=4)] = None,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
    ## 提交绘画形变任务
    - action : UPSCALE(放大); VARIATION(变换); REROLL(重新生成),可用值:UPSCALE,VARIATION,REROLL,示例值(UPSCALE)
    - index : 序号(1~4), action为UPSCALE,VARIATION时必传,示例值(1)
    - taskid : 任务ID,示例值(1320098173412546)
    """
  # 准备数据
  data = {
    "action": action,
    # "index": index,
    # "notifyHook": callback_url,
    "state": uuid.uuid4().hex,
    "taskId": taskid,
  }

  # 如果 action 是 UPSCALE 或 VARIATION，index 必传
  if action in [ChatMjTasksChangeAction.UPSCALE, ChatMjTasksChangeAction.VARIATION]:
    if index is None:
      raise ClientVisibleException("index is required for UPSCALE or VARIATION action.")
    data["index"] = index
  else:
    # action 为 REROLL 时，index 不必传
    data["index"] = None

  # 异步查询任务
  stmt = select(ChatMjTasks).filter(ChatMjTasks.taskid == taskid)
  result = await db.execute(stmt)
  pTask = result.scalars().first()

  if not pTask:
    raise ClientVisibleException("任务不存在")
  if pTask.status != "SUCCESS":
    raise ClientVisibleException("任务未完成")

  # 异步创建任务
  Task, error = await create_task("change", db, user, request_data=data, taskid=taskid, channels=channels)
  if error:
    # 添加空的 data 字段以满足 imagineResponse 的要求
    raise ClientVisibleException(error)

  Task.prompt = pTask.prompt
  Task.prompt_en = pTask.prompt_en
  Task.pid = pTask.id
  Task.prompt_img = pTask.prompt_img  # 继承父级任务的prompt_img字段

  db.add(Task)
  await db.commit()  # 异步提交
  await db.refresh(Task)  # 异步刷新

  return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))


@router.get("/get_task_state", tags=["midjourney"], response_model=imagineResponse)
async def getTaskState(
  taskid: int,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
    获取任务状态
    """
  # 异步查询任务
  stmt = select(ChatMjTasks).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.taskid == taskid
  )
  result = await db.execute(stmt)
  Task = result.scalars().first()

  if not Task:
    raise ClientVisibleException("任务不存在")

  # 当前时间
  # now = datetime.now()

  # 判断任务是否生成超过10分钟
  if Task.uptime - Task.submit_time > timedelta(minutes=10):
    Task.status = "FAILURE"
    await db.commit()  # 提交数据库更改
    return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))

  # 如果 task.uptime 比现在小10秒钟
  if (
    Task.status not in ["SUCCESS", "FAILURE"]
    and datetime.now() - Task.uptime > timedelta(seconds=10)
  ):
    """
        获取任务信息更新状态
        """
    data = await get_task(taskid)
    # print(f"任务的数据是：{data}")
    await update_task(Task, data, db)

  # 如果任务状态为 SUCCESS，则获取 seed 并更新数据库
  if Task.status == "SUCCESS" and Task.seed is None and Task.action == "UPSCALE":
    # 调用 get_seed 获取 seed 值
    seed_response = await get_seed(Task.taskid)
    # 判断 seed 请求是否成功
    if seed_response.get("code") == 1:
      Task.seed = seed_response.get("result")  # 更新 seed 字段
      await db.commit()  # 提交数据库更改
      # await db.refresh(Task)  # 刷新 Task 对象

  return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))


async def update_task(Task: ChatMjTasks, data: dict, db: AsyncSession):
  Task.status = data["status"]
  Task.progress = data["progress"]

  Task.prompt = Task.prompt or data.get("prompt")
  Task.prompt_en = Task.prompt_en or data.get("promptEn")

  if data["imageUrl"] is not None:
    Task.image_url = data["imageUrl"]

  if data["failReason"] is not None:
    Task.fail_reason = data["failReason"]

  if data["finishTime"] is not None:
    Task.finish_time = timestamp_to_string(data["finishTime"])

  if data["buttons"] is not None:
    Task.buttons = data["buttons"]
    # 调用封装的格式化函数处理 buttons
    Task.button = format_mj_buttons(data["buttons"])

  if data["status"] == "SUCCESS" or data["status"] == "FAILURE":
    Task.uptime = datetime.now()
    await db.commit()
    await db.refresh(Task)

    # # 成功生成图片后加入队列
    # if data["status"] == "SUCCESS":
    #   task_data = {
    #     "task_module": "upload_file_to_oss",
    #     "task_argv": json.dumps({
    #       "table": Task.__tablename__,
    #       "image_url": Task.image_url,
    #       "id": Task.id
    #     })
    #   }
    #   # 将任务添加到 Redis Stream 队列
    #   # asyncio.create_task创建异步任务
    #   logger.info(f"Adding task to queue: {task_data}")
    #   await add_task(task_data)
    #   logger.info("Task added to queue")

    # 成功生成图片后处理资产存储
    if data["status"] == "SUCCESS" and Task.image_url and Task.action != "DESCRIBE":
      # try:
      # 使用save_image_by_url上传图片到OSS
      logger.info(f"开始将MidJourney图片上传到OSS: {Task.image_url}")
      oss_url = await save_image_by_url(Task.image_url)
      
      if oss_url:
        # 更新任务中的图片URL为OSS URL
        Task.image_url = oss_url
        await db.commit()
        await db.refresh(Task)
        logger.info(f"MidJourney图片已上传到OSS: {oss_url}")
        
        # 将资产信息存储到资产表
        try:
          # 根据username查询用户实例
          user_stmt = select(User).filter(User.username == Task.username)
          user_result = await db.execute(user_stmt)
          task_user = user_result.scalars().first()
          
          if task_user:
            # 存储资产到资产表
            asset = await store_asset_from_instance(Task, task_user, db)
            if asset:
              logger.info(f"成功将MidJourney任务存储到资产表，资产ID: {asset.id}")
            else:
              logger.warning(f"MidJourney任务存储到资产表失败，任务ID: {Task.id}")
          else:
            logger.error(f"未找到用户: {Task.username}，无法存储资产")
        except Exception as e:
          logger.error(f"存储MidJourney资产失败: {str(e)}")
    elif data["status"] == "SUCCESS" and Task.image_url and Task.action == "DESCRIBE":
      # 图片反推（DESCRIBE）任务不需要存储到资产表，但仍需要上传到OSS
      logger.info(f"开始将MidJourney DESCRIBE图片上传到OSS: {Task.image_url}")
      oss_url = await save_image_by_url(Task.image_url)
      
      if oss_url:
        # 更新任务中的图片URL为OSS URL
        Task.image_url = oss_url
        await db.commit()
        await db.refresh(Task)
        logger.info(f"MidJourney DESCRIBE图片已上传到OSS，跳过资产存储: {oss_url}")

  return Task


@router.post("/callback", tags=["midjourney"])
async def callback(
  id: Annotated[int, Body()],
  state: Annotated[str, Body()],
  request: Request,
  db: AsyncSession = Depends(get_db),
):
  """
  mj-proxy 的任务回调
  """
  # 异步查询任务
  stmt = select(ChatMjTasks).filter(ChatMjTasks.taskid == id)
  result = await db.execute(stmt)
  Task = result.scalars().first()

  if not Task:
    raise ClientVisibleException("任务不存在")

  if Task.status in ["SUCCESS", "FAILURE"]:
    return "success"

  if Task.state != state:
    raise ClientVisibleException("任务状态不匹配")

  data = await request.json()

  # print(f"data111: {data}")

  # 异步更新任务
  await update_task(Task, data, db)

  return "success"


# 历史记录搜索
@router.get("/get_search_tasks", tags=["midjourney"], response_model=getMyTaskResponse)
async def get_search_tasks(
  search_prompt: str,
  page: int = 1,
  page_size: int = 10,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  stmt = select(ChatMjTasks).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.prompt.ilike(f"%{search_prompt}%")
  ).order_by(ChatMjTasks.submit_time.desc()).limit(page_size).offset((page - 1) * page_size)

  result = await db.execute(stmt)
  data = result.scalars().all()  # 获取查询结果

  return getMyTaskResponse(data=[ChatMjTasksOut(**d.__dict__) for d in data])


class WorksTasksResponseData(BaseModel):
  tasks: list[WorksTasksOut]
  total_page: int


class getWorksTasksResponse(success):
  data: WorksTasksResponseData


@router.get("/get_works_tasks", tags=["midjourney"], response_model=getWorksTasksResponse)
async def getWorksTasks(
  page: int = 1,
  page_size: int = 10,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  # 计算总数据量
  count_stmt = select(func.count()).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.status == "SUCCESS",
    ChatMjTasks.image_url.isnot(None)
  )
  total_count_result = await db.execute(count_stmt)
  total_count = total_count_result.scalar_one()

  # 计算总页数
  total_page = (total_count + page_size - 1) // page_size

  # 获取当前页数据
  stmt = select(ChatMjTasks).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.status == "SUCCESS",
    ChatMjTasks.image_url.isnot(None),
    ChatMjTasks.action != "DESCRIBE"
  ).order_by(ChatMjTasks.submit_time.desc()).limit(page_size).offset((page - 1) * page_size)

  result = await db.execute(stmt)
  data = result.scalars().all()  # 获取查询结果

  # 构建返回数据列表
  tasks_out = [
    WorksTasksOut(
      id=d.id,
      pid=d.pid,
      username=d.username,
      taskid=d.taskid,
      action=d.action.value,
      status=d.status.value if d.status else None,
      prompt=d.prompt,
      image_url=d.image_url,
      progress=d.progress,
      fail_reason=d.fail_reason,
      total_page=total_page
    ) for d in data
  ]

  # 构建响应数据
  response_data = WorksTasksResponseData(tasks=tasks_out, total_page=total_page)

  return getWorksTasksResponse(data=response_data)


class TextInput(BaseModel):
  text: str


@router.post("/get_mj_prompt", tags=["midjourney"])
async def get_mj_prompt(input: TextInput):
  # prompt = PromptTemplate.from_template(gpt_prompt)
  # mj_prompt = chat_model.invoke(prompt.format(text=input.text)).content
  # return {"code": "0000", "data": {"mj_prompt": mj_prompt}}
  messages = [
    (
      "system",
      gpt_prompt,
    ),
    ("human", input.text),
  ]
  mj_prompt = chat_model.invoke(messages).content
  return {"code": "0000", "data": {"mj_prompt": mj_prompt}}


async def get_seed(task_id):
  """
  获取图片seed值
  """
  return await http("GET", f"/mj/task/{task_id}/image-seed")


@router.get("/get_img_seed", tags=["midjourney"])
async def get_img_seed(task_id: str):
  # 调用 get_seed 获取原始数据
  response = await get_seed(task_id)

  # 根据返回结果调整格式
  if response.get("code") == 1:
    return {
      "code": "0000",
      "data": {
        "seed": response.get("result")
      }
    }

  # 返回失败信息
  raise ClientVisibleException(response.get("description", "unknown error"))


# 格式化动作标识返回前端的数据
def format_mj_buttons(buttons: list) -> list:
  # 构造映射数据
  type_group_map = {
    "JOB": "任务",
    "INPAINT": "修图",
    "OUTPAINT": "扩图",
    "CUSTOMZOOM": "缩放",
    "BOOKMARK": "收藏",
  }

  action_map = {
    "upsample_v6r1_2x_subtle": "轻度放大",
    "upsample_v6r1_2x_creative": "创意放大",
    "low_variation": "轻度变化",
    "high_variation": "强度变化",
    "pan_left": "左移",
    "pan_right": "右移",
    "pan_up": "上移",
    "pan_down": "下移",
    "75": "1.5倍",
    "50": "2倍",
  }

  formatted_buttons = []

  # 循环处理按钮数据
  for button in buttons:
    button_array = button["customId"].split("::")
    type_key = button_array[1]  # 获取类型
    action_key = button_array[2]  # 获取动作

    type_name = type_group_map.get(type_key.upper(), "")  # 转为大写匹配

    # 如果没有找到类型名称，跳过此按钮
    if not type_name:
      continue

    # 查找是否存在同名的 type_name
    find_item = next(
      (item for item in formatted_buttons if item["type_name"] == type_name), None
    )

    # 构造按钮数据
    actions_item = {
      "label": action_map.get(action_key, action_key),  # 若找不到 action 映射则直接使用 action_key
      "customId": button["customId"],
      "emoji": button.get("emoji", ""),  # 确保 emoji 存在，若不存在则返回空字符串
    }

    # 如果已存在同类型的组，追加 actions；否则创建新组
    if find_item:
      find_item["actions"].append(actions_item)
    else:
      formatted_buttons.append(
        {"type": type_key, "type_name": type_name, "actions": [actions_item]}
      )

  return formatted_buttons


# 图像按钮执行动作 创建任务
async def create_action_task(
  db: AsyncSession,
  user: User,
  request_data: dict,
  taskid: int = None,
  channels: int = 0,
):
  """
  创建任务
  """
  # 使用 select 语句替代 query
  stmt = select(ChatMjTasks).filter(
    ChatMjTasks.username == user.username,
    ChatMjTasks.status.in_(["NOT_START", "SUBMITTED", "IN_PROGRESS"]),
    text("submit_time > now() - INTERVAL 5 MINUTE")
  )

  result = await db.execute(stmt)
  tasks = result.scalars().all()  # 获取查询结果并转换为列表
  cut = len(tasks)  # 使用 len() 计算数量

  if cut > 3:
    # 不抛出异常，而是返回错误状态
    return None, "任务数超限，请稍后重试"

  try:
    # 创建任务
    res = await http(
      type="POST",
      uri="/mj/submit/action",
      json=request_data,
    )

    logger.debug(f"HTTP response: {res}")

    if res["code"] == 1:
      taskid = res.get("result", res.get("taskId", taskid))
      # 获取任务信息
      task_data = await get_task(taskid)
      # 创建数据库记录
      Task = ChatMjTasks(
        username=user.username,
        taskid=taskid,
        action=task_data.get("action"),
        status=task_data.get("status"),
        prompt=task_data.get("prompt"),
        prompt_en=task_data.get("prompt_en"),
        description=task_data.get("description"),
        state=task_data.get("state"),
        submit_time=timestamp_to_string(task_data.get("submitTime")),
        start_time=timestamp_to_string(task_data.get("startTime")),
        finish_time=timestamp_to_string(task_data.get("finishTime")),
        image_url=task_data.get("imageUrl"),
        progress=task_data.get("progress"),
        fail_reason=task_data.get("failReason"),
        uptime=datetime.now(),
        channels=channels,
        like_count=0
      )
      # 成功时返回任务和None
      return Task, None
    elif res["code"] == 21:
      # 处理 code == 21 的逻辑
      task_id = res.get("result")
      modal_res = await http(
        type="POST",
        uri="/mj/submit/modal",
        json={"taskId": task_id},
      )
      logger.debug(f"Modal HTTP response: {modal_res}")

      if modal_res.get("code") != 1:
        # 不抛出异常，而是返回错误状态
        return None, f"Modal submission failed: {modal_res.get('description', 'Unknown error')}"

      # 如果 modal 返回成功，直接返回创建的任务对象
      taskid = modal_res.get("result")
      task_data = await get_task(taskid)

      Task = ChatMjTasks(
        username=user.username,
        taskid=taskid,
        action=task_data.get("action"),
        status=task_data.get("status"),
        prompt=task_data.get("prompt"),
        prompt_en=task_data.get("prompt_en"),
        description=task_data.get("description"),
        state=task_data.get("state"),
        submit_time=timestamp_to_string(task_data.get("submitTime")),
        start_time=timestamp_to_string(task_data.get("startTime")),
        finish_time=timestamp_to_string(task_data.get("finishTime")),
        image_url=task_data.get("imageUrl"),
        progress=task_data.get("progress"),
        fail_reason=task_data.get("failReason"),
        uptime=datetime.now(),
        channels=channels,  # 新增字段
        like_count=0
      )
      # 成功时返回任务和None
      return Task, None
    elif res["code"] == 22:
      # 不抛出异常，而是返回错误状态
      return None, "任务排队中"
    else:
      # 不抛出异常，而是返回错误状态
      return None, "您输入的提示词存在违禁词,请重新调整提示词后重试"

  except Exception as e:
    logger.error(f"error is: {e}")
    logger.error(f"Failed request data: {request_data}")
    # 不抛出异常，而是返回错误状态
    return None, "创作失败,请尝试更换提示词或稍后重试."


@router.post("/submit/action", tags=["midjourney"], response_model=imagineResponse)
async def action(
  customId: Annotated[str, Body()],
  taskId: Annotated[int, Body()],
  channels: Annotated[int, Body()] = 0,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
  提交绘画任务
  """
  if customId == "":
    raise ClientVisibleException("动作标识 不能为空")

  data = {
    "customId": customId,
    "taskId": taskId,
    "state": uuid.uuid4().hex,
  }

  #
  Task, error = await create_action_task(db, user, request_data=data, channels=channels)
  
  if error:
    # 添加空的 data 字段以满足 imagineResponse 的要求
    raise ClientVisibleException(error)

  Task.prompt_en = Task.prompt
  if contains_chinese(Task.prompt):
    try:
      Task.prompt_en = await get_translate(Task.prompt, "zh-CN", "en")
    except:
      raise ClientVisibleException("翻译失败")

  db.add(Task)
  await db.commit()  # 异步提交
  await db.refresh(Task)  # 异步刷新

  return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))

class DescribeRequest(BaseModel):
    base64: str
    # channels: int

@router.post("/submit/describe", tags=["midjourney"], response_model=imagineResponse)
async def describe(
  request: DescribeRequest,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
  提交图片反推提示词任务
  """
  if request.base64 == "":
    raise ClientVisibleException("请上传图片")


  # 打印 base64 前30个字符
  logger.debug(f"Received base64 (first 30 chars): {request.base64[:30]}")

  data = {
    "base64": await process_input(request.base64),
    # "notifyHook": callback_url,
    "state": uuid.uuid4().hex,
  }

  # 使用 await 调用 create_task 函数
  Task, error = await create_task(
    "describe",
    db,
    user,
    request_data=data,
    # channels=request.channels
  )
  
  if error:
    # 添加空的 data 字段以满足 imagineResponse 的要求
    raise ClientVisibleException(error)

  # Task.prompt = prompt
  # Task.prompt_en = prompt_en
  db.add(Task)
  await db.commit()  # 异步提交
  await db.refresh(Task)  # 异步刷新

  return imagineResponse(data=ChatMjTasksOut(**Task.__dict__))


@router.get("/get_channels", tags=["midjourney"])
async def get_channels(
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
    """
    根据用户信息返回对应的团队及频道列表。
    1. 如果请求接口的用户是对应的团队用户，则默认can_view=1，can_send=1。
    2. 如果是添加进去的用户(即非频道所属团队成员)，则需要关注ChannelUser中赋予的权限。

    返回格式示例：
    {code:"0000",data:{
        "public_channel":{ # 公有频道
            "频道名称1":{"channel_id":xxx,"can_view":1,"can_send":1},
            "频道名称2":{"channel_id":xxx,"can_view":1,"can_send":1}
        },
        "团队名称1":{
            "频道名称1":{"channel_id":xxx,"can_view":1,"can_send":1},
            "频道名称2":{"channel_id":xxx,"can_view":1,"can_send":1}
        },
        "团队名称2":{
            "频道名称1":{"channel_id":xxx,"can_view":1,"can_send":1},
            "频道名称2":{"channel_id":xxx,"can_view":1,"can_send":1}
        }
    }}
    """

    user_id = user.id
    company_name = user.company
    logger.debug(f"user_id: {user_id}, company_name: {company_name}")

    # 修改: 从app_setting表查询公司信息，而不是从Company表
    from models.app_seting import AppSetting
    import json
    
    # 先尝试从app_setting中查询公司信息
    company_record = await db.execute(
        select(AppSetting).where(
            AppSetting.key_type == "company",
            AppSetting.value_type == "json"
        )
    )
    
    company_settings = company_record.scalars().all()
    company_info = None
    team_id = 0
    team_name = company_name
    
    # 遍历所有公司配置，查找匹配的公司名称
    for setting in company_settings:
        try:
            company_config = json.loads(setting.key_value)
            if 'company_name' in company_config and company_config['company_name'] == company_name:
                company_info = {
                    'id': setting.id,
                    'team_name': company_config['company_name']
                }
                team_id = setting.id
                team_name = company_config['company_name']
                break
        except Exception as e:
            logger.error(f"解析公司配置失败: {str(e)}")
            continue
    
    # 兼容逻辑: 如果在app_setting中未找到公司，尝试使用原来的Company表查询
    if not company_info:
        # 尝试查询company表作为兼容
        company_legacy_record = await db.execute(
            text("SELECT id, team_name FROM work_teams WHERE team_name = :company_name"),
            {"company_name": company_name}
        )
        company_info = company_legacy_record.fetchone()
        
        if company_info:
            team_id = company_info.id
            team_name = company_info.team_name

    if not company_info:
        return success(data={"public_channel": {}})

    # 查询用户角色是否为超级管理员
    user_role_record = await db.execute(
        text("SELECT role_id FROM user_role WHERE user_id = :user_id"),
        {"user_id": user_id}
    )
    user_role = user_role_record.scalar()
    logger.debug(f"user_role: {user_role}")

    data_dict = {}

    # 获取用户在ChannelUser中的权限记录
    channel_user_records = await db.execute(
        text("SELECT channel_id, can_view, can_send FROM channel_user WHERE user_id = :user_id"),
        {"user_id": user_id}
    )
    user_channel_permissions = channel_user_records.fetchall()
    user_channel_perm_map = {r.channel_id: (r.can_view, r.can_send) for r in user_channel_permissions}

    def get_channel_permission(channel_team_id, channel_id, channel_permission):
        # 公有频道(creator_team=0或permission='public')
        if channel_team_id == 0 or channel_permission == 'public':
            return (1, 1)
        # 私有频道
        if channel_permission == 'private':
            # 同团队
            if channel_team_id == team_id:
                return (1, 1)
            else:
                # 非团队用户查看channel_user权限
                return user_channel_perm_map.get(channel_id, None)
        # 默认处理
        return (1, 1)

    if user_role == 1:
        logger.debug("超级管理员")
        # 超级管理员
        # 修改: 从app_setting获取所有公司
        all_companies = []
        # 先从app_setting中查询
        for setting in company_settings:
            try:
                company_config = json.loads(setting.key_value)
                if 'company_name' in company_config:
                    all_companies.append({
                        'id': setting.id, 
                        'team_name': company_config['company_name']
                    })
            except Exception as e:
                logger.error(f"解析公司配置失败: {str(e)}")
                continue
                
        # 兼容处理: 尝试从原Company表获取额外的公司
        all_company_legacy_records = await db.execute(text("SELECT id, team_name FROM work_teams"))
        legacy_companies = all_company_legacy_records.fetchall()
        
        # 合并两个来源的公司列表
        existing_company_names = {comp['team_name'] for comp in all_companies}
        for legacy_comp in legacy_companies:
            if legacy_comp.team_name not in existing_company_names:
                all_companies.append({'id': legacy_comp.id, 'team_name': legacy_comp.team_name})

        channels_records = await db.execute(
            text("SELECT id, channel_name, creator_team, permission FROM mj_channels")
        )
        all_channels = channels_records.fetchall()

        team_map = {c['id']: c['team_name'] for c in all_companies}

        public_channels = {}
        team_channels_map = {}

        for ch in all_channels:
            ch_id = ch.id
            ch_name = ch.channel_name
            ch_team = ch.creator_team
            ch_permission = ch.permission

            # 既然是超级管理员，perm 永远是 (1, 1)
            can_view, can_send = (1, 1)

            channel_data = {
                "channel_id": ch_id,
                "can_view": can_view,
                "can_send": can_send
            }

            if ch_team == 0 or ch_permission == 'public':
                public_channels[ch_name] = channel_data
            else:
                team_name_tmp = team_map.get(ch_team, f"team_{ch_team}")
                if team_name_tmp not in team_channels_map:
                    team_channels_map[team_name_tmp] = {}
                team_channels_map[team_name_tmp][ch_name] = channel_data

        data_dict["public_channel"] = public_channels
        data_dict.update(team_channels_map)

    else:
        # 非超级管理员
        # 公有频道
        public_channels_records = await db.execute(
            text("SELECT id, channel_name, creator_team, permission FROM mj_channels WHERE creator_team=0 OR permission='public'")
        )
        public_channels_data = public_channels_records.fetchall()

        public_channels = {}
        for pc in public_channels_data:
            pc_id = pc.id
            perm = get_channel_permission(pc.creator_team, pc_id, pc.permission)
            if perm:
                can_view, can_send = perm
                public_channels[pc.channel_name] = {
                    "channel_id": pc_id,
                    "can_view": can_view,
                    "can_send": can_send
                }

        # 获取所属团队的频道
        team_channels_records = await db.execute(
            text("SELECT id, channel_name, creator_team, permission FROM mj_channels WHERE creator_team=:team_id"),
            {"team_id": team_id}
        )
        team_channels_data = team_channels_records.fetchall()

        team_channels_map = {}

        if not team_channels_data:
            data_dict["public_channel"] = public_channels
            data_dict[team_name] = {}
            return success(data=data_dict)

        for ch in team_channels_data:
            ch_id = ch.id
            ch_name = ch.channel_name
            ch_team = ch.creator_team
            ch_permission = ch.permission

            perm = get_channel_permission(ch_team, ch_id, ch_permission)
            if perm is None:
                continue

            can_view, can_send = perm
            if team_name not in team_channels_map:
                team_channels_map[team_name] = {}
            team_channels_map[team_name][ch_name] = {
                "channel_id": ch_id,
                "can_view": can_view,
                "can_send": can_send
            }

        data_dict["public_channel"] = public_channels
        data_dict.update(team_channels_map)

    return success(data=data_dict)



class AddChannelModel(BaseModel):
    channel_name: str = Field(..., description="频道名称")
    permission: Literal["private", "public"] = Field(..., description="频道权限：private或public")

@router.post("/add_channel", tags=["midjourney"])
async def add_channel(
    params: AddChannelModel,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    传入参数：
    channel_name：频道名称
    permission：频道权限：private或public
    """
    # 修改: 从app_setting表查询公司信息，而不是从Company表
    from models.app_seting import AppSetting
    import json
    
    # 先尝试从app_setting中查询公司信息
    company_record = await db.execute(
        select(AppSetting).where(
            AppSetting.key_type == "company",
            AppSetting.value_type == "json"
        )
    )
    
    company_settings = company_record.scalars().all()
    company_info = None
    
    # 遍历所有公司配置，查找匹配的公司名称
    for setting in company_settings:
        try:
            company_config = json.loads(setting.key_value)
            if 'company_name' in company_config and company_config['company_name'] == user.company:
                company_info = {
                    'id': setting.id
                }
                break
        except Exception as e:
            logger.error(f"解析公司配置失败: {str(e)}")
            continue
    
    # 兼容逻辑: 如果在app_setting中未找到公司，尝试使用原来的Company表查询
    if not company_info:
        # 查询company表获取id及team_name（creator_team）
        company_legacy_record = await db.execute(
            text("SELECT id FROM work_teams WHERE team_name = :company_name"),
            {"company_name": user.company}
        )
        company_info = company_legacy_record.fetchone()

    if not company_info:
        # 未能找到对应团队，无法创建频道
        return success(data={"msg": 0})

    creator_team = company_info['id'] if isinstance(company_info, dict) else company_info.id
    creator = user.id
    modifier = user.id

    # 插入新频道数据
    insert_sql = text("""
        INSERT INTO mj_channels (channel_name, permission, creator, creator_team, modifier, create_time, update_time)
        VALUES (:channel_name, :permission, :creator, :creator_team, :modifier, NOW(), NOW())
    """)
    await db.execute(insert_sql, {
        "channel_name": params.channel_name,
        "permission": params.permission,
        "creator": creator,
        "creator_team": creator_team,
        "modifier": modifier
    })
    await db.commit()

    return success(data={"msg": 1})



class UserLikeResponseData(BaseModel):
    user: int  # MjInteractive.user_id
    id: int  # MjInteractive.taskid
    is_like: int
    like_count: int


class UserLikeResponse(success):
  data: list[UserLikeResponseData]


@router.post("/user_like", tags=["midjourney"], response_model=UserLikeResponse)
async def user_like(
    taskid: Annotated[int, Body(..., embed=True)],
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    切换用户点赞状态
    """
    try:
        # Step 1: Check if the interaction exists
        stmt = select(MjInteractive).where(
            MjInteractive.taskid == taskid,
            MjInteractive.user_id == user.id
        )
        result = await db.execute(stmt)
        interaction = result.scalars().first()

        # Fetch the corresponding ChatMjTasks record
        task_stmt = select(ChatMjTasks).where(ChatMjTasks.id == taskid)
        task_result = await db.execute(task_stmt)
        task = task_result.scalars().first()

        if not task:
            raise ClientVisibleException("Task not found.")

        if not interaction:
            # Step 2: No existing interaction, create one with is_like=1
            new_interaction = MjInteractive(
                user_id=user.id,
                taskid=taskid,
                is_like=1
            )
            db.add(new_interaction)

            # Increment like_count
            task.like_count = (task.like_count or 0) + 1
            await db.flush()  # Ensure the like_count is updated before commit

            response_data = UserLikeResponseData(
                user=new_interaction.user_id,
                id=new_interaction.taskid,
                is_like=new_interaction.is_like,
                like_count=task.like_count
            )
            response = UserLikeResponse(code="0000", data=[response_data])
        else:
            if interaction.is_like == 1:
                interaction.is_like = 0
                task.like_count = (task.like_count or 1) - 1
            else:
                interaction.is_like = 1
                task.like_count = (task.like_count or 0) + 1

            await db.flush()  # Ensure changes are persisted

            response_data = UserLikeResponseData(
                user=interaction.user_id,
                id=interaction.taskid,
                is_like=interaction.is_like,
                like_count=task.like_count
            )
            response = UserLikeResponse(code="0000", data=[response_data])

        # 提交事务
        await db.commit()
        return response

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"Database error: {e}")
        raise ClientVisibleException("操作失败") from e
    except Exception as e:
        await db.rollback()
        logger.error(f"Unexpected error: {e}")
        raise ClientVisibleException("操作失败") from e



@router.get("/channel_list", tags=["midjourney"])
async def channels(
    page: int = 1,
    page_size: int = 10,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    返回全部频道，用于管理员获取频道列表管理频道
    """
    user_id = user.id
    # 查询用户角色是否为超级管理员
    user_role_record = await db.execute(
        text("SELECT role_id FROM user_role WHERE user_id = :user_id"),
        {"user_id": user_id}
    )
    user_role = user_role_record.scalar()

    if user_role == 1:
        # 如果是超级管理员，查询全部频道
        channels_query = text("SELECT * FROM mj_channels LIMIT :limit OFFSET :offset")
        offset = (page - 1) * page_size
        channels_record = await db.execute(
            channels_query,
            {"limit": page_size, "offset": offset}
        )
        channels = channels_record.fetchall()

        # 获取 creator_team 的映射关系
        creator_team_ids = {row.creator_team for row in channels if row.creator_team != 0}

        # 查询团队映射
        team_mapping = {}
        if creator_team_ids:
            team_records = await db.execute(
                text("SELECT id, team_name FROM work_teams WHERE id IN :team_ids"),
                {"team_ids": tuple(creator_team_ids)}
            )
            team_mapping = {row.id: row.team_name for row in team_records.fetchall()}

        # 构建返回数据
        result_channels = []
        for row in channels:
            channel = dict(row._mapping)
            # 获取 creator 的用户名
            if row.creator == 0:
                channel["creator"] = "默认创建"
            elif row.creator == user.id:
                channel["creator"] = user.username
            else:
                channel["creator"] = f"用户 {row.creator}"

            # 获取 creator_team 的团队名
            channel["creator_team"] = team_mapping.get(row.creator_team, "默认创建")
            result_channels.append(channel)

        return success(data={"channels": result_channels, "page": page, "page_size": page_size})
    else:
        # 如果不是超级管理员，返回权限不足
        raise ClientVisibleException("权限不足")


class ChannelDeleteRequest(BaseModel):
  channel_id: int

@router.post("/del_channel", tags=["midjourney"])
async def del_channel(request: ChannelDeleteRequest, user: User = Depends(get_request_user), db: AsyncSession = Depends(get_db)):
  """
  删除频道
  """
  channel_id = request.channel_id

  if channel_id == 1:
    raise ClientVisibleException("无法删除默认频道")

  user_id = user.id
  # 查询用户角色是否为超级管理员
  user_role_record = await db.execute(
    text("SELECT role_id FROM user_role WHERE user_id = :user_id"),
    {"user_id": user_id}
  )
  user_role = user_role_record.scalar()

  if user_role == 1:
    # 检查频道是否存在
    channel_record = await db.execute(
      text("SELECT id FROM mj_channels WHERE id = :channel_id"),
      {"channel_id": channel_id}
    )
    channel = channel_record.scalar()

    if not channel:
      raise ClientVisibleException("频道不存在")

    # 删除频道
    await db.execute(
      text("DELETE FROM mj_channels WHERE id = :channel_id"),
      {"channel_id": channel_id}
    )
    await db.commit()
    return success(msg="删除成功", data={"del_id": channel_id, "is_del": 1})
  else:
    # 如果不是超级管理员，返回权限不足
    raise ClientVisibleException("权限不足")


class DeleteTaskRequest(BaseModel):
  task_id: int = Field(..., description="要删除的任务ID")

@router.post("/delete_task", tags=["midjourney"])
async def delete_task(
    request: DeleteTaskRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除指定的任务及其关联的资产记录
    """
    try:
        # 查询任务是否存在且属于当前用户
        stmt = select(ChatMjTasks).where(
            ChatMjTasks.taskid == request.task_id,
            ChatMjTasks.username == user.username
        )
        result = await db.execute(stmt)
        task = result.scalars().first()

        if not task:
          raise ClientVisibleException("任务不存在或无权限删除")

        logger.info(f"开始删除任务 {request.task_id}，用户: {user.username}，MJ任务ID: {task.taskid}")

        # 删除相关的资产记录
        assets_deleted_count = 0
        if task.taskid:
            try:
                logger.info(f"开始删除taskid为{task.taskid}的相关资产")
                success_flag, deleted_count, error_msg = await delete_assets_by_taskid_from_manager(
                    db=db,
                    taskid=str(task.taskid),
                    user_id=user.id
                )
                
                if success_flag:
                    assets_deleted_count = deleted_count
                    if deleted_count > 0:
                        logger.info(f"成功删除taskid为{task.taskid}的{deleted_count}个相关资产")
                    else:
                        logger.info(f"taskid为{task.taskid}没有找到相关资产")
                else:
                    # 资产删除失败时记录错误，但不阻止任务删除
                    logger.warning(f"删除taskid为{task.taskid}的资产失败: {error_msg}")
                    
            except Exception as e:
                # 资产删除出现异常时记录错误，但不阻止任务删除
                logger.error(f"删除taskid为{task.taskid}的资产时发生异常: {str(e)}")

        # 删除任务记录
        await db.delete(task)
        await db.commit()

        logger.info(f"任务删除完成，任务ID: {request.task_id}，删除资产数量: {assets_deleted_count}")

        return success(
            msg="任务删除成功", 
            data={
                "task_id": request.task_id,
                "assets_deleted_count": assets_deleted_count
            }
        )

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"删除任务时数据库错误: {e}")
        raise ClientVisibleException("删除任务失败") from e
    except Exception as e:
        await db.rollback()
        logger.error(f"删除任务时发生未预期错误: {e}")
        raise ClientVisibleException("删除任务失败") from e

