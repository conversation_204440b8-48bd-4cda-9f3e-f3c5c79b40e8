import json
from pathlib import Path
from typing import TypedDict, Any

from app.audio.synthesis.http_model import ModelFunction

# 确保 id 有父级的前缀，且使用“.”分隔
pretrained_tone_categories = [
    {
        "id": "cosy_voice",
        "name": "CosyVoice 预训练模型",
        "children": None,
    },
    {
        "id": "sovites",
        "name": "SoVITS 语音合成",
        "children": [
            {
                "id": "sovites.genshin",
                "name": "原神",
                "children": None,
            },
            {
                "id": "sovites.honkai_impact_3rd",
                "name": "崩坏三",
                "children": None,
            },
            {
                "id": "sovites.honkai_star_rail",
                "name": "崩坏：星穹铁道",
                "children": None,
            },
            {
                "id": "sovites.zenless_zone_zero",
                "name": "绝区零",
                "children": None,
            },
            {
                "id": "sovites.v1",
                "name": "V1 旧版本",
                "children": None,
            },
        ],
    },
    {
        "id": "volcengine",
        "name": "火山语音",
        "children": [
            {
                "id": "volcengine.doubao",
                "name": "豆包同款",
                "children": None,
            },
            {
                "id": "volcengine.assistant",
                "name": "智能助手",
                "children": None,
            },
            {
                "id": "volcengine.reader",
                "name": "有声阅读",
                "children": None,
            },
            {
                "id": "volcengine.featured",
                "name": "特色配音",
                "children": None,
            },
            {
                "id": "volcengine.multilingual",
                "name": "多语种",
                "children": None,
            },
            {
                "id": "volcengine.dialect",
                "name": "方言",
                "children": None,
            },
        ],
    },
]

class PretrainedTone(TypedDict):
    id: str
    name: str
    gender: int | None
    lang: str | None
    description: str
    prompt_text: str
    audio_url: str
    categories: list[str]
    model_function: ModelFunction
    params: dict[str, Any]

_pretrained_tones_path = Path(__file__).parent / "pretrained_tones.json"
pretrained_tones: list[PretrainedTone] = json.loads(_pretrained_tones_path.read_text(encoding='utf-8'))
for _tone in pretrained_tones:
    _tone['model_function'] = ModelFunction(_tone['model_function'])


def get_pretrained_tone_by_id(tone_id: str) -> PretrainedTone | None:
    for _t in pretrained_tones:
        if _t['id'] == tone_id:
            return _t
    return None