/** The storage namespace */
declare namespace StorageType {
  /** Framepack form cache data */
  interface FramepackFormCache {
    /** Image preview URL */
    mediaPreview: string | null;
    /** Uploaded image URL */
    uploadedUrl: string | null;
    /** Media type */
    mediaType: 'image' | null;
    /** Video description/prompt */
    videoDescription: string;
    /** Video duration in seconds */
    videoSeconds: number;
    /** Video seed */
    videoSeed: string;
    /** Selected model platform */
    selectedModel: string;
    /** Selected sub model */
    selectedSubModel: string;
    /** Cache timestamp */
    cachedAt: number;
  }

  interface Session {
    /** The theme color */
    themeColor: string;
    // /**
    //  * the theme settings
    //  */
    // themeSettings: App.Theme.ThemeSetting;
    visited: Object[];
    /** Framepack form cache */
    formCache_video_framepack: FramepackFormCache;
  }

  interface Local {
    /** The i18n language */
    lang: App.I18n.LangType;
    /** The token */
    token: string;
    /** Fixed sider with mix-menu */
    mixSiderFixed: CommonType.YesOrNo;
    siderCollapse: CommonType.YesOrNo;
    /** The refresh token */
    refreshToken: string;
    /** The theme color */
    themeColor: string;
    /** The theme settings */
    themeSettings: App.Theme.ThemeSetting;
    /**
     * The override theme flags
     *
     * The value is the build time of the project
     */
    overrideThemeFlag: string;
    /** The global tabs */
    globalTabs: App.Global.Tab[];
    /** The backup theme setting before is mobile */
    backupThemeSettingBeforeIsMobile: {
      layout: UnionKey.ThemeLayoutMode;
      siderCollapse: boolean;
    };
  }
}
