<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Progress Bars with SSE</title>
    <style>
      .progress-container {
        margin-bottom: 20px;
      }
      .progress-bar {
        width: 100%;
        background-color: #f3f3f3;
        border-radius: 5px;
        overflow: hidden;
      }
      .progress {
        height: 30px;
        width: 0;
        background-color: #4caf50;
        text-align: center;
        color: white;
        line-height: 30px;
        transition: width 0.1s;
      }
    </style>
  </head>
  <body>
    <div class="progress-container">
      <button onclick="startProgress(1, 'progress1')">扣普通积分类型1-Task 1</button>
      <div class="progress-bar"><div id="progress1" class="progress"></div></div>
    </div>
    <div class="progress-container">
      <button onclick="startProgress(2, 'progress2')">扣普通积分类型1-Task 2</button>
      <div class="progress-bar"><div id="progress2" class="progress"></div></div>
    </div>
    <div class="progress-container">
      <button onclick="startProgress(3, 'progress3')">扣普通积分类型1-Task 3</button>
      <div class="progress-bar"><div id="progress3" class="progress"></div></div>
    </div>
    <div class="progress-container">
      <button id="apiButton">扣积分2类型测试</button>
      <div id="result"></div>
    </div>

    <script>
      document.getElementById('apiButton').addEventListener('click', function () {
        fetch('/proxy-default/volcano/ttype2')
          .then(response => response.json())
          .then(data => {
            document.getElementById('result').innerHTML = JSON.stringify(data);
          })
          .catch(error => {
            console.error('Error:', error);
            document.getElementById('result').innerHTML = '调用API时出错';
          });
      });
      function startProgress(taskId, progressId) {
        const eventSource = new EventSource(`/proxy-default/volcano/ttstest?q=${taskId}`);
        const progressBar = document.getElementById(progressId);

        eventSource.onmessage = function (event) {
          // console.log(event.data)
          // {"progress": 7, "total": 60, "data": {"status": 0, "task": "Mw==", "result": ""}}'
          obj = JSON.parse(event.data);
          // progressBar.style.width = event.data + '%';
          // progressBar.textContent = event.data + '%';
          progressBar.style.width = Math.ceil((obj.progress / obj.total) * 100) + '%';
          progressBar.textContent = Math.ceil((obj.progress / obj.total) * 100) + '%';

          // if (event.data === '100') {
          if (obj.data.status === 1 || obj.progress == obj.total) {
            eventSource.close();
          }
        };
      }
    </script>
  </body>
</html>
