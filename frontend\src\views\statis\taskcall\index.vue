<script setup lang="tsx">
import { ref } from 'vue';
import dayjs from 'dayjs';
import type { DataTableSortState } from 'naive-ui';
import { orderBy } from 'lodash-es';
import { fetchTaskAnalyze } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
// import { $t } from '@/locales';
import TaskSearch from './modules/task-search.vue';
import TaskDetail from './modules/task-detail.vue';

const appStore = useAppStore();

const { columns, data, loading, getData, searchParams, resetSearchParams } = useTable({
  apiFn: fetchTaskAnalyze,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    month: dayjs().format('YYYYMM'),
    date: dayjs().format('YYYYMMDD'),
    start_date: '',
    end_date: '',
    filter_type: 'date',
    company: ''
  },
  columns: () => [
    {
      key: 'api_name',
      title: '功能',
      align: 'center',
      minWidth: 120,
      render(row) {
        return (
          <a class="cursor-pointer underline" onClick={() => handleRowClick(row)}>
            {row.api_name}
          </a>
        );
      }
    },
    // {
    //   key: 'api_path',
    //   title: '接口',
    //   align: 'center',
    //   minWidth: 120,
    //   render(row) {
    //     return (
    //       <a class="cursor-pointer underline" onClick={() => handleRowClick(row)}>
    //         {row.api_path}
    //       </a>
    //     );
    //   }
    // },
    {
      key: 'count_api',
      title: '调用次数',
      minWidth: 120,
      sorter: true
    },
    {
      key: 'count_user',
      title: '调用人数',
      minWidth: 120,
      sorter: true
    },
    {
      key: 'avg_duration',
      title: '平均时间/s',
      minWidth: 120,
      sorter: true
    }
  ]
});
//
const sorter = (options: DataTableSortState) => {
  if (options.order === false || options.order === 'descend') {
    data.value = orderBy(data.value, [options.columnKey], ['desc']);
  } else {
    data.value = orderBy(data.value, [options.columnKey], ['asc']);
  }
};

const show_detail = ref(false);
// const search_api_path = ref<string>('');
const taskDetailRef = ref<any>(null);
function handleRowClick(row: any) {
  show_detail.value = true;
  // search_api_path.value = row.api_path;
  // console.log(search_api_path.value, searchParams);
  taskDetailRef.value?.getTableData(searchParams, row.api_name);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <TaskSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="任务调用统计" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="(row: any) => row.id"
        :on-update:sorter="sorter"
        class="sm:h-full"
      />
    </NCard>
    <TaskDetail ref="taskDetailRef" v-model:show="show_detail" />
  </div>
</template>

<style scoped></style>
