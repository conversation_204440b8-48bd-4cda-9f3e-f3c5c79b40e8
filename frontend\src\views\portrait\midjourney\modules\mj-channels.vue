<script setup lang="ts">
import { h, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { addChannel, getChannel } from '@/service/api/midjourney';

const message = useMessage();

/** 缓存相关 * */
const LOCAL_STORAGE_KEY = 'selectedChannelKey';
const LOCAL_STORAGE_LABEL = 'selectedChannelLabel';

// 从缓存读取上次选中的频道 key、label（没有则默认是 personal-channel / 私人频道）
const defaultSelectedChannelKey = localStorage.getItem(LOCAL_STORAGE_KEY) || 'personal-channel';
const defaultSelectedChannelLabel = localStorage.getItem(LOCAL_STORAGE_LABEL) || '私人频道';

/** 频道数据/菜单等 * */
const channelMap = ref<Record<string, number>>({});
// 这两个状态在页面还未请求到数据时，就先赋值为“缓存值”，避免出现闪烁
const selectedChannelLabel = ref(defaultSelectedChannelLabel);
const selectedChannelKey = ref(defaultSelectedChannelKey);

const options = ref<
  Array<{
    label: string;
    key: string;
    icon: () => any;
    children?: Array<{
      label: string;
      key: string;
      icon: () => any;
    }>;
  }>
>([]);

const isModalVisible = ref(false);
const formData = ref({
  channelName: '',
  permission: false
});

const formRules = {
  channelName: {
    required: true,
    message: '请输入频道名称',
    trigger: 'blur'
  }
};

// 用于向父组件传递事件
const emit = defineEmits<{
  (e: 'channel-selected', channelId: number): void;
  (e: 'initial-channel', channelId: number): void;
}>();

const buildMenuOptions = (data: Record<string, any>) => {
  const publicChannels = data.public_channel || {};
  const teamData = { ...data };
  delete teamData.public_channel;

  const personalMenu = {
    label: '私人频道',
    key: 'personal-channel',
    icon: () => h(SvgIcon, { icon: 'mdi-human-greeting' })
  };

  // 公共频道菜单
  const publicMenu = {
    label: '公共频道',
    key: 'public',
    icon: () => h(SvgIcon, { icon: 'mdi-radio-tower' }),
    children: Object.keys(publicChannels).map(chanName => {
      const key = chanName.toLowerCase().replace(/\s+/g, '-');
      channelMap.value[key] = publicChannels[chanName].channel_id;
      return {
        label: chanName,
        key,
        icon: () => h(SvgIcon, { icon: 'mdi-assistant' })
      };
    })
  };

  // 团队频道菜单
  const teamMenus = Object.keys(teamData).map(teamName => {
    const teamChannels = teamData[teamName] || {};
    const teamKey = `team-${teamName}`;
    return {
      label: teamName,
      key: teamKey,
      icon: () => h(SvgIcon, { icon: 'mdi-theater' }),
      children: Object.keys(teamChannels).map(chanName => {
        const key = `team-${teamName}-${chanName.toLowerCase().replace(/\s+/g, '-')}`;
        channelMap.value[key] = teamChannels[chanName].channel_id;
        return {
          label: chanName,
          key,
          icon: () => h(SvgIcon, { icon: 'mdi-assistant' })
        };
      })
    };
  });

  // const addChannelItem = {
  //   label: '添加频道',
  //   key: 'add-channel',
  //   icon: () => h(SvgIcon, { icon: 'mdi-calendar-plus' })
  // };

  options.value = [
    personalMenu,
    publicMenu,
    ...teamMenus
    // addChannelItem
  ];
};

/** 频道选择 */
function handleSelect(key: string | number, isUserAction = true) {
  // 根据 key 找到对应的 label
  function findLabel(items: typeof options.value, targetKey: string | number): string | null {
    for (const item of items) {
      if (item.key === targetKey) {
        return item.label;
      }
      if (item.children) {
        const childLabel = findLabel(item.children, targetKey);
        if (childLabel) {
          return childLabel;
        }
      }
    }
    return null;
  }

  const label = findLabel(options.value, key) || String(key);

  // 如果点击的是「添加频道」，则弹出添加频道的对话框
  if (key === 'add-channel') {
    isModalVisible.value = true;
    return;
  }

  // 更新选中状态
  selectedChannelKey.value = String(key);
  selectedChannelLabel.value = label;
  localStorage.setItem(LOCAL_STORAGE_KEY, String(key));
  localStorage.setItem(LOCAL_STORAGE_LABEL, label);

  // 如果是手动点击，才提示切换频道；如果是缓存恢复，就不提示
  if (isUserAction) {
    message.info(`切换频道: ${label}`);
  }

  // 如果是私人频道，就给父组件传 0，否则传实际的 channel_id
  if (key === 'personal-channel') {
    emit('channel-selected', 0);
  } else {
    const selectedChannelId = channelMap.value[key as string];
    if (selectedChannelId) {
      emit('channel-selected', selectedChannelId);
    }
  }
}

async function fetchChannelData() {
  try {
    const response = await getChannel();
    if (response.data) {
      buildMenuOptions(response.data);

      const validChannelId = channelMap.value[selectedChannelKey.value];
      if (!validChannelId && selectedChannelKey.value !== 'personal-channel') {
        // 无效时回退到私人频道
        selectedChannelKey.value = 'personal-channel';
        selectedChannelLabel.value = '私人频道';
        localStorage.setItem(LOCAL_STORAGE_KEY, 'personal-channel');
        localStorage.setItem(LOCAL_STORAGE_LABEL, '私人频道');
        emit('initial-channel', 0);
      } else if (selectedChannelKey.value === 'personal-channel') {
        emit('initial-channel', 0);
      } else {
        emit('initial-channel', validChannelId);
      }
    } else {
      message.error('获取频道数据失败');
    }
  } catch (error) {
    message.error('请求频道数据出错');
  }
}

onMounted(() => {
  fetchChannelData();
});

/** 提交添加频道表单 */
const handleSubmit = async () => {
  const channelName = formData.value.channelName.trim();
  if (!channelName) {
    message.error('频道名称不能为空');
    return;
  }
  const permission = formData.value.permission ? 'public' : 'private';
  try {
    const res = await addChannel(channelName, permission);
    if (res.data && res.data.msg === 1) {
      message.success(`频道 "${channelName}" 添加成功`);
      await fetchChannelData();
      isModalVisible.value = false;
      formData.value.channelName = '';
      formData.value.permission = false;
    } else {
      message.error('添加频道失败');
    }
  } catch (error) {
    message.error('请求添加频道出错');
  }
};
</script>

<template>
  <NDropdown :options="options" placement="bottom-start" trigger="click" @select="key => handleSelect(key, true)">
    <NButton type="success">
      <SvgIcon icon="mdi-white-balance-sunny" class="mr-0.5" />
      {{ selectedChannelLabel }}
    </NButton>
  </NDropdown>

  <NModal
    v-model:show="isModalVisible"
    title="添加频道"
    preset="dialog"
    @close="
      () => {
        isModalVisible = false;
        formData.channelName = '';
        formData.permission = false;
      }
    "
  >
    <NCard>
      <NForm ref="formRef" :model="formData" :rules="formRules" label-width="0px">
        <NFormItem label="频道名称" path="channelName">
          <NSpace justify="space-between" align="center">
            <NInput v-model:value="formData.channelName" placeholder="请输入频道名称" class="channel-input" />
            <NCheckbox v-model:checked="formData.permission">公开</NCheckbox>
          </NSpace>
        </NFormItem>

        <NAlert title="提示" type="info" class="mb-3">
          如选择公开频道，频道内容所有人可见。
          <!--
 <br />
          默认仅所属公司成员可见。
-->
        </NAlert>

        <NFormItem class="flex justify-end">
          <NButton
            class="mr-2"
            @click="
              () => {
                isModalVisible = false;
                formData.channelName = '';
                formData.permission = false;
              }
            "
          >
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </NFormItem>
      </NForm>
    </NCard>
  </NModal>
</template>

<style scoped>
:deep(.channel-input) .n-input-wrapper {
  width: 19em;
}
</style>
