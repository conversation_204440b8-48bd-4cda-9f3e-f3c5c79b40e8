<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { debounce } from 'lodash';
import { fetchModelPriceItem } from '@/service/api';
import type { AiCapacity } from '@/utils/AiCapacity';
import { useCreditStore } from '@/store/modules/credit';

const props = defineProps<{
  capacity?: AiCapacity;
  model?: string;
}>();

const creditStore = useCreditStore();

const price = ref<number | null>(null);
const loading = ref(false);

const fetchPrice = debounce(async () => {
  loading.value = true;
  try {
    const res = await fetchModelPriceItem({
      capacity: props.capacity,
      model: props.model
    });
    if (!res.error && res.data) {
      const data: { credit: number } = res.data;
      price.value = data.credit;
    } else {
      price.value = null;
    }
  } catch (e) {
    console.error(e);
    price.value = null;
  } finally {
    loading.value = false;
  }
}, 100);

onMounted(fetchPrice);
watch(() => props.capacity, fetchPrice);
watch(() => props.model, fetchPrice);

const creditEnough = computed(() => {
  if (price.value === null) return true;
  return creditStore.creditInfo.total_credit >= price.value;
});
</script>

<template>
  <NTooltip trigger="hover" :disabled="creditEnough">
    <template #trigger>
      <slot :loading="loading" :credit="price" :credit-enough="creditEnough"></slot>
    </template>
    {{ $t('common.creditNotEnough') }}
  </NTooltip>
</template>
