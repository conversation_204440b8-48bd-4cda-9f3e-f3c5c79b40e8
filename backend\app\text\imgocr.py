import logging
import os
import time
from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from utils.database import get_db
from pydantic import BaseModel
from base64 import b64encode
import httpx
import aiofiles
import urllib

from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

API_KEY = app_settings.baidu_api_key
SECRET_KEY = app_settings.baidu_secret_key


class ExtractRequest(BaseModel):
  file_path: str = ''
  prompt_img_b64: str = ''
  format: str
  model: str = ''


def delete_old_files(directory, hours=3):
  # 计算时间阈值，当前时间减去指定小时数
  time_threshold = time.time() - hours * 3600

  # 遍历目录中的所有文件和子目录
  for root, dirs, files in os.walk(directory):
    for file in files:
      file_path = os.path.join(root, file)
      # 获取文件的最后修改时间
      file_mod_time = os.path.getmtime(file_path)

      # 检查文件的最后修改时间是否早于时间阈值
      if file_mod_time < time_threshold:
        try:
          os.remove(file_path)
          logger.info(f'已删除文件: {file_path}')
        except OSError as e:
          logger.info(f'删除文件时出错: {file_path} - {e}')


async def get_file_content_as_base64(path, urlencoded=False):
  """
  异步获取文件base64编码
  :param path: 文件路径
  :param urlencoded: 是否对结果进行urlencoded
  :return: base64编码信息
  """
  async with aiofiles.open(path, "rb") as f:
    content = b64encode(await f.read()).decode("utf8")
    if urlencoded:
      content = urllib.parse.quote_plus(content)
  return content


async def get_access_token():
  """
  使用 AK，SK 生成鉴权签名（Access Token）
  :return: access_token，或是None(如果错误)
  """
  url = "https://aip.baidubce.com/oauth/2.0/token"
  params = {"grant_type": "client_credentials", "client_id": API_KEY, "client_secret": SECRET_KEY}

  async with httpx.AsyncClient() as client:
    response = await client.post(url, params=params)
    return str(response.json().get("access_token"))


@router.post("/extract_text")
async def extract_text(request: ExtractRequest, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)):
  try:
    logger.info(f"image_url:{request.file_path}")
    # 将传入的文件路径转换为Base64格式
    file_base64 = await get_file_content_as_base64(request.file_path, True)
    if file_base64:
      logger.info(f"Base64 Content (first 30 chars): {file_base64[:30]}")

    # 获取百度OCR的Access Token
    access_token = await get_access_token()
    if not access_token:
      raise ClientVisibleException("获取授权失败")

    # 调用百度OCR API
    ocr_url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic?access_token={access_token}"
    headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json'
    }
    payload = f'image={file_base64}'

    async with httpx.AsyncClient() as client:
      response = await client.post(ocr_url, headers=headers, data=payload)

    # 处理OCR响应
    if response.status_code == 200:
      ocr_result = response.json()
      extracted_text = "\n".join([item['words'] for item in ocr_result.get('words_result', [])])

      # 添加后台任务，识别完成后删除图片
      background_tasks.add_task(delete_old_files, './upload/tmp', 5)

      return {
        "code": "0000",
        "msg": "success",
        "data": {"text": extracted_text}
      }
    else:
      logger.error(f"OCR识别失败: {response.text}")
      raise ClientVisibleException("识别失败，请重试")

  except Exception as e:
    logger.error(f"extract_text error: {str(e)}")
    raise ClientVisibleException("识别失败，请重试") from e
