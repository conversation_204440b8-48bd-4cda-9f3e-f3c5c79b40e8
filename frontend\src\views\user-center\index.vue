<script lang="ts" setup>
import { computed, ref } from 'vue';
import BasicSetting from './modules/basic-setting.vue';
import SafetySetting from './modules/safety-setting.vue';

type TabItem = {
  key: string;
  name: string;
  desc: string;
};
const typeTabList: TabItem[] = [
  {
    name: '基本设置',
    desc: '个人账户信息设置',
    key: 'BasicSetting'
  },
  {
    name: '安全设置',
    desc: '密码，第三方等设置',
    key: 'SafetySetting'
  }
];
const typeTitle = ref(typeTabList[0].name);
const tab = ref(typeTabList[0].key);
const activeTab = computed(
  () =>
    ({
      BasicSetting,
      SafetySetting
    })[tab.value]
);
const switchType = (e: TabItem) => {
  typeTitle.value = e.name;
  tab.value = e.key;
};
</script>

<template>
  <NGrid :x-gap="20" class="h-full">
    <NGridItem span="6">
      <NCard :bordered="false" size="small" class="proCard h-full p-5">
        <NThing
          v-for="item in typeTabList"
          :key="item.key"
          class="thing-cell"
          :class="{ 'thing-cell-on': tab === item.key }"
          @click="switchType(item)"
        >
          <template #header>{{ item.name }}</template>
          <template #description>{{ item.desc }}</template>
        </NThing>
      </NCard>
    </NGridItem>
    <NGridItem span="18">
      <NCard :bordered="false" :title="typeTitle" class="proCard h-full p-5">
        <component :is="activeTab" />
      </NCard>
    </NGridItem>
  </NGrid>
</template>

<style lang="scss" scoped>
.thing-cell {
  margin: 0 -16px 10px;
  padding: 5px 16px;

  &:hover {
    // background: #f3f3f3;
    cursor: pointer;
  }
}

.thing-cell-on {
  // background: #f0faff;
  // color: #2d8cf0;

  :deep(.n-thing-main .n-thing-header .n-thing-header__title) {
    color: #2d8cf0;
  }
  :deep(.n-thing-main .n-thing-main__description) {
    color: #2d8cf0;
  }

  // &:hover {
  //   background: #f0faff;
  // }
}
</style>
