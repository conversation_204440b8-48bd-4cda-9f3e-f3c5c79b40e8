import asyncio
import logging
import os
import json
import uuid

from fastapi import APIRouter, Depends, BackgroundTasks
from pydantic import BaseModel
import httpx
from base64 import b64encode
from typing import Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession

from models.assets import AssetType
from models.users import User, get_request_user
from config import app_settings
from service.file import save_audio
from utils.asset_storage import store_asset_directly
from utils.database import get_db
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

AI_SERVER = app_settings.ai_server


class CosyRequest(BaseModel):
    text: str
    speaker: str
    new: Optional[int] = None
    speed: Optional[float] = None
    streaming: Optional[int] = None
    seed: Optional[int] = None


class ZeroShotRequest(BaseModel):
    text: str
    prompt_audio: str
    prompt_audio_b64: str
    prompt_audio_start: float = 0
    prompt_audio_end: float = 0
    speed: float = 1
    prompt_text: str
    save_name: str = ''
    seed: Optional[int] = None
    instruct: str = ''


@router.get("/speaker", tags=["cosy"])
async def get_speaker(
        user: User = Depends(get_request_user)
):
    async with httpx.AsyncClient(timeout=300) as client:
        try:
            # 添加 username 参数到请求 URL
            params = {"username": user.username}
            response = await client.get(
                url=f'{AI_SERVER}/cosyvoice/speaker',
                params=params
            )
            response.raise_for_status()
            return {"code": "0000", "data": response.json()}
        except httpx.TimeoutException as e:
            # 处理超时异常
            raise ClientVisibleException("生成时间过长，请重试") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("请求失败，请重试") from e


@router.post("/task", tags=["cosy"])
async def task(
        req: CosyRequest,
        background_task: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    data = {
        "text": req.text,
        "speaker": req.speaker,
        "new": req.new,
        "speed": req.speed,
        "streaming": 0,
        "username": user.username
    }

    if req.seed is not None:
        data["seed"] = req.seed

    logger.info(f"Request data: {data}")
    async with httpx.AsyncClient(timeout=300) as client:
        try:
            response = await client.post(
                url=f'{AI_SERVER}/cosyvoice/task',
                headers={
                    "Content-Type": "application/json"
                },
                json=data
            )
            response.raise_for_status()

            logger.info(f"Response: {response.content}")

            # 解析响应JSON
            response_data = json.loads(response.content)
            # 直接使用返回的URL
            url = response_data.get("b64", "")
            response_type = response_data.get("type", "data:audio/wav;")

            background_task.add_task(
                store_asset_directly,
                db=db,
                asset_type=AssetType.AUDIO,
                url=url,
                user_id=user.id,
                biz_id='audio_mimic_pretrained',
                parameter=data,
            )
            return {"code": "0000", "data": {"b64": url, "type": response_type}}


        except httpx.TimeoutException as e:
            # 处理超时异常
            raise ClientVisibleException("生成时间过长，请重试") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("请求失败，请重试") from e


async def save_zero_shot_asset(
        db: AsyncSession,
        url: str,
        user_id: int,
        parameter: Dict[str, Any] = None,
):
    parameter = parameter.copy() if parameter else {}
    prompt_wav_base64 = parameter.pop("prompt_wav_base64", "")
    prompt_wav_url = ""
    if prompt_wav_base64:
        prompt_wav_url = await asyncio.to_thread(save_audio, prompt_wav_base64, f'prompt_wav_{user_id}_{uuid.uuid4()}.mp3')
    parameter["prompt_wav_url"] = prompt_wav_url
    await store_asset_directly(
        db=db,
        asset_type=AssetType.AUDIO,
        url=url,
        user_id=user_id,
        biz_id='audio_mimic_fast',
        parameter=parameter,
    )


@router.post("/zero_shot", tags=["cosy"])
async def zero_shot(
        req: ZeroShotRequest,
        background_task: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    data = {
        "text": req.text,
        "prompt_wav_base64": req.prompt_audio_b64,
        "prompt_text": req.prompt_text,
        "save_name": req.save_name,
        "speed": req.speed,
        "streaming": 0,
        "instruct_text": req.instruct,
        "username": user.username
    }

    if req.seed is not None:
        data["seed"] = req.seed
    async with httpx.AsyncClient(timeout=300) as client:

        if req.prompt_audio != '':
            if not os.path.exists(req.prompt_audio):
                raise ClientVisibleException("音频文件不存在")

            with open(req.prompt_audio, "rb") as audio_file:
                logger.info(f"读取音频文件: {req.prompt_audio}")
                audio_base64 = b64encode(audio_file.read()).decode('utf-8')
                logger.info(f"音频文件base64: {audio_base64}")
                data['prompt_wav_base64'] = audio_base64

        elif req.prompt_audio_b64:
            _, base64_str = req.prompt_audio_b64.split(',', 1)
            data['prompt_wav_base64'] = base64_str
            logger.info(f"音频文件base64: {base64_str}")
        else:
            raise ClientVisibleException("未上传音频文件")

        try:
            response = await client.post(
                url=f'{AI_SERVER}/cosyvoice/zero_shot',
                headers={
                    "Content-Type": "application/json"
                },
                json=data
            )
            response.raise_for_status()

            logger.info(f"Response: {response.content}")

            # 解析响应JSON
            response_data = json.loads(response.content)
            # 直接使用返回的URL
            url = response_data.get("b64", "")
            response_type = response_data.get("type", "data:audio/wav;")

            background_task.add_task(
                save_zero_shot_asset,
                db=db,
                url=url,
                user_id=user.id,
                parameter=data,
            )
            return {"code": "0000", "data": {"b64": url, "type": response_type}}
        except httpx.TimeoutException as e:
            # 处理超时异常
            raise ClientVisibleException("生成超时，请重试") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("请求失败，请重试") from e
