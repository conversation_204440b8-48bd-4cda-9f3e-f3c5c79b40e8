import type { Directive } from 'vue';
import { createApp, h } from 'vue';
import { NSpin } from 'naive-ui';

const loading: Directive = {
  mounted(el, binding) {
    const app = createApp({
      render() {
        return h(NSpin);
      }
    });

    const instance = app.mount(document.createElement('div'));

    if (binding.value) {
      el.appendChild(instance.$el);
      el.loadingInstance = instance;
    }
  },
  updated(el, binding) {
    if (binding.value) {
      if (!el.loadingInstance) {
        const app = createApp({
          render() {
            return h(NSpin);
          }
        });
        const instance = app.mount(document.createElement('div'));
        el.appendChild(instance.$el);
        el.loadingInstance = instance;
      }
    } else if (el.loadingInstance) {
      el.removeChild(el.loadingInstance.$el);
      el.loadingInstance.unmount();
      delete el.loadingInstance;
    }
  }
  // unmounted(el) {
  //   if (el.loadingInstance) {
  //     el.removeChild(el.loadingInstance.$el);
  //     el.loadingInstance.unmount();
  //     delete el.loadingInstance;
  //   }
  // }
};

export default loading;
