import Crypto from 'crypto-js';

type SignResult = {
  unixTimestamp: number;
  signature: string;
};

// 将数字转换成字节数据，按小端序排列
function numberToBytes(timestamp: number): Uint8Array {
  const buffer = new ArrayBuffer(8);
  const view = new DataView(buffer);

  view.setBigUint64(0, BigInt(timestamp), true);

  // Convert to Uint8Array for easier handling
  return new Uint8Array(buffer);
}

export const getSignature = (): SignResult => {
  const timestamp = Date.now();
  const unixTimestamp = Math.floor(timestamp / 1000);
  const bytes = numberToBytes(unixTimestamp);
  const words = Crypto.lib.WordArray.create(bytes);
  const key = import.meta.env.VITE_SIGN_KEY;
  if (!key) {
    throw new Error('SIGN_KEY is not defined');
  }
  const value = Crypto.HmacMD5(words, key);
  return {
    unixTimestamp,
    signature: value.toString()
  };
};
