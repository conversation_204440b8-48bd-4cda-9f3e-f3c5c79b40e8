<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { getMjModels } from '@/service/api/midjourney';

// 定义localStorage的key
const PLATFORM_STORAGE_KEY = 'mj_selected_platform';
const MODEL_STORAGE_KEY = 'mj_selected_model';

// 定义类型
interface ModelOption {
  value: string;
  label: string;
}

interface PlatformModel {
  label: string;
  value: string;
  models: ModelOption[];
}

// 定义props和emits
const props = defineProps<{
  modelValue?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'platformChange', platform: string): void;
}>();

// 状态管理
const loading = ref(false);
const platformModels = ref<PlatformModel[]>([]);

// 平台选择 - 默认选择midjourney
const platform = ref('midjourney');

// 根据平台获取对应的模型列表
const getModelsByPlatform = () => {
  const currentPlatform = platformModels.value.find(p => p.value.toLowerCase() === platform.value.toLowerCase());
  return currentPlatform?.models || [];
};

// 当前显示的模型列表
const currentModels = ref<ModelOption[]>([]);

// 获取存储的模型或默认模型 - 针对当前平台
const getSavedOrDefaultModel = () => {
  // 如果有props.modelValue，优先使用，但需要验证是否在当前平台的模型列表中
  if (props.modelValue && currentModels.value.length > 0) {
    const isValidModel = currentModels.value.some(model => model.value === props.modelValue);
    if (isValidModel) {
      return props.modelValue;
    }
  }

  // 尝试从localStorage获取模型，但需要针对当前平台进行验证
  const savedModel = localStorage.getItem(`${MODEL_STORAGE_KEY}_${platform.value.toLowerCase()}`);
  if (savedModel && currentModels.value.length > 0) {
    // 验证保存的模型是否在当前平台的模型列表中
    const isValidModel = currentModels.value.some(model => model.value === savedModel);
    if (isValidModel) {
      return savedModel;
    }
  }

  // 如果没有有效的保存模型，使用当前平台的第一个模型
  return currentModels.value[0]?.value || '';
};

// 当前选择的模型
const selectedModel = ref('');

// 更新当前平台的模型列表
const updateCurrentModels = () => {
  currentModels.value = getModelsByPlatform();

  // 获取当前平台应该选中的模型
  const newSelectedModel = getSavedOrDefaultModel();

  // 强制更新选中的模型（避免跨平台值混乱）
  selectedModel.value = newSelectedModel;

  // 立即保存到对应平台的localStorage
  if (newSelectedModel) {
    localStorage.setItem(`${MODEL_STORAGE_KEY}_${platform.value.toLowerCase()}`, newSelectedModel);
  }
};

// 初始化平台选择
const initializePlatform = () => {
  // 优先从localStorage读取，如果没有则使用默认值'midjourney'
  const savedPlatform = localStorage.getItem(PLATFORM_STORAGE_KEY);
  if (savedPlatform) {
    // 验证保存的平台是否在可用平台列表中
    const isValidPlatform = platformModels.value.some(p => p.value.toLowerCase() === savedPlatform.toLowerCase());
    if (isValidPlatform) {
      platform.value = savedPlatform;
    }
  }

  // 如果没有找到有效的保存平台，默认选择midjourney
  if (!platform.value || platform.value === '') {
    platform.value = 'midjourney';
  }
};

// 加载模型数据
const loadModels = async () => {
  try {
    loading.value = true;
    const response = await getMjModels();
    if (response.data && response.data.models) {
      platformModels.value = response.data.models;

      // 初始化平台选择
      initializePlatform();

      // 更新模型列表
      updateCurrentModels();

      // 触发初始事件
      emit('update:modelValue', selectedModel.value);
      emit('platformChange', platform.value);
    }
  } catch (error) {
    console.error('加载模型列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 监听平台变化，更新模型列表和选中的模型，并保存到localStorage
watch(
  platform,
  newPlatform => {
    // 保存平台选择到localStorage
    localStorage.setItem(PLATFORM_STORAGE_KEY, newPlatform);

    // 更新当前平台的模型列表和选中模型
    updateCurrentModels();

    // 触发事件通知父组件
    emit('update:modelValue', selectedModel.value);
    emit('platformChange', newPlatform);
  },
  { immediate: false }
);

// 监听选中模型变化，向父组件发送事件并保存到localStorage
watch(selectedModel, newValue => {
  if (newValue) {
    // 按平台分别保存模型选择
    localStorage.setItem(`${MODEL_STORAGE_KEY}_${platform.value.toLowerCase()}`, newValue);
    emit('update:modelValue', newValue);
  }
});

// 处理模型选择变化
const handleModelChange = (value: string) => {
  selectedModel.value = value;
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadModels();
});
</script>

<template>
  <NFlex class="w-full" vertical :size="12">
    <NSpin :show="loading">
      <NRadioGroup v-model:value="platform" name="platform-select">
        <NRadioButton
          v-for="platformModel in platformModels"
          :key="platformModel.value"
          :value="platformModel.value.toLowerCase()"
        >
          {{ platformModel.label }}
        </NRadioButton>
      </NRadioGroup>

      <NSelect
        v-model:value="selectedModel"
        class="w-full"
        :options="currentModels"
        :disabled="loading || currentModels.length === 0"
        placeholder="请选择模型"
        @update:value="handleModelChange"
      />
    </NSpin>
  </NFlex>
</template>

<style scoped>
.n-radio-group {
  margin-bottom: 12px;
}
</style>
