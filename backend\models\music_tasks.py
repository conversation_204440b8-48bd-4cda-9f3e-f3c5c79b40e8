from utils.database import Base
from sqlalchemy import Column, Integer, String, Enum, DateTime, Text, JSON
from pydantic import ConfigDict
from models.pydanticBase import BaseModel
from enum import Enum as EnumType
from datetime import datetime
from typing import Optional


class MusicTasksStatus(str, EnumType):
    """音乐任务状态枚举"""
    NOT_START = "NOT_START"
    SUBMITTED = "SUBMITTED"
    IN_PROGRESS = "IN_PROGRESS"
    FAILURE = "FAILURE"
    SUCCESS = "SUCCESS"


class MusicTasksAction(str, EnumType):
    """音乐任务操作类型枚举"""
    GENERATE = "GENERATE"
    CONTINUE = "CONTINUE"
    REMIX = "REMIX"
    # 可根据实际需求添加更多操作类型


class MusicTasksOut(BaseModel):
    """
    音乐任务输出模型
    """
    id: int
    pid: Optional[int] = None
    username: str
    taskid: Optional[str] = None
    music_type: Optional[str] = None
    status: Optional[str] = None
    prompt: Optional[str] = None
    prompt_en: Optional[str] = None
    state: Optional[str] = None
    submit_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    finish_time: Optional[datetime] = None
    audio_url: Optional[str] = None
    progress: Optional[str] = None
    fail_reason: Optional[str] = None
    uptime: Optional[datetime] = None
    queue_position: Optional[int] = None
    action: Optional[str] = None
    music_title: str
    model_config = ConfigDict(from_attributes=True)


class MusicTasks(Base):
    """音乐任务数据库模型"""
    __tablename__ = "music_tasks"
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    pid = Column(Integer, nullable=True, comment="父任务ID")
    username = Column(String(255), nullable=False, index=True, comment="用户名")
    taskid = Column(String(255), nullable=True, index=True, comment="任务ID")
    music_type = Column(String(24), nullable=True, comment="音乐类型")
    status = Column(Enum(MusicTasksStatus), nullable=True, comment="任务状态")
    prompt = Column(Text, nullable=True, comment="提示词")
    prompt_en = Column(Text, nullable=True, comment="英文提示词")
    state = Column(String(255), nullable=True, comment="状态信息")
    submit_time = Column(DateTime, nullable=True, comment="提交时间")
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    finish_time = Column(DateTime, nullable=True, comment="完成时间")
    audio_url = Column(Text, nullable=True, comment="音频URL")
    progress = Column(String(255), nullable=True, comment="进度信息")
    fail_reason = Column(Text, nullable=True, comment="失败原因")
    uptime = Column(DateTime, nullable=True, comment="更新时间")
    queue_position = Column(Integer, nullable=True, comment="队列位置")
    action = Column(String(24), nullable=True, comment="操作类型")
    music_title = Column(String(24), nullable=False, comment="音乐标题")
    # task_params = Column(JSON, nullable=True, comment="任务参数")
    # task_result = Column(JSON, nullable=True, comment="任务结果")
    
    def __repr__(self):
        return f"<MusicTasks(id={self.id}, username={self.username}, taskid={self.taskid}, status={self.status})>"
