import logging
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path

from config import app_settings

LOG_DIR = Path('.') / "logs"


def setup_logging():
    LOG_DIR.mkdir(parents=True, exist_ok=True)
    root = logging.root
    common_handler = TimedRotatingFileHandler(
        LOG_DIR / "app.log",
        when="midnight",
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    common_handler.suffix = "%Y%m%d"
    error_handler = TimedRotatingFileHandler(
        LOG_DIR / "app_error.log",
        when="midnight",
        interval=1,
        backupCount=30,
        encoding='utf-8',
    )
    error_handler.setLevel(logging.ERROR)
    formatter = logging.Formatter("%(asctime)s [%(levelname)s]\t%(name)s: %(message)s")
    common_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)
    root.addHandler(common_handler)
    root.addHandler(error_handler)
    if app_settings.debug:
        # 添加控制台输出
        console = logging.StreamHandler()
        console.setLevel(logging.DEBUG)
        console.setFormatter(formatter)
        root.addHandler(console)
        root.setLevel(logging.DEBUG)
    else:
        root.setLevel(logging.INFO)
