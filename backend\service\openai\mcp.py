import logging
from functools import lru_cache

from fastmcp import Client
from fastmcp.utilities.mcp_config import MCPConfig, RemoteMCPServer

from config import app_settings


logger = logging.getLogger(__name__)

@lru_cache(maxsize=1)
def _get_map_client(url: str):
    """如果 url 变化，LRU Cache 就会更新"""
    config = MCPConfig(mcpServers={
        # 尽量使用 supergateway 和 fastmcp 将 MCP 的服务器统一管理，不要放在这里面
        "default": RemoteMCPServer(url=url, transport='sse'),
    })
    logger.info("Create a new mcp client.")
    return Client(config)  # type: ignore


def get_map_client():
    return _get_map_client(app_settings.mcp_server)


async def list_tools():
    async with get_map_client() as client:
        logger.debug('list_tools')
        return await client.list_tools()


async def call_tool(*args, **kwargs):
    async with get_map_client() as client:
        logger.debug(f'call_tool: {args}')
        return await client.call_tool(*args, **kwargs)
