import Compressor from 'compressorjs';
import Viewer from 'viewerjs';

export function getCurrentDate() {
  const date = new Date();
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  return `${year}-${month}-${day}`;
}

export function thumbUrl(src: string, w = 300, h = 300): string {
  if (import.meta.env.VITE_OSS_HOST && src.indexOf(import.meta.env.VITE_OSS_HOST) === 0) {
    return `${src}?imageMogr2/thumbnail/${w}x`;
  }

  const thumbApi = import.meta.env.VITE_APP_THUMB_API;
  return `${thumbApi}?src=${encodeURIComponent(src)}&w=${w}&h=${h}`;
}

export function getFileBase64(file: File | Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

/* eslint-disable no-new */
export function getImageData(file: File, maxWidth = 1024) {
  return new Promise(resolve => {
    new Compressor(file, {
      maxWidth,
      success(result: any) {
        getFileBase64(result).then(base64 => {
          resolve(base64);
        });
      },
      error(error: any) {
        console.log(error);
        getFileBase64(file).then(base64 => {
          resolve(base64);
        });
      }
    });
  });
}

export function viewImage(image: HTMLImageElement | string) {
  let imgElement: HTMLImageElement;

  if (typeof image === 'string') {
    const url = image;
    imgElement = new Image();
    imgElement.src = url;
  } else {
    imgElement = image;
  }

  const viewer = new Viewer(imgElement, {
    inline: false,
    transition: false,
    // container: '#app',
    toolbar: {
      zoomIn: 1,
      zoomOut: 1,
      oneToOne: 1,
      reset: 1,
      rotateLeft: 1,
      rotateRight: 1,
      flipHorizontal: 1,
      flipVertical: 1
    },
    hidden() {
      viewer.destroy();
    }
  });

  viewer.show();
  return viewer;
}

export function copyText(text: string): Promise<void> {
  return navigator.clipboard.writeText(text);
}
