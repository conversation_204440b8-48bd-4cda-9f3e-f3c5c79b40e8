<script setup lang="tsx">
import { NButton, NCard, NDataTable, NEmpty, NFlex, NSelect, NTag, NTooltip } from 'naive-ui';
import { h, onMounted, ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import SvgIcon from '@/components/custom/svg-icon.vue';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';

interface TimbreData {
  id: number;
  name: string;
  gender: string;
  language: string;
  description: string;
  addTime: string;
  isCollect: boolean;
  audioUrl: string;
}

interface Props {
  fetchData: (filters?: { gender?: string; language?: string }) => Promise<any>;
  loading?: boolean;
  currentPlayingId?: number | null;
}

interface Emits {
  (e: 'toggle-collect', timbre: TimbreData): void;
  (e: 'preview', timbre: TimbreData): void;
  (e: 'edit', timbre: TimbreData): void;
  (e: 'delete', timbre: TimbreData): void;
  (e: 'add'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentPlayingId: null
});

const emit = defineEmits<Emits>();

const appStore = useAppStore();
const hoveredRowId = ref<number | null>(null);

// gender/language筛选相关ref和watch，必须在getData定义前
const genderOptions = [
  { label: '女声', value: '0' },
  { label: '男声', value: '1' }
];
const languageOptions = [
  { label: '中文-普通话', value: '中文-普通话' },
  { label: '中文-粤语', value: '中文-粤语' },
  { label: '英语', value: '英语' },
  { label: '日语', value: '日语' },
  { label: '韩语', value: '韩语' }
];

const selectedGender = ref<string | null>(null);
const selectedLanguage = ref<string | null>(null);

const { columns, columnChecks, data, loading, getData, mobilePagination } = useTable({
  apiFn: async () => {
    try {
      // 构建筛选参数
      const filters: { gender?: string; language?: string } = {};

      if (selectedGender.value) {
        // 将选项值转换为显示文本
        const genderMap: Record<string, string> = {
          '0': '女声',
          '1': '男声'
        };
        filters.gender = genderMap[selectedGender.value];
      }

      if (selectedLanguage.value) {
        // 直接传递语言代码（枚举键），不需要转换
        filters.language = selectedLanguage.value;
      }

      // 调用父组件的fetchData函数并传递筛选参数
      const response = await props.fetchData(filters);

      return {
        error: null,
        data: response?.data || {
          records: [],
          total: 0,
          size: 10,
          current: 1
        }
      };
    } catch (error: any) {
      return {
        error,
        data: {
          records: [],
          total: 0,
          size: 10,
          current: 1
        }
      };
    }
  },
  showTotal: true,
  columns: () =>
    [
      {
        key: 'name',
        title: '音色名称',
        align: 'center',
        minWidth: 180,
        width: 200,
        ellipsis: {
          tooltip: true
        }
      },
      {
        key: 'gender',
        title: '音色性别',
        align: 'center',
        width: 100,
        render: (row: TimbreData) => {
          if (!row.gender) return '-';
          const type = row.gender === '男声' ? 'info' : 'warning';
          return h(NTag, { type, size: 'small' }, { default: () => row.gender });
        }
      },
      {
        key: 'language',
        title: '音色语言',
        align: 'center',
        width: 120,
        render: (row: TimbreData) => {
          return row.language || '-';
        }
      },
      {
        key: 'description',
        title: '音色描述',
        align: 'center',
        minWidth: 200,
        width: 300,
        ellipsis: {
          tooltip: true
        },
        render: (row: TimbreData) => {
          return h(
            NTooltip,
            {
              placement: 'top',
              style: { 'max-width': '350px', 'text-align': 'center', 'white-space': 'pre-line' }
            },
            {
              trigger: () =>
                h(
                  'span',
                  null,
                  row.description ? row.description.slice(0, 20) + (row.description.length > 20 ? '...' : '') : '-'
                ),
              default: () => row.description || '-'
            }
          );
        }
      },
      {
        key: 'addTime',
        title: '添加日期',
        align: 'center',
        width: 150,
        render: (row: TimbreData) => {
          // 将 'T' 替换为空格
          return row.addTime ? row.addTime.replace('T', ' ') : '-';
        }
      },
      {
        key: 'collect',
        title: '收藏状态',
        align: 'center',
        width: 100,
        render: (row: TimbreData) => {
          return h(
            'div',
            {
              class: 'collect-container',
              onMouseenter: () => handleRowHover(row.id),
              onMouseleave: () => handleRowLeave()
            },
            [
              h(
                NButton,
                {
                  text: true,
                  class: [
                    'collect-button',
                    {
                      'always-show': row.isCollect,
                      'hover-show': hoveredRowId.value === row.id
                    }
                  ],
                  onClick: (e: MouseEvent) => handleToggleCollect(row, e)
                },
                {
                  default: () =>
                    h(SvgIcon, {
                      icon: row.isCollect ? 'fluent:star-32-filled' : 'fluent:star-32-regular',
                      class: ['star-icon', { filled: row.isCollect }]
                    })
                }
              )
            ]
          );
        }
      },
      {
        key: 'actions',
        title: '操作',
        align: 'center',
        width: 180,
        render: (row: TimbreData) => {
          // 判断当前行是否正在播放
          const isPlaying = props.currentPlayingId === row.id;

          return h('div', { class: 'action-buttons' }, [
            h(
              NTooltip,
              { trigger: 'hover' },
              {
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: isPlaying ? 'warning' : 'primary',
                      ghost: true,
                      onClick: () => handlePreview(row)
                    },
                    {
                      icon: () =>
                        h(SvgIcon, {
                          icon: isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'
                        }),
                      default: () => (isPlaying ? '暂停' : '播放')
                    }
                  ),
                default: () => (isPlaying ? '暂停音色播放' : '播放音色预览')
              }
            ),
            h(
              NTooltip,
              { trigger: 'hover' },
              {
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: 'warning',
                      ghost: true,
                      style: { marginLeft: '8px' },
                      onClick: () => handleEdit(row)
                    },
                    { default: () => '编辑' }
                  ),
                default: () => '编辑音色信息'
              }
            ),
            h(
              NTooltip,
              { trigger: 'hover' },
              {
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: 'error',
                      ghost: true,
                      style: { marginLeft: '8px' },
                      onClick: () => handleDelete(row)
                    },
                    { default: () => '删除' }
                  ),
                default: () => '删除音色'
              }
            )
          ]);
        }
      }
    ] as any
});

watch([selectedGender, selectedLanguage], () => {
  getData();
});

// 处理鼠标悬停
function handleRowHover(id: number) {
  hoveredRowId.value = id;
}

function handleRowLeave() {
  hoveredRowId.value = null;
}

// 处理收藏切换
function handleToggleCollect(timbre: TimbreData, e: MouseEvent) {
  e.stopPropagation();
  emit('toggle-collect', timbre);
}

// 处理预览
function handlePreview(timbre: TimbreData) {
  emit('preview', timbre);
}

// 处理编辑
function handleEdit(timbre: TimbreData) {
  emit('edit', timbre);
}

// 处理删除
function handleDelete(timbre: TimbreData) {
  emit('delete', timbre);
}

// 处理新增
function handleAdd() {
  emit('add');
}

// 刷新数据
function refreshData() {
  getData();
}

// 初始化时加载数据
onMounted(() => {
  getData();
});

// 暴露方法给父组件
defineExpose({
  refreshData
});
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper" style="overflow-x: auto">
    <template #header>
      <!--
 <div class="flex items-center">
        <SvgIcon icon="mdi:music-note" class="mr-2 text-lg" />
        <span class="text-lg font-medium">音色库管理</span>
      </div>
-->
      <NFlex class="w-100">
        <NSelect
          v-model:value="selectedGender"
          class="flex-1"
          placeholder="请选择音色性别"
          :options="genderOptions"
          clearable
        />
        <NSelect
          v-model:value="selectedLanguage"
          class="flex-1"
          placeholder="请选择语言"
          :options="languageOptions"
          clearable
        />
      </NFlex>
    </template>

    <template #header-extra>
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :loading="loading || props.loading"
        @add="handleAdd"
        @refresh="refreshData"
      />
    </template>

    <NDataTable
      :columns="columns"
      :data="data"
      size="small"
      :flex-height="!appStore.isMobile"
      :scroll-x="1500"
      :loading="loading || props.loading"
      remote
      :row-key="(row: TimbreData) => row.id"
      :pagination="mobilePagination"
      class="table-container"
      style="min-width: 1200px"
    >
      <template #empty>
        <NEmpty description="暂无音色数据" />
      </template>
    </NDataTable>
  </NCard>
</template>

<style scoped lang="scss">
.card-wrapper {
  height: 93%;
}

.table-container {
  height: calc(100vh - 20em);
}

.collect-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.collect-button {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  opacity: 0.3;

  &.always-show {
    opacity: 1;
  }

  &.hover-show {
    opacity: 0.8;
  }

  &:hover {
    opacity: 1;
    background-color: rgba(24, 160, 88, 0.1);
  }

  .star-icon {
    font-size: 16px;
    color: #d0d0d0;
    transition: color 0.3s ease;

    &.filled {
      color: #f0a020;
    }
  }
}

:deep(.action-buttons) {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  gap: 8px;
}
</style>
