<script lang="ts" setup>
import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';
import type { VNodeChild } from 'vue';
import type { SelectGroupOption, SelectOption } from 'naive-ui';
import { useDialog, useMessage } from 'naive-ui';
import { useScreenDetection } from '@/utils/detectionScreen';
import SvgIcon from '@/components/custom/svg-icon.vue';
import {
  Lang,
  ModelFunction,
  type ModifyToneReq,
  type PretrainedToneCategory,
  type SynthesisTone,
  ToneType,
  changeToneFavoriteStatus,
  deleteTone,
  getPretrainedToneCategories,
  getSynthesisTones,
  updateTone
} from '@/service/api/audio';
// import TimbresOperate from '../../timbres/modules/timbres-operate.vue';
import TimbresImport from './timbres-import.vue';

// 定义 props
const props = defineProps<{
  currentPlayingToneId?: string | null;
}>();

// 定义 emit 事件
const emit = defineEmits<{
  'hide-model-select': [];
  'tone-selected': [tone: SynthesisTone];
  'play-audio': [tone: SynthesisTone];
}>();

// 扩展 SelectOption 类型以包含 icon 属性
interface ActionSelectOption extends SelectOption {
  icon?: string;
}

// 使用复用的屏幕检测函数
const { isLargeScreen } = useScreenDetection(1500);

// 使用消息提示和对话框
const message = useMessage();
const dialog = useDialog();

// 动态计算弹窗宽度
const popSelectWidth = computed(() => {
  return isLargeScreen.value ? '37.5em' : '26.5em';
});

const mySynthesis = ref<string | null>(null);
const templateSynthesis = ref<string | null>(null);

// 音色列表相关状态
const tonesList = ref<SynthesisTone[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalTones = ref(0);
const loading = ref(false);
const hasMore = ref(true);
const listTitle = ref('最近使用');

// 搜索相关状态
const searchText = ref('');

// 鼠标悬停状态
const hoveredToneId = ref<string | null>(null);

// 音频播放状态管理（由父组件统一管理，这里只需要接收播放状态）
const playingToneId = computed(() => props.currentPlayingToneId);

// 收藏状态加载管理
const favoriteLoadingTones = ref<Set<string>>(new Set());

// 编辑模态框状态
const editModalVisible = ref(false);
const editingTone = ref<SynthesisTone | null>(null);
const editLoading = ref(false);

// 编辑表单相关
const editFormRef = ref();
const editFormModel = ref({
  name: '',
  gender: null as 0 | 1 | null,
  lang: null as Lang | null,
  description: ''
});

// 编辑表单验证规则
const editFormRules = {
  name: {
    required: true,
    message: '请输入音色名称',
    trigger: 'blur'
  }
};

// 性别选项
const genderOptions = [
  { label: '女声', value: 0 },
  { label: '男声', value: 1 }
];

// 语言选项
const languageOptions = [
  { label: '中文-普通话', value: Lang.CMN },
  { label: '中文-粤语', value: Lang.YUE },
  { label: '英文', value: Lang.ENG },
  { label: '日文', value: Lang.JPN },
  { label: '韩语', value: Lang.KOR }
];

// 将字符串语言转换为Lang枚举的辅助函数
const mapStringToLang = (langStr: string | null): Lang | null => {
  if (!langStr) return null;

  const langMap: Record<string, Lang> = {
    'zh-CN': Lang.CMN,
    'zh-HK': Lang.YUE,
    'en-US': Lang.ENG,
    'ja-JP': Lang.JPN,
    'ko-KR': Lang.KOR,
    cmn: Lang.CMN,
    yue: Lang.YUE,
    eng: Lang.ENG,
    jpn: Lang.JPN,
    kor: Lang.KOR
  };

  return langMap[langStr] || null;
};

const mySynthesisOptions = [
  { label: '全部音色', value: 'all' },
  { label: '最近使用', value: 'recentlyUsed' },
  { label: '我的收藏', value: 'myCollection' }
];

// 响应式数据存储API响应
const toneCategories = ref<PretrainedToneCategory[]>([]);

// 构建查询参数的辅助函数
const buildQueryParams = () => {
  const params: any = {
    current: currentPage.value,
    size: pageSize.value
  };

  // 添加搜索参数（如果有搜索文本）
  if (searchText.value.trim()) {
    params.search_text = encodeURIComponent(searchText.value.trim());
    listTitle.value = `搜索结果: "${searchText.value.trim()}"`;
    return params;
  }

  // 根据选择的选项构建查询参数
  if (mySynthesis.value) {
    params.tone_type = 'custom';

    if (mySynthesis.value === 'recentlyUsed') {
      params.is_recent = true;
      listTitle.value = '最近使用';
    } else if (mySynthesis.value === 'myCollection') {
      params.is_favorite = true;
      listTitle.value = '我的收藏';
    } else {
      listTitle.value = '全部音色';
    }
  } else if (templateSynthesis.value) {
    params.tone_type = 'pretrained';

    if (templateSynthesis.value === 'templateRecentlyUsed') {
      params.is_recent = true;
      listTitle.value = '最近使用';
    } else if (templateSynthesis.value === 'templateMyCollection') {
      params.is_favorite = true;
      listTitle.value = '我的收藏';
    } else {
      params.category = templateSynthesis.value;
      // 根据分类ID找到对应的名称作为标题
      const findCategoryName = (categories: PretrainedToneCategory[], targetId: string): string => {
        for (const category of categories) {
          if (category.id === targetId) {
            return category.name;
          }
          if (category.children) {
            const childName = findCategoryName(category.children, targetId);
            if (childName) return childName;
          }
        }
        return '模板库音色';
      };
      listTitle.value = findCategoryName(toneCategories.value, templateSynthesis.value);
    }
  } else {
    // 默认加载最近使用
    params.is_recent = true;
    listTitle.value = '最近使用';
  }

  return params;
};

// 动态构建模板音色选项
const templateSynthesisOptions = computed<Array<SelectOption | SelectGroupOption>>(() => {
  // 常驻项：最近使用和我的收藏
  const fixedOptions: Array<SelectOption | SelectGroupOption> = [
    {
      label: '最近使用',
      value: 'templateRecentlyUsed'
    },
    {
      label: '我的收藏',
      value: 'templateMyCollection'
    }
  ];

  // 根据API响应动态构建选项
  const dynamicOptions: Array<SelectOption | SelectGroupOption> = toneCategories.value.map(category => {
    if (category.children && category.children.length > 0) {
      // 有子分类的作为分组
      return {
        type: 'group' as const,
        label: category.name,
        key: category.id,
        children: category.children.map(child => ({
          label: child.name,
          value: child.id
        }))
      };
    }
    // 无子分类的作为普通选项
    return {
      label: category.name,
      value: category.id
    };
  });

  return [...fixedOptions, ...dynamicOptions];
});

// 加载音色列表
const loadTonesList = async (reset = false) => {
  if (loading.value) return;

  // 如果是重置，或者还有更多数据才继续加载
  if (!reset && !hasMore.value) return;

  loading.value = true;

  try {
    if (reset) {
      currentPage.value = 1;
      tonesList.value = [];
      hasMore.value = true;
    }

    const params = buildQueryParams();
    const { data } = await getSynthesisTones(params);

    // 检查data是否存在
    if (!data) {
      console.warn('API返回的数据为空');
      return;
    }

    const records = data.records || [];

    if (reset) {
      tonesList.value = records;
    } else {
      tonesList.value.push(...records);
    }

    totalTones.value = data.total || 0;

    // 检查是否还有更多数据
    if (records.length < pageSize.value) {
      hasMore.value = false;
    } else {
      hasMore.value = true;
    }

    // 只有在非重置模式且成功加载数据后才增加页码
    if (!reset && records.length > 0) {
      currentPage.value += 1;
    }
  } catch (error) {
    console.error('加载音色列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 切换收藏状态
const toggleFavorite = async (event: Event, tone: SynthesisTone) => {
  // 阻止事件冒泡，避免触发音色选择
  event.stopPropagation();

  const toneKey = `${tone.tone_type}-${tone.tone_id}`;

  // 如果正在处理中，直接返回
  if (favoriteLoadingTones.value.has(toneKey)) {
    return;
  }

  try {
    // 添加到加载状态
    favoriteLoadingTones.value.add(toneKey);

    const newFavoriteStatus = !tone.is_favorite;

    // 调用API更新收藏状态
    const response = await changeToneFavoriteStatus(tone.tone_id, {
      tone_type: tone.tone_type,
      is_favorite: newFavoriteStatus
    });

    // 根据API响应更新本地状态
    if (response.data) {
      tone.is_favorite = response.data.is_favorite;

      // 显示成功消息
      message.success(response.data.is_favorite ? '已添加到收藏' : '已取消收藏');

      // 如果当前在收藏页面且取消了收藏，需要从列表中移除该项
      if (mySynthesis.value === 'myCollection' && !response.data.is_favorite) {
        const index = tonesList.value.findIndex(t => `${t.tone_type}-${t.tone_id}` === toneKey);
        if (index > -1) {
          tonesList.value.splice(index, 1);
          totalTones.value = Math.max(0, totalTones.value - 1);
        }
      }
    } else {
      // 如果没有返回数据，使用预期的状态
      tone.is_favorite = newFavoriteStatus;
      message.success(newFavoriteStatus ? '已添加到收藏' : '已取消收藏');
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);

    // 显示错误消息
    message.error('操作失败，请稍后重试');
  } finally {
    // 移除加载状态
    favoriteLoadingTones.value.delete(toneKey);
  }
};

// 无限滚动加载处理
const handleInfiniteLoad = () => {
  if (!hasMore.value || loading.value) {
    return;
  }

  // 直接调用loadTonesList，不需要reset参数，因为这是追加加载
  loadTonesList();
};

// 格式化性别显示
// const formatGender = (gender: number | null): string => {
//   if (gender === 0) return '女';
//   if (gender === 1) return '男';
//   return '';
// };

// 检查音色是否正在切换收藏状态
const isFavoriteLoading = (tone: SynthesisTone): boolean => {
  return favoriteLoadingTones.value.has(`${tone.tone_type}-${tone.tone_id}`);
};

// 获取更多操作选项（根据音色类型动态生成）
const getMoreActionOptions = (tone: SynthesisTone): Array<ActionSelectOption> => {
  const options: Array<ActionSelectOption> = [];

  // 只对自定义音色显示编辑和删除选项
  if (tone.tone_type === 'custom') {
    options.push(
      {
        label: '编辑',
        value: 'edit',
        icon: 'tdesign:adjustment'
      },
      {
        label: '删除',
        value: 'delete',
        icon: 'material-symbols:delete-outline-rounded'
      }
    );
  }

  return options;
};

// 渲染选项标签（包含图标）
const renderActionLabel = (option: ActionSelectOption): VNodeChild => {
  return [
    h(SvgIcon, {
      icon: option.icon || '',
      style: {
        marginRight: '4px',
        verticalAlign: '-0.15em'
      }
    }),
    option.label as string
  ];
};

// 删除音色
const handleDeleteTone = async (tone: SynthesisTone) => {
  // 只允许删除自定义音色
  if (tone.tone_type !== 'custom') {
    message.warning('只能删除自定义音色');
    return;
  }

  // 显示删除确认对话框
  dialog.warning({
    title: '删除确认',
    content: `确认删除音色"${tone.tone_name}"吗？删除后将无法恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 调用删除API
        await deleteTone(Number(tone.tone_id));

        // 显示成功消息
        message.success('音色删除成功');

        // 从列表中移除该音色
        const toneKey = `${tone.tone_type}-${tone.tone_id}`;
        const index = tonesList.value.findIndex(t => `${t.tone_type}-${t.tone_id}` === toneKey);
        if (index > -1) {
          tonesList.value.splice(index, 1);
          totalTones.value = Math.max(0, totalTones.value - 1);
        }

        // 播放状态由父组件统一管理，这里不需要处理
      } catch (error) {
        console.error('删除音色失败:', error);
        message.error('删除音色失败，请稍后重试');
      }
    }
  });
};

// 处理编辑音色
const handleEditTone = (tone: SynthesisTone) => {
  editingTone.value = tone;

  // 初始化表单数据
  editFormModel.value = {
    name: tone.tone_name,
    gender: tone.gender,
    lang: mapStringToLang(tone.lang),
    description: tone.description
  };

  editModalVisible.value = true;
};

// 处理编辑表单提交
const handleEditFormSubmit = async () => {
  if (!editingTone.value) return;

  try {
    // 表单验证
    await editFormRef.value?.validate();

    editLoading.value = true;

    // 构建更新请求数据
    const updateData: ModifyToneReq = {
      name: editFormModel.value.name,
      gender: editFormModel.value.gender!,
      lang: editFormModel.value.lang,
      description: editFormModel.value.description,
      audio_url: editingTone.value.url, // 保持原有音频URL
      is_favorite: editingTone.value.is_favorite,
      need_reduction: false // 编辑时不需要重新降噪
    };

    // 调用更新API
    const response = await updateTone(Number(editingTone.value.tone_id), updateData);

    if (response.data) {
      message.success('音色更新成功');

      // 更新本地列表中的音色数据
      const toneKey = `${editingTone.value.tone_type}-${editingTone.value.tone_id}`;
      const index = tonesList.value.findIndex(t => `${t.tone_type}-${t.tone_id}` === toneKey);
      if (index > -1) {
        // 更新音色信息
        tonesList.value[index] = {
          ...tonesList.value[index],
          tone_name: response.data.name,
          description: response.data.description,
          gender: response.data.gender,
          lang: response.data.lang,
          is_favorite: response.data.is_favorite
        };
      }

      // 关闭模态框
      editModalVisible.value = false;
      editingTone.value = null;
    }
  } catch (error) {
    console.error('更新音色失败:', error);
    message.error('更新音色失败，请稍后重试');
  } finally {
    editLoading.value = false;
  }
};

// 处理更多操作选择
const handleMoreActionSelect = (value: string, tone: SynthesisTone) => {
  switch (value) {
    case 'edit':
      handleEditTone(tone);
      break;
    case 'delete':
      handleDeleteTone(tone);
      break;
    default:
      console.log('未知操作:', value);
      break;
  }
};

// 处理鼠标悬停
const handleToneHover = (toneId: string) => {
  hoveredToneId.value = toneId;
};

const handleToneLeave = () => {
  hoveredToneId.value = null;
};

// 处理音频播放/暂停
const handleTonePlay = (event: Event, tone: SynthesisTone) => {
  // 阻止事件冒泡，避免触发音色选择
  event.stopPropagation();

  // 通过 emit 事件通知父组件播放音频，让父组件的统一音频播放器处理
  emit('play-audio', tone);
};

// 处理音色选择
const handleToneSelect = (tone: SynthesisTone) => {
  emit('tone-selected', tone);
  emit('hide-model-select');
};

// 处理音色导入成功（参考system-menu的逻辑）
const handleTimbreImported = (toneData: any) => {
  // 将导入的音色数据转换为SynthesisTone格式
  const synthesisTone: SynthesisTone = {
    tone_id: toneData.id,
    tone_name: toneData.name,
    tone_type: ToneType.CUSTOM, // 导入的音色都是自定义音色
    model_function: ModelFunction.FAST_CLONE, // 导入的音色通常使用fast_clone
    gender: toneData.gender,
    lang: toneData.lang || 'zh',
    description: toneData.description,
    url: toneData.audio_url,
    is_favorite: toneData.is_favorite || false,
    prompt_text: toneData.prompt_text || '' // 使用原始数据中的prompt_text，如果没有则为空字符串
  };

  // 通过emit通知父组件选择了这个音色，这样会触发index.vue中的handleToneSelected方法
  // 该方法会将音色应用到最后一个AudioContent组件，并切换到设置标签页显示system-menu
  emit('tone-selected', synthesisTone);
  emit('hide-model-select');
};

// 处理搜索
const handleSearch = () => {
  // 清除当前选择的分类
  mySynthesis.value = null;
  templateSynthesis.value = null;
  // 重新加载音色列表
  loadTonesList(true);
};

// 处理搜索输入框回车事件
const handleSearchKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleSearch();
  }
};

// 处理返回按钮点击
const handleGoBack = () => {
  emit('hide-model-select');
};

// 初始化加载音色分类数据
const loadToneCategories = async () => {
  try {
    const { data } = await getPretrainedToneCategories();
    toneCategories.value = data || [];
  } catch (error) {
    console.error('加载音色分类失败:', error);
    toneCategories.value = [];
  }
};

// 监听我的音色库选择变化
watch(mySynthesis, newValue => {
  if (newValue !== null) {
    templateSynthesis.value = null; // 清除模板音色选择
    searchText.value = ''; // 清除搜索文本
    loadTonesList(true); // 重新加载音色列表
  }
});

// 监听模板音色选择变化
watch(templateSynthesis, newValue => {
  if (newValue !== null) {
    mySynthesis.value = null; // 清除我的音色库选择
    searchText.value = ''; // 清除搜索文本
    loadTonesList(true); // 重新加载音色列表
  }
});

// 监听搜索文本变化
watch(searchText, newValue => {
  // 当搜索文本被清空时，重新加载默认列表
  if (!newValue.trim() && (mySynthesis.value || templateSynthesis.value)) {
    loadTonesList(true);
  }
});

// 清理音频资源（现在由父组件统一管理，这里不需要做任何操作）
const cleanupAudio = () => {
  // 播放状态由父组件统一管理，这里不需要处理
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadToneCategories();
  // 页面初始化时加载最近使用的音色
  await loadTonesList(true);
});

// 组件卸载时清理音频资源
onUnmounted(() => {
  cleanupAudio();
});

// 递归查找选中选项的label
const getSelectedOptionLabel = (
  selectedValue: string | null,
  options: Array<SelectOption | SelectGroupOption>
): string => {
  if (!selectedValue) return '模板库音色';

  for (const option of options) {
    if ('type' in option && option.type === 'group' && 'children' in option && Array.isArray(option.children)) {
      // 递归查找分组子选项
      const childOption = option.children.find((child: SelectOption) => child.value === selectedValue);
      if (childOption) return childOption.label as string;
    } else if ('value' in option && option.value === selectedValue) {
      // 直接匹配顶级选项
      return option.label as string;
    }
  }

  return '模板库音色';
};
</script>

<template>
  <NFlex vertical class="h-full w-full p-3">
    <!-- 头部 返回按钮 标题 -->
    <NFlex>
      <NButton text @click="handleGoBack">
        <SvgIcon icon="lets-icons:back" class="mr-3 text-2xl" />
      </NButton>
      <NText class="text-lg">选择音色</NText>
    </NFlex>

    <!-- 音色搜索 导入音色 -->
    <NFlex class="mt-3 w-full" align="center" :wrap="false">
      <div class="h-10 w-4/5">
        <NInputGroup class="h-full">
          <NInput v-model:value="searchText" placeholder="搜索" class="h-full" @keydown="handleSearchKeydown">
            <template #prefix>
              <SvgIcon icon="mingcute:search-line" class="mr-2 text-xl" />
            </template>
          </NInput>
          <NButton class="h-full" type="info" @click="handleSearch">搜索</NButton>
        </NInputGroup>
      </div>

      <TimbresImport class="flex-1" @timbre-imported="handleTimbreImported" />
    </NFlex>

    <!-- 我的音色库 -->
    <NPopselect
      v-model:value="mySynthesis"
      :options="mySynthesisOptions"
      trigger="click"
      class="my_synthesis_popselect w-full"
      :style="{ width: popSelectWidth }"
    >
      <NTag size="large" class="my_synthesis_select h-10 w-full cursor-pointer">
        <NButton text class="w-full flex justify-between text-base">
          <NFlex align="center" justify="center" :wrap="false">
            <SvgIcon icon="mingcute:voice-line" class="text-2xl" />
            <NText class="text-left text-1.1em">
              {{ mySynthesisOptions.find(item => item.value === mySynthesis)?.label || '我的音色库' }}
            </NText>
          </NFlex>
          <SvgIcon icon="formkit:down" class="text-0.8em" />
        </NButton>
      </NTag>
    </NPopselect>

    <!-- 模板库音色 -->
    <NPopselect
      v-model:value="templateSynthesis"
      :options="templateSynthesisOptions"
      trigger="click"
      class="my_synthesis_popselect w-full"
      :style="{ width: popSelectWidth }"
      scrollable
    >
      <NTag size="large" class="my_synthesis_select h-10 w-full cursor-pointer">
        <NButton text class="w-full flex justify-between text-base">
          <NFlex align="center" justify="center" :wrap="false">
            <SvgIcon icon="icon-park-solid:voice-one" class="text-2xl" />
            <NText class="text-left text-1.1em">
              {{ getSelectedOptionLabel(templateSynthesis, templateSynthesisOptions) }}
            </NText>
          </NFlex>
          <SvgIcon icon="formkit:down" class="text-0.8em" />
        </NButton>
      </NTag>
    </NPopselect>

    <!-- 音色列表 -->
    <NFlex class="mt-3 w-full" vertical>
      <!-- 动态标题 -->
      <NText class="text-1.1em text-gray-400">{{ listTitle }}</NText>

      <!-- 音色列表容器 -->
      <div class="tone-list-container" :class="isLargeScreen ? 'h-115' : 'h-74'">
        <NInfiniteScroll :distance="50" @load="handleInfiniteLoad">
          <!-- 动态渲染音色列表 -->
          <NFlex
            v-for="tone in tonesList"
            :key="`${tone.tone_type}-${tone.tone_id}`"
            class="mb-3 w-full cursor-pointer"
            justify="space-between"
            align="center"
            @mouseenter="handleToneHover(`${tone.tone_type}-${tone.tone_id}`)"
            @mouseleave="handleToneLeave"
          >
            <NCard
              class="audio_generate_task_card"
              embedded
              :class="{ 'tone-card-hovered': hoveredToneId === `${tone.tone_type}-${tone.tone_id}` }"
              @click="handleToneSelect(tone)"
            >
              <NFlex justify="space-between" align="center" :wrap="false">
                <NButton text @click="handleTonePlay($event, tone)">
                  <!-- 播放/暂停图标 -->
                  <SvgIcon
                    :icon="
                      playingToneId === `${tone.tone_type}-${tone.tone_id}` ? 'gridicons:pause' : 'lsicon:play-filled'
                    "
                    class="text-4xl"
                  />
                </NButton>

                <!-- 任务信息 -->
                <NFlex vertical class="ml-3">
                  <!-- 音色名称 -->
                  <NEllipsis :line-clamp="1">{{ tone.tone_name }}</NEllipsis>

                  <!-- 音色描述 -->
                  <NEllipsis :line-clamp="1" class="text-0.8em" :class="isLargeScreen ? 'max-w-90' : 'max-w-53'">
                    {{ tone.description || tone.prompt_text }}
                    <template #tooltip>
                      <div class="line-clamp-5 max-w-100 overflow-hidden text-center">
                        {{ tone.description || tone.prompt_text }}
                      </div>
                    </template>
                  </NEllipsis>

                  <!-- 音色 其他信息 -->
                  <!--
 <NFlex align="center" :wrap="false">
                    <NTag v-if="formatGender(tone.gender)" type="info" round>{{ formatGender(tone.gender) }}</NTag>
                    <NTag v-if="tone.lang" type="info" round>{{ tone.lang }}</NTag>
                  </NFlex>
-->
                </NFlex>
              </NFlex>

              <!-- 指示 交互图标  按钮 -->
              <NFlex align="center" class="ml-3">
                <!-- 收藏 -->
                <NButton
                  text
                  class="collect-button"
                  :loading="isFavoriteLoading(tone)"
                  @click="toggleFavorite($event, tone)"
                >
                  <SvgIcon
                    v-if="!isFavoriteLoading(tone)"
                    :icon="tone.is_favorite ? 'fluent:star-32-filled' : 'fluent:star-32-regular'"
                    class="star-icon text-2xl"
                    :class="{ filled: tone.is_favorite }"
                  />
                </NButton>

                <!-- 更多操作 -->
                <NPopselect
                  v-if="getMoreActionOptions(tone).length > 0"
                  trigger="click"
                  :options="getMoreActionOptions(tone)"
                  :render-label="renderActionLabel"
                  @update:value="(value: string) => handleMoreActionSelect(value, tone)"
                >
                  <NButton text @click="$event.stopPropagation()">
                    <SvgIcon icon="nrk:more" class="text-2xl" />
                  </NButton>
                </NPopselect>
              </NFlex>
            </NCard>
          </NFlex>

          <!-- 加载状态 -->
          <NFlex v-if="loading" justify="center" class="py-8">
            <NSpin size="medium" />
            <NText class="ml-2 text-gray-400">加载中...</NText>
          </NFlex>

          <!-- 无更多数据提示 -->
          <NFlex v-if="!hasMore && tonesList.length > 0" justify="center" class="py-4">
            <NText class="text-sm text-gray-400">没有更多音色了</NText>
          </NFlex>

          <!-- 空状态 -->
          <NFlex v-if="!loading && tonesList.length === 0" justify="center" class="py-8">
            <NText class="text-gray-500">暂无音色数据</NText>
          </NFlex>
        </NInfiniteScroll>
      </div>
    </NFlex>

    <!-- 编辑音色模态框 -->
    <NModal
      v-model:show="editModalVisible"
      title="编辑音色"
      preset="card"
      class="max-w-40vw w-700px"
      :mask-closable="false"
      :closable="true"
    >
      <NScrollbar class="max-h-70vh">
        <NForm
          ref="editFormRef"
          :model="editFormModel"
          :rules="editFormRules"
          label-placement="left"
          label-width="100"
          require-mark-placement="right-hanging"
        >
          <!-- 音色详情信息 -->
          <div class="more-details-section">
            <NFlex>
              <NFormItem path="name" class="flex-1">
                <NInput v-model:value="editFormModel.name" placeholder="请输入音色名称" :maxlength="20" clearable />
              </NFormItem>

              <NFormItem path="gender" class="flex-1">
                <NSelect v-model:value="editFormModel.gender" :options="genderOptions" placeholder="请选择音色性别" />
              </NFormItem>

              <NFormItem path="lang" class="flex-1">
                <NSelect v-model:value="editFormModel.lang" :options="languageOptions" placeholder="请选择音色语言" />
              </NFormItem>
            </NFlex>

            <NFormItem class="mt-3">
              <NInput
                v-model:value="editFormModel.description"
                type="textarea"
                placeholder="请输入音色描述..."
                :rows="3"
                clearable
              />
            </NFormItem>
          </div>
        </NForm>
      </NScrollbar>

      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" :loading="editLoading" @click="handleEditFormSubmit">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </NFlex>
</template>

<style scoped lang="scss">
.tone-list-container {
  overflow-y: auto;
  width: 100%;
}

.my_synthesis_select :deep(.n-tag__content) {
  width: 100% !important;
}

.my_synthesis_select :deep(.n-tag__content) .n-button .n-button__content {
  width: 100% !important;
  justify-content: space-between !important;
}

.audio_generate_task_card > :deep(.n-card__content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px !important;
}

:deep(.collect-button) svg {
  font-size: 20px !important;
}

:deep(.collect-button) .filled {
  color: #faad14;
}

:deep(.collect-button.n-button--loading) {
  opacity: 0.6;
}

:deep(.collect-button.n-button--disabled) {
  cursor: not-allowed;
}

/* 音色卡片悬停高亮样式 */
.tone-card-hovered {
  background-color: rgba(24, 160, 88, 0.1) !important;
  /* border-color: #18a058 !important; */
  // transition: all 0.2s ease-in-out;
}

// .tone-card-hovered :deep(.n-card__content) {
// background-color: rgba(24, 160, 88, 0.05) !important;
// }
</style>

<style lang="scss">
/* 全局样式 - 专门用于 NPopselect 下拉菜单 */
.n-popselect-menu .n-base-select-option__content {
  display: flex !important;
  align-items: center !important;
}
</style>
