"""
测试用
"""
import logging

from sqlalchemy import create_engine
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm import sessionmaker
import os
import traceback
from dotenv import load_dotenv
from sqlalchemy.inspection import inspect

load_dotenv()
logger = logging.getLogger(__name__)

SQLALCHEMY_DATABASE_URL = (
    "mysql+pymysql://{username}:{password}@{host}:{port}/{database}".format(
        username='appyser',
        password='app123',
        host='***********',
        port=3306,
        database='ai_admin',
    )
)
# SQLALCHEMY_DATABASE_URL = "********************************************"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=os.getenv("debug", 0) == "1",
    pool_recycle=60,  # SQLAlchemy 会在指定时间内回收连接，单位为秒。
    pool_size=1000,  # 最大连接数
)
SessionLocal = sessionmaker(autocommit=False, autoflush=True, bind=engine)


class Base(DeclarativeBase):

    def assign_dict(self, data):
        """
        安全地将字典数据分配给SQLAlchemy模型实例。
        只更新模型中实际存在的属性。

        :param model_instance: SQLAlchemy模型实例，如 A()
        :param data: 包含要赋值的数据的字典，如 a
        """
        # 获取模型的所有属性（即列名）
        attr_names = {c.key for c in inspect(self.__class__).mapper.column_attrs}

        # 遍历字典，仅更新存在于模型中的属性
        for key, value in data.items():
            if key in attr_names:
                setattr(self, key, value)


def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception:
        logger.error(traceback.format_exc())
        db.rollback()
        raise
    finally:
        db.close()
