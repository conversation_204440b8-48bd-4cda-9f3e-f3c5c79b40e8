from utils.database import Base
from sqlalchemy import Column, Integer, String,JSON,DateTime
from sqlalchemy.dialects.mysql import TINYINT
import datetime

class Menu(Base):
    __tablename__ = "menu"
    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    parentId = Column(Integer, nullable=False, comment='父级ID')
    menuName = Column(String(20), nullable=False, comment='路由显示名字')
    routeName = Column(String(50), nullable=False, comment='同时是 前端路由名，后端权限名')
    menuType = Column(TINYINT(unsigned=True), default=1, nullable=False, comment='类型 1：目录，2：菜单')
    routePath = Column(String(200), nullable=False, comment='路由路径')
    component = Column(String(200), nullable=False, comment='组件')
    i18nKey = Column(String(100), nullable=False, comment='国际化key')
    icon = Column(String(100), nullable=False, comment='图标')
    href = Column(String(100), nullable=False, comment='外链')
    activeMenu = Column(String(100), nullable=False, comment='高亮菜单')
    iconType = Column(TINYINT(unsigned=True), default=1, nullable=False, comment='图标类型')
    keepAlive = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='是否缓存路由')
    constant = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='是否常量路由')
    order = Column(Integer, nullable=False, comment='排序')
    hideInMenu = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='隐藏菜单')
    multiTab = Column(TINYINT(unsigned=True), default=0, nullable=False, comment='支持多页签')
    fixedIndexInTab = Column(Integer, nullable=False, comment='支持多页签')
    query = Column(JSON, nullable=False, comment='路由参数')
    buttons = Column(JSON, nullable=False, comment='按钮')
    status = Column(TINYINT(unsigned=True), default=1, nullable=False, comment='状态 1:启用 2:禁用')
    createBy = Column(String(50), nullable=True, default='', comment='创建者')
    createTime = Column(DateTime, default=datetime.datetime.now, nullable=False, comment='创建时间')
    updateBy = Column(String(50), nullable=True, default='', comment='更新者')
    updateTime = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')


def generate_tree(source,parent,cache=[]):
  tree = []
  for item in source:
    if item.id in cache:
      continue
    if item.parentId == parent:
      item.children = generate_tree(source, item.id,cache)
      tree.append(item)
  return tree
