from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from typing import List, Optional
from pydantic import BaseModel
from models.assets import AssetOut, Assets, AssetType
from models.users import User, get_request_user, get_request_role
from utils.database import get_db

import logging

from utils.exceptions import ClientVisibleException

logger = logging.getLogger(__name__)

router = APIRouter()


class AssetsPaginatedData(BaseModel):
    """资产分页数据模型"""
    records: List[AssetOut]
    current: int
    size: int
    total: int


class AssetsPaginatedResponse(BaseModel):
    """资产分页响应模型"""
    code: str = "0000"
    msg: str = "success"
    data: AssetsPaginatedData


class AssetResponse(BaseModel):
    """单个资产响应模型"""
    code: str = "0000"
    msg: str = "success"
    data: AssetOut


@router.get("/get_my_assets", response_model=AssetsPaginatedResponse, tags=["asset"])
async def get_my_assets(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    type: str = Query(AssetType.IMAGE, description="资产类型筛选，使用'all'返回所有类型"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
    iscollect: Optional[int] = Query(None, description="是否收藏筛选，使用'1'返回收藏的资产，不提供则返回所有资产"),
) -> AssetsPaginatedResponse:
    """
    获取当前用户的资产信息
    
    Args:
        page: 页码，从1开始
        size: 每页大小，范围1-100
        type: 资产类型筛选，默认为IMAGE，使用'all'返回所有类型
        user: 当前登录用户
        db: 数据库会话
        iscollect: 是否收藏筛选，未提供时返回所有资产，使用'1'返回收藏的资产，使用'0'返回未收藏的资产
        
    Returns:
        包含用户资产分页数据的响应
    """
    try:
        async with db as session:
            # 构建查询条件
            base_query = select(Assets).filter(Assets.user_id == user.id)
            count_query = select(func.count()).select_from(Assets).filter(Assets.user_id == user.id)
            
            # 如果不是请求所有类型，则按类型筛选
            if type.upper() != "ALL":
                try:
                    asset_type = AssetType(type.upper())
                    base_query = base_query.filter(Assets.type == asset_type)
                    count_query = count_query.filter(Assets.type == asset_type)
                except ValueError:
                    # 如果传入的类型不是有效的AssetType，则默认使用IMAGE类型
                    base_query = base_query.filter(Assets.type == AssetType.IMAGE)
                    count_query = count_query.filter(Assets.type == AssetType.IMAGE)
            
            # 根据收藏状态筛选，仅当明确提供参数时
            if iscollect is not None:
                if iscollect == 1:
                    base_query = base_query.filter(Assets.iscollect == 1)
                    count_query = count_query.filter(Assets.iscollect == 1)
                elif iscollect == 0:
                    base_query = base_query.filter(Assets.iscollect == 0)
                    count_query = count_query.filter(Assets.iscollect == 0)
            
            # 获取总数
            count_result = await session.execute(count_query)
            total = count_result.scalar()
            
            # 获取分页数据，按创建时间降序排列
            data_query = base_query.order_by(Assets.create_time.desc()).offset((page - 1) * size).limit(size)
            data_result = await session.execute(data_query)
            assets = data_result.scalars().all()
            
            # 转换为输出模型
            records = [AssetOut.model_validate(asset) for asset in assets]
            
            return AssetsPaginatedResponse(
                code="0000",
                msg="success",
                data=AssetsPaginatedData(
                    records=records,
                    current=page,
                    size=size,
                    total=total
                )
            )
            
    except Exception as e:
        logger.error(f"获取用户资产失败: {str(e)}")
        raise ClientVisibleException("获取用户资产失败") from e


@router.post("/toggle_collect", response_model=AssetResponse, tags=["asset"])
async def toggle_collect(
    taskid: str,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
) -> AssetResponse:
    """
    切换资产的收藏状态
    
    Args:
        taskid: 任务标识
        user: 当前登录用户
        db: 数据库会话
        
    Returns:
        更新后的资产信息
    """
    try:
        async with db as session:
            # 查询资产是否存在
            query = select(Assets).filter(Assets.taskid == taskid)
            result = await session.execute(query)
            asset = result.scalars().first()
            
            # 检查资产是否存在
            if not asset:
                return AssetResponse(
                    code="1001",
                    msg="资产不存在",
                    data=None
                )
            
            # 验证用户是否为资产所有者
            if asset.user_id != user.id:
                return AssetResponse(
                    code="1002",
                    msg="无权操作此资产",
                    data=None
                )
            
            # 切换收藏状态
            asset.iscollect = 0 if asset.iscollect == 1 else 1
            
            # 提交更改
            await session.commit()
            await session.refresh(asset)
            
            # 返回更新后的资产数据
            return AssetResponse(
                code="0000",
                msg="收藏状态更新成功",
                data=AssetOut.model_validate(asset)
            )
            
    except Exception as e:
        logger.error(f"更新收藏状态失败: {str(e)}")
        await db.rollback()
        raise ClientVisibleException("更新收藏状态失败") from e
    


@router.get("/asset_info_list", response_model=AssetsPaginatedResponse, tags=["asset"])
async def get_asset_info_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(4, ge=1, le=100, description="每页大小"),
    type: str = Query(AssetType.IMAGE, description="资产类型筛选，使用'all'返回所有类型"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
    # iscollect: Optional[int] = Query(None, description="是否收藏筛选，使用'1'返回收藏的资产，不提供则返回所有资产"),
    taskid: Optional[str] = Query(None, description="任务ID筛选"),
    reset_page: Optional[bool] = Query(True, description="当提供taskid时，是否重置页码到目标资产所在页"),
    userid: Optional[int] = Query(None, description="要查询的用户ID，仅超级管理员可用")
) -> AssetsPaginatedResponse:
    """
    获取资产信息列表，当提供taskid时，可选择是否重置页码
    
    Args:
        page: 页码，从1开始
        size: 每页大小，默认4，范围1-100
        type: 资产类型筛选，默认为IMAGE，使用'all'返回所有类型
        user: 当前登录用户
        db: 数据库会话
        taskid: 任务ID筛选，如果提供，将对应资产置于首位
        reset_page: 当提供taskid时，是否重置页码到目标资产所在页，默认为True
        userid: 要查询的用户ID，仅超级管理员可用，不提供则查询当前用户资产
        
    Returns:
        包含资产列表的分页响应，如果指定了taskid，则对应资产会被置于首位
    """
    try:
        async with db as session:
            # 确定要查询的用户ID
            target_user_id = user.id
            
            # 当提供了userid参数时，检查当前用户权限
            if userid is not None and userid != user.id:
                # 获取当前用户的角色
                roles = await get_request_role(user, db=db)
                # 检查当前用户是否为超级管理员
                is_super_admin = 'super_admin' in roles
                
                if not is_super_admin:
                    return AssetsPaginatedResponse(
                        code="1000",
                        msg="权限不足",
                        data=AssetsPaginatedData(
                            records=[],
                            current=page,
                            size=size,
                            total=0
                        )
                    )
                
                # 如果是超级管理员，使用提供的userid
                target_user_id = userid
            
            # 构建查询条件
            base_query = select(Assets).filter(Assets.user_id == target_user_id)
            count_query = select(func.count()).select_from(Assets).filter(Assets.user_id == target_user_id)
            
            # 如果不是请求所有类型，则按类型筛选
            if type.upper() != "ALL":
                try:
                    asset_type = AssetType(type.upper())
                    base_query = base_query.filter(Assets.type == asset_type)
                    count_query = count_query.filter(Assets.type == asset_type)
                except ValueError:
                    # 如果传入的类型不是有效的AssetType，则默认使用IMAGE类型
                    base_query = base_query.filter(Assets.type == AssetType.IMAGE)
                    count_query = count_query.filter(Assets.type == AssetType.IMAGE)
            
            # 获取总数
            count_result = await session.execute(count_query)
            total = count_result.scalar()
            
            # 当提供taskid时的特殊处理
            target_asset = None
            target_page = None
            if taskid:
                # 查找指定的资产
                task_query = select(Assets).filter(Assets.taskid == taskid, Assets.user_id == target_user_id)
                task_result = await session.execute(task_query)
                target_asset = task_result.scalars().first()
                
                if target_asset:
                    # 计算这个资产在总列表中的位置（按创建时间降序）
                    position_query = select(func.count()).select_from(Assets).filter(
                        Assets.user_id == target_user_id,
                        Assets.create_time > target_asset.create_time  # 修改为大于，因为降序排列时更新的资产排在前面
                    )
                    
                    if type.upper() != "ALL":
                        try:
                            asset_type = AssetType(type.upper())
                            position_query = position_query.filter(Assets.type == asset_type)
                        except ValueError:
                            position_query = position_query.filter(Assets.type == AssetType.IMAGE)
                    
                    position_result = await session.execute(position_query)
                    position = position_result.scalar()
                    
                    # 计算目标资产所在的页码
                    target_page = position // size + 1
                    
                    # 只有在reset_page为True时才重置页码
                    if reset_page:
                        page = target_page
            
            # 获取分页数据
            data_query = base_query.order_by(Assets.create_time.desc()).offset((page - 1) * size).limit(size)
            data_result = await session.execute(data_query)
            assets = data_result.scalars().all()
            
            # 转换为输出模型
            records = [AssetOut.model_validate(asset) for asset in assets]
            
            # 如果目标资产在当前页面，将其移到首位
            if target_asset and any(record.taskid == taskid for record in records):
                for i, record in enumerate(records):
                    if record.taskid == taskid:
                        if i > 0:  # 只有当不在首位时才需要移动
                            target = records.pop(i)
                            records.insert(0, target)
                        break
            
            # 如果提供了taskid但未在当前页找到，可以选择是否添加额外信息
            asset_position_info = None
            if taskid and target_asset and target_page and not any(record.taskid == taskid for record in records):
                asset_position_info = {
                    "target_taskid": taskid,
                    "target_page": target_page,
                    "current_page": page
                }
            
            return AssetsPaginatedResponse(
                code="0000",
                msg="success",
                data=AssetsPaginatedData(
                    records=records,
                    current=page,
                    size=size,
                    total=total
                )
            )
            
    except Exception as e:
        logger.error(f"获取资产信息列表失败: {str(e)}")
        raise ClientVisibleException("获取资产信息列表失败") from e