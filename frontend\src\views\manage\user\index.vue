<script setup lang="tsx">
import { N<PERSON><PERSON><PERSON>, NPopconfirm, NTag } from 'naive-ui';
import { ref } from 'vue';
import { fetchGetUserList, postDelUser } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import {
  enableStatusRecord
  // userGenderRecord
} from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useCreditStore } from '@/store/modules/credit';
import UserOperateModal from './modules/user-operate-modal.vue';
import UserSearch from './modules/user-search.vue';
import UserCreditManagement from './modules/user-credit-management.vue';

const appStore = useAppStore();
const creditStore = useCreditStore();

type EditingCreditUser = { id: number; credit: number };
const editingCreditUser = ref<EditingCreditUser | null>(null);
const showCreditManagement = ref(false);
const handleCreditManagement = (row: EditingCreditUser) => {
  editingCreditUser.value = row;
  showCreditManagement.value = true;
};

const { columns, columnChecks, data, getData, loading, mobilePagination, searchParams, resetSearchParams } = useTable({
  apiFn: fetchGetUserList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    username: '',
    nickname: '',
    status: null,
    company: ''
  },
  columns: () => [
    // {
    //   type: 'selection',
    //   align: 'center',
    //   width: 48
    // },
    // {
    //   key: 'index',
    //   title: $t('common.index'),
    //   align: 'center',
    //   width: 64
    // },
    {
      key: 'id',
      title: $t('page.manage.user.id'),
      align: 'center',
      minWidth: 50
    },
    {
      key: 'username',
      title: $t('page.manage.user.userName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'nickname',
      title: $t('page.manage.user.nickName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'email',
      title: $t('page.manage.user.userEmail'),
      align: 'center',
      minWidth: 200
    },
    {
      key: 'company',
      title: $t('page.manage.user.company'),
      align: 'center',
      minWidth: 150
    },
    // {
    //   key: 'gender',
    //   title: $t('page.manage.user.userGender'),
    //   align: 'center',
    //   width: 100,
    //   render: row => {
    //     if (row.gender === null) {
    //       return null;
    //     }

    //     const tagMap: Record<Api.SystemManage.UserGender, NaiveUI.ThemeColor> = {
    //       0: 'info',
    //       1: 'primary',
    //       2: 'error'
    //     };

    //     const label = $t(userGenderRecord[`${row.gender}`]);

    //     return <NTag type={tagMap[row.gender]}>{label}</NTag>;
    //   }
    // },
    {
      key: 'loginip',
      title: $t('page.manage.user.loginIp'),
      align: 'center',
      minWidth: 150
    },
    {
      key: 'logintime',
      title: $t('page.manage.user.loginTime'),
      align: 'center',
      minWidth: 150,
      render: row => {
        if (!row.logintime) return null;
        const date = new Date(Number(row.logintime) * 1000);
        return date.toLocaleString();
      }
    },
    {
      key: 'updatetime',
      title: $t('page.manage.user.updateTime'),
      align: 'center',
      minWidth: 200
    },
    {
      key: 'updateBy',
      title: $t('page.manage.user.updateBy'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'status',
      title: $t('page.manage.user.userStatus'),
      align: 'center',
      width: 100,
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          2: 'warning'
        };

        const label = $t(enableStatusRecord[row.status]);

        return <NTag type={tagMap[row.status]}>{label}</NTag>;
      }
    },
    {
      key: 'credit',
      title: $t('page.manage.user.credit'),
      align: 'center',
      minWidth: 10
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 240,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleCreditManagement(row)}>
            {$t('page.manage.user.creditManagement')}
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}
const handleCreditManagementSubmit = () => {
  getData();
  creditStore.updateCreditInfo();
};

function handleDelete(id: number) {
  // request
  postDelUser(id).then(() => {
    onDeleted();
  });
}

function edit(id: number) {
  handleEdit(id);
}

type Row = {
  id: any;
};
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard :title="$t('page.manage.user.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1700"
        :loading="loading"
        remote
        :row-key="(row: Row) => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <UserOperateModal
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
    <UserCreditManagement
      v-model:visible="showCreditManagement"
      :user="editingCreditUser"
      @submitted="handleCreditManagementSubmit"
    />
  </div>
</template>

<style scoped></style>
