<script setup lang="ts">
import { computed, reactive } from 'vue';
import {
  // useFormRules,  输入格式规则验证
  useNaiveForm
} from '@/hooks/common/form';

defineOptions({
  name: 'GameSearch'
});

interface Emits {
  (e: 'search', params: Api.SystemManage.CommonSearchParams): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  tool_type: '',
  name: ''
});

// type RuleKey = 'gamecode' | 'gamename';

const rules = computed(() => {
  // const { patternRules } = useFormRules();

  return {
    // gamecode: patternRules,
    // gamename: patternRules
    // lang: patternRules.lang
  };
});

async function search() {
  await validate();
  // console.log('gamecode:', model);
  const searchParams = {
    name: model.name,
    tool_type: model.tool_type,
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}

// 下拉框的选项
const type_options = [
  { label: '聊天', value: '聊天' },
  { label: '绘图', value: '绘图' },
  { label: '写作工具', value: '写作工具' },
  { label: '办公工具', value: '办公工具' },
  { label: '设计工具', value: '设计工具' },
  { label: '视频', value: '视频' },
  { label: '其他', value: '其他' },
  { label: '编程工具', value: '编程工具' },
  { label: '音乐', value: '音乐' }
];
</script>

<template>
  <NCard :bordered="false" size="small" class="flex items-center card-wrapper">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive class="grid-box">
        <NFormItemGi span="24 s:12 m:6" label="工具名称" path="name" class="pr-24px">
          <NInput v-model:value="model.name"></NInput>
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="类型" path="tool_type" class="pr-24px">
          <NSelect v-model:value="model.tool_type" :options="type_options" placeholder="请选择工具分类" clearable />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped>
/* :deep(.grid-box) .n-form-item-feedback-wrapper {
  display: none;
} */
</style>
