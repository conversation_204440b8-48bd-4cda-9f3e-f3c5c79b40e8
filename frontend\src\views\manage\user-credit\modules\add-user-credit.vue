<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'AddUserCredit'
});

interface Props {
  operateType: 'add' | 'edit';
  rowData?: any | null;
}

const props = defineProps<Props>();

type SubmitModel = {
  user_id: string;
  type: number;
  credit: number;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '增加用户积分' : '修改用户积分';
});

type Model = {
  user_id: string;
  type: number;
  credit: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    user_id: '',
    type: 1,
    credit: 1
  };
}

type RuleKey = 'user_id' | 'type' | 'credit';

const rules: Record<RuleKey, any> = {
  user_id: defaultRequiredRule,
  type: defaultRequiredRule,
  credit: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'add' && props.rowData) {
    const rowData = { ...props.rowData };
    rowData.credit = 0;
    Object.assign(model, rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  const submitModel = {
    ...model
  };
  closeDrawer();
  emit('submitted', submitModel);
}

// const creditTypeOptions = [
//   { label: '普通积分', value: 1 },
//   { label: '视频积分', value: 2 },
//   { label: '音乐积分', value: 3 },
//   { label: '赠送积分', value: 4 },
//   { label: '购买积分', value: 5 }
// ];

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="用户 ID" path="user_id">
          <NInput v-model:value="model.user_id" placeholder="Userid" disabled />
        </NFormItem>
        <!--
        <NFormItem label="积分类型" path="type">
          <NSelect v-model:value="model.type" :options="creditTypeOptions" placeholder="请选择积分类型" />
        </NFormItem>
        -->
        <NFormItem label="积分" path="credit">
          <NInputNumber
            v-model:value="model.credit"
            type="number"
            placeholder="请输入积分"
            clearable
            class="w-full"
            :min="0"
            :precision="0"
            :step="1"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
