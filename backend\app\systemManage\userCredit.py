import logging

from fastapi import APIRouter, Depends, Query, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from service.credit import add_daily_gift_credit
from utils.database import get_db
from models.user_credit import UserCredit, UserCreditLog
from pydantic import BaseModel
from typing import List, Optional, Dict
import datetime
from models.users import User, get_request_user, get_roles_checker

router = APIRouter()
logger = logging.getLogger(__name__)

class UserOut(BaseModel):
  userid: int
  username: str
  company: str
  class Config:
    from_attributes = True

class UserCreditOut(BaseModel):
  id: int
  user_id: int
  user_name: str
  credit: int
  updatetime: Optional[datetime.datetime]

  class Config:
    from_attributes = True


class PaginatedData(BaseModel):
  records: List[UserCreditOut]
  current: int
  size: int
  total: int

class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str

class UserCreditInfo(BaseModel):
    total_credit: int
    credit_details: List[UserCreditOut]

class UserCreditLogDetail(BaseModel):
    credit: int
    after_credit: int
    capacity: str
    model: str
    matter: str
    createtime: datetime.datetime

    class Config:
        from_attributes = True

class UserCreditLogPaginatedData(BaseModel):
    records: List[UserCreditLogDetail]
    total: int
    current: int
    size: int

class UserCreditLogResponse(BaseModel):
    code: str
    data: UserCreditLogPaginatedData

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()

@router.get(
    "/all",
    dependencies=[Depends(get_roles_checker('super_admin'))],
    response_model=PaginatedResponse,
    tags=["manage"]
)
async def get_all_credit_log(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  async with db as session:
    # 获取分页数据
    result = await session.execute(
      select(UserCredit.id,UserCredit.user_id,UserCredit.credit,UserCredit.updatetime, User.nickname.label('user_name'))
      .join(User, UserCredit.user_id == User.id)
      .offset((page - 1) * size)
      .limit(size)
      .order_by(UserCredit.id)
    )
    records = result.all()


    # 获取总数
    total = await get_total_count(session, UserCredit)

    records_out = [UserCreditOut.model_validate(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )

class UserChild(BaseModel):
    label: str
    key: int

# 定义公司分组结构
class CompanyGroup(BaseModel):
    label: str
    key: int
    children: List[UserChild]

# 定义最终的返回格式
class RecordsOut(BaseModel):
    records: List[CompanyGroup]


# 添加新用户积分的下拉选择数据
@router.get(
    "/newcreditlist",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def get_new_user_credit(
  db: AsyncSession = Depends(get_db)
):
  async with db as session:
    result = await db.execute(
        select(User.id.label('userid'), User.nickname.label('username'), User.company)
        .where(User.id.notin_(select(UserCredit.user_id)))
        .order_by(User.company, User.id)
    )

    records = result.all()  # 获取查询结果列表

    # 构造分组数据
    company_dict: Dict[str, List[UserChild]] = {}
    for userid, username, company in records:
        if company not in company_dict:
            company_dict[company] = []
        company_dict[company].append(UserChild(label=username, key=userid))

    # 构造最终返回的数据
    records_out = RecordsOut(
        records=[
            CompanyGroup(label=company, key=index, children=children)
            for index, (company, children) in enumerate(company_dict.items())
        ]
    )

    return JSONResponse(status_code=200,content={"code": "0000", "msg": "success","data":records_out.model_dump()})

@router.get(
    "/search",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def search_game_management(
  user_id: Optional[str] = Query(None),
  user_name: Optional[str] = Query(None),
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  # 构建查询语句
  query = select(
        UserCredit.id,
        UserCredit.user_id,
        UserCredit.credit,
        UserCredit.updatetime,
        User.nickname.label('user_name')
    ).join(User, UserCredit.user_id == User.id)
  if user_id :
    query = query.where(UserCredit.user_id == user_id)
  if user_name :
    query = query.where(User.nickname.like(f'%{user_name}%'))

  async with db as session:
    # 获取分页数据
    result = await session.execute(query.offset((page - 1) * size).limit(size))
    records = result.all()

    # 获取总数
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await session.execute(count_query)
    total = total_result.scalar()

    records_out = [UserCreditOut.model_validate(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )

@router.get("/get_user_credit", response_model=PaginatedResponse, tags=["manage"])
async def get_user_credit(
  userid: Optional[str] = Query(None),
  db: AsyncSession = Depends(get_db)
):
  # 构建查询语句
  result = await db.execute( select(UserCredit.credit).where(UserCredit.user_id == userid) )
  user_credit = result.scalars().first() or 0
  return JSONResponse(status_code=200,content={"code": "0000", "msg": "success","data":user_credit})


@router.get("/info",  tags=["manage"])
async def get_user_credit_info(
    request: Request,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    await add_daily_gift_credit(user.id, request.state.client_ip, db)
    async with db as session:
        # 获取用户所有积分记录
        result = await session.execute(
            select(
                UserCredit.id,
                UserCredit.user_id,
                UserCredit.credit,
                UserCredit.updatetime,
                User.nickname.label('user_name')
            )
            .join(User, UserCredit.user_id == User.id)
            .where(UserCredit.user_id == user.id)
            .order_by(UserCredit.updatetime.desc())
        )
        records = result.all()

        # 计算总积分
        total_credit = sum(record.credit for record in records)

        # 转换记录格式
        credit_details = [UserCreditOut.model_validate(record) for record in records]

        return {
            "code": "0000",
            "data": {
                "total_credit": total_credit,
                "credit_details": credit_details
            }
        }

@router.get("/personal_log", response_model=UserCreditLogResponse, tags=["manage"])
async def get_personal_credit_log(
    user: User = Depends(get_request_user),
    page: int = Query(1, alias="current"),
    size: int = Query(10),
    db: AsyncSession = Depends(get_db)
):
    async with db as session:
        query = (
            select(
                UserCreditLog.credit,
                UserCreditLog.after_credit,
                UserCreditLog.capacity,
                UserCreditLog.model,
                UserCreditLog.matter,
                UserCreditLog.createtime
            )
            .where(UserCreditLog.user_id == user.id)
            .order_by(UserCreditLog.createtime.desc())
        )

        # 计算偏移量
        offset = (page - 1) * size

        # 执行分页查询
        result = await session.execute(
            query.offset(offset).limit(size)
        )
        records = result.all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(UserCreditLog.id)
            .where(UserCreditLog.user_id == user.id)
            .subquery()
        )
        total_result = await session.execute(count_query)
        total = total_result.scalar()

        return {
            "code": "0000",
            "data": {
                "records": [UserCreditLogDetail.model_validate(record) for record in records],
                "total": total,
                "current": page,
                "size": size
            }
        }

