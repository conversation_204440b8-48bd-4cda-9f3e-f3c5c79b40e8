import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Annotated

import nanoid
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio.session import AsyncSession

from models.share import ShareType, Share
from models.users import User, get_request_user
from utils.database import get_db
from utils.exceptions import ClientVisibleException

router = APIRouter()

logger = logging.getLogger(__name__)


class CreateShareReq(BaseModel):
    share_type: Annotated[ShareType, Field(description="分享类型")]
    params: Annotated[dict[str, str | int | float | list[str]], Field(description="内容生成参数")]
    result: Annotated[dict[str, str | int | float], Field(description="内容生成结果")]


class CreateShareRes(BaseModel):
    code: str = '0000'
    msg: str = ''
    data: str = Field(description='分享 Key，用于放到 URL 中标识一次分享')


@router.post("/create", description="创建分享 Key", response_model=CreateShareRes)
async def create_share(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        payload: CreateShareReq,
):
    async with db as session:
        stmt = (select(Share)
                .where(Share.creator == user.username)
                .where(Share.expire_time > now()))
        result = await session.execute(stmt)
        # 防止重复创建链接，有效期内只创建一条链接
        for item in result.scalars().all():
            item: Share
            if item.result == payload.result:
                return CreateShareRes(data=item.share_key)
        share_key = nanoid.generate()
        share = Share(
            share_key=share_key,
            share_type=payload.share_type,
            params=payload.params,
            result=payload.result,
            creator=user.username,
        )
        session.add(share)
        await session.flush()
        await session.commit()
    return CreateShareRes(data=share_key)


class ShareItem(BaseModel):
    share_key: Annotated[str, Field(description='分享 Key，用于放到 URL 中标识一次分享')]
    share_type: Annotated[ShareType, Field(description="分享类型")]
    params: Annotated[dict[str, str | int | float | list[str]], Field(description="内容生成参数")]
    result: Annotated[dict[str, str | int | float], Field(description="内容生成结果")]


class GetShareRes(BaseModel):
    code: str = '0000'
    msg: str = ''
    data: ShareItem | None = Field(default=None, description='分享数据')


def now():
    return datetime.now(tz=timezone(timedelta(hours=8)))


@router.get("/records", description="获取分享数据", response_model=GetShareRes)
async def get_share_data(
        share_key: Annotated[str, Query(alias='key', description="分享 Key")],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    async with db as session:
        stmt = (select(Share)
                .where(Share.share_key == share_key)
                .where(Share.expire_time > now()))
        result = await session.execute(stmt)
        share: Share | None = result.scalar_one_or_none()
        await session.commit()
    if share is None:
        raise ClientVisibleException("分享 Key 不存在")
    params = share.params
    if isinstance(params, str):
        params = json.loads(params)
    res = GetShareRes()
    res.data = ShareItem(
        share_key=share.share_key,
        share_type=share.share_type,
        params=params,
        result=share.result,
    )
    return res
