<script lang="ts" setup>
import { computed, nextTick, onMounted, onUnmounted, onUpdated, ref, watch } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import mila from 'markdown-it-link-attributes';
import mdKatex from '@traptitech/markdown-it-katex';
import DOMPurify from 'dompurify';
// import { viewImage } from '../../../../utils/uploadimage';
import { debounce, unescape } from 'lodash';
import { $t } from '@/locales';
import { getIsMobile } from '@/utils/mobile';

function loadHighlightStyle(isDark: boolean) {
  const existingStyles = document.querySelectorAll('link[data-highlight-style]');
  existingStyles.forEach(style => style.remove());

  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.setAttribute('data-highlight-style', 'true');

  if (isDark) {
    link.href = new URL('highlight.js/styles/github-dark.css', import.meta.url).href;
  } else {
    link.href = new URL('highlight.js/styles/github.css', import.meta.url).href;
  }

  document.head.appendChild(link);
}

function setupThemeWatcher() {
  const isDark = document.documentElement.classList.contains('dark');
  loadHighlightStyle(isDark);

  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.attributeName === 'class') {
        const currentIsDark = document.documentElement.classList.contains('dark');
        loadHighlightStyle(currentIsDark);
      }
    });
  });

  observer.observe(document.documentElement, { attributes: true });
  return observer;
}

function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

const mdi = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
  highlight(code, language) {
    const validLang = Boolean(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang);
    }
    return highlightBlock(hljs.highlightAuto(code).value, '');
  }
});

mdi.use(mila, {
  attrs: {
    target: '_blank',
    rel: 'noopener'
  }
});

mdi.use(mdKatex, {
  throwOnError: false,
  errorColor: '#cc0000',
  output: 'html',
  blockClass: 'katexmath-block rounded-md p-[10px]',
  trust: true,
  strict: false,
  macros: {
    '\\RR': '\\mathbb{R}',
    '\\NN': '\\mathbb{N}',
    '\\ZZ': '\\mathbb{Z}'
  }
});

// 添加 think 标签处理插件
mdi.use(md => {
  const thinkRule = (state: any) => {
    const tokens = state.tokens;
    for (let i = 0; i < tokens.length; i++) {
      if (tokens[i].type === 'inline' && tokens[i].content) {
        // 处理未闭合的 think 标签
        tokens[i].content = tokens[i].content.replace(/<think>(?![^<]*<\/think>)/g, '<think></think>');

        // 处理完整的 think 标签对
        const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
        if (thinkRegex.test(tokens[i].content)) {
          tokens[i].content = tokens[i].content.replace(thinkRegex, (_: string, p1: string) => {
            return `<div class="think-block">${p1}</div>`;
          });
          tokens[i].children = null; // 防止进一步处理
        }
      }
    }
    return true;
  };

  md.core.ruler.push('custom_think', thinkRule);
});

interface File {
  type: string;
  url: string;
}

interface Props {
  inversion?: boolean;
  error?: boolean;
  text?: string;
  loading?: boolean;
  asRawText?: boolean;
  files?: File[];
  // 新增属性：是否启用流式渲染
  streamRendering?: boolean;
  isMobile?: boolean;
}

const props = defineProps<Props>();

const textRef = ref<HTMLElement>();
// 用于流式渲染的当前显示文本
const currentDisplayText = ref('');
// 渲染队列
const renderQueue = ref<string[]>([]);
// 是否正在渲染
const isRendering = ref(false);
// 上一次渲染的时间戳
const lastRenderTime = ref(0);
// 渲染间隔（毫秒）
const RENDER_INTERVAL = 10; // 渲染频率10ms
const RENDER_BATCH_SIZE = 1; // 每次渲染的字符数

const wrapClass = computed(() => {
  return [
    'text-wrap',
    'min-w-[20px]',
    'rounded-md',
    // 根据条件添加样式
    props.inversion ? 'bg-[#d2f9d1]' : '',
    props.inversion ? 'dark:bg-[#a1dc95]' : '',
    props.inversion ? 'message-request' : 'message-reply',
    { 'text-red-500': props.error }
  ];
});

const imgGenerationRegex = /<aichat-img-generation>([\S\s]*?)<\/aichat-img-generation>/g;
const generatedImages = computed<string[]>(() => {
  let content = props.text;
  if (props.streamRendering && !props.inversion) {
    content = currentDisplayText.value;
  }
  if (!content) {
    return [];
  }
  const matches = content.matchAll(imgGenerationRegex);
  let result: string[] = [];
  for (const match of matches) {
    const group = match[1];
    const decodedContent = unescape(group);
    try {
      const json = JSON.parse(decodedContent);
      result = [...result, ...json];
    } catch (e) {
      console.error(e);
    }
  }
  return result;
});

const imgGeneratingRegex = /<aichat-img-generating>([\S\s]*?)<\/aichat-img-generating>([\S\s]?)/g;
const imageGeneratingClass = computed<string[]>(() => {
  let content = props.text;
  if (props.streamRendering && !props.inversion) {
    content = currentDisplayText.value;
  }
  if (!content) {
    return [];
  }
  const matches = content.matchAll(imgGeneratingRegex);
  const result: string[] = [];
  for (const match of matches) {
    if (match[2]) {
      // eslint-disable-next-line no-continue
      continue;
    }
    const group = match[1];
    const decodedContent = unescape(group);
    switch (decodedContent) {
      case '1:1':
        result.push('w-48 h-48');
        break;
      case '16:9':
        result.push('w-85.33333 h-48');
        break;
      case '9:16':
        result.push('w-27 h-48');
        break;
      default:
        result.push('w-48 h-48');
        break;
    }
  }
  return result.slice(generatedImages.value.length);
});

const searchTagRegex = /<aichat-web-search>[\S\s]*?<\/aichat-web-search>(\S)/g;

const WebSearchResultPattern = /<aichat-web-search-result>([\S\s]+?)<\/aichat-web-search-result>/g;
type WebSearchItem = {
  title: string;
  link: string;
  desc: string;
  icon: string;
  media: string;
  refId: number;
};
const webSearchResult = computed<WebSearchItem[]>(() => {
  let content = props.text;
  if (props.streamRendering && !props.inversion) {
    content = currentDisplayText.value;
  }
  if (!content) {
    return [];
  }
  const matches = content.matchAll(WebSearchResultPattern);
  let result: WebSearchItem[] = [];
  for (const match of matches) {
    const group = match[1];
    const decodedContent = unescape(group);
    const json = JSON.parse(decodedContent);
    result = [...result, ...json];
  }
  return result;
});

// 将一些不合法的 a 标签改写掉，之后用点击事件来处理
const invalid_link_regex = /<a\s+[^>]*href\s*=\s*["']([^"']*)["'][^>]*>([^<]*)<\/a>/gi;
const filter_invalid_link = (content: string) => {
  return content.replace(invalid_link_regex, (match, p1, p2) => {
    const url = decodeURIComponent(p1);
    // 只有 http 开头的链接才允许展示
    if (url.startsWith('http')) {
      return match;
    }
    return `<a data-href="${url}" href="#">${p2}</a>`;
  });
};
const refRegexes = [/网页(\d+)/];
const handleInvalidLinkClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (target.tagName.toLowerCase() !== 'a') {
    return;
  }
  const href = target.dataset.href;
  if (!href) {
    return;
  }
  for (const regex of refRegexes) {
    const match = href.match(regex);
    if (!match) return;

    let refIndex = Number.parseInt(match[1], 10);
    if (Number.isNaN(refIndex)) return;

    refIndex -= 1;
    if (refIndex < 0 || refIndex >= webSearchResult.value.length) return;

    const refItem = webSearchResult.value[refIndex];
    if (!refItem) return;

    const { link } = refItem;
    window.open(link, '_blank');
  }
};

const text = computed(() => {
  // 如果启用了流式渲染，使用currentDisplayText
  if (props.streamRendering && !props.inversion) {
    let processedText = currentDisplayText.value;

    // 去掉搜索标签，只处理已经完成搜索的
    processedText = processedText.replace(searchTagRegex, '$1');

    // 处理所有 think 内容,包括未完成的
    processedText = processedText.replace(/(<think>)([\s\S]*?)(?:<\/think>|$)/g, (_match, _start, content) => {
      return `<div class="think-block">${content}</div>`;
    });

    if (!props.asRawText) return filter_invalid_link(mdi.render(processedText));
    return filter_invalid_link(DOMPurify.sanitize(processedText));
  }

  // 否则使用原来的逻辑
  let processedText = props.text ?? '';

  // 去掉搜索标签，只处理已经完成搜索的
  processedText = processedText.replace(searchTagRegex, '$1');

  // 处理所有 think 内容,包括未完成的
  processedText = processedText.replace(/(<think>)([\s\S]*?)(?:<\/think>|$)/g, (_match, _start, content) => {
    return `<div class="think-block">${content}</div>`;
  });

  if (!props.asRawText) return filter_invalid_link(mdi.render(processedText));
  return filter_invalid_link(DOMPurify.sanitize(processedText));
});

async function copyToClip(aa: string): Promise<void> {
  return await navigator.clipboard
    .writeText(aa)
    .then(() => {
      // console.log('Text copied to clipboard');
    })
    .catch(err => {
      // console.error('Failed to copy text: ', err);
      throw err;
    });
}

function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy');
    copyBtn.forEach(btn => {
      btn.addEventListener('click', () => {
        const code = btn.parentElement?.nextElementSibling?.textContent;
        if (code) {
          copyToClip(code).then(() => {
            btn.textContent = '复制成功';
            setTimeout(() => {
              btn.textContent = '复制代码';
            }, 1000);
          });
        }
      });
    });
  }
}

function removeCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy');
    copyBtn.forEach(btn => {
      btn.removeEventListener('click', () => {});
    });
  }
}

// 流式渲染函数
function processRenderQueue() {
  if (isRendering.value || renderQueue.value.length === 0) return;

  const now = Date.now();
  if (now - lastRenderTime.value < RENDER_INTERVAL) {
    // 如果距离上次渲染时间不足间隔，使用requestAnimationFrame安排下一次渲染
    requestAnimationFrame(processRenderQueue);
    return;
  }

  isRendering.value = true;
  lastRenderTime.value = now;

  // 从队列中取出一批字符进行渲染
  const batchSize = Math.min(RENDER_BATCH_SIZE, renderQueue.value.length);
  const nextChars = renderQueue.value.splice(0, batchSize);
  if (nextChars.length > 0) {
    currentDisplayText.value += nextChars.join('');
  }

  isRendering.value = false;

  // 如果队列中还有字符，继续处理
  if (renderQueue.value.length > 0) {
    requestAnimationFrame(processRenderQueue);
  }
}

// 将文本添加到渲染队列
function addToRenderQueue(newText: string) {
  // 将文本分解为单个字符并添加到队列
  const chars = newText.split('');
  renderQueue.value.push(...chars);

  // 开始处理渲染队列
  if (!isRendering.value) {
    processRenderQueue();
  }
}

let themeObserver: MutationObserver | null = null;

onMounted(() => {
  addCopyEvents();

  themeObserver = setupThemeWatcher();

  // 如果启用了流式渲染，初始化当前文本
  if (props.streamRendering && props.text) {
    currentDisplayText.value = '';
    addToRenderQueue(props.text);
  }
});

onUpdated(() => {
  addCopyEvents();
});

const imgViewer: any = null;
onUnmounted(() => {
  if (imgViewer) imgViewer.destroy();
  removeCopyEvents();

  if (themeObserver) {
    themeObserver.disconnect();
    themeObserver = null;
  }
});

// 监听文本内容变化
watch(
  () => props.text,
  (newText, oldText) => {
    if (props.streamRendering && !props.inversion) {
      if (!newText) {
        currentDisplayText.value = '';
        renderQueue.value = [];
        return;
      }

      // 如果是全新的文本，重置当前显示
      if (!oldText || oldText === '思考中...') {
        currentDisplayText.value = '';
        renderQueue.value = [];
        addToRenderQueue(newText);
      } else if (newText !== oldText) {
        // 如果文本有变化，只添加新增的部分
        const commonLength = getCommonPrefixLength(oldText, newText);
        const newContent = newText.substring(commonLength);
        if (newContent) {
          addToRenderQueue(newContent);
        }
      }
    }

    nextTick(() => {
      addCopyEvents();
    });
  },
  { immediate: true }
);

// 获取两个字符串的共同前缀长度
function getCommonPrefixLength(str1: string, str2: string): number {
  const minLength = Math.min(str1.length, str2.length);
  for (let i = 0; i < minLength; i++) {
    if (str1[i] !== str2[i]) {
      return i;
    }
  }
  return minLength;
}

const showWebSearchResult = ref(false);

const formatDesc = (desc: string) => {
  const content = desc.replace('\n', '<br>');
  return DOMPurify.sanitize(content);
};

const getMedia = (media: string, url: string) => {
  if (media) {
    return media;
  }
  if (!url) {
    return '';
  }
  return new URL(url).host;
};

const toggleWebSearchResultClick = () => {
  showWebSearchResult.value = !showWebSearchResult.value;
};

const showWebSearchResultPopover = ref(false);
const selectedWebSearchResultRef = ref<WebSearchItem | null>(null);
const webSearchResultRefPopoverPos = ref<[number, number]>([0, 0]);
const mouseInPopover = ref(false);

const refTags = ['web-search-ref', 'aichat-web-search-ref'];

const handleRefHover = debounce((e: MouseEvent) => {
  if (selectedWebSearchResultRef.value && !mouseInPopover.value) {
    showWebSearchResultPopover.value = false;
  }
  const target = e.target as HTMLElement;
  if (refTags.includes(target.tagName.toLowerCase())) {
    const textContent = target.textContent?.trim();
    if (!textContent) {
      return;
    }
    for (const item of webSearchResult.value) {
      if (item.refId.toString() === textContent) {
        const rect = target.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top;
        webSearchResultRefPopoverPos.value = [x, y];
        selectedWebSearchResultRef.value = item;
        showWebSearchResultPopover.value = true;
        return;
      }
    }
  }
}, 200);

const webSearchingText = computed(() => {
  return {
    '--searching-text': `'${$t('page.chat.webSearchingText')}'`
  };
});

const hasThinkBlock = computed(() => {
  return text.value.includes('<div class="think-block">');
});
const showThinkBlock = ref(!getIsMobile());
const thinkBlockDisplay = computed(() => {
  if (showThinkBlock.value) {
    return 'block';
  }
  return 'none';
});
</script>

<template>
  <div
    :class="wrapClass"
    class="w-fit"
    :style="webSearchingText"
    @mouseover="handleRefHover($event)"
    @click="handleInvalidLinkClick"
  >
    <div v-if="files?.length && inversion" class="mt-1 px-3">
      <NImage v-for="item in files" :key="item.url" :show-toolbar="false" :src="item.url" class="h-48 rounded" />
    </div>
    <div ref="textRef" class="break-words leading-relaxed" :style="{ '--think-block-display': thinkBlockDisplay }">
      <div v-if="!inversion">
        <NButton
          v-if="hasThinkBlock"
          secondary
          size="tiny"
          icon-placement="right"
          class="px-2"
          @click="showThinkBlock = !showThinkBlock"
        >
          <template #icon>
            <NIcon>
              <SvgIcon class="font-sm" :icon="showThinkBlock ? 'ic:baseline-unfold-less' : 'ic:baseline-unfold-more'" />
            </NIcon>
          </template>
          <span class="flex gap-1">
            <SvgIcon icon="mdi:lightbulb-on-10" />
            思考
          </span>
        </NButton>
        <div v-if="!asRawText" :key="streamRendering ? undefined : text" class="markdown-body" v-html="text" />
        <div v-else class="whitespace-pre-wrap">{{ streamRendering ? currentDisplayText : text }}</div>
        <div class="mt-4 flex flex-wrap gap-2">
          <NImage v-for="item in generatedImages" :key="item" :src="item" class="h-48 rounded" />
          <div v-for="c of imageGeneratingClass" :key="c" class="relative">
            <NSkeleton :class="c" :sharp="false" />
            <div class="absolute left-0 top-0 h-full w-full flex items-center justify-center">
              <NText>
                <SvgIcon icon="fluent:spinner-ios-16-filled" class="h-8 w-8 animate-spin" />
              </NText>
            </div>
          </div>
        </div>
      </div>
      <!--  whitespace-pre-wrap -->
      <div v-else class="px-2" v-html="text" />
    </div>
    <div v-if="files && !inversion">
      <NImage v-for="item in files" :key="item.url" :src="item.url" class="h-48 rounded" />
    </div>
    <div v-if="webSearchResult.length" class="mt-2">
      <NButton size="tiny" round icon-placement="right" @click="toggleWebSearchResultClick">
        <template #icon>
          <SvgIcon icon="weui:arrow-filled" />
        </template>
        <NText>{{ webSearchResult.length }} {{ $t('page.chat.webSearchResult') }}</NText>
      </NButton>
      <NDrawer
        v-model:show="showWebSearchResult"
        :placement="isMobile ? 'bottom' : 'right'"
        :width="isMobile ? undefined : '33vw'"
        :height="isMobile ? '66vh' : undefined"
      >
        <NDrawerContent :title="$t('page.chat.webSearchResultTitle')" closable>
          <NList hoverable>
            <NListItem v-for="item in webSearchResult" :key="item.refId">
              <a
                :href="item.link || 'javascript:void(0)'"
                :target="item.link ? '_blank' : ''"
                rel="noopener noreferrer"
              >
                <NText tag="h2" class="text-lg font-bold">
                  <NBadge :value="item.refId" type="info" class="mr-2" />
                  {{ item.title }}
                </NText>
                <NTooltip placement="left" :disabled="isMobile" scrollable>
                  <template #trigger>
                    <NText tag="p" class="line-clamp-2 my-1 text-sm text-zinc-600 dark:text-zinc-400">
                      {{ item.desc }}
                    </NText>
                  </template>
                  <NScrollbar class="max-h-[61.8vh]">
                    <div v-html="formatDesc(item.desc)" />
                  </NScrollbar>
                </NTooltip>
                <div class="text-zinc-600 dark:text-zinc-400">
                  <div v-if="item.link" class="flex items-center gap-2">
                    <NImage v-if="item.icon" :src="item.icon" class="h-4 w-4 rounded" :lazy="true" />
                    <SvgIcon v-else icon="mdi:file-search" />
                    <div>{{ getMedia(item.media, item.link) }}</div>
                  </div>
                </div>
              </a>
            </NListItem>
          </NList>
        </NDrawerContent>
      </NDrawer>
    </div>
    <NPopover
      :show="showWebSearchResultPopover"
      :x="webSearchResultRefPopoverPos[0]"
      :y="webSearchResultRefPopoverPos[1]"
      trigger="manual"
      class="max-w-96"
      scrollable
      @mouseenter="mouseInPopover = true"
      @mouseleave="mouseInPopover = false"
    >
      <template #header>
        <NText v-if="selectedWebSearchResultRef" tag="h2" class="font-bold">
          <a
            v-if="selectedWebSearchResultRef.link"
            :href="selectedWebSearchResultRef.link"
            class="hover:underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            {{ selectedWebSearchResultRef.title }}
          </a>
          <span v-else>
            {{ selectedWebSearchResultRef.title }}
          </span>
        </NText>
      </template>
      <template #footer>
        <NFlex
          v-if="selectedWebSearchResultRef && selectedWebSearchResultRef.link"
          justify="space-between"
          align="center"
        >
          <NFlex align="center">
            <NImage
              v-if="selectedWebSearchResultRef.icon"
              :src="selectedWebSearchResultRef.icon"
              class="h-4 w-4 rounded"
            />
            <SvgIcon v-else icon="mdi:file-search" />
            <div>{{ getMedia(selectedWebSearchResultRef.media, selectedWebSearchResultRef.link) }}</div>
          </NFlex>
          <a :href="selectedWebSearchResultRef.link" target="_blank" rel="noopener noreferrer">
            <SvgIcon icon="proicons:open" />
          </a>
        </NFlex>
      </template>
      <NText
        v-if="selectedWebSearchResultRef"
        tag="p"
        class="line-clamp-3 my-1 text-sm text-zinc-600 dark:text-zinc-400"
      >
        {{ selectedWebSearchResultRef.desc }}
      </NText>
    </NPopover>
  </div>
</template>

<style lang="less">
@import url(./style.less);

.markdown-body {
  color: #24292f;

  img {
    max-height: 240px;
  }

  .katex {
    font-size: 1.2em;
    text-indent: 0;

    .katex-html {
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 2px 0;
    }
  }

  .katex-display {
    margin: 1em 0;
    padding: 1em;
    text-align: center;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

html.dark .markdown-body {
  .katex-display {
    background: rgba(255, 255, 255, 0.05);
  }
  .katex {
    color: #e5e7eb;
  }
  color: #c9d1d9;
}

.think-block {
  display: var(--think-block-display);
  overflow: hidden;
}

@keyframes cursor-blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

aichat-web-search {
  @apply block my-1 w-fit rounded-lg bg-zinc-300/60 p-1 px-2 text-xs dark:bg-zinc-600/60 gap-x-1 before:content-["🌏"] before:mr-1 after:content-[var(--searching-text)] after:ml-1;
}

// web-search-ref 主要是为了兼容，之后不要在使用
web-search-ref,
aichat-web-search-ref {
  @apply px-1 text-xs bg-zinc-300/60 dark:bg-zinc-600/60 rounded mr-0.5 cursor-pointer hover:underline align-super;
}

// 隐藏搜索结果，结尾统一展示
aichat-web-search-result {
  @apply hidden;
}

// 隐藏搜索图片生成结果
aichat-img-generation {
  @apply hidden;
}

// 隐藏搜索图片生成中状态
aichat-img-generating {
  @apply hidden;
}
</style>
