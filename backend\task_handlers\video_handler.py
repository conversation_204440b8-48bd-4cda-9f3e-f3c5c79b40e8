import logging
import json
import httpx
from typing import Dict, Any

from config import app_settings
from models.tasks import Task, TaskStatus
from task_queue.task_manager import TaskManager


logger = logging.getLogger(__name__)


# 环境变量
AI_SERVER = app_settings.ai_server
TENCENT_COS_HOST = app_settings.tencent_cos_host


# async def poll_task_status(task_id: str, max_retries: int = 10, retry_delay: int = 5) -> Dict[str, Any]:
#     """
#     轮询视频生成任务状态 (弃用)
    
#     参数:
#     - task_id: 任务ID
#     - max_retries: 最大重试次数
#     - retry_delay: 每次重试间隔（秒）
    
#     返回:
#     - 任务状态响应
#     """
#     api_endpoint = f"{AI_SERVER}/framepack/tasks/{task_id}"
#     retries = 0
    
#     logger.info(f"开始轮询任务状态: {task_id}")
    
#     while retries < max_retries:
#         try:
#             async with httpx.AsyncClient(timeout=30.0) as client:
#                 response = await client.get(api_endpoint)
#                 response.raise_for_status()
                
#                 task_status = response.json()
#                 status = task_status.get("status", "")
                
#                 # 如果任务完成或失败，返回最终状态
#                 if status in ["completed", "failed", "canceled"]:
#                     logger.info(f"任务 {task_id} 已完成，状态: {status}")
#                     return task_status
                
#                 # 任务仍在处理中，记录进度
#                 progress = task_status.get("progress", 0)
#                 logger.info(f"任务 {task_id} 进度: {progress}%")
                
#                 retries += 1
#                 await asyncio.sleep(retry_delay)
#         except Exception as e:
#             logger.error(f"轮询任务 {task_id} 状态时出错: {e}", exc_info=True)
#             retries += 1
#             await asyncio.sleep(retry_delay)
    
#     # 超过最大重试次数，返回错误
#     return {
#         "status": "failed",
#         "error": "轮询任务状态超时"
#     }


async def handle_video_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理视频生成任务
    
    参数:
    - task_data: 任务数据，包含以下字段:
      - db_task_id: 数据库任务ID
      - taskid: 任务ID
      - username: 用户名
      - action: 任务动作
      - params: 任务参数
      - db: 数据库会话
    
    返回:
    - 处理结果字典，包含以下字段:
      - success: 处理是否成功
      - resource_url: 资源URL（如果成功）
      - result: 详细结果（如果成功）
      - error: 错误信息（如果失败）
    """
    # 解析任务数据
    db_task_id = task_data.get("db_task_id")
    taskid = task_data.get("taskid")
    username = task_data.get("username")
    action = task_data.get("action")
    params = task_data.get("params", {})
    db = task_data.get("db")
    
    try:
        logger.info(f"处理视频任务: TaskID={taskid}, Action={action}")
        
        # 准备API调用参数
        api_endpoint = f"{AI_SERVER}/framepack/generate"
        payload = {}
        
        # 根据动作类型设置不同的API端点和参数
        if action == "GENERATE":
            # 获取图像数据参数
            image_data = params.get("image_base64")

            logger.info(f"image_data: {image_data}")
            
            # 获取数据库任务对象
            task = await db.get(Task, db_task_id)
            
            # 如果image_base64不存在但prompt_media_url存在，使用prompt_media_url作为图像数据
            if not image_data and task and task.prompt_media_url:
                image_data = task.prompt_media_url
            
            # 验证URL格式，确保使用的是长期URL
            if image_data and isinstance(image_data, str) and TENCENT_COS_HOST and image_data.startswith(TENCENT_COS_HOST):
                if '/24_hours/' in image_data:
                    # 检测到临时URL，记录警告
                    logger.warning(f"检测到临时URL: {image_data}，应该在framepack.py中已经转换为长期URL")
                    
                    # 构造可能的长期URL以尝试恢复
                    cos_key = image_data.replace(TENCENT_COS_HOST, "")
                    new_cos_key = cos_key.replace('/24_hours/', '/1_years/')
                    new_url = TENCENT_COS_HOST + new_cos_key
                    
                    logger.info(f"使用构造的长期URL: {new_url}")
                    image_data = new_url
                else:
                    logger.info(f"验证通过：使用的是长期URL: {image_data}")
            
            # 构建请求体
            payload = {
                "image_base64": image_data,
                "prompt": params.get("translated_prompt", params.get("prompt", "")),
                "negative_prompt": params.get("negative_prompt", ""),
                "seed": params.get("seed", 31337),
                "total_second_length": params.get("total_second_length", 5.0),
                "latent_window_size": params.get("latent_window_size", 9),
                "steps": params.get("steps", 25),
                "cfg": params.get("cfg", 1.0),
                "gs": params.get("gs", 10.0),
                "rs": params.get("rs", 0.0),
                "gpu_memory_preservation": params.get("gpu_memory_preservation", 6.0),
                "use_teacache": params.get("use_teacache", True),
                "mp4_crf": params.get("mp4_crf", 16),
                "task_id": taskid
            }
            
            # 检查并记录发送给AI服务器的URL是否是长期URL
            if isinstance(payload["image_base64"], str) and '/24_hours/' in payload["image_base64"]:
                logger.warning(f"警告：发送给AI服务器的URL仍然包含临时路径: {payload['image_base64']}")
            else:
                logger.info(f"确认：发送给AI服务器的URL已使用长期路径: {payload['image_base64']}")
                
        else:
            return {
                "success": False,
                "error": f"不支持的任务动作: {action}"
            }
        
        # 调用AI服务生成视频并直接等待响应
        logger.info(f"向 {api_endpoint} 发送请求 for task {taskid}")
        async with httpx.AsyncClient(timeout=2000.0) as client:  # 33分钟超时
            response = await client.post(api_endpoint, json=payload)
            response.raise_for_status()  # 抛出4xx/5xx错误
            
            # 处理成功响应
            generate_response = response.json()
            logger.info(f"任务 {taskid} 完成: {generate_response}")
            
            # 从响应中提取结果
            task_id = generate_response.get("task_id")
            status = generate_response.get("status")
            message = generate_response.get("message", "")
            resource_url = generate_response.get("result_url")
            
            # 检查必要字段
            if not task_id:
                raise ValueError("外部API响应格式无效，缺少 'task_id' 字段")
            
            # 构建完整结果
            result_json = {
                "task_id": task_id,
                "status": status,
                "message": message,
                "result_url": resource_url
            }
            
            # 在更新任务状态前，先检查任务当前状态
            task = await db.get(Task, db_task_id)
            
            # 如果任务已经被取消，则不要覆盖其状态
            if task and task.status == TaskStatus.CANCELED:
                logger.info(f"任务 {taskid} 已被取消，不更新其状态")
                # 仅记录结果但不更改状态
                await TaskManager.update_task_result(
                    db=db,
                    task_id=db_task_id,
                    result=result_json,
                    update_status=False  # 添加参数以指示不更新状态
                )
                
                logger.warning(f"任务 {taskid} 已被取消但生成过程仍返回了结果，返回失败状态")
                return {
                    "success": False,  # 返回False表示不要更新任务状态
                    "error": "任务已被取消"
                }
            
            # 如果任务未被取消，则正常更新结果
            await TaskManager.update_task_result(
                db=db,
                task_id=db_task_id,
                result=result_json
            )
            
            # 检查任务是否成功
            if status == "completed" and resource_url:
                # 验证结果视频URL格式，确保使用的是长期URL
                if resource_url and TENCENT_COS_HOST and resource_url.startswith(TENCENT_COS_HOST):
                    if '/24_hours/' in resource_url:
                        # 检测到临时URL，记录警告
                        logger.warning(f"检测到结果视频临时URL: {resource_url}，应该在AI服务器返回长期URL")
                        
                        # 构造可能的长期URL以尝试恢复
                        result_cos_key = resource_url.replace(TENCENT_COS_HOST, "")
                        new_result_cos_key = result_cos_key.replace('/24_hours/', '/1_years/')
                        new_result_url = TENCENT_COS_HOST + new_result_cos_key
                        
                        logger.info(f"使用构造的结果视频长期URL: {new_result_url}")
                        
                        # 更新资源URL和结果JSON
                        resource_url = new_result_url
                        result_json["result_url"] = new_result_url
                        
                        # 更新数据库中的任务结果
                        await TaskManager.update_task_result(
                            db=db,
                            task_id=db_task_id,
                            result=result_json,
                            resource_url=new_result_url
                        )
                    else:
                        logger.info(f"验证通过：结果视频使用的是长期URL: {resource_url}")
                
                logger.info(f"任务 {taskid} 成功完成，返回有效的资源URL: {resource_url}")
                # 返回成功结果
                return {
                    "success": True,
                    "resource_url": resource_url,
                    "result": result_json
                }
            # 检查是否是已取消状态
            elif status == "canceled":
                logger.info(f"任务 {taskid} 的外部API响应状态为canceled，保持取消状态")
                # 更新数据库中的任务状态为已取消（直接在处理器中更新，而不是返回给调用者）
                await TaskManager.update_task_status(
                    db=db,
                    task_id=db_task_id,
                    status=TaskStatus.CANCELED,
                    result=result_json,
                    fail_reason="用户取消或外部系统取消"
                )
                
                # 返回特殊标记，表示任务已被外部确认取消
                return {
                    "success": False,
                    "error": "任务已被取消",
                    "task_already_canceled": True  # 添加特殊标记
                }
            else:
                # 任务未完成或未提供结果URL
                logger.warning(f"任务 {taskid} 未完成或未返回有效资源URL，状态: {status}, URL: {resource_url}")
                return {
                    "success": False,  # 修改为False，表示不要将任务状态更新为成功
                    "error": f"任务未完成或未返回有效的结果URL，状态: {status}"
                }
            
    except httpx.TimeoutException as timeout_err:
        logger.error(f"任务 {taskid} 调用外部API超时: {timeout_err}")
        logger.error(f"调用详情 - URL: {api_endpoint}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        
        return {
            "success": False,
            "error": f"生成视频超时"
        }
    except httpx.HTTPStatusError as http_err:
        logger.error(f"任务 {taskid} 调用外部API失败 (HTTP Status): {http_err.response.status_code} - {http_err.response.text}")
        logger.error(f"调用详情 - URL: {api_endpoint}, 状态码: {http_err.response.status_code}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        logger.error(f"响应头: {dict(http_err.response.headers)}")
        
        return {
            "success": False,
            "error": f"外部API返回错误状态"
        }
    except Exception as e:
        logger.error(f"处理任务 {taskid} 时发生内部错误: {e}", exc_info=True)
        if 'api_endpoint' in locals() and 'payload' in locals():
            logger.error(f"调用详情 - URL: {api_endpoint}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        
        return {
            "success": False,
            "error": f"处理任务时发生内部错误"
        } 
    

async def cancel_video_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    取消视频生成任务
    
    参数:
    - task_data: 任务数据，包含以下字段:
      - taskid: 任务ID
    
    返回:
    - 处理结果字典，包含以下字段:
      - success: 取消是否成功
      - message: 详细信息
      - error: 错误信息（如果失败）
    """
    # 解析任务数据
    taskid = task_data.get("taskid")
    
    try:
        logger.info(f"取消视频任务: TaskID={taskid}")
        
        # 准备API调用参数
        api_endpoint = f"{AI_SERVER}/task/cancel"
        
        # 构建请求体
        payload = {
            "task_id": taskid
        }
        
        # 调用AI服务取消任务
        logger.info(f"向 {api_endpoint} 发送请求取消任务 {taskid}")
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(api_endpoint, json=payload)
            response.raise_for_status()  # 抛出4xx/5xx错误
            
            # 处理成功响应
            cancel_response = response.json()
            logger.info(f"任务 {taskid} 取消响应: {cancel_response}")
            
            # 获取响应状态和消息
            status = cancel_response.get("status")
            message = cancel_response.get("message", "")
            
            # 检查是否成功
            if status == "success":
                return {
                    "success": True,
                    "message": message or "任务已成功取消"
                }
            else:
                return {
                    "success": False,
                    "error": message or "取消任务失败"
                }
            
    except httpx.TimeoutException as timeout_err:
        logger.error(f"取消任务 {taskid} 超时: {timeout_err}")
        
        return {
            "success": False,
            "error": "取消任务请求超时"
        }
    except httpx.HTTPStatusError as http_err:
        logger.error(f"取消任务 {taskid} 失败 (HTTP Status): {http_err.response.status_code} - {http_err.response.text}")
        
        return {
            "success": False,
            "error": f"取消任务请求失败: HTTP {http_err.response.status_code}"
        }
    except Exception as e:
        logger.error(f"取消任务 {taskid} 时发生内部错误: {e}", exc_info=True)
        
        return {
            "success": False,
            "error": f"取消任务时发生内部错误"
        }
