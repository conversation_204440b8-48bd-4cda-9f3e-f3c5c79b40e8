import asyncio
import json
import logging
from itertools import count
from typing import Annotated, Literal

from pydantic import BaseModel, Field

from .client import get_zhipu_client

logger = logging.getLogger(__name__)

_web_search_tool_desc = """"
Zhipu Web Search API 是一款聚合型的搜索引擎工具，可以通过参数使用不同的搜索引擎完成搜索任务。
""".strip()

_search_recency_filter_desc = """
搜索指定时间范围内的网页。可选的值如下：
- noLimit：不限制时间范围
- oneDay：过去一天
- oneWeek：过去一周
- oneMonth：过去一个月
- oneYear：过去一年
""".strip()

_content_size_desc = """
控制网页摘要的字数。可选值如下：
- medium：平衡模式，适用于大多数查询。 返回约 400-600 字。
- high：最大化上下文以提供更全面的回答，返回约 2500 字。
""".strip()

class WebSearchParams(BaseModel):
    search_query: Annotated[str, Field(description="需要进行搜索的内容，建议搜索 query 不超过 70 个字符。")]
    count: Annotated[int | None, Field(default=10, ge=1, le=50, description="返回结果的条数，范围在 1-50 之间。")]
    search_domain_filter: Annotated[str | None, Field(default=None, description="用于限定搜索结果的范围，仅返回指定白名单域名的内容。白名单：直接输入域名（如 'www.example.com'）")]
    search_recency_filter: Annotated[Literal["noLimit", "oneDay", "oneWeek", "oneMonth", "oneYear"], Field(default="noLimit", description=_search_recency_filter_desc)]
    content_size: Annotated[Literal['medium', 'high'], Field(default="medium", description=_content_size_desc)]

WEB_SEARCH_TOOL = {
    "type": "function",
    "function": {
        "name": "zhipu_web_search_api",
        "description": _web_search_tool_desc,
        "parameters": WebSearchParams.model_json_schema(),
        "strict": True
    }
}

async def do_web_search(content: str) -> list:
    """执行网络搜索"""
    try:
        params = WebSearchParams.model_validate_json(content)
    except ValueError as e:
        logger.error(f"Invalid web search params: {e}")
        raise
    client = get_zhipu_client()
    args = params.model_dump()
    logger.info(f"Start web search with params: {args}")
    res = await asyncio.to_thread(
        client.web_search.web_search,
        **args,
        search_engine='search_pro',
    )
    if res.search_result is None:
        return []
    logger.info(f"Found {len(res.search_result)} results from search engine.")
    return [i.model_dump_json() for i in res.search_result]


def web_search_result_formatter():
    c = count(1)
    def formatter(items: list[str]) -> str:
        result = []
        for item in items:
            try:
                deserialized = json.loads(item)
            except json.JSONDecodeError:
                logger.error(f'JSONDecodeError: {item}')
                continue
            result.append({
                "title": deserialized.get('title', ''),
                "link": deserialized.get('link', ''),
                "desc": deserialized.get('content', ''),
                "icon": deserialized.get('icon', ''),
                "media": deserialized.get('media', ''),
                "refId": next(c)
            })
        return json.dumps(result, ensure_ascii=False)
    return formatter
