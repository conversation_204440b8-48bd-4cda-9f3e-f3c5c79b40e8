import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import random
import asyncio
from collections import defaultdict
import time

from config import app_settings

logger = logging.getLogger(__name__)

def get_email_config():
    return {
        "host": app_settings.email_host,
        "port": app_settings.email_port,  # 确保端口号是整数
        "address": app_settings.email_addr,
        "password": app_settings.email_pass,
        "name": app_settings.email_name,
    }


def generate_verification_code(length=6):
    """生成指定长度的数字验证码"""
    return ''.join(random.choices('0123456789', k=length))


async def send_verification_email(to_email, verification_code):
    """
    发送验证码邮件
    """
    logger.info(f"开始发送验证码邮件到: {to_email}")
    # 获取邮件配置
    email_config = get_email_config()
    logger.info(f"邮件配置信息: host={email_config['host']}, port={email_config['port']}, address={email_config['address']}")

    # 验证配置完整性
    required_fields = ['host', 'port', 'address', 'password']
    missing_fields = [field for field in required_fields if not email_config.get(field)]
    if missing_fields:
        error_msg = f"邮件配置缺失: {', '.join(missing_fields)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    try:
        # 创建邮件内容
        message = MIMEMultipart()
        sender_address = email_config['address']
        sender_name = email_config.get('name', sender_address.split('@')[0])
        logger.info(f"发件人信息: name={sender_name}, address={sender_address}")

        # 设置邮件头
        message['From'] = f"{sender_name} <{sender_address}>"
        message['To'] = to_email
        message['X-Priority'] = '1'  # 设置邮件优先级
        message['X-MSMail-Priority'] = 'High'
        message['Importance'] = 'High'
        # 添加防止重复过滤的唯一标识
        message['Message-ID'] = f'<{verification_code}-{to_email}>'

        # 修改邮件主题，避免被识别为垃圾邮件
        message['Subject'] = f'AIHub 验证码 [{verification_code}]'
        logger.info("邮件头部设置完成")

        # 邮件正文
        body = f"""
        <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #333;">AIHub 邮箱验证</h2>
                    <p>尊敬的用户：</p>
                    <p>您的验证码是: <strong style="font-size: 24px; color: #1a73e8;">{verification_code}</strong></p>
                    <p>验证码有效期为2分钟，请尽快使用。</p>
                    <p style="color: #666;">如果这不是您的操作，请忽略此邮件。</p>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                    <p style="color: #999; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
                </div>
            </body>
        </html>
        """
        message.attach(MIMEText(body, 'html', 'utf-8'))
        logger.info("邮件正文设置完成")

        # 连接邮件服务器并发送
        logger.info(f"正在连接邮件服务器: {email_config['host']}:{email_config['port']}")
        with smtplib.SMTP_SSL(email_config['host'], email_config['port']) as server:
            logger.info("成功建立SSL连接")
            # 使用完整邮箱地址进行登录
            server.login(email_config['address'], email_config['password'])
            logger.info("邮件服务器登录成功")
            # 确保发件人地址与登录账号一致
            # 添加发送延迟，避免频率限制
            await asyncio.sleep(1)
            server.sendmail(email_config['address'], [to_email], message.as_string())
            logger.info(f"验证码邮件发送成功: {verification_code} -> {to_email}")

    except Exception as e:
        error_message = f"邮件发送失败: {str(e)}"
        logger.error(f"邮件发送详细错误: {error_message}")
        # 添加更多错误信息记录
        logger.error(f"收件人: {to_email}")
        logger.error(f"验证码: {verification_code}")
        logger.error(f"错误类型: {type(e).__name__}")
        raise Exception(error_message)

# 添加邮件发送频率限制
_email_send_records = defaultdict(list)
MIN_INTERVAL = 60  # 最小发送间隔（秒）

def check_email_frequency(email):
    """检查邮件发送频率"""
    current_time = time.time()
    # 清理过期记录
    _email_send_records[email] = [t for t in _email_send_records[email]
                                 if current_time - t < 3600]

    if _email_send_records[email]:
        last_send_time = _email_send_records[email][-1]
        if current_time - last_send_time < MIN_INTERVAL:
            raise Exception(f"发送过于频繁，请在{MIN_INTERVAL}秒后重试")

    _email_send_records[email].append(current_time)
    return True
