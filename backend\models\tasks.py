from utils.database import Base
from sqlalchemy import Column, Integer, String, Text, JSON, BigInteger, DateTime, Index, ForeignKey
from sqlalchemy.sql import func
from pydantic import ConfigDict, Field
from models.pydanticBase import BaseModel
from enum import Enum as EnumType
from datetime import datetime
from typing import Optional, Dict, Any, List


class TaskStatus(str, EnumType):
    """通用任务状态枚举"""
    NOT_START = "not_start"
    SUBMITTED = "submitted" 
    IN_PROGRESS = "in_progress"
    FAILURE = "failure"
    SUCCESS = "success"
    CANCELED = "canceled"


class TaskType(str, EnumType):
    """任务类型枚举"""
    MUSIC = "music"
    IMAGE = "image"
    VIDEO = "video"
    CHAT = "chat"
    # 可根据实际需求添加更多任务类型


class TaskOut(BaseModel):
    """
    任务输出模型
    """
    id: int
    taskid: str
    username: str
    task_type: str
    action: str
    status: str
    pid: Optional[int] = None
    queue_position: Optional[int] = 0
    submit_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    finish_time: Optional[datetime] = None
    fail_reason: Optional[str] = None
    task_params: Optional[Dict[str, Any]] = None
    task_result: Optional[Dict[str, Any]] = None
    prompt_media_url: Optional[str] = None
    resource_url: Optional[str] = None
    uptime: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)


class Task(Base):
    """通用任务数据库模型"""
    __tablename__ = "tasks"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    taskid = Column(String(64), nullable=False, comment="任务ID")
    username = Column(String(64), nullable=False, comment="用户名")
    task_type = Column(String(32), nullable=False, comment="任务类型(music/image/chat等)")
    action = Column(String(32), nullable=False, comment="任务动作")
    status = Column(String(20), nullable=False, default=TaskStatus.NOT_START, comment="任务状态")
    pid = Column(BigInteger, nullable=True, comment="父任务ID")
    queue_position = Column(Integer, nullable=True, default=0, comment="队列位置")
    submit_time = Column(DateTime, nullable=False, server_default=func.now(), comment="提交时间")
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    finish_time = Column(DateTime, nullable=True, comment="完成时间")
    fail_reason = Column(Text, nullable=True, comment="失败原因")
    task_params = Column(JSON, nullable=True, comment="任务参数(存储任务特定的参数)")
    task_result = Column(JSON, nullable=True, comment="任务结果(存储任务输出)")
    prompt_media_url = Column(Text, nullable=True, comment="prompt多媒体资源URL")
    resource_url = Column(Text, nullable=True, comment="任务生成的资源URL(音频/视频等)")
    uptime = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    model = Column(String(32), nullable=True, comment="模型名称")

    __table_args__ = (
        Index('idx_taskid', taskid),
        Index('idx_username', username),
        Index('idx_type_status', task_type, status),
    )
    
    def __repr__(self):
        return f"<Task(id={self.id}, username={self.username}, taskid={self.taskid}, type={self.task_type}, status={self.status})>" 