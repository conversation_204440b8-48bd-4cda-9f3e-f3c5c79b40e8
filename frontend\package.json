{"name": "soybean-admin", "type": "module", "version": "1.2.3", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLibCheck --incremental true --composite false", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.1.2", "@mojs/core": "^1.7.1", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@traptitech/markdown-it-katex": "^3.6.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "10.11.0", "ant-design-vue": "~4.2.3", "compressorjs": "^1.2.1", "cos-js-sdk-v5": "^1.8.7", "crypto-js": "4.2.0", "dayjs": "1.11.11", "dompurify": "^3.2.6", "echarts": "5.5.0", "event-source-polyfill": "^1.0.31", "gsap": "^3.12.5", "json-editor-vue": "^0.18.1", "lodash": "^4.17.21", "lodash-es": "4.17.21", "markdown-it": "^14.1.0", "mo.js": "^0.0.2", "mobile-detect": "^1.4.5", "mojs": "^0.2.0", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "2.1.7", "starback": "^2.1.6", "tailwind-merge": "^2.3.0", "vditor": "^3.10.4", "viewerjs": "^1.11.6", "vue": "3.4.27", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "0.5.0", "vue-drawing-canvas": "^1.0.14", "vue-i18n": "9.13.1", "vue-router": "4.3.2", "vue-waterfall-plugin-next": "^2.4.6", "wavesurfer.js": "^7.8.2", "xlsx": "^0.18.5"}, "devDependencies": {"@elegant-router/vue": "0.3.7", "@iconify/json": "2.2.217", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.3.6", "@types/crypto-js": "4.2.2", "@types/lodash-es": "4.17.12", "@types/markdown-it": "^14.1.1", "@types/markdown-it-link-attributes": "^3.0.5", "@types/node": "20.14.2", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.60.4", "@unocss/preset-icons": "0.60.4", "@unocss/preset-uno": "0.60.4", "@unocss/transformer-directives": "0.60.4", "@unocss/transformer-variant-group": "0.60.4", "@unocss/vite": "0.60.4", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "4.0.0", "eslint-plugin-vue": "9.26.0", "less": "^4.2.0", "markdown-it-link-attributes": "^4.0.1", "sass": "1.77.4", "simple-git-hooks": "2.11.1", "tsx": "4.12.0", "typescript": "5.4.5", "unplugin-icons": "0.19.0", "unplugin-vue-components": "0.27.0", "vite": "5.2.12", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.3.7", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.19"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://admin.soybeanjs.cn"}