'''
{
  "ref":"refs\/heads\/master",
  "before":"c7363acf4235e8e992d786adee238f230d3c9714",
  "after":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "compare_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/compare\/c7363acf4235e8e992d786adee238f230d3c9714...2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "commits":[
    {"id":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "message":"test\n",
  "url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/commit\/2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "author":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "committer":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "verification":null,"timestamp":"2024-06-21T14:43:52+08:00",
  "added":["backend\/test\/test.py"],"removed":[],"modified":[]}],
  "total_commits":1,
  "head_commit":{"id":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "message":"test\n","url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/commit\/2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "author":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "committer":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "verification":null,"timestamp":"2024-06-21T14:43:52+08:00",
  "added":["backend\/test\/test.py"],
  "removed":[],"modified":[]},
  "repository":{
    "id":1187,
    "owner":{
      "id":98,"login":"isystem","login_name":"","full_name":"","email":"",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/e0d5dd2ef1b1ac62337fc245f5587039",
      "language":"","is_admin":false,"last_login":"0001-01-01T00:00:00Z",
      "created":"2024-04-15T15:54:27+08:00","restricted":false,"active":false,
      "prohibit_login":false,"location":"","pronouns":"","website":"","description":"",
      "visibility":"private","followers_count":0,"following_count":0,"starred_repos_count":0,
      "username":"isystem"
    },
      "name":"ai-admin","full_name":"isystem\/ai-admin","description":"",
      "empty":false,"private":true,"fork":false,"template":false,"parent":null,"mirror":false,
      "size":862,"language":"","languages_url":"https:\/\/git.gdsre.cn\/api\/v1\/repos\/isystem\/ai-admin\/languages",
      "html_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin","url":"https:\/\/git.gdsre.cn\/api\/v1\/repos\/isystem\/ai-admin",
      "link":"","ssh_url":"<EMAIL>:isystem\/ai-admin.git","clone_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin.git",
      "original_url":"","website":"","stars_count":0,"forks_count":0,"watchers_count":7,"open_issues_count":0,"open_pr_counter":0,
      "release_counter":0,"default_branch":"master","archived":false,"created_at":"2024-06-21T10:13:24+08:00",
      "updated_at":"2024-06-21T14:36:48+08:00","archived_at":"1970-01-01T08:00:00+08:00","permissions":{"admin":true,"push":true,"pull":true},
      "has_issues":true,
      "internal_tracker":{"enable_time_tracker":true,"allow_only_contributors_to_track_time":true,"enable_issue_dependencies":true},
      "has_wiki":true,"wiki_branch":"master","has_pull_requests":true,
      "has_projects":true,"has_releases":true,"has_packages":true,
      "has_actions":true,"ignore_whitespace_conflicts":false,"allow_merge_commits":true,
      "allow_rebase":true,"allow_rebase_explicit":true,"allow_squash_merge":true,"allow_fast_forward_only_merge":true,
      "allow_rebase_update":true,"default_delete_branch_after_merge":false,"default_merge_style":"merge",
      "default_allow_maintainer_edit":false,"avatar_url":"","internal":false,"mirror_interval":"","object_format_name":"sha1",
      "mirror_updated":"0001-01-01T00:00:00Z","repo_transfer":null
    },
      "pusher":{
        "id":94,"login":"wuhuazhang","login_name":"","full_name":"\u5434\u534e\u7ae0",
      "email":"<EMAIL>,noreply.heyyogame.com,noreply.originmood.com",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/72a9d33dbfb67e314d183eb336ddcf6b",
      "language":"","is_admin":false,"last_login":"0001-01-01T00:00:00Z","created":"2024-04-11T17:33:22+08:00",
      "restricted":false,"active":false,"prohibit_login":false,"location":"","pronouns":"","website":"","description":"",
      "visibility":"limited","followers_count":0,"following_count":0,"starred_repos_count":0,"username":"wuhuazhang"
    },
      "sender":{
        "id":94,"login":"wuhuazhang","login_name":"","full_name":"\u5434\u534e\u7ae0",
      "email":"<EMAIL>,noreply.heyyogame.com,noreply.originmood.com",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/72a9d33dbfb67e314d183eb336ddcf6b","language":"","is_admin":false,
      "last_login":"0001-01-01T00:00:00Z","created":"2024-04-11T17:33:22+08:00","restricted":false,"active":false,
      "prohibit_login":false,"location":"","pronouns":"","website":"","description":"","visibility":"limited","followers_count":0,
      "following_count":0,"starred_repos_count":0,"username":"wuhuazhang"
      }
    }
'''

import time
import json
import datetime
import json
import traceback
import pika
import os
import threading
import requests
import sys

#test
exchange = 'osa-git-repo'
queue = 'osa-git-repo'
def connect_mq():
    user = 'om'
    if not user:
        raise Exception("rabbit mq user env not found.")
    credentials = pika.PlainCredentials(user, 'oknjiu')
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(
            '**********', credentials=credentials,
            socket_timeout=5
        )
    )
    channel = connection.channel()
    print('connected to rabbitmq')
    return channel



def handle_message(ch, method, properties, body):
    try:
        data = json.loads(body)
        # print(data)
        if not isinstance(data, dict) or not data.get('repository', None):
            ch.basic_ack(delivery_tag = method.delivery_tag)
            return

        if data['repository']['name'] == 'ai-admin':
            print("git pull ai-admin",flush=True)
            os.system("cd /data/web/ai-admin && git reset --hard && git pull > /tmp/git.log 2>&1")
            if 'commits' in data and len( data['commits'] ):
              print("send ding ding",flush=True)
              commit = data['commits'][0]
              with open('/tmp/git.log', 'r') as f:
                lines = f.readlines()
                print("log lines %s"%len(lines),flush=True)
                if len( lines ):
                  last_line = lines[-1]
                  print("last line : ",last_line,flush=True)
                  if 'up-to-date' not in last_line:
                    branch = data['ref'].split('/')[-1]
                    # send_msg = "ai-admin 发布\nbranch: %s\ncommitter: %s\nmessage: %s"%(data['ref'].split('/')[-1],commit['committer']['name'],commit['message'])
                    # print("send msg %s"%send_msg,flush=True)
                    sendDD(title="AI Admin (%s >> %s) 发布"%(commit['committer']['name'],branch),
                           msg=commit['message'],
                           messageUrl="https://git.gdsre.cn/isystem/ai-admin/commit/%s"%(commit['id'],))
            ch.basic_ack(delivery_tag = method.delivery_tag)

    except Exception as e:
        print(e)

def sendDD(title,msg,messageUrl):
    dtalk_rebot = "https://oapi.dingtalk.com/robot/send?access_token=483f6bc8db30d2e5f3c98af51b93104eeb207ea27f307a21061868b476e9661a"
    headers = {"Content-Type": "application/json"}
    parameter = {
        "msgtype": "link",
        "link": {
            "title": title,
            "text": msg,
            "messageUrl": messageUrl,
            "picUrl" : "https://xxfpfg-gg.gdsre.cn/images/202407/9EE44897-196D-4ee2-ABF4-DCC75403292A.png"
        }
    }
    print(parameter,flush=True)
    data = json.dumps(parameter)
    res = requests.post(dtalk_rebot, data=data, headers=headers)
    print(res.text,flush=True)

def consumer(queue):
    while True:
        print('start consumer..')
        try:
            channel = connect_mq()
            channel.basic_consume(
                queue=queue,
                on_message_callback=handle_message,
                auto_ack=False,
            )
            print("Waiting for messages. To exit press CTRL+C")
            channel.start_consuming()
        except pika.exceptions.StreamLostError as e:
            print(str(e))
            time.sleep(5)

            continue
        except pika.exceptions.ChannelWrongStateError as e:
            print("channel closed ,retry .", str(e))
            time.sleep(5)
            continue
        except pika.exceptions.AMQPConnectionError as e:
            print("conncetion error,retry .")
            time.sleep(5)
            continue
        except Exception as e:
            traceback.print_exc()
            time.sleep(5)
            continue
consumer(queue)
