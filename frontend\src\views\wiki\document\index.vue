<script setup lang="ts">
import { onActivated, onMounted } from 'vue';

// interface Props {
//   url: string;
// }

// defineProps<Props>();

onMounted(() => {
  console.log('mounted');
});

onActivated(() => {
  console.log('activated');
});
</script>

<template>
  <div class="h-full">
    <iframe
      id="iframePage"
      class="size-full"
      src="https://yvcl5y3vugs.feishu.cn/wiki/KdsywzaZaiRgVhkD7SEcRKuFnee?from=from_copylink"
    ></iframe>
  </div>
</template>

<style scoped></style>
