import asyncio
import logging

from config import app_settings
from models.chat import chatContent
from typing import List
from http import HTTPStatus
import dashscope
from dashscope import Generation
from ..openai.prompts import get_system_prompt


dashscope.api_key = app_settings.bailian_api_key
logger = logging.getLogger(__name__)


def chat(
    model_name: str,
    contents: List[chatContent],
    systemPrompt: str = "",
    memory: str = "",
    temperature: float = 0.8,
    mcp: bool = False,
    web_search: bool = False,
    loop: asyncio.AbstractEventLoop = None,
):

    system_prompt = get_system_prompt(model_name, memory)

    if systemPrompt:
        system_prompt = systemPrompt

    messages = [{"role": "system", "content": system_prompt}]

    for r in contents:
        content = {
            "role": "user" if r.role == "user" else "assistant",
            "content": [{"type": "text", "text": r.content}],
        }
        if r.files:
            for f in r.files:
                if f.type == "image":
                    content["content"].append(
                        {"type": "image_url", "image_url": {"url": f.url}}
                    )
        messages.append(content)
    # 要发送的消息

    completion = Generation.call(
        model_name,
        messages=messages,
        result_format="message",  # 设置输出为'message'格式
        stream=True,  # 设置输出方式为流式输出
        incremental_output=True,  # 增量式流式输出
    )

    for chunk in completion:
        """
        chunk:{"status_code": 200, "request_id": "942ec5de-f9a1-9d59-9e6d-bb45ac00f8d1", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "null", "message": {"role": "assistant", "content": "Hello"}}]}, "usage": {"input_tokens": 76, "output_tokens": 1, "total_tokens": 77}}
        """
        logger.debug(chunk)
        yield (
            chunk["output"]["choices"][0]["message"]["content"]
            if chunk.status_code == HTTPStatus.OK
            else chunk.message
        )

    yield (
        {
            "input_tokens": chunk["usage"]["input_tokens"],
            "output_tokens": chunk["usage"]["output_tokens"],
        }
    )
