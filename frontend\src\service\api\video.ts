import { request } from '../request';

// 视频生成请求参数接口
export interface VideoGenerationParams {
  image_base64: string;
  prompt: string;
  negative_prompt?: string;
  seed?: number;
  total_second_length?: number;
  latent_window_size?: number;
  steps?: number;
  cfg?: number;
  gs?: number;
  rs?: number;
  gpu_memory_preservation?: number;
  use_teacache?: boolean;
  mp4_crf?: number;
  model?: string;
}

// 任务响应接口
export interface VideoTaskResponse {
  code: string;
  data?: {
    id: number;
    taskid: string;
    username: string;
    status: string;
    action: string;
    submit_time: string | null;
    start_time?: string | null;
    finish_time?: string | null;
    queue_position: number;
    fail_reason?: string;
    prompt: string;
    video_data?: any;
    video_url?: string;
    task_params?: any;
    prompt_media_url?: string;
    model?: string;
  } | null;
  msg?: string | null;
}

// 历史记录响应接口
export interface VideoHistoryResponse {
  code: string;
  data?: VideoHistoryItem[] | null;
  msg?: string | null;
}

// 历史记录项目接口
export interface VideoHistoryItem {
  id: number;
  taskid: string;
  username: string;
  status: string;
  action: string;
  submit_time: string | null;
  start_time: string | null;
  finish_time: string | null;
  queue_position: number;
  fail_reason?: string;
  prompt: string;
  task_params?: any;
  video_data?: any;
  video_url?: string;
  model?: string;
}

// 取消任务响应接口
export interface CancelTaskResponse {
  code: string;
  msg?: string | null;
}

// 删除任务响应接口
export interface DeleteTaskResponse {
  code: string;
  msg?: string | null;
}

/**
 * 生成视频 (FramePack模型)
 *
 * @param params - 视频生成参数
 */
export function generateFramepackVideo(params: VideoGenerationParams) {
  return request<VideoTaskResponse>({
    url: '/framepack/generate',
    method: 'post',
    data: params
  });
}

/**
 * 生成视频 (即梦AI模型)
 *
 * @param params - 视频生成参数
 */
export function generateVolcengineVideo(params: VideoGenerationParams) {
  return request<VideoTaskResponse>({
    url: '/volcengine/generate',
    method: 'post',
    data: params
  });
}

/**
 * 获取视频生成历史记录
 *
 * @param page - 页码，从1开始
 * @param size - 每页数量
 */
export function getVideoHistory(page: number = 1, size: number = 10) {
  return request<VideoHistoryResponse>({
    url: '/framepack/history',
    method: 'get',
    params: { page, size }
  });
}

/**
 * 获取视频任务状态
 *
 * @param taskid - 任务ID
 */
export function getVideoTaskStatus(taskid: string) {
  return request<VideoTaskResponse>({
    url: `/framepack/task_status/${taskid}`,
    method: 'get'
  });
}

/**
 * 取消视频生成任务
 *
 * @param taskid - 任务ID
 */
export function cancelVideoTask(taskid: string) {
  return request<CancelTaskResponse>({
    url: '/framepack/tasks/cancel',
    method: 'post',
    data: { taskid }
  });
}

/**
 * 删除视频生成任务
 *
 * @param taskid - 任务ID
 */
export function deleteVideoTask(taskid: string) {
  return request<DeleteTaskResponse>({
    url: '/framepack/tasks/delete',
    method: 'post',
    data: { taskid }
  });
}
