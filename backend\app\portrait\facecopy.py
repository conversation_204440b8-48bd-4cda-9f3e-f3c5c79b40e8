import logging

import httpx
import random
import time
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
import magic  # 用于判断文件类型
import json
import base64
from typing import Optional
from config import app_settings
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取远程服务的IP地址
remote_ip = app_settings.ai_server
server_url = f"{remote_ip}:8965"


class ResponseModel(BaseModel):
  code: str
  data: dict
  msg: str


class PromptData(BaseModel):
  imagename: str
  videoname: str
  animation_region: Optional[str] = None  # 可选参数
  driving_option: Optional[str] = None  # 可选参数
  driving_multiplier: Optional[float] = 1.0  # 可选参数，默认值为1.0


@router.post("/upload", response_model=ResponseModel, tags=["facecopy"])
async def upload(file: UploadFile = File(...)):
  """
  将文件上传至资源机
  """
  try:
    # 读取文件的二进制数据
    file_data = await file.read()

    # 生成文件名称
    current_time = int(time.time())
    random_number = random.randint(10000, 99999)
    file_extension = magic.from_buffer(file_data, mime=True).split('/')[-1]
    file_name = f"{current_time}_{random_number}.{file_extension}"

    # 构建请求体
    files = {'image': (file_name, file_data, 'application/octet-stream')}

    # 发送 POST 请求到远程服务器
    async with httpx.AsyncClient() as client:
      response = await client.post(f"{server_url}/upload/image", files=files)

    # 检查响应状态码
    if response.status_code == 200:
      return ResponseModel(code="0000", data={"filename": file_name}, msg="上传成功")
    else:
      logger.error(f"上传失败，远程服务器返回状态码：{response.status_code}")
      raise ClientVisibleException("上传失败，请重试")

  except Exception as e:
    logger.error(f"上传文件时发生错误: {e}")
    raise ClientVisibleException("上传失败，请重试") from e


@router.post("/send_prompt", response_model=ResponseModel, tags=["facecopy"])
async def send_prompt(prompt_data: PromptData):
  """
  发送生成请求到远程服务器
  """
  try:
    # 读取JSON文件
    with open('data/facecopy/workflow_api.json', 'r') as file:
      workflow = json.load(file)

    # 打印调试信息
    logger.debug(f"prompt_data.imagename:{prompt_data.imagename}")
    logger.debug(f"prompt_data.videoname:{prompt_data.videoname}")

    # 替换占位符
    workflow["4"]["inputs"]["image"] = prompt_data.imagename
    workflow["8"]["inputs"]["video"] = prompt_data.videoname

    # 构建请求体
    payload = {"prompt": workflow}

    # 发送 POST 请求到远程服务器
    async with httpx.AsyncClient() as client:
      response = await client.post(f"{server_url}/prompt", json=payload)

    # 检查响应状态码
    if response.status_code == 200:
      return ResponseModel(code="0000", data=response.json(), msg="请求成功")
    else:
      logger.error(f"请求失败，远程服务器返回状态码：{response.status_code}")
      raise ClientVisibleException("请求失败，请重试")

  except Exception as e:
    logger.error(f"发送请求时发生错误: {e}")
    raise ClientVisibleException("请求失败，请重试") from e


@router.get("/queue_status", response_model=ResponseModel, tags=["facecopy"])
async def get_queue_status():
  """
  获取服务器当前剩余任务列队的数量
  """
  try:
    async with httpx.AsyncClient() as client:
      response = await client.get(f"{server_url}/prompt")

    if response.status_code == 200:
      data = response.json()
      return ResponseModel(code="0000", data=data, msg="请求成功")
    else:
      logger.error(f"获取队列状态时发生错误: {response.status_code}")
      raise ClientVisibleException("获取失败，请重试")
  except Exception as e:
    logger.error(f"获取队列状态时发生错误: {e}")
    raise ClientVisibleException("获取失败，请重试") from e


@router.get("/history/{prompt_id}", response_model=ResponseModel, tags=["facecopy"])
async def get_history(prompt_id: str):
  """
  获取历史任务数据（根据任务ID获取历史数据）
  """
  try:
    async with httpx.AsyncClient() as client:
      response = await client.get(f"{server_url}/history/{prompt_id}")
      if response.status_code == 200:
        data = response.json()
        # 检查是否有数据返回
        if data:
          # 任务已完成，尝试获取文件名
          if prompt_id in data:
            task_data = data[prompt_id]
            if "outputs" in task_data and "32" in task_data["outputs"]:
              filename = task_data["outputs"]["32"]["gifs"][0]["filename"]
              return ResponseModel(code="0000", data={"filename": filename}, msg="任务已完成")
            else:
              return ResponseModel(code="0000", data={"error": "error"}, msg="任务数据结构异常")
          else:
            return ResponseModel(code="0000", data={"error": "error"}, msg="未找到对应的任务数据")
        else:
          # 任务未完成或不存在
          return ResponseModel(code="0000", data={}, msg="任务未完成或不存在")
      else:
        logger.error(f"请求失败，远程服务器返回状态码：{response.status_code}")
        raise ClientVisibleException("请求失败")
  except Exception as e:
    # 异常情况下返回code="0000"，并在data中添加error字段
    logger.error(f"获取历史数据时发生错误: {e}")
    raise ClientVisibleException("获取失败，请重试") from e


@router.get("/view", response_model=ResponseModel, tags=["facecopy"])
async def view_image(filename: str):
  """
    生成结果预览
    """
  try:
    params = {
      "filename": filename,
      "type": "output"
    }
    async with httpx.AsyncClient() as client:
      response = await client.get(f"{server_url}/view", params=params)

    if response.status_code == 200:
      # 获取内容类型
      content_type = response.headers.get("content-type", "application/octet-stream")

      # 将二进制数据转换为Base64编码
      base64_data = base64.b64encode(response.content).decode('utf-8')

      # 构建响应数据
      data = {
        "file_content": base64_data,
        "content_type": content_type,
        "filename": filename
      }

      return ResponseModel(code="0000", data=data, msg="请求成功")
    else:
      logger.error(f"获取图片预览时发生错误: {response.status_code}")
      raise ClientVisibleException("预览失败，请重试")
  except Exception as e:
    logger.error(f"获取图片预览时发生错误: {e}")
    raise ClientVisibleException("预览失败，请重试") from e


"""
官方项目推理,不走comfyui
"""
facecopy_url = f"{remote_ip}/facecopy/isvedio"  # 人物模型推理
# facecopy_url = f"http://************:5002/facecopy/isvedio"  # 动物模型推理


@router.post("/isvedio", response_model=ResponseModel, tags=["facecopy"])
async def generate_video(request: PromptData):
  """
    如果用户上传的预处理文件为视频格式，则不使用comfyui进行处理，而是使用远端服务进行本地命令行操作以提升处理速度
    """
  try:
    logger.debug(f"request.imagename:{request.imagename}")
    logger.debug(f"request.videoname:{request.videoname}")
    async with httpx.AsyncClient(timeout=120) as client:
      response = await client.post(facecopy_url, json=request.dict())

    if response.status_code == 200:
      # 获取内容类型
      content_type = response.headers.get("content-type", "application/octet-stream")

      logger.info(f"response.content:{response.content}")

      # 解析响应内容中的JSON数据
      response_data = json.loads(response.content)
      
      # 获取URL
      url = response_data.get("b64")
      
      return ResponseModel(code="0000", data={"file": url, "content_type": content_type}, msg="生成成功")
    else:
      logger.error(f"生成视频失败，远程服务器返回状态码：{response.status_code}")
      raise ClientVisibleException("生成失败，请重试")

  except Exception as e:
    logger.error(f"生成视频时发生错误: {e}")
    raise ClientVisibleException("生成失败，请重试") from e


# 数据上传资源机 不走comfyui api
@router.post("/Upload", response_model=ResponseModel, tags=["facecopy"])
async def upload_file(file: UploadFile = File(...)):
  try:
    # 读取文件的二进制数据
    file_data = await file.read()

    # 生成文件名称
    current_time = int(time.time())
    random_number = random.randint(10000, 99999)
    file_extension = file.filename.split('.')[-1]  # 使用上传文件的扩展名
    file_name = f"{current_time}_{random_number}.{file_extension}"

    logger.debug(file_name)

    # 将二进制数据编码为base64字符串
    file_data_base64 = base64.b64encode(file_data).decode('utf-8')

    # 准备上传到远程服务的数据
    upload_url = f"{remote_ip}/facecopy/no_comfyui_upload"
    # upload_url = f"http://************:5002/facecopy/no_comfyui_upload"

    logger.debug(f"upload_url:{upload_url}")
    payload = {
      "file_name": file_name,
      "file_data": file_data_base64  # 使用base64字符串
    }

    # 发送 POST 请求到远程服务器
    async with httpx.AsyncClient(timeout=30) as client:
      response = await client.post(upload_url, json=payload)
      if response.status_code != 200:
        # 更详细的错误处理
        error_detail = response.text
        logger.error(f"Error from remote server: {error_detail}")
        raise ClientVisibleException("上传失败,请重新上传")

    # 返回成功信息
    return ResponseModel(code="0000", data={"filename": file_name}, msg="上传成功")

  except HTTPException as http_exc:
    raise ClientVisibleException("上传失败,请重新上传") from http_exc
  except Exception as exc:
    logger.error(f"Unexpected exception: {str(exc)}")
    raise ClientVisibleException("上传失败,请重新上传") from exc


# 动物模型推理
facecopy_animal_url = f"{remote_ip}/facecopy/isAnimal"
# facecopy_animal_url = f"http://************:5002/facecopy/isAnimal"


@router.post("/isAnimal", response_model=ResponseModel, tags=["facecopy"])
async def generate_animalvideo(request: PromptData):
  try:
    logger.debug(f"request.imagename:{request.imagename}")
    logger.debug(f"request.videoname:{request.videoname}")
    async with httpx.AsyncClient(timeout=120) as client:
      response = await client.post(facecopy_animal_url, json=request.dict())

    if response.status_code == 200:
      # 获取内容类型
      content_type = response.headers.get("content-type", "application/octet-stream")

      # 解析响应内容中的JSON数据
      response_data = json.loads(response.content)
      
      # 获取URL
      url = response_data.get("b64")

      return ResponseModel(code="0000", data={"file": url, "content_type": content_type}, msg="生成成功")
    else:
      logger.error(f"生成视频失败，远程服务器返回状态码：{response.status_code}")
      raise ClientVisibleException("生成失败，请重试")

  except Exception as e:
    logger.error(f"生成视频时发生错误: {e}")
    raise ClientVisibleException("生成失败，请重试") from e
