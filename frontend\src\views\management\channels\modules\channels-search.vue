<script setup lang="ts">
import { reactive } from 'vue';
import { NButton, NForm, NFormItemGi, NGrid, NInput, NSelect } from 'naive-ui';

interface SearchParams {
  channel_name?: string;
  permission?: string; // public/private
}

// 向外抛出搜索事件
const emit = defineEmits<{
  (e: 'search', params: SearchParams): void;
}>();

// 搜索表单
const model = reactive<SearchParams>({
  channel_name: '',
  permission: ''
});

// 权限选项
const permissionOptions = [
  { label: '公共频道', value: 'public' },
  { label: '私有频道', value: 'private' }
];

// 点击搜索按钮
function handleSearch() {
  emit('search', { ...model });
}
</script>

<template>
  <NCard size="small">
    <NForm label-placement="left" :inline="true" :label-width="80" class="mt-4">
      <NGrid cols="24" x-gap="12">
        <NFormItemGi label="频道名称" path="channel_name" span="6">
          <NInput v-model:value="model.channel_name" placeholder="请输入频道名称" />
        </NFormItemGi>
        <NFormItemGi label="权限" path="permission" span="6">
          <NSelect v-model:value="model.permission" :options="permissionOptions" placeholder="请选择" clearable />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="handleSearch">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
