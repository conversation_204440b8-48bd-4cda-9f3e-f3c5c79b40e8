<script lang="ts" setup>
import { ref } from 'vue';
import PasswordModal from './password-modal.vue';
const passwordModalVisible = ref(false);
</script>

<template>
  <div>
    <PasswordModal v-model:visible="passwordModalVisible" />

    <NGrid cols="1" responsive="screen" class="h-full -mt-3">
      <NGridItem>
        <NList hoverable clickable bordered>
          <NListItem class="cursor-pointer" @click="passwordModalVisible = true">
            <NThing title="账户密码">
              <template #description><span class="text-gray-400">修改账户密码</span></template>
            </NThing>
            <template #suffix>
              <NButton type="primary" size="medium">修改</NButton>
            </template>
          </NListItem>

          <!--
 <NListItem>
            <template #suffix>
              <NButton type="primary" text disabled>修改</NButton>
            </template>
            <NThing title="绑定手机">
              <template #description><span class="text-gray-400">已绑定手机号：+86189****4877</span></template>
            </NThing>
          </NListItem>
-->

          <!--
 <NListItem>
            <template #suffix>
              <NButton type="primary" text disabled>设置</NButton>
            </template>
            <NThing title="密保问题">
              <template #description>
                <span class="text-gray-400">未设置密保问题，密保问题可有效保护账户安全</span>
              </template>
            </NThing>
          </NListItem>
-->

          <!--
 <NListItem>
            <template #suffix>
              <NButton type="primary" disabled text>修改</NButton>
            </template>
            <NThing title="个性域名">
              <template #description><span class="text-gray-400">已绑定域名：https://www.naiveui.com</span></template>
            </NThing>
          </NListItem>
-->
        </NList>
      </NGridItem>
    </NGrid>
  </div>
</template>

<style lang="scss" scoped></style>
