import logging
from typing import Any, Dict
import json
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from models.system_settings import SystemSettings
from utils.redis import redis
from utils.database import get_db
from config import app_settings

logger = logging.getLogger(__name__)

class ConfigLoader:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigLoader, cls).__new__(cls)
        return cls._instance

    @classmethod
    async def initialize(cls) -> None:
        """初始化配置加载器"""
        if cls._initialized:
            logger.info("配置加载器已初始化，跳过")
            return
        
        try:
            logger.info("开始初始化系统配置...")
            instance = cls()
            async for db in get_db():
                await instance._load_configs(db)
                break  # 只需加载一次
            
            cls._initialized = True
        except Exception as e:
            # 不抛出异常，允许应用继续启动
            logger.error(f"系统配置初始化失败，使用默认配置启动系统: {str(e)}")

    async def _load_configs(self, db: AsyncSession) -> None:
        """从数据库加载配置"""
        # 如果 Field 有 alias，那么 wait_merge 的 Key 就是 alias，否则就是 Field Name
        wait_merge: dict[str, Any] = {}
        try:
            # 查询所有启用的系统配置
            query = select(SystemSettings).where(SystemSettings.status == True)
            result = await db.execute(query)
            settings = result.scalars().all()
            
            logger.info(f"从数据库查询到 {len(settings)} 条系统配置")

            for setting in settings:
                try:
                    value = self._convert_value(setting.config_value, setting.data_type)

                    for field_name, field in app_settings.model_fields.items():
                        if field.alias and field.alias.lower() == setting.config_key.lower():
                            wait_merge[field.alias] = value
                            logger.info(f"配置项 {setting.config_key} 匹配 Field Alias：{field.alias}")
                            break
                        elif field_name.lower() == setting.config_key.lower():
                            wait_merge[field_name] = value
                            logger.info(f"配置项 {setting.config_key} 匹配 Field Name：{field_name}")
                            break
                    else:
                        logger.warning(f"配置项 {setting.config_key} 没有找到匹配的 Field，忽略")
                        continue

                    # 如果配置需要缓存，则存入Redis
                    if setting.cached and value is not None:
                        await redis.set(f"config:{setting.config_key}", str(value))
                        logger.info(f"配置项 {setting.config_key} 已载入缓存")
                except Exception as e:
                    logger.error(f"配置 {setting.config_key} 加载失败: {str(e)}")
                    continue

            # 有更改就更新
            if wait_merge:
                # 先检查是否合法
                current_settings = app_settings.model_dump(by_alias=True)
                current_settings.update(wait_merge)
                new_settings = app_settings.model_validate(current_settings)
                alias_map = dict()
                for fn, f in app_settings.model_fields.items():
                    if f.alias:
                        alias_map[f.alias] = fn
                    else:
                        alias_map[fn] = fn
                cnt = 0
                # 合法的那就赋值回去
                for key, value in wait_merge.items():
                    field_name = alias_map[key]
                    new_value = getattr(new_settings, field_name)
                    setattr(app_settings, field_name, new_value)
                    logger.debug(f"已加载配置: {key}")
                    cnt += 1
                logger.info(f"成功从数据库加载 {cnt} 条系统配置")
        except Exception as e:
            logger.error(f"加载系统配置失败: {str(e)}")
            raise

    @staticmethod
    def _convert_value(value: str, data_type: str) -> Any:
        """根据数据类型转换配置值"""
        if value is None:
            return None
            
        try:
            if data_type == "number":
                return float(value) if '.' in value else int(value)
            elif data_type == "boolean":
                return value.lower() in ('true', '1', 'yes')
            elif data_type == "json":
                return json.loads(value)
            else:  # string, password 等其他类型
                return value
        except Exception as e:
            logger.error(f"配置值转换失败: {str(e)}")
            return value