<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';
import {
  type Setting,
  type SettingCreate,
  type SettingGroup,
  type SettingGroupCreate,
  fetchCreateSetting,
  fetchCreateSettingGroup,
  fetchGetGroupSettings,
  fetchGetSettingGroups,
  fetchUpdateSetting
} from '@/service/api/system-manage';

const message = useMessage();
const formRef = ref<FormInst | null>(null);
const activeTab = ref('');
const groups = ref<SettingGroup[]>([]);
const settings = ref<Setting[]>([]);
const loading = ref(false);
const showAddModal = ref(false);
const showAddGroupModal = ref(false);
const showEditModal = ref(false);
const currentGroupId = ref<number>(0);
const editingSetting = ref<Setting>({
  id: 0,
  group_id: 0,
  config_key: '',
  config_value: '',
  data_type: 'string',
  form_type: 'input',
  description: '',
  placeholder: '',
  options: '[]',
  is_required: true,
  is_sensitive: false,
  seq: 0,
  status: true
});
const newSetting = ref<Partial<Setting>>({
  config_key: '',
  config_value: '',
  data_type: 'string',
  form_type: 'input',
  description: '',
  placeholder: '',
  options: '[]',
  is_required: true,
  is_sensitive: false,
  seq: 0,
  status: true
});

const newGroup = ref<SettingGroupCreate>({
  group_code: '',
  group_name: '',
  description: '',
  seq: 0,
  status: true
});

const formTypes = [
  { label: '输入框', value: 'input' },
  { label: '文本域', value: 'textarea' },
  { label: '下拉选择', value: 'select' },
  { label: '开关', value: 'switch' }
];

const dataTypes = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: '数组', value: 'array' },
  { label: '对象', value: 'object' }
];

const rules: FormRules = {
  config_value: {
    required: true,
    message: '请输入配置值',
    trigger: 'blur',
    validator: (_rule, value) => {
      // 如果是布尔值(开关)，直接返回true
      if (typeof value === 'boolean') {
        return true;
      }
      // 其他类型值，检查是否为空
      return Boolean(value);
    }
  }
};

const newSettingRules: FormRules = {
  config_key: {
    required: true,
    message: '请输入配置键',
    trigger: 'blur'
  },
  description: {
    required: true,
    message: '请输入配置描述',
    trigger: 'blur'
  }
};

const newGroupRules: FormRules = {
  group_code: {
    required: true,
    message: '请输入板块名称  如`Text`',
    trigger: 'blur'
  },
  group_name: {
    required: true,
    message: '请输入分组名称,如`OpenAI`',
    trigger: 'blur'
  }
};

const loadSettings = async (groupId: number) => {
  loading.value = true;
  try {
    const response = await fetchGetGroupSettings(groupId);
    if (response.data && Array.isArray(response.data)) {
      settings.value = response.data.map(setting => {
        if (setting.form_type === 'switch') {
          if (setting.config_value === 'true') {
            setting.config_value = true;
          } else if (setting.config_value === 'false') {
            setting.config_value = false;
          }
        }
        return setting;
      });
      currentGroupId.value = groupId;
    } else {
      settings.value = [];
      message.error('加载配置项失败');
    }
  } catch (error) {
    message.error('加载配置项失败');
    console.error('加载配置项失败:', error);
  } finally {
    loading.value = false;
  }
};

const loadGroups = async () => {
  try {
    const response = await fetchGetSettingGroups();
    if (response.data && Array.isArray(response.data)) {
      groups.value = response.data;
      if (groups.value.length > 0) {
        activeTab.value = groups.value[0].group_code;
        await loadSettings(groups.value[0].id);
      }
    } else {
      groups.value = [];
      message.error('加载配置分组失败');
    }
  } catch (error) {
    message.error('加载配置分组失败');
    console.error('加载配置分组失败:', error);
  }
};

const handleTabChange = async (key: string) => {
  const group = groups.value.find(g => g.group_code === key);
  if (group) {
    await loadSettings(group.id);
  }
};

const handleSubmit = async () => {
  try {
    const promises = settings.value.map(setting => {
      // 创建一个配置项的拷贝
      const settingToSubmit = { ...setting };

      // 将布尔值转换为字符串
      if (typeof settingToSubmit.config_value === 'boolean') {
        settingToSubmit.config_value = String(settingToSubmit.config_value);
      }

      return fetchUpdateSetting(settingToSubmit.id, settingToSubmit).catch(err => {
        message.error(`保存 ${setting.config_key} 失败: ${err.message}`);
        return null;
      });
    });

    const results = await Promise.all(promises);
    const allSuccess = results.every(r => r);
    if (allSuccess) {
      message.success('保存成功');
    } else {
      const successCount = results.filter(r => r).length;
      message.warning(`部分配置保存成功(${successCount}/${settings.value.length})`);
    }
  } catch (error) {
    message.error('保存失败，请检查填写内容');
    console.error('保存失败:', error);
  }
};

const openAddModal = () => {
  newSetting.value = {
    config_key: '',
    config_value: '',
    data_type: 'string',
    form_type: 'input',
    description: '',
    placeholder: '',
    options: '[]',
    is_required: true,
    is_sensitive: false,
    seq: settings.value.length > 0 ? Math.max(...settings.value.map(s => s.seq)) + 1 : 0,
    status: true
  };
  showAddModal.value = true;
};

const openAddGroupModal = () => {
  newGroup.value = {
    group_code: '',
    group_name: '',
    description: '',
    seq: groups.value.length > 0 ? Math.max(...groups.value.map(g => g.seq)) + 1 : 0,
    status: true
  };
  showAddGroupModal.value = true;
};

const openEditModal = (setting: Setting) => {
  editingSetting.value = { ...setting };
  showEditModal.value = true;
};

const handleAddSetting = async () => {
  try {
    if (newSetting.value.config_key && newSetting.value.description) {
      const addSettingFormRef = document.getElementById('addSettingForm') as any;
      if (addSettingFormRef && addSettingFormRef.validate) {
        await addSettingFormRef.validate();
      }

      const settingToSubmit = { ...newSetting.value };

      // 将布尔值转换为字符串
      if (typeof settingToSubmit.config_value === 'boolean') {
        settingToSubmit.config_value = String(settingToSubmit.config_value);
      }

      const response = await fetchCreateSetting({
        ...settingToSubmit,
        group_id: currentGroupId.value
      } as SettingCreate);

      if (response.data) {
        message.success('添加配置项成功');
        showAddModal.value = false;
        await loadSettings(currentGroupId.value);
      } else {
        message.error('添加配置项失败');
      }
    } else {
      message.error('请填写完整的配置项信息');
    }
  } catch (error) {
    message.error('添加配置项失败');
    console.error('添加配置项失败:', error);
  }
};

const handleEditSetting = async () => {
  try {
    const editSettingFormRef = document.getElementById('editSettingForm') as any;
    if (editSettingFormRef && editSettingFormRef.validate) {
      await editSettingFormRef.validate();
    }

    const settingToSubmit = { ...editingSetting.value };

    // 将布尔值转换为字符串
    if (typeof settingToSubmit.config_value === 'boolean') {
      settingToSubmit.config_value = String(settingToSubmit.config_value);
    }

    const response = await fetchUpdateSetting(settingToSubmit.id, settingToSubmit);

    if (response.data) {
      message.success('编辑配置项成功');
      showEditModal.value = false;
      await loadSettings(currentGroupId.value);
    } else {
      message.error('编辑配置项失败');
    }
  } catch (error) {
    message.error('编辑配置项失败');
    console.error('编辑配置项失败:', error);
  }
};

const handleAddGroup = async () => {
  try {
    const addGroupFormRef = document.getElementById('addGroupForm') as any;
    if (addGroupFormRef && addGroupFormRef.validate) {
      await addGroupFormRef.validate();
    }

    const response = await fetchCreateSettingGroup(newGroup.value);

    if (response.data) {
      message.success('添加配置分组成功');
      showAddGroupModal.value = false;
      await loadGroups();
    } else {
      message.error('添加配置分组失败');
    }
  } catch (error) {
    message.error('添加配置分组失败');
    console.error('添加配置分组失败:', error);
  }
};

onMounted(() => {
  loadGroups();
});
</script>

<template>
  <div class="server-setting">
    <NCard>
      <div class="mb-4 flex items-center justify-between">
        <h2>系统配置</h2>
        <NButton type="primary" @click="openAddGroupModal">添加配置组</NButton>
      </div>

      <NTabs v-model:value="activeTab" type="line" animated placement="left" @update:value="handleTabChange">
        <NTabPane v-for="group in groups" :key="group.group_code" :name="group.group_code" :tab="group.group_name">
          <NScrollbar class="max-h-[calc(80vh-19em)]">
            <div class="mb-4 flex items-center justify-between">
              <h3>{{ group.description || group.group_name }}</h3>
              <NButton type="primary" @click="openAddModal">添加配置项</NButton>
            </div>

            <NForm
              ref="formRef"
              :model="settings"
              :rules="rules"
              label-placement="left"
              label-width="200"
              label-align="right"
              require-mark-placement="right-hanging"
              class="config-form"
            >
              <NSpace vertical>
                <NFormItem
                  v-for="setting in settings"
                  :key="setting.id"
                  :label="setting.description"
                  :path="`[${settings.indexOf(setting)}].config_value`"
                  :rule="setting.is_required ? rules.config_value : undefined"
                >
                  <div class="flex items-center">
                    <NInput
                      v-if="setting.form_type === 'input'"
                      v-model:value="setting.config_value"
                      :type="setting.is_sensitive ? 'password' : 'text'"
                      :placeholder="setting.placeholder"
                      class="flex-1"
                    />
                    <NInput
                      v-else-if="setting.form_type === 'textarea'"
                      v-model:value="setting.config_value"
                      :placeholder="setting.placeholder"
                      type="textarea"
                      class="flex-1"
                    />
                    <NSelect
                      v-else-if="setting.form_type === 'select'"
                      v-model:value="setting.config_value"
                      :options="JSON.parse(setting.options || '[]')"
                      :placeholder="setting.placeholder"
                      class="flex-1"
                    />
                    <NSwitch
                      v-else-if="setting.form_type === 'switch'"
                      v-model:value="setting.config_value"
                      :checked="String(setting.config_value) === 'true'"
                      @update:value="(value: any) => (setting.config_value = value)"
                    />

                    <NButton class="ml-2" quaternary circle type="info" @click="openEditModal(setting)">
                      <SvgIcon icon="mdi:pencil" />
                    </NButton>
                  </div>
                </NFormItem>
              </NSpace>
            </NForm>

            <div class="mt-4">
              <NButton type="primary" @click="handleSubmit">保存配置</NButton>
            </div>
          </NScrollbar>
        </NTabPane>
      </NTabs>
    </NCard>

    <NModal v-model:show="showAddModal" preset="dialog" title="添加配置项" class="config-modal">
      <NForm
        id="addSettingForm"
        :model="newSetting"
        :rules="newSettingRules"
        label-placement="left"
        label-width="100"
        label-align="right"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="配置键" path="config_key">
          <NInput v-model:value="newSetting.config_key" placeholder="请输入配置键" />
        </NFormItem>
        <NFormItem label="配置描述" path="description">
          <NInput v-model:value="newSetting.description" placeholder="请输入配置描述" />
        </NFormItem>
        <NFormItem label="配置类型" path="data_type">
          <NSelect v-model:value="newSetting.data_type" :options="dataTypes" placeholder="请选择配置类型" />
        </NFormItem>
        <NFormItem label="表单类型" path="form_type">
          <NSelect v-model:value="newSetting.form_type" :options="formTypes" placeholder="请选择表单类型" />
        </NFormItem>
        <NFormItem label="配置默认值" path="config_value">
          <NInput v-model:value="newSetting.config_value" placeholder="请输入配置默认值" />
        </NFormItem>
        <NFormItem label="提示文本" path="placeholder">
          <NInput v-model:value="newSetting.placeholder" placeholder="请输入提示文本" />
        </NFormItem>
        <NFormItem v-if="newSetting.form_type === 'select'" label="选项配置" path="options">
          <NInput
            v-model:value="newSetting.options"
            type="textarea"
            placeholder='[{"label":"选项1","value":"value1"},{"label":"选项2","value":"value2"}]'
          />
        </NFormItem>
        <NFormItem label="是否必填" path="is_required">
          <NSwitch v-model:value="newSetting.is_required" />
        </NFormItem>
        <NFormItem label="是否敏感" path="is_sensitive">
          <NSwitch v-model:value="newSetting.is_sensitive" />
        </NFormItem>
      </NForm>
      <template #action>
        <NButton @click="showAddModal = false">取消</NButton>
        <NButton type="primary" @click="handleAddSetting">确认</NButton>
      </template>
    </NModal>

    <NModal v-model:show="showEditModal" preset="dialog" title="编辑配置项" class="config-modal">
      <NForm
        id="editSettingForm"
        :model="editingSetting"
        :rules="newSettingRules"
        label-placement="left"
        label-width="100"
        label-align="right"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="配置键" path="config_key">
          <NInput v-model:value="editingSetting.config_key" placeholder="请输入配置键" />
        </NFormItem>
        <NFormItem label="配置描述" path="description">
          <NInput v-model:value="editingSetting.description" placeholder="请输入配置描述" />
        </NFormItem>
        <NFormItem label="配置类型" path="data_type">
          <NSelect v-model:value="editingSetting.data_type" :options="dataTypes" placeholder="请选择配置类型" />
        </NFormItem>
        <NFormItem label="表单类型" path="form_type">
          <NSelect v-model:value="editingSetting.form_type" :options="formTypes" placeholder="请选择表单类型" />
        </NFormItem>
        <NFormItem label="配置默认值" path="config_value">
          <NInput v-model:value="editingSetting.config_value" placeholder="请输入配置默认值" />
        </NFormItem>
        <NFormItem label="提示文本" path="placeholder">
          <NInput v-model:value="editingSetting.placeholder" placeholder="请输入提示文本" />
        </NFormItem>
        <NFormItem v-if="editingSetting.form_type === 'select'" label="选项配置" path="options">
          <NInput
            v-model:value="editingSetting.options"
            type="textarea"
            placeholder='[{"label":"选项1","value":"value1"},{"label":"选项2","value":"value2"}]'
          />
        </NFormItem>
        <NFormItem label="是否必填" path="is_required">
          <NSwitch v-model:value="editingSetting.is_required" />
        </NFormItem>
        <NFormItem label="是否敏感" path="is_sensitive">
          <NSwitch v-model:value="editingSetting.is_sensitive" />
        </NFormItem>
      </NForm>
      <template #action>
        <NButton @click="showEditModal = false">取消</NButton>
        <NButton type="primary" @click="handleEditSetting">保存</NButton>
      </template>
    </NModal>

    <NModal v-model:show="showAddGroupModal" preset="dialog" title="添加配置组" class="config-modal">
      <NForm
        id="addGroupForm"
        :model="newGroup"
        :rules="newGroupRules"
        label-placement="left"
        label-width="100"
        label-align="right"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="板块名称" path="group_code">
          <NInput v-model:value="newGroup.group_code" placeholder="请输入板块名称，如`Text`" />
        </NFormItem>
        <NFormItem label="分组名称" path="group_name">
          <NInput v-model:value="newGroup.group_name" placeholder="请输入分组名称，如`OpenAI`" />
        </NFormItem>
        <NFormItem label="分组描述" path="description">
          <NInput v-model:value="newGroup.description" placeholder="请输入分组描述" />
        </NFormItem>
      </NForm>
      <template #action>
        <NButton @click="showAddGroupModal = false">取消</NButton>
        <NButton type="primary" @click="handleAddGroup">确认</NButton>
      </template>
    </NModal>
  </div>
</template>

<style lang="scss" scoped>
.server-setting {
  padding: 16px;

  .mt-4 {
    margin-top: 16px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .flex-1 {
    flex: 1;
    min-width: 350px; /* 确保输入框有一个较大的最小宽度 */
  }

  .config-form {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }

  /* 弹窗表单样式优化 */
  :deep(.n-modal) {
    .n-form {
      width: 100%;

      .n-form-item {
        width: 100%;
        margin-right: 0;

        .n-form-item-feedback-wrapper {
          min-height: 20px;
        }

        .n-input,
        .n-select {
          width: 100%;
        }
      }
    }

    .n-modal-body {
      padding: 16px 24px;
    }
  }

  .config-modal {
    width: 100%;
    max-width: 600px;
  }
}
</style>
