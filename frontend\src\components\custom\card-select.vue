<script lang="ts" setup>
import { computed, ref } from 'vue';
import { darkTheme } from 'naive-ui';
import { useThemeStore } from '@/store/modules/theme';

defineOptions({
  name: 'CardSelect'
});
interface Props {
  value: string | number;
  modelValue: string | number;
  image: string;
  label: string;
  desc: string;
}
const props = defineProps<Props>();

interface Emits {
  (e: 'update:modelValue', value: string | number): void;
}
const emit = defineEmits<Emits>();
const themeStore = useThemeStore();
const naiveDarkTheme = computed(() => (themeStore.darkMode ? darkTheme : undefined));
const isSelected = computed(() => props.modelValue === props.value);

const select = () => {
  emit('update:modelValue', props.value);
};
</script>

<template>
  <NConfigProvider :theme="naiveDarkTheme">
    <div
      class="custom-radio m-2 flex cursor-pointer items-center b rounded p-2 hover:(b-primary)"
      :class="[{ 'b-primary bg-gradient-to-r from-[#bfe2f7] ': isSelected }]"
      @click="select"
    >
      <NAvatar round class="mr-10px" :src="image" />
      <div class="radio-content flex flex-col">
        <h3>
          <NEllipsis :line-clamp="1">
            {{ label }}
          </NEllipsis>
        </h3>
        <div class="min-h-23px">
          <div v-if="$slots.desc">
            <slot name="desc"></slot>
          </div>
          <div v-else>
            <NEllipsis :line-clamp="1">
              {{ desc }}
            </NEllipsis>
          </div>
        </div>
      </div>
    </div>
  </NConfigProvider>
</template>

<style scoped>
/* .custom-radio {
  transition:
    transform 0.3s,
    border-color 0.3s;
} */
</style>
