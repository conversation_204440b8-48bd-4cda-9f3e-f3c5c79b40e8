<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { fetchAllUserCompany } from '@/service/api';
// import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
defineOptions({
  name: 'StaticSearch'
});

interface Emits {
  (e: 'search'): void;
}
const emit = defineEmits<Emits>();

const { formRef } = useNaiveForm();

const filterType: CommonType.Option[] = [
  { label: '按日', value: 'date' },
  { label: '按月', value: 'month' },
  { label: '按年', value: 'year' }
];
const companyOption = ref<CommonType.Option[]>([]);
const company = ref('');

const model = defineModel<Api.Statis.TaskStatisSearchParams>('model', {
  required: true
});

onMounted(() => {
  fetchAllUserCompany().then(({ error, data }) => {
    if (!error) {
      const tmp: CommonType.Option[] = [];
      data.records.forEach(e => {
        company.value = e;
        tmp.push({
          value: e,
          label: e
        });
      });

      // 如果只有一条记录，则去掉 "所有公司" 选项
      if (data.records.length === 1) {
        companyOption.value = tmp;
      } else {
        companyOption.value = [
          {
            value: '',
            label: '所有公司'
          },
          ...tmp
        ];
      }
    }
  });
});

watch(
  () => model,
  () => {
    emit('search');
  },
  {
    deep: true
  }
);
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper pt-5">
    <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
      <NSpace>
        <NFormItem>
          <NSelect v-model:value="model.filter_type" :options="filterType" class="w-30" />
        </NFormItem>

        <NFormItem>
          <NDatePicker
            v-if="model.filter_type === 'month'"
            v-model:formatted-value="model.month"
            type="month"
            format="y-M"
            year-format="y"
            month-format="M"
            value-format="yyyyMM"
          />
          <NDatePicker
            v-if="model.filter_type === 'date'"
            v-model:formatted-value="model.date"
            type="date"
            format="y-M-dd"
            year-format="y"
            month-format="M"
            value-format="yyyyMMdd"
          />
        </NFormItem>
        <NFormItem>
          <NSelect
            v-if="companyOption.length > 1"
            v-model:value="model.company"
            :options="companyOption"
            class="w-30"
            placeholder="筛选公司"
          />
          <NInput v-else v-model:value="company" disabled />
        </NFormItem>
        <!--
 <NFormItem>
          <NButton type="primary" ghost @click="search">
            <template #icon>
              <icon-ic-round-search class="text-icon" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </NFormItem>
-->
      </NSpace>
    </NForm>
  </NCard>
</template>

<style scoped></style>
