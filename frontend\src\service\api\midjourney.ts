import { request } from '../request';

// 定义 ButtonAction
interface ButtonAction {
  emoji: string; // 按钮的 emoji 图标
  label: string; // 按钮的显示标签
  customId: string; // 按钮的自定义 ID，用于区分操作
}

// 定义 ButtonType
export interface ButtonType {
  type: string; // 任务类型，如 "JOB", "Inpaint", "Outpaint" 等
  type_name: string; // 任务类型名称
  actions: ButtonAction[]; // 对应的操作按钮数组
}

// 定义任务返回类型
export interface ChatMjTask {
  id: number;
  pid: number | null;
  username: string;
  taskid: number;
  action: string;
  status: string;
  prompt: string;
  prompt_en: string;
  description: string;
  state: string;
  submit_time: string;
  start_time: string | null;
  finish_time: string | null;
  image_url: string | null;
  image_urls?: string[] | null;
  progress: string | null;
  fail_reason: string | null;
  uptime: string;
  timestamp?: string;
  date?: string;
  total?: number;
  seed?: string;
  button?: ButtonType[];
  channels?: number;
  like_count?: number;
  is_like?: number;
  model?: string;
  manufacturer?: string;
  prompt_img?: string[];
  prompt_img_oss?: string[];
  expected_count?: number; // 期望生成的图片数量
}

// 作品集
export interface WorksTask {
  id: number;
  pid: number | null;
  username: string;
  taskid: number;
  action: string;
  status: string;
  prompt: string;
  image_url: string | null;
  progress: string | null;
  fail_reason: string | null;
}

export interface WorksTasksResponse {
  tasks: WorksTask[];
  total_page: number;
}

// 提交绘画任务
export function imagine(payload: { prompt: string; base64Array?: string[]; channels?: number; taskid?: number }) {
  return request<ChatMjTask>({
    url: '/midjourney/submit/imagine',
    method: 'post',
    data: payload
  });
}

// 提交绘画形变任务
export function change(payload: { taskid: number; action: string; index?: number; channels?: number }) {
  return request<ChatMjTask>({
    url: '/midjourney/submit/change',
    method: 'post',
    data: payload
  });
}

// 获取任务状态
export function getTaskState(taskid: number) {
  return request<ChatMjTask>({
    url: '/midjourney/get_task_state',
    method: 'get',
    params: { taskid }
  });
}

// 获取用户的任务列表
export function getMyTasks(page: number = 1, page_size: number = 10, channels: number = 0) {
  return request<ChatMjTask[]>({
    url: '/midjourney/get_my_tasks',
    method: 'get',
    params: { page, page_size, channels }
  });
}

// 获取搜索后的任务列表
export function getSearchTasks(search_prompt: string, page: number = 1, page_size: number = 10) {
  return request<ChatMjTask[]>({
    url: '/midjourney/get_search_tasks',
    method: 'get',
    params: { search_prompt, page, page_size }
  });
}

// 获取作品任务列表
export function getWorksTasks(page: number = 1, page_size: number = 10) {
  return request<WorksTasksResponse>({
    url: '/midjourney/get_works_tasks',
    method: 'get',
    params: { page, page_size }
  });
}

// 提示词优化
export function getPrompt(text: string) {
  return request({
    url: '/midjourney/get_mj_prompt',
    method: 'post',
    data: {
      text
    }
  });
}

// 获取图片seed值
export function getSeed(taskid: string) {
  return request({
    url: '/midjourney/get_img_seed',
    method: 'get',
    params: { taskid }
  });
}

// 执行动作
export function jobAction(taskid: number, customid: string, channels: number = 0) {
  return request({
    url: '/midjourney/submit/action',
    method: 'post',
    data: {
      taskId: taskid,
      customId: customid,
      channels
    }
  });
}

// 提交图片反推提示词任务
// , channels: number = 0
// ,channels
export function describe(base64: string) {
  return request({
    url: '/midjourney/submit/describe',
    method: 'post',
    data: {
      base64
    }
  });
}

// 频道
// 获取频道列表
export function getChannel() {
  return request({
    url: '/midjourney/get_channels',
    method: 'get'
  });
}

// 添加频道
export function addChannel(channel_name: string, permission: string) {
  return request({
    url: '/midjourney/add_channel',
    method: 'post',
    data: {
      channel_name,
      permission
    }
  });
}

// 删除频道
// export function delChannel(name: string) {
//   return request({
//     url: '/midjourney/del_channel',
//     method: 'post',
//     data: {
//       name
//     }
//   });
// }

// 频道添加指定用户
export function addChannelUser(name: string, username: string) {
  return request({
    url: '/midjourney/add_channel_user',
    method: 'post',
    data: {
      name,
      username
    }
  });
}

// 频道删除指定用户
export function delChannelUser(name: string, username: string) {
  return request({
    url: '/midjourney/del_channel_user',
    method: 'post',
    data: {
      name,
      username
    }
  });
}

// 用户切换作品点赞状态
export function changeLike(taskid: number) {
  return request({
    url: '/midjourney/user_like',
    method: 'post',
    data: {
      taskid
    }
  });
}

// 获取模型列表
export function getMjModels() {
  return request<{ models: Array<{ label: string; value: string; models: Array<{ value: string; label: string }> }> }>({
    url: '/flux/models',
    method: 'get'
  });
}

// Flux图片生成
export function fluxImage(payload: {
  prompt: string;
  model_name: string;
  num?: number;
  taskid?: string;
  preserve_history?: boolean;
  input_image?: string;
  seed?: number;
  aspect_ratio?: string;
  output_format?: string;
  webhook_url?: string;
  webhook_secret?: string;
  prompt_upsampling?: boolean;
  safety_tolerance?: number;
  regenerate_index?: number;
}) {
  return request({
    url: '/flux/create',
    method: 'post',
    data: payload
  });
}

// Flux ComfyUI图片生成
export function fluxComfyUIImage(payload: {
  prompt: string;
  model_name: string;
  num?: number;
  taskid?: string;
  preserve_history?: boolean;
  input_image?: string;
  seed?: number;
  aspect_ratio?: string;
  output_format?: string;
  webhook_url?: string;
  webhook_secret?: string;
  prompt_upsampling?: boolean;
  safety_tolerance?: number;
  regenerate_index?: number;
}) {
  return request({
    url: '/flux/comfyui',
    method: 'post',
    data: payload
  });
}

// openai gpt-image-1 图片生成
export function gptImage(payload: {
  prompt: string;
  image_urls: string[];
  taskid?: string;
  num?: number;
  preserve_history?: boolean;
  proportion?: string;
  regenerate_index?: number;
}) {
  return request({
    url: '/gptimage/generate',
    method: 'post',
    data: payload
  });
}

// openai 获取图片生成任务状态
export function getGptImageTask(taskid: string) {
  return request({
    url: `/gptimage/task/${taskid}`,
    method: 'get'
  });
}

// openai 扩展图片（向左、向右、向上、向下）
export function panImage(payload: {
  prompt: string; // btn1, btn2, btn3, btn4
  image_urls: string[];
  taskid?: string;
  num?: number;
  preserve_history?: boolean;
  proportion?: string;
  regenerate_index?: number;
}) {
  return request({
    url: '/gptimage/change/pan',
    method: 'post',
    data: payload
  });
}

// flux 扩展图片（向左、向右、向上、向下）
export function fluxPanImage(payload: {
  prompt: string; // btn1, btn2, btn3, btn4
  image_urls: string[];
  taskid?: string;
  num?: number;
  preserve_history?: boolean;
  model_name?: string;
  seed?: number;
  aspect_ratio?: string;
  output_format?: string;
  webhook_url?: string;
  webhook_secret?: string;
  prompt_upsampling?: boolean;
  safety_tolerance?: number;
  regenerate_index?: number;
}) {
  return request({
    url: '/flux/change/pan',
    method: 'post',
    data: payload
  });
}

// 删除任务
export function deleteTask(taskId: number) {
  return request({
    url: '/midjourney/delete_task',
    method: 'post',
    data: {
      task_id: taskId
    }
  });
}
