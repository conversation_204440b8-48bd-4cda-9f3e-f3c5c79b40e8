<script setup lang="tsx">
import { computed, h, onBeforeMount, ref, watch } from 'vue';
import type { SelectRenderTag } from 'naive-ui';
import { NTag } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';
import type { Setting, SettingGroup } from '@/service/api';
import { fetchGetGroupSettings, fetchGetSettingGroups, fetchUpdateSetting } from '@/service/api';
import type { FlatResponseData } from '~/packages/axios/src';
import { getAiModels } from './shared';

const GROUP_KEY = 'AVALIABLE_AI_MODELS';

type Props = {
  name: string;
  configKey: string;
};

const props = defineProps<Props>();

const loading = ref(false);

const groupId = ref<number | undefined>();
const setting = ref<Setting | undefined>();
const selectedModels = ref<number[]>([]);
watch(setting, val => {
  if (val === undefined) {
    return [];
  }
  const ids = [];
  const id_str = val.config_value.split(',');
  for (const id_str_item of id_str) {
    const id = Number.parseInt(id_str_item);
    if (id) {
      ids.push(id);
    }
  }
  selectedModels.value = ids;
});

const fetchSelectedModels = async () => {
  if (groupId.value === undefined) {
    const groupRes = (await fetchGetSettingGroups(false)) as FlatResponseData<SettingGroup[]>;
    if (groupRes.error) {
      loading.value = false;
      return;
    }
    let id: number | undefined;
    for (const item of groupRes.data) {
      if (item.group_code === GROUP_KEY) {
        id = item.id;
        break;
      }
    }
    if (id === undefined) {
      return;
    }
    groupId.value = id;
  }
  const settingRes = (await fetchGetGroupSettings(groupId.value!)) as FlatResponseData<Setting[]>;
  if (settingRes.error) {
    return;
  }
  for (const item of settingRes.data) {
    if (item.config_key === props.configKey) {
      setting.value = item;
      break;
    }
  }
};
onBeforeMount(async () => {
  loading.value = true;
  await fetchSelectedModels();
  loading.value = false;
});

const models = ref<Api.SystemManage.Model[]>([]);
onBeforeMount(async () => {
  models.value = await getAiModels();
});
const options = computed(() => {
  return models.value.map(item => {
    return {
      label: item.label || item.name,
      value: item.id,
      disabled: !item.status
    };
  });
});

const renderTag: SelectRenderTag = ({ option, handleClose }) => {
  const id = option.value;
  let model: Api.SystemManage.Model | undefined;
  models.value.forEach(item => {
    if (item.id === id) {
      model = item;
    }
  });
  let optionType = '';
  let icon;
  if (!model || !model.status) {
    optionType = 'error';
    icon = h(SvgIcon, {
      icon: 'ph:empty'
    });
  }
  return h(
    NTag,
    {
      type: optionType,
      closable: true,
      onMousedown: (e: FocusEvent) => {
        e.preventDefault();
      },
      onClose: (e: MouseEvent) => {
        e.stopPropagation();
        handleClose();
      }
    },
    {
      default: () => option.label,
      icon: () => icon
    }
  );
};

const handleSave = async () => {
  if (setting.value === undefined || groupId.value === undefined) {
    return;
  }
  loading.value = true;
  try {
    await fetchUpdateSetting(setting.value!.id, {
      group_id: groupId.value!,
      config_key: props.configKey,
      config_value: selectedModels.value.join(',')
    });
    await fetchSelectedModels();
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <NCard :title="props.name">
    <NSelect
      v-model:value="selectedModels"
      :disabled="loading"
      :options="options"
      multiple
      clearable
      :render-tag="renderTag"
    />
    <template #action>
      <NFlex justify="end">
        <NButton type="primary" :disabled="loading" @click="handleSave">
          {{ $t('page.manage.model.setting.saveBtn') }}
        </NButton>
      </NFlex>
    </template>
  </NCard>
</template>

<style scoped></style>
