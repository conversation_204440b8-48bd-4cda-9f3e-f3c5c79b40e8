<script lang="ts" setup>
import FunctionTabs from './modules/function-tabs.vue';
import HeaderMenu from './modules/header-menu.vue';
import ContentArea from './modules/content-area.vue';
import EditHistory from './modules/edit-history.vue';
</script>

<template>
  <NLayout>
    <NLayoutHeader class="h-16" bordered>
      <HeaderMenu />
    </NLayoutHeader>

    <NLayout has-sider>
      <NLayoutSider
        :width="100"
        content-style="max-height: [calc(100vh-12.3rem)];"
        :native-scrollbar="false"
        class="h-[calc(100vh-12.3rem)]"
      >
        <FunctionTabs />
      </NLayoutSider>

      <NLayoutContent class="relative h-[calc(100vh-12.3rem)]">
        <ContentArea />
        <div class="absolute right-3 top-1/2 z-10 transform -translate-y-1/2">
          <EditHistory />
        </div>
      </NLayoutContent>
    </NLayout>
  </NLayout>
</template>

<style scoped></style>
