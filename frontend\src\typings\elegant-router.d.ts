/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "document": "/document";
    "document_project": "/document/project";
    "document_project-link": "/document/project-link";
    "document_vue": "/document/vue";
    "document_vite": "/document/vite";
    "document_unocss": "/document/unocss";
    "document_naive": "/document/naive";
    "document_antd": "/document/antd";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "about": "/about";
    "aichat": "/aichat";
    "aichat_assistant": "/aichat/assistant";
    "asset-manage": "/asset-manage";
    "asset-manage_asset-info": "/asset-manage/asset-info";
    "asset-manage_asset-report": "/asset-manage/asset-report";
    "asset-manage_user-assets": "/asset-manage/user-assets";
    "asset-manage_user-report": "/asset-manage/user-report";
    "audio": "/audio";
    "audio_clearvoice": "/audio/clearvoice";
    "audio_cosy": "/audio/cosy";
    "audio_inspiremusic": "/audio/inspiremusic";
    "audio_reduction": "/audio/reduction";
    "audio_separate": "/audio/separate";
    "audio_synthesis": "/audio/synthesis";
    "audio_texttospeech": "/audio/texttospeech";
    "audio_timbres": "/audio/timbres";
    "audio_volcano": "/audio/volcano";
    "function": "/function";
    "function_hide-child": "/function/hide-child";
    "function_hide-child_one": "/function/hide-child/one";
    "function_hide-child_three": "/function/hide-child/three";
    "function_hide-child_two": "/function/hide-child/two";
    "function_multi-tab": "/function/multi-tab";
    "function_request": "/function/request";
    "function_super-page": "/function/super-page";
    "function_tab": "/function/tab";
    "function_toggle-auth": "/function/toggle-auth";
    "home": "/home";
    "iframe-page": "/iframe-page/:url";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "manage": "/manage";
    "manage_creditlog": "/manage/creditlog";
    "manage_menu": "/manage/menu";
    "manage_models": "/manage/models";
    "manage_role": "/manage/role";
    "manage_settings": "/manage/settings";
    "manage_system-config": "/manage/system-config";
    "manage_system-config_modules": "/manage/system-config/modules";
    "manage_system-config_modules_ai-models-setting": "/manage/system-config/modules/ai-models-setting";
    "manage_user": "/manage/user";
    "manage_user-credit": "/manage/user-credit";
    "manage_user-detail": "/manage/user-detail/:id";
    "manage_workteams": "/manage/workteams";
    "management": "/management";
    "management_channels": "/management/channels";
    "management_credit": "/management/credit";
    "management_game": "/management/game";
    "management_toolset": "/management/toolset";
    "monitor": "/monitor";
    "monitor_process": "/monitor/process";
    "multi-menu": "/multi-menu";
    "multi-menu_first": "/multi-menu/first";
    "multi-menu_first_child": "/multi-menu/first/child";
    "multi-menu_second": "/multi-menu/second";
    "multi-menu_second_child": "/multi-menu/second/child";
    "multi-menu_second_child_home": "/multi-menu/second/child/home";
    "navigation": "/navigation";
    "portrait": "/portrait";
    "portrait_aidraw": "/portrait/aidraw";
    "portrait_cutout": "/portrait/cutout";
    "portrait_enlarge": "/portrait/enlarge";
    "portrait_facecopy": "/portrait/facecopy";
    "portrait_image-edit": "/portrait/image-edit";
    "portrait_midjourney": "/portrait/midjourney";
    "portrait_mimicbrush": "/portrait/mimicbrush";
    "portrait_mjworks": "/portrait/mjworks";
    "portrait_sdwebui": "/portrait/sdwebui";
    "portrait_subtitle": "/portrait/subtitle";
    "share-page": "/share-page";
    "stablediffusion": "/stablediffusion";
    "statis": "/statis";
    "statis_page": "/statis/page";
    "statis_taskcall": "/statis/taskcall";
    "text": "/text";
    "text_copywriting": "/text/copywriting";
    "text_imgocr": "/text/imgocr";
    "text_translate": "/text/translate";
    "user-center": "/user-center";
    "video": "/video";
    "video_framepack": "/video/framepack";
    "wiki": "/wiki";
    "wiki_ainews": "/wiki/ainews";
    "wiki_document": "/wiki/document";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */ 
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document"
    | "document_project"
    | "document_project-link"
    | "document_vue"
    | "document_vite"
    | "document_unocss"
    | "document_naive"
    | "document_antd"
  >;

  /**
   * the generated route key
   */ 
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "about"
    | "aichat"
    | "asset-manage"
    | "audio"
    | "function"
    | "home"
    | "iframe-page"
    | "login"
    | "manage"
    | "management"
    | "monitor"
    | "multi-menu"
    | "navigation"
    | "portrait"
    | "share-page"
    | "stablediffusion"
    | "statis"
    | "text"
    | "user-center"
    | "video"
    | "wiki"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "document"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "iframe-page"
    | "login"
    | "about"
    | "aichat_assistant"
    | "asset-manage_asset-info"
    | "asset-manage_asset-report"
    | "asset-manage_user-assets"
    | "asset-manage_user-report"
    | "audio_clearvoice"
    | "audio_cosy"
    | "audio_inspiremusic"
    | "audio_reduction"
    | "audio_separate"
    | "audio_synthesis"
    | "audio_texttospeech"
    | "audio_timbres"
    | "audio_volcano"
    | "function_hide-child_one"
    | "function_hide-child_three"
    | "function_hide-child_two"
    | "function_multi-tab"
    | "function_request"
    | "function_super-page"
    | "function_tab"
    | "function_toggle-auth"
    | "home"
    | "manage_creditlog"
    | "manage_menu"
    | "manage_models"
    | "manage_role"
    | "manage_settings"
    | "manage_system-config"
    | "manage_system-config_modules_ai-models-setting"
    | "manage_user-credit"
    | "manage_user-detail"
    | "manage_user"
    | "manage_workteams"
    | "management_channels"
    | "management_credit"
    | "management_game"
    | "management_toolset"
    | "monitor_process"
    | "multi-menu_first_child"
    | "multi-menu_second_child_home"
    | "navigation"
    | "portrait_aidraw"
    | "portrait_cutout"
    | "portrait_enlarge"
    | "portrait_facecopy"
    | "portrait_image-edit"
    | "portrait_midjourney"
    | "portrait_mimicbrush"
    | "portrait_mjworks"
    | "portrait_sdwebui"
    | "portrait_subtitle"
    | "share-page"
    | "stablediffusion"
    | "statis_page"
    | "statis_taskcall"
    | "text_copywriting"
    | "text_imgocr"
    | "text_translate"
    | "user-center"
    | "video_framepack"
    | "wiki_ainews"
    | "wiki_document"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document_project"
    | "document_project-link"
    | "document_vue"
    | "document_vite"
    | "document_unocss"
    | "document_naive"
    | "document_antd"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
