<script setup lang="ts">
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'TeamSearch'
});

interface Emits {
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef } = useNaiveForm();

const model = defineModel<Api.SystemManage.TeamSearchParams>('model', { required: true });

function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper pt-5">
    <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="团队名称" path="team_name" class="pr-24px">
          <NInput v-model:value="model.team_name" placeholder="团队名称" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="团队编码" path="team_code" class="pr-24px">
          <NInput v-model:value="model.team_code" placeholder="团队编码" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6">
          <NSpace justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
