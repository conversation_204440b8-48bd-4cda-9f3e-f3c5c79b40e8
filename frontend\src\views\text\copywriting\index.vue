<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { $t } from '@/locales';
import { copywritingHistory, fetchCompanyGames, generateCopywriting, stopGeneration } from '@/service/api/text';
import type { CopywritingRecord, GenerateCopywritingPayload } from '@/service/api/text';

// 提示信息
const msg = useMessage();
//
// 表单1变量和选项
const selformRef = ref<any>(null);
const selformValue = ref({
  game: '女巫',
  style: '生动有趣',
  duration: '30秒',
  gender: '不限',
  age: '18-35',
  region: '欧美'
});

// // 游戏选择
// const gameOptions = ref([
//   { label: '恋恋清庭', value: '恋恋清庭' },
//   { label: '花舞宫廷', value: '花舞宫廷' },
//   { label: '女巫', value: '女巫' },
//   { label: '驯龙', value: '驯龙' },
//   { label: '地城之光', value: '地城之光' }
// ]);

// 游戏选择
const gameOptions = ref<{ label: string; value: string }[]>([]);

// 页面加载时初始化游戏名称列表
onMounted(async () => {
  try {
    const response = await fetchCompanyGames();
    if (response && response.data.games.length > 0) {
      // 更新游戏选项
      gameOptions.value = response.data.games.map((game: string) => ({ label: game, value: game }));

      // 默认选择第一个游戏
      selformValue.value.game = response.data.games[0];
    } else {
      msg.warning('未能获取到任何游戏名称');
    }
  } catch (error) {
    msg.error('获取游戏名称列表时出错');
  }
});

// 文案风格
const styleOptions = ref([
  { label: '风趣幽默', value: '风趣幽默' },
  { label: '引发共鸣', value: '引发共鸣' },
  { label: '生动有趣', value: '生动有趣' },
  { label: '生动形象', value: '生动形象' },
  { label: '认真严谨', value: '认真严谨' }
]);

// 视频时长
const durationOptions = ref([
  { label: '15秒', value: '15秒' },
  { label: '30秒', value: '30秒' },
  { label: '45秒', value: '45秒' },
  { label: '60秒', value: '60秒' }
]);

// 受众人群
const genderOptions = ref([
  { label: '不限', value: '不限' },
  { label: '女性', value: '女性' },
  { label: '男性', value: '男性' }
]);

// 受众地区
const regionOptions = ref([
  { label: '港澳台', value: '港澳台' },
  { label: '欧美', value: '欧美' },
  { label: '东南亚', value: '东南亚' },
  { label: '日韩', value: '日韩' }
  // 其他选项
]);

// 表单1必填项
const selFormRules = {
  game: [
    {
      required: true,
      message: '请选择游戏',
      trigger: 'blur'
    }
  ],
  style: [
    {
      required: true,
      message: '请选择文案风格',
      trigger: 'blur'
    }
  ],
  duration: [
    {
      required: true,
      message: '请选择视频时长',
      trigger: 'blur'
    }
  ]
};

// // 卖点标签 需关联卖点输入框值
// const tags = ref<string[]>([]);

// 表单2变量和验证规则
const secondFormRef = ref<any>(null);
const secondFormValue = ref({
  tags: [] as string[],
  emotion: '',
  scene: '',
  description: '',
  event: '',
  reference: ''
});

// 表单2必填项
const secondFormRules = {
  // tags: [{ required: true, message: '卖点不能为空', trigger: 'blur' }],
  tags: [
    {
      required: true,
      validator: (_rule: any, _value: any) => {
        if (secondFormValue.value.tags.length === 0) {
          return new Error('请添加卖点标签');
        }
        return true;
      },
      trigger: 'blur'
    }
  ],
  emotion: [{ required: true, message: '请描述引导情感深度事件', trigger: 'blur' }],
  event: [{ required: true, message: '请描述特定事件', trigger: 'blur' }]
};

// 添加卖点标签逻辑
// const addTag = (tag: string) => {
//   if (tags.value.length >= 5) {
//     msg.error($t('page.text.maxTags'));
//     return;
//   }
//   if (tag && !tags.value.includes(tag)) {
//     tags.value.push(tag);
//     secondFormValue.value.tags = ''; // 清空输入框
//   }
// };

// 清空所有卖点标签
const clearTags = () => {
  secondFormValue.value.tags = [];
};

const handleTagsChange = (value: string[]) => {
  if (value.length > 5) {
    msg.error('最多只能选择五个标签');
    secondFormValue.value.tags.pop();
  }
};

// 删除卖点标签逻辑
// const removeTag = (tag: string) => {
//   const index = tags.value.indexOf(tag);
//   if (index !== -1) {
//     tags.value.splice(index, 1);
//   }
// };

const loading = ref<boolean>(false);

const typingStr = ref('');

let client: { cancel: () => void } | null = null;

const interrupt = ref<boolean>(false);
// 计时器
const reqTime = ref<number>(0);
let timer: ReturnType<typeof setInterval> | undefined;

// 历史记录
const records = ref<CopywritingRecord[]>([]);

const isaddhistory = ref<boolean>(true);

const stop = async () => {
  try {
    isaddhistory.value = false;
    await stopGeneration();
  } catch (error) {
    console.error('Failed to stop generation:', error);
  } finally {
    interrupt.value = false;
  }
};

// 生成文案的逻辑
const generateCopywritingHandler = async () => {
  // 验证表单
  const isSelformValid = await selformRef.value?.validate().catch(() => false);
  const isSecondFormValid = await secondFormRef.value?.validate().catch(() => false);

  if (!isSelformValid || !isSecondFormValid) {
    msg.error($t('page.text.requiredValue'));
    return;
  }

  const payload: GenerateCopywritingPayload = {
    game: selformValue.value.game,
    style: selformValue.value.style,
    duration: selformValue.value.duration,
    gender: selformValue.value.gender,
    age: selformValue.value.age,
    region: selformValue.value.region,
    tags: secondFormValue.value.tags,
    emotion: secondFormValue.value.emotion,
    scene: secondFormValue.value.scene,
    description: secondFormValue.value.description,
    event: secondFormValue.value.event,
    reference: secondFormValue.value.reference
  };

  typingStr.value = ''; // 清空之前的内容
  isaddhistory.value = true;
  reqTime.value = 0;
  timer = setInterval(() => {
    reqTime.value += 1;
  }, 1000);

  loading.value = true;
  interrupt.value = true;

  client = await generateCopywriting(
    payload,
    message => {
      typingStr.value += message
        .trim() // 去除首尾空格
        .replace(/\*/g, '') // 去掉所有的*号
        .replace(/】(?!：)(?!【)/g, '】\n') // 在"】"后面加换行符，除非后面紧跟"："或"【"
        .replace(/(?<!】)【/g, '\n【') // 在"【"前面加换行符，除非前面是"】"
        .replace(/】【/g, '】\n【'); // 处理"】【"连在一起的情况，只添加一个换行符
    },
    () => {
      // 接口完成回调
      loading.value = false;
      clearInterval(timer); // 停止计时器
      interrupt.value = false;
      //  将生成的文案添加到历史记录中
      if (isaddhistory.value) {
        // 如果用户打断生成,则不添加历史记录
        const newRecord: CopywritingRecord = {
          game: payload.game,
          style: payload.style,
          duration: payload.duration,
          gender: payload.gender,
          age: payload.age,
          region: payload.region,
          tags: payload.tags,
          emotion: payload.emotion,
          scene: payload.scene,
          description: payload.description,
          event: payload.event,
          reference: payload.reference,
          generated_copy: typingStr.value,
          generated_time: new Date().toISOString().replace('T', ' ').split('.')[0] // 格式化时间
        };
        records.value.unshift(newRecord);
      }
    },
    () => {
      // 接口开始回调
      loading.value = false;
    }
  );
};

onUnmounted(() => {
  if (client) {
    client.cancel();
  }
  if (timer) {
    clearInterval(timer);
  }
});

const loadingHistory = ref(false);
const noMoreHistory = ref(false);
const page = ref(1);
const pageSize = ref(10);

const loadMoreHistory = async () => {
  if (loadingHistory.value || noMoreHistory.value) return;

  loadingHistory.value = true;
  try {
    const response = await copywritingHistory(page.value, pageSize.value);

    // 对 response.data 进行处理
    const processedData = response.data.map((record: CopywritingRecord) => {
      return {
        ...record,
        generated_time: record.generated_time.replace('T', ' ')
        // generated_copy: record.generated_copy.replace(/\n+/g, '<br />')
      };
    });

    if (response.data.length < pageSize.value) {
      noMoreHistory.value = true;
    }
    records.value.push(...processedData);
    page.value += 1;
  } catch (error) {
    loadingHistory.value = false;
  } finally {
    loadingHistory.value = false;
  }
};

const importHistory = (record: CopywritingRecord) => {
  selformValue.value.game = record.game;
  selformValue.value.style = record.style;
  selformValue.value.duration = record.duration;
  selformValue.value.gender = record.gender;
  selformValue.value.age = record.age;
  selformValue.value.region = record.region;
  secondFormValue.value.tags = record.tags;
  secondFormValue.value.emotion = record.emotion;
  secondFormValue.value.scene = record.scene;
  secondFormValue.value.description = record.description;
  secondFormValue.value.event = record.event;
  secondFormValue.value.reference = record.reference;
};

onMounted(() => {
  loadMoreHistory();
});

// 查看完整文案模态框
const showModal = ref(false);
const currentRecord = ref<CopywritingRecord | null>(null);

const formatCopywriting = (copy: string): string => {
  return copy
    .trim()
    .replace(/\*/g, '')
    .replace(/】(?!：)(?!【)/g, '】\n')
    .replace(/(?<!】)【/g, '\n【')
    .replace(/】【/g, '】\n【')
    .replace(/\n+/g, '\n');
};

const viewFullCopy = (record: CopywritingRecord) => {
  currentRecord.value = { ...record, generated_copy: formatCopywriting(record.generated_copy) };
  showModal.value = true;
};

const autoCompleteOptions = ref([
  '傲慢：通过展示荣誉系统、成就系统以及丰富的装备收集和交易，让玩家在游戏中享受尊崇感和地位提升的快感。',
  '嫉妒：展示游戏中的排行榜、竞技场和顶级玩家的成就，激发玩家的追赶欲望和对高级道具、装备的渴望。',
  '暴怒：通过呈现游戏中刺激惊险的战斗场面和失败的后果，引导玩家为了战胜困难和强力敌人而持续投入，不断尝试。',
  '懒惰：强调游戏的自动挂机、轻松升级以及轻松上手的特点，满足玩家追求轻松和高效的心理，减少游戏负担。',
  '贪婪：通过丰厚的奖励和成就感，吸引玩家进行长时间和高投入的游戏。强调限时活动、大奖和丰厚福利，激发玩家的参与度。',
  '嫉妒：展示角色在成长和进步过程中的巨大差异，让玩家看到他人的炫酷装扮、美丽皮肤和强大技能，激发其攀比和追求心理。',
  '饕餮：展示游戏丰富的内容、独特的玩法和不断更新的活动，保持玩家的新鲜感和持续兴趣。强调各种稀有道具、神秘宝箱和隐藏任务，激发玩家探索和收集的欲望。',
  '成就与荣耀：通过展示玩家在游戏中的辉煌成就和荣誉，激发玩家的自豪感和成就感。',
  '社交共鸣：强调游戏中的社交互动和公会战斗，让玩家感受到团队合作和友情的力量。',
  '情感连接：让玩家通过剧情互动和美男攻略，体验深层次的人物关系和情感交流。',
  '竞争与激励：展示排行榜和竞技场，激发玩家的竞争心理和不断超越他人的动力。',
  '收藏与珍藏：强调稀有道具和装备的收集，让玩家享受收集和珍藏的乐趣。',
  '探索与发现：展示奇幻世界的广阔与神秘，满足玩家对冒险和探索的渴望和好奇心。',
  '视觉享受：通过捏脸定制和换装打扮，让玩家享受个性化和视觉上的满足感。',
  '轻松休闲：展示游戏中轻松升级、自动挂机和不肝不氪的特点，让玩家感受到游戏的轻松和愉悦。',
  '紧张刺激：通过展示战斗升级和强力敌人，让玩家体验紧张刺激的对战场面和挑战的快感。',
  '成长与培养：展示萌宠养成和萌娃培养的过程，让玩家体验到培养和成长的乐趣。',
  '奖励激励：强调签到奖励和连续登录奖励，用丰富的福利吸引玩家的持续投入。',
  '成就展示：通过展示玩家在游戏中的成就和顶级装备，满足玩家炫耀和展示的心理需求。',
  '任务与挑战：展示丰富的任务挑战和剧情解锁，让玩家感受到不断完成任务和解锁新剧情的成就感。',
  '炫耀与攀比：展示玩家在游戏中的稀有装备和顶级成就，激发他们炫耀和攀比的心态。',
  '复仇与挑战：通过展示失败后的奋发图强和连续挑战，让玩家体验到通过努力获得成功的快感。',
  '策略与智慧：展示烧脑解谜、资源掠夺等玩法，让玩家在策略和智慧的选择中感受到成就感。',
  '团结合作：强调多人组队和公会建设，让玩家在团队合作中找到归属感和凝聚力。',
  '物资获取：通过展示资源采集和材料收集，吸引玩家对物资获取和资源管理的兴趣。',
  '个性打造：展示捏脸定制和人物展示的个性化设定，让玩家享受独一无二的角色创造过程。',
  '轻松愉悦：通过展示轻松升级和趣味小游戏，满足玩家在轻松愉悦环境中的娱乐需求。',
  '多样玩法：展示宠物对战、养娃养宠物等多样化玩法，确保玩家游戏体验的丰富性与新鲜感。',
  '美丽诱惑：通过人物换装和美女陪玩的展示，吸引玩家追求游戏中的美丽与诱惑。',
  '永续发展：强调资源采集、公会建设和自由交易让玩家不断追求长久的发展和进步。',
  '传奇重生：展示角色重生穿越和逆袭的剧情，让玩家在跌宕起伏的故事中感受到成长和蜕变。',
  '惊险与刺激：通过展示紧张刺激的战斗和任务挑战，满足玩家对极限对抗和冒险的求新心理。',
  '独特身份：通过展示角色的特殊技能和独特外观，满足玩家追求个性化和与众不同的心理需求。',
  '成长喜悦：展示角色从弱小到强大的成长历程，让玩家体验到培养和成长带来的满足感。',
  '探索欲望：通过展示神秘的未知区域和隐藏任务，激发玩家的好奇心和探索欲。',
  '收藏乐趣：展示丰富的装备、宠物和道具收集系统，满足玩家的收藏癖和完整感。',
  '技能掌控：通过展示复杂而强大的技能系统，让玩家感受到掌控力和精通带来的成就感。',
  '社交认同：展示游戏内的社交互动和公会活动，满足玩家被认同和归属的社交需求。',
  '创造乐趣：通过展示房屋建造和装备打造等系统，激发玩家的创造欲和成就感。',
  '复古怀旧：利用游戏中的复古元素，唤起玩家对经典游戏的怀念，激发情感共鸣。',
  '角色代入：通过丰富的剧情体验和角色互动，让玩家更深入地代入游戏角色，增强情感投入。',
  '挑战自我：展示高难度副本和竞技场，激发玩家挑战自我、突破极限的欲望。',
  '虚拟伙伴：通过展示萌宠养成和招募伙伴系统，满足玩家对虚拟伙伴的情感需求。',
  '即时反馈：强调游戏中的即时奖励系统，如签到奖励和连续登录奖励，给予玩家持续的正面反馈。',
  '梦想实现：通过展示游戏中的逆袭剧情和成功案例，激发玩家追求梦想的动力。',
  '英雄崇拜：展示游戏中的传奇人物和英雄故事，满足玩家对英雄主义的向往。',
  '责任感：通过展示公会建设和团队任务，激发玩家的责任感和使命感。',
  '自我提升：强调游戏中学习和掌握新技能的过程，满足玩家自我提升的需求。'
]);

const showAutoComplete = (value: string) => {
  return value.includes('@');
};

function getSecondPart(value: string) {
  const parts = value.split('：'); // 使用中文冒号作为分隔符
  return parts[1] || value; // 返回分割后的第二个部分
}

const handleSelect = (value: string) => {
  secondFormValue.value.emotion = getSecondPart(value);
  // console.log(secondFormValue.value.emotion);
};

const descriptionOptions = ref([
  '这是剧情向的视频文案，不要旁白',
  '这是解说向的视频文案，不要对白',
  '这是解说向的视频文案，不要对白；不要刻意介紹太多的玩法；',
  '这是解说向的视频文案，不要对白；對參考案例進行本地化',
  '这是真人出演的视频文案，前面15秒是一段真人劇情，真人劇情有衝突有反轉，過渡到遊戲解說，後20秒是遊戲解說',
  '这是剧情向的视频文案，不要旁白；劇情不要出現太多角色；要剧情连贯，避免过于片段化，类似短剧一样一个简短故事',
  '这是剧情向的视频文案，不要旁白和對白純畫面表達故事；劇情不要出現太多角色；不要刻意介紹太多的玩法，要剧情连贯，避免过于片段化，类似短剧一样一个简短故事；'
]);

const tags_options = ref([
  { label: '进阶升级', value: '进阶升级' },
  { label: '合成进化', value: '合成进化' },
  { label: '换装打扮', value: '换装打扮' },
  { label: '攻略美男', value: '攻略美男' },
  { label: '美色诱惑', value: '美色诱惑' },
  { label: '游戏福利', value: '游戏福利' },
  { label: '装备收集', value: '装备收集' },
  { label: '战斗升级', value: '战斗升级' },
  { label: '萌娃培养', value: '萌娃培养' },
  { label: '攀比炫耀', value: '攀比炫耀' },
  { label: '捏脸定制', value: '捏脸定制' },
  { label: '宠物对战', value: '宠物对战' },
  { label: '重生穿越', value: '重生穿越' },
  { label: '人物展示', value: '人物展示' },
  { label: '模拟经营', value: '模拟经营' },
  { label: '资源掠夺', value: '资源掠夺' },
  { label: '任务挑战', value: '任务挑战' },
  { label: '剧情体验', value: '剧情体验' },
  { label: '自由交易', value: '自由交易' },
  { label: '人物养成', value: '人物养成' },
  { label: '竞技对抗', value: '竞技对抗' },
  { label: '资源采集', value: '资源采集' },
  { label: '公会战斗', value: '公会战斗' },
  { label: '社交互动', value: '社交互动' },
  { label: '剧情互动', value: '剧情互动' },
  { label: '美女陪玩', value: '美女陪玩' },
  { label: '不肝不氪', value: '不肝不氪' },
  { label: '材料收集', value: '材料收集' },
  { label: '技能升级', value: '技能升级' },
  { label: '趣味小游戏', value: '趣味小游戏' },
  { label: '萌宠养成', value: '萌宠养成' },
  { label: '签到奖励', value: '签到奖励' },
  { label: '装备强化', value: '装备强化' },
  { label: '多人组队', value: '多人组队' },
  { label: '连续登录奖励', value: '连续登录奖励' },
  { label: '公会建设', value: '公会建设' },
  { label: '养成经营', value: '养成经营' },
  { label: '剧情解锁', value: '剧情解锁' },
  { label: '复古情怀', value: '复古情怀' },
  { label: '逆袭', value: '逆袭' },
  { label: '房屋建造', value: '房屋建造' },
  { label: '轻松升级', value: '轻松升级' },
  { label: '烧脑解谜', value: '烧脑解谜' },
  { label: '招募伙伴', value: '招募伙伴' },
  { label: '养娃', value: '养娃' },
  { label: '养宠物', value: '养宠物' },
  { label: '锻造神器', value: '锻造神器' },
  { label: '打造装备', value: '打造装备' },
  { label: '装备交易', value: '装备交易' }
]);
</script>

<template>
  <NGrid x-gap="12" cols="2">
    <NGi>
      <NCard>
        <NForm
          ref="selformRef"
          inline
          :model="selformValue"
          label-placement="left"
          class="selformbox"
          :rules="selFormRules"
        >
          <NFormItem label="游戏" path="game">
            <NSelect
              v-model:value="selformValue.game"
              :placeholder="$t('page.text.pleaseChoose')"
              :options="gameOptions"
            />
          </NFormItem>
          <NFormItem label="风格" path="style">
            <NSelect
              v-model:value="selformValue.style"
              :placeholder="$t('page.text.pleaseChoose')"
              :options="styleOptions"
            />
          </NFormItem>
          <NFormItem label="时长" path="duration">
            <NSelect
              v-model:value="selformValue.duration"
              :placeholder="$t('page.text.pleaseChoose')"
              :options="durationOptions"
            />
          </NFormItem>
          <NFormItem label="性别" path="gender">
            <NSelect
              v-model:value="selformValue.gender"
              :placeholder="$t('page.text.pleaseChoose')"
              :options="genderOptions"
            />
          </NFormItem>
          <NFormItem label="年龄" path="age">
            <NInput v-model:value="selformValue.age" placeholder="请输入" />
          </NFormItem>
          <NFormItem label="地区" path="region">
            <NSelect
              v-model:value="selformValue.region"
              :placeholder="$t('page.text.pleaseChoose')"
              :options="regionOptions"
            />
          </NFormItem>
        </NForm>
        <NDivider />
        <NForm ref="secondFormRef" :model="secondFormValue" :rules="secondFormRules" label-placement="top">
          <NFormItem label="卖点" path="tags">
            <NInputGroup>
              <NSelect
                v-model:value="secondFormValue.tags"
                filterable
                multiple
                tag
                :options="tags_options"
                :max-tag-count="5"
                placeholder="选择或输入标签"
                @update:value="handleTagsChange"
              />
              <NButton type="info" @click="clearTags">清空</NButton>
            </NInputGroup>
          </NFormItem>
          <!--
 <NFormItem class="selling">
          <NTag v-for="(tag, index) in tags" :key="index" closable @close="removeTag(tag)">
            {{ tag }}
          </NTag>
        </NFormItem>
-->
          <NFormItem label="引导情感深度" path="emotion">
            <NAutoComplete
              v-model:value="secondFormValue.emotion"
              :options="autoCompleteOptions"
              placeholder="输入@查看提示"
              :get-show="showAutoComplete"
              @update:value="handleSelect"
            />
          </NFormItem>
          <NFormItem label="具体化场景" path="scene">
            <NInput
              v-model:value="secondFormValue.scene"
              placeholder="指定文案中需出现的画面或情景,进一步限定内容范围,适用于匹配已有素材或特定场景。"
            />
          </NFormItem>
          <NFormItem label="补充说明" path="description">
            <NAutoComplete
              v-model:value="secondFormValue.description"
              :options="descriptionOptions"
              :get-show="showAutoComplete"
            >
              <template #default="{ handleInput, handleBlur, handleFocus, value: slotValue }">
                <NInput
                  type="textarea"
                  rows="2"
                  placeholder="输入@查看提示"
                  :value="slotValue"
                  @input="handleInput"
                  @focus="handleFocus"
                  @blur="handleBlur"
                />
              </template>
            </NAutoComplete>
          </NFormItem>
          <NFormItem label="特定事件" path="event">
            <NInput
              v-model:value="secondFormValue.event"
              placeholder="提供文案的大致情景描述,基于這個描述創作。"
              type="textarea"
              rows="2"
            />
          </NFormItem>
          <NFormItem label="案例参考" path="reference">
            <NInput
              v-model:value="secondFormValue.reference"
              placeholder="输入具体参考案例,也可用于优化现有文案。"
              type="textarea"
              rows="4"
            />
          </NFormItem>
        </NForm>
      </NCard>
    </NGi>
    <NGi>
      <NCard>
        <NTabs type="segment" animated>
          <NTabPane name="chap1" tab="文案生成">
            <NSpace justify="space-between" align="center">
              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton type="success" class="generate" @click="generateCopywritingHandler">生成</NButton>
                </template>
                点击开始撰写文案
              </NTooltip>
              <p v-if="interrupt">
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton type="info" class="generate" @click="stop">中断生成</NButton>
                  </template>
                  点击中止文案撰写
                </NTooltip>
              </p>
              <p class="time_data">
                <SvgIcon icon="lucide:alarm-clock" />
                <NText>生成时间:</NText>
                <NNumberAnimation :from="reqTime !== 0 ? reqTime - 1 : reqTime" :to="reqTime"></NNumberAnimation>
                <NText>秒</NText>
              </p>
            </NSpace>
            <NCard class="finally_writing" embedded>
              <NScrollbar content-style="max-height: 75%; height: 41em;">
                <NSpin v-if="loading" size="large" class="spincenter">
                  <template #description>文案撰写中,请耐心等待</template>
                </NSpin>
                <NEl v-else id="ai_text">
                  <!-- 使用pre标签来保持文本格式 -->
                  <pre>{{ typingStr }}</pre>
                </NEl>
              </NScrollbar>
            </NCard>
          </NTabPane>
          <NTabPane name="chap2" tab="历史记录" class="history">
            <NInfiniteScroll :distance="30" class="infinite_scroll" @load="loadMoreHistory">
              <NCard v-for="record in records" :key="record.generated_time" hoverable embedded class="history_box">
                <NDivider title-placement="center" class="finallytime">
                  <NText>{{ record.generated_time }}</NText>
                </NDivider>
                <div class="flex justify-between">
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <NButton round class="switch" type="info" strong secondary @click="importHistory(record)">
                        <template #icon>
                          <SvgIcon icon="mdi:export"></SvgIcon>
                        </template>
                        导入
                      </NButton>
                    </template>
                    游戏: {{ record.game }}
                    <br />
                    风格: {{ record.style }}
                    <br />
                    时长: {{ record.duration }}
                    <br />
                    性别: {{ record.gender }}
                    <br />
                    年龄: {{ record.age }}
                    <br />
                    地区: {{ record.region }}
                    <br />
                    ...
                  </NTooltip>
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <NButton text class="text-7" @click="viewFullCopy(record)">
                        <SvgIcon icon="weui:eyes-on-outlined" />
                      </NButton>
                    </template>
                    点击查看完整文案
                  </NTooltip>
                </div>
                <NEllipsis line-clamp="4" :tooltip="false" class="historytext">
                  {{ record.generated_copy }}
                </NEllipsis>
              </NCard>
              <NDivider title-placement="center">
                <div v-if="loadingHistory" class="text">加载中...</div>
                <div v-if="noMoreHistory" class="text">没有更多了~</div>
              </NDivider>
            </NInfiniteScroll>
            <NModal v-model:show="showModal" transform-origin="center" class="w-2/4">
              <NCard title="完整文案" :bordered="false" size="huge" role="dialog" aria-modal="true">
                <NScrollbar class="max-h-96">
                  <div class="complete_box">{{ currentRecord?.generated_copy }}</div>
                </NScrollbar>
              </NCard>
            </NModal>
          </NTabPane>
        </NTabs>
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped>
/* .main {
  display: flex;
  gap: 1em;
} */

.right {
  /* height: 100%; */
  height: 61em !important;
}

.selformbox {
  display: flex;
  height: 10%;
  margin-bottom: -2.5em;
  flex-wrap: wrap;
}

.selformbox > * {
  min-width: 11em;
}

.selling {
  /* display: flex;
  flex-direction: column; */
  margin-top: -2em;
  margin-bottom: -1em;
}

.generate {
  width: 10em;
}

.time_data {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  font-size: 1.5em;
  /* margin-right: 0.5em; */
  gap: 0.2em;
  font-size: 16px !important;
}

.finally_writing {
  height: 75%;
  max-height: 75%;
  margin-top: 2em;
  position: relative;
}

.spincenter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#ai_text pre,
.complete_box {
  white-space: pre-wrap; /* 保持换行符 */
  word-wrap: break-word; /* 长词换行 */
  font-family: inherit;
}

.finallytime {
  margin-top: 0px;
  margin-bottom: 0.5em;
}

.history .switch {
  margin-bottom: 0.5em;
}

.history {
  display: flex;
  height: 49em !important;
}

.history .history_box {
  margin-bottom: 1em;
  white-space: pre-wrap;
}
</style>
