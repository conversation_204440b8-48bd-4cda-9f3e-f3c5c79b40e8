// WAVE 文件的头部信息
function writeString(view: DataView, offset: number, string: string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

function floatTo16BitPCM(output: DataView, _offset: number, input: Float32Array) {
  let offset = _offset;
  for (let i = 0; i < input.length; i++, offset += 2) {
    const s = Math.max(-1, Math.min(1, input[i]));
    output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
}
// 转音频
export function bufferToWave(abuffer: AudioBuffer, len: number): Blob {
  const numOfChan = abuffer.numberOfChannels;
  const length = len * numOfChan * 2 + 44;
  const buffer = new ArrayBuffer(length);
  const view = new DataView(buffer);
  const channels = [];
  let i;
  let sample;
  let offset = 0;
  let pos = 0;

  // write WAVE header
  // "RIFF"
  setUint32(0x46464952);
  // file length - 8
  setUint32(length - 8);
  // "WAVE"
  setUint32(0x45564157);
  // "fmt " chunk
  setUint32(0x20746d66);
  // length = 16
  setUint32(16);
  // PCM (uncompressed)
  setUint16(1);
  setUint16(numOfChan);
  setUint32(abuffer.sampleRate);
  // avg. bytes/sec
  setUint32(abuffer.sampleRate * 2 * numOfChan);
  // block-align
  setUint16(numOfChan * 2);
  // 16-bit (hardcoded in this demo)
  setUint16(16);
  // "data" - chunk
  setUint32(0x61746164);
  // chunk length
  setUint32(length - pos - 4);

  // write interleaved data
  for (i = 0; i < abuffer.numberOfChannels; i += 1) channels.push(abuffer.getChannelData(i));

  while (pos < length) {
    // interleave channels
    for (i = 0; i < numOfChan; i += 1) {
      // clamp
      sample = Math.max(-1, Math.min(1, channels[i][offset]));
      // scale to 16-bit signed int
      sample = 0.5 + sample < 0 ? sample * 32768 : sample * 32767;
      // write 16-bit sample
      view.setInt16(pos, sample, true);
      pos += 2;
    }
    // next source sample
    offset += 1;
  }

  // create Blob
  return new Blob([buffer], { type: 'audio/wav' });

  function setUint16(data: number) {
    view.setUint16(pos, data, true);
    pos += 2;
  }

  function setUint32(data: number) {
    view.setUint32(pos, data, true);
    pos += 4;
  }
}

export function audioBufferToFile(audioBuffer: AudioBuffer, fileName: string): File {
  const numberOfChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length * numberOfChannels * 2 + 44; // 每个样本占用2个字节，加上头部的44字节
  const buffer = new ArrayBuffer(length);
  const view = new DataView(buffer);
  const channels: Float32Array[] = [];

  function writeWAVHeader() {
    // RIFF 标志
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + audioBuffer.length * numberOfChannels * 2, true);
    // WAVE 标志
    writeString(view, 8, 'WAVE');
    // fmt 子块
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, audioBuffer.sampleRate, true);
    view.setUint32(28, audioBuffer.sampleRate * 4, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    // data 子块
    writeString(view, 36, 'data');
    view.setUint32(40, audioBuffer.length * numberOfChannels * 2, true);
  }

  function encodeAudioData() {
    let offset = 44;
    for (let i = 0; i < numberOfChannels; i++) {
      channels.push(audioBuffer.getChannelData(i));
    }
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let j = 0; j < numberOfChannels; j++) {
        const sample = Math.max(-1, Math.min(1, channels[j][i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7fff, true);
        offset += 2;
      }
    }
  }

  writeWAVHeader();
  encodeAudioData();

  return new File([buffer], fileName, { type: 'audio/wav', lastModified: Date.now() });
}

// 定义一个函数，将 File 转换为 ArrayBuffer
export function fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise<ArrayBuffer>((resolve, reject) => {
    const reader = new FileReader();

    // 设置处理读取完成的事件处理器
    reader.onload = () => {
      if (reader.result) {
        resolve(reader.result as ArrayBuffer);
      } else {
        reject(new Error('File was empty'));
      }
    };

    // 设置处理读取错误的事件处理器
    reader.onerror = () => {
      reject(reader.error);
    };
    // 开始读取文件，将其读取为 ArrayBuffer
    reader.readAsArrayBuffer(file);
  });
}
export async function blobUrlToAudioBuffer(blobUrl: string): Promise<AudioBuffer> {
  const audioContext = new AudioContext();

  // 使用 fetch 获取 Blob 数据
  const response = await fetch(blobUrl);
  const blob = await response.blob();

  // 将 Blob 转换为 ArrayBuffer
  const arrayBuffer = await blob.arrayBuffer();

  // 使用 Web Audio API 的 decodeAudioData 方法将 ArrayBuffer 解码为 AudioBuffer
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

  return audioBuffer;
}
export async function sliceAudio(audioBuffer: AudioBuffer, startTime: number, endTime: number): Promise<AudioBuffer> {
  const audioContext = new AudioContext();

  // 验证时间是否合理
  if (startTime < 0 || startTime >= endTime) {
    throw new Error('Invalid start or end time');
  }
  // eslint-disable-next-line no-param-reassign
  endTime = endTime < audioBuffer.duration ? endTime : audioBuffer.duration; // 如果结束时间大于音频长度，则将结束时间设置为音频长度

  // 计算开始和结束的样本索引
  const startSample = Math.floor(startTime * audioBuffer.sampleRate);
  const endSample = Math.floor(endTime * audioBuffer.sampleRate);

  // 计算样本数
  const numSamples = endSample - startSample;

  // 创建一个新的AudioBuffer来存储剪切后的音频
  const newAudioBuffer = audioContext.createBuffer(audioBuffer.numberOfChannels, numSamples, audioBuffer.sampleRate);

  // 逐通道复制样本数据
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    const oldData = audioBuffer.getChannelData(channel);
    const newData = newAudioBuffer.getChannelData(channel);
    for (let i = 0; i < numSamples; i++) {
      newData[i] = oldData[startSample + i];
    }
  }

  return newAudioBuffer;
}
export async function audioBufferToBlobUrl(audioBuffer: AudioBuffer): Promise<string> {
  // 获取音频数据的通道数
  const numOfChannels = audioBuffer.numberOfChannels;
  const sampleRate = audioBuffer.sampleRate;
  const length = audioBuffer.length;

  // 创建缓冲区来存储交织的音频数据
  const interleaved = new Float32Array(numOfChannels * length);

  // 交织音频数据
  for (let channel = 0; channel < numOfChannels; channel++) {
    const channelData = audioBuffer.getChannelData(channel);
    for (let i = 0, j = channel; i < channelData.length; i++, j += numOfChannels) {
      interleaved[j] = channelData[i];
    }
  }

  // 将交织的音频数据转换为 WAV 格式
  const buffer = new ArrayBuffer(44 + interleaved.length * 2);
  const view = new DataView(buffer);

  // 写入 WAV 文件头
  // RIFF identifier
  writeString(view, 0, 'RIFF');
  // file length minus RIFF identifier length and file description length
  view.setUint32(4, 44 + interleaved.length * 2 - 8, true);
  // RIFF type
  writeString(view, 8, 'WAVE');
  // format chunk identifier
  writeString(view, 12, 'fmt ');
  // format chunk length
  view.setUint32(16, 16, true);
  // sample format (raw)
  view.setUint16(20, 1, true);
  // channel count
  view.setUint16(22, numOfChannels, true);
  // sample rate
  view.setUint32(24, sampleRate, true);
  // byte rate (sample rate * block align)
  view.setUint32(28, sampleRate * 4, true);
  // block align (channel count * bytes per sample)
  view.setUint16(32, numOfChannels * 2, true);
  // bits per sample
  view.setUint16(34, 16, true);
  // data chunk identifier
  writeString(view, 36, 'data');
  // data chunk length
  view.setUint32(40, interleaved.length * 2, true);

  // 写入PCM音频数据
  floatTo16BitPCM(view, 44, interleaved);

  // 创建 Blob 并生成 Blob URL
  const blob = new Blob([view], { type: 'audio/wav' });
  const blobUrl = URL.createObjectURL(blob);

  return blobUrl;
}

// base64ToArrayBuffer 函数
export function base64ToArrayBuffer(base64: string) {
  const binary_string = window.atob(base64);
  const len = binary_string.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binary_string.charCodeAt(i);
  }
  return bytes;
}

export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.onload = e => {
      resolve(e.target?.result as string);
    };
    // readAsDataURL
    fileReader.readAsDataURL(blob);
    fileReader.onerror = () => {
      reject(new Error('blobToBase64 error'));
    };
  });
}
