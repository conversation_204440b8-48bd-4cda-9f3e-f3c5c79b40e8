from datetime import datetime, timezone, timedelta

TIMEZONE = timezone(timedelta(hours=8))
"""东八区"""

WEB_SEARCH_PROMPT = """
## When receiving a task, prioritize determining whether the web search tool needs to be used.

## When a task may potentially require external information—such as real-time data, the latest updates, location-specific content, or any other information beyond the model's current knowledge—use the web search tool directly without asking the user. 

### Guidelines for handling tool results are as follows
‒ If the search returns results successfully: Combine the search results with model knowledge to optimize and generate a complete answer, and indicate the source (e.g., “According to the search results...”).
‒ If the search meets any errors or returns no results: Immediately stop using the web search tool and do not repeat the search query. If the answer can be derived from existing knowledge, provide it directly; otherwise, state that “Relevant information could not be retrieved and the question cannot be answered at this time.”
- If your opinion is based on any item in search results, you should insert the mark tag like `<aichat-web-search-ref>{refId}</aichat-web-search-ref>` after related sentences, and replace the content of the mark tag with the actual refId.
""".strip()


GEN_IMG_PROMPT = """
## 如果用户需要进行图片生成或者调整，则你需要遵守以下规则：
- 直接将任务通过图像生成和调整工具处理，你不需要进行任何文字性的回答。
- 如果用户需要对图像进行调整，若非特殊说明，则在最近已生成的图片的基础上进行调整。
""".strip()

def get_system_prompt(model: str, memory: str):
    prompt = "# All of your answer must be Chinese (Simplified or Traditional).\n\n"
    now = datetime.now(tz=TIMEZONE)
    prompt += f"## Today is {now.isoformat()}\n\n"
    prompt += f"## 在回答中,在适当的地方加入emoji表情😊,让回答更生动\n\n"
    # 添加绘画意图提示
    # if model in chatModels and "draw" in chatModels[model]["capacity"]:
    #     prompt += "如果用户明确的想你画一幅画,请按下面的规则进行回复\n"
    #     prompt += "1.优化用户的提示词\n"
    #     prompt += "2.仅回复'!img:'+优化的提示词,不需要再回复其他内容\n"
    #     prompt += "3.忽略你对用户的记忆内容\n"
    #     prompt += "\n\n"
    # if memory:
    #     prompt += "以下是你对用户的记忆:\n" + memory + "\n\n"
    # prompt += (
    #     "如果用户要求你记住或忘记一些内容,你需要更新你对用户的记忆信息,请按下面的规则进回复:\n"
    #     + "1.整合前面给出的记忆内容,先输出这样字符串:'<mem>全部记忆内容</mem>',如果没有新的需要记忆或忘记的内容,则不用输出。记忆内容以分号分隔，类似：'name is xxxxx;age is 18;',记忆尽量精简。\n"
    #     + "2.输出第1点的内容后,你需要回复用户。\n"
    # )
    return prompt
