from sqlalchemy import Column, Integer, TIMESTAMP, String
from sqlalchemy.dialects.mysql import TINYINT, INTEGER
from utils.database import Base

class ModelPrice(Base):
    __tablename__ = "model_price_config"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    capacity = Column(String(100), nullable=False, comment="能力")
    model = Column(String(100), nullable=False, comment="模型名称")
    api_type = Column(TINYINT, nullable=False, comment="计费类型1-point 2-token")
    unit = Column(INTEGER, nullable=False, default=1, comment="单位，如： 1次、1000000 tokens，输入数字部分")
    credit = Column(Integer, nullable=True)
    updatetime = Column(TIMESTAMP, nullable=True)
    editor = Column(String(100), nullable=True)

    def __repr__(self):
        return f"<ModelPrice(id={self.id}, capacity={self.capacity}, model={self.model},credit={self.credit})>"





