import logging

from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Enum, DateTime, text
from utils.database import Base, AsyncSessionLocal
from typing import List
from datetime import datetime
import json


logger = logging.getLogger(__name__)

class chatHistory(BaseModel):
    isEdit: bool = False
    title: str = ...
    uuid: int = ...
    id: int = 0
    create_time: datetime = None
    role: str = "assistant"
    memory: str = ""


class chatState(BaseModel):
    active: int = 0
    chat: list = []
    history: List[chatHistory] = []
    usingContext: bool = True
    models: List = []


class ChatConversations(Base):
    __tablename__ = "chat_conversations"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)
    chatid = Column(Integer, nullable=False, index=True)
    title = Column(String, nullable=False)
    state = Column(Integer, default=1)
    create_time = Column(DateTime)
    roleid = Column(Integer, default=1)
    last_message = Column(String, nullable=True)


class ChatUserMemory(Base):
    __tablename__ = "chat_user_memory"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)
    memory = Column(String, nullable=True, index=False)
    uptime = Column(DateTime)


class ChatRoles(Base):
    __tablename__ = "chat_roles"
    id = Column(Integer, primary_key=True, index=True)
    rolename = Column(String, nullable=False, index=True)
    avatar = Column(String, nullable=False, index=True)
    prompt = Column(String, nullable=False)
    greeting = Column(String, nullable=False)
    editor = Column(String, nullable=False)
    uptime = Column(DateTime)


class chatHistoryState(int, Enum):
    inactive = 0  # 非active的记录
    active = 1  # active的记录


class chatHistoryIsError(int, Enum):
    no = 0
    yes = 1


class chatHistoryFiles(BaseModel):
    mimetype: str | None = "image/png"
    url: str
    gs_uri: str | None = None
    type: str | None = "image"
    ext: str | None = "png"


class chatHistoryRole(str, Enum):
    system = "system"
    user = "user"
    assistant = "assistant"
    model = "model"


class ChatHistory(Base):
    __tablename__ = "chat_history"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)
    chatid = Column(Integer, nullable=False, index=True)
    model = Column(String, nullable=False)
    role = Column(
        Enum("system", "user", "assistant", "model", name="chat_history_role"),
        nullable=False,
    )
    state = Column(Integer, default=chatHistoryState.active)
    qid = Column(Integer, comment="用户提问的id")
    content = Column(String, nullable=False)
    create_time = Column(DateTime)
    files = Column(String, comment="文件对象(chatHistoryFiles)的列表")
    is_error = Column(Integer, default=chatHistoryIsError.no)
    memory = Column(String, nullable=True, index=False)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)
    ip = Column(String, nullable=False, default="")


class chatContent(BaseModel):
    role: str
    content: str
    files: List[chatHistoryFiles] | None


async def get_user_chat_history(username, chatid, qid):
    async with AsyncSessionLocal() as db:
        try:
            # 记录查询参数
            logger.info(f"获取用户历史记录: username={username}, chatid={chatid}, qid={qid}")

            # 修改SQL查询，获取所有有效历史记录
            sql = """select role, content, unix_timestamp(create_time) as create_time, files, id, qid, model
                from chat_history
                where chatid = :chatid
                and state = 1
                and is_error = 0
                and username = :username
                order by id asc
            """
            data = await db.execute(
                text(sql), {"chatid": chatid, "username": username}
            )
            result = data.fetchall()

            logger.info(f"查询到历史记录数量: {len(result)}")

            # 用于存储处理后的消息
            processed_messages = []

            # 用于跟踪用户问题和对应的助手回复
            user_questions = {}  # qid -> user message
            assistant_replies = {}  # qid -> [assistant messages]

            # 记录问题ID的顺序
            question_order = []

            # 第一步：解析所有消息并按问题ID分组
            for r in result:
                (role, content, msgtime, files_json, msg_id, msg_qid, model) = r

                # 跳过空内容
                if not content:
                    continue

                # 解析files字段的JSON数据
                files = []
                if files_json:
                    try:
                        files_data = json.loads(files_json)
                        if isinstance(files_data, list):
                            for file_obj in files_data:
                                if isinstance(file_obj, dict) and "type" in file_obj and "url" in file_obj:
                                    if "mimetype" not in file_obj:
                                        file_obj["mimetype"] = "image/png" if file_obj["type"] == "image" else "video/mp4"
                                    if "ext" not in file_obj:
                                        file_obj["ext"] = "png" if file_obj["type"] == "image" else "mp4"
                                    if "gs_uri" not in file_obj:
                                        file_obj["gs_uri"] = None
                            files = [chatHistoryFiles(**file) for file in files_data]
                    except Exception as e:
                        logger.error(f"解析文件JSON出错: {e}, files_json: {files_json}")

                # 创建消息对象
                message = {
                    "content": chatContent(role=role, content=content, files=files),
                    "time": msgtime,
                    "id": msg_id,
                    "qid": msg_qid,
                    "model": model
                }

                # 按角色分组存储
                if role == "user":
                    # 用户消息：直接以其ID为键存储
                    user_questions[msg_id] = message
                    # 记录问题顺序
                    if msg_id not in question_order:
                        question_order.append(msg_id)
                elif role == "assistant":
                    # 助手消息：按问题ID分组
                    if msg_qid not in assistant_replies:
                        assistant_replies[msg_qid] = []
                    assistant_replies[msg_qid].append(message)

            # 第二步：构建有效对话历史
            valid_qids = []

            # 按创建时间排序问题ID
            question_order.sort()

            # 找出当前问题之前的所有有效问题ID
            for q_id in question_order:
                # 如果指定了qid且当前问题ID大于等于指定的qid，则停止
                if qid and q_id >= qid:
                    break
                valid_qids.append(q_id)

            # 第三步：构建对话序列，每个用户问题选择最新的助手回复
            conversation = []
            for q_id in valid_qids:
                # 添加用户问题
                if q_id in user_questions:
                    conversation.append(user_questions[q_id])

                # 查找该问题的助手回复
                if q_id in assistant_replies and assistant_replies[q_id]:
                    # 按时间排序，选择最新回复
                    replies = sorted(assistant_replies[q_id], key=lambda x: x["time"], reverse=True)
                    conversation.append(replies[0])  # 只保留最新回复

            # 第四步：限制对话长度，保留最新的几轮对话
            MAX_TURNS = 10  # 最多保留10轮对话
            if len(conversation) > MAX_TURNS * 2:
                conversation = conversation[-MAX_TURNS * 2:]

            # 第五步：提取最终对话内容
            final_contents = []

            # 找出最后一个assistant的回复索引
            last_assistant_idx = -1
            for i in range(len(conversation)-1, -1, -1):
                if conversation[i]["content"].role == "assistant":
                    last_assistant_idx = i
                    break

            # 处理对话内容，对assistant回复进行特殊处理
            for i, msg in enumerate(conversation):
                # 如果是assistant角色且不是最后一个assistant回复，则只保留前100个字符
                if msg["content"].role == "assistant" and i != last_assistant_idx:
                    # 复制一个新的chatContent对象，避免修改原始对象
                    modified_content = msg["content"].content
                    if len(modified_content) > 100:
                        modified_content = modified_content[0:100] + "..."

                    final_contents.append(chatContent(
                        role=msg["content"].role,
                        content=modified_content,
                        files=msg["content"].files
                    ))
                else:
                    final_contents.append(msg["content"])

            # 记录处理结果
            logger.info(f"处理后的历史内容数量: {len(final_contents)}")
            for i, content in enumerate(final_contents):
                has_files = bool(content.files and len(content.files) > 0)
                logger.info(f"历史内容[{i}]: role={content.role}, 有文件={has_files}")
                if has_files:
                    logger.info(f"  文件: {content.files}")

            return final_contents
        except Exception as e:
            logger.error(f"获取聊天历史时出错: {str(e)}")
            # 出错时返回空列表，不中断整个请求流程
            return []
        finally:
            await db.close()
