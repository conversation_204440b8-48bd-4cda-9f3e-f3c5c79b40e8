<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'GameOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: 'add' | 'edit';
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

type SubmitModel = {
  gamecode: string;
  gamename: string;
  info: string;
  lang: string;
  editor: string;
  company: string;
  english_name: string;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '新增游戏' : '编辑信息';
});

type Model = {
  gamecode: string;
  gamename: string;
  info: string;
  lang: string[];
  editor: string;
  company: string;
  english_name: string;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    gamecode: '',
    gamename: '',
    info: '',
    lang: [],
    editor: '',
    company: '',
    english_name: ''
  };
}

type RuleKey = 'gamecode' | 'gamename' | 'lang' | 'company';

const { patternRules } = useFormRules();

const rules: Record<RuleKey, any> = {
  gamecode: defaultRequiredRule,
  gamename: defaultRequiredRule,
  lang: patternRules.lang,
  company: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  // console.log('Model after assigning default values:', props.rowData);

  if (props.operateType === 'edit' && props.rowData) {
    const rowData = { ...props.rowData, lang: props.rowData.lang ? props.rowData.lang.split(',') : [] };
    Object.assign(model, rowData);
    // console.log('Model after assigning rowData:', model);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // Mock request
  // console.log(model);
  const submitModel = {
    ...model,
    lang: model.lang.filter(Boolean).join(',') // 将 lang 数组转换为逗号分隔的字符串
  };
  closeDrawer();
  emit('submitted', submitModel);
}

// 语言选项
const langOptions = [
  { label: '中文', value: 'zh' },
  { label: '英文', value: 'en' }
];

// 所属公司选项
const companyOptions = [
  { label: 'originmood', value: 'originmood' },
  { label: 'heyyogame', value: 'heyyogame' },
  { label: 'igamebuy', value: 'igamebuy' }
];

watch(visible, newVal => {
  // console.log('visible changed:', newVal);
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="游戏编号" path="gamecode">
          <NInput
            v-model:value="model.gamecode"
            placeholder="请输入游戏编号"
            :disabled="props.operateType === 'edit'"
          />
        </NFormItem>
        <NFormItem label="游戏名称" path="gamename">
          <NInput v-model:value="model.gamename" placeholder="请输入游戏名称" />
        </NFormItem>
        <NFormItem label="英文名称" path="english_name">
          <NInput v-model:value="model.english_name" placeholder="请输入游戏名称" />
        </NFormItem>
        <NFormItem label="游戏资料" path="info">
          <NInput v-model:value="model.info" type="textarea" placeholder="请输入游戏资料" rows="6" />
        </NFormItem>
        <NFormItem label="语言" path="lang">
          <NSelect v-model:value="model.lang" multiple :options="langOptions" placeholder="请选择语言" />
        </NFormItem>
        <NFormItem label="所属公司" path="company">
          <NSelect v-model:value="model.company" :options="companyOptions" placeholder="请选择所属公司" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
