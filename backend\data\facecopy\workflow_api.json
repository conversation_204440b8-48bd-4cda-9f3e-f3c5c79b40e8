{"1": {"inputs": {}, "class_type": "DownloadAndLoadLivePortraitModels", "_meta": {"title": "(Down)Load LivePortraitModels"}}, "4": {"inputs": {"image": "2.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "8": {"inputs": {"video": "d0.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1}, "class_type": "VHS_LoadVideo", "_meta": {"title": "Load Video (Upload) 🎥🅥🅗🅢"}}, "30": {"inputs": {"dsize": 512, "scale": 2.3, "vx_ratio": 0, "vy_ratio": -0.125, "lip_zero": true, "eye_retargeting": false, "eyes_retargeting_multiplier": 1, "lip_retargeting": false, "lip_retargeting_multiplier": 1, "stitching": true, "relative": true, "pipeline": ["1", 0], "source_image": ["4", 0], "driving_images": ["8", 0]}, "class_type": "LivePortraitProcess", "_meta": {"title": "LivePortraitProcess"}}, "32": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["30", 1]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}}