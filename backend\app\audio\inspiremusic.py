import logging
import json
import importlib
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from utils.database import get_db
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from models.users import User, get_request_user
from app.api_request_time import record_time
from app.audio.musciPrompt import music_prompt, generate_title_prompt
from models.tasks import TaskStatus, TaskType
from task_queue.task_manager import TaskManager
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

# 动态导入OpenAI聊天服务模块
chatServer = importlib.import_module("service.openai.chat")

# task_handlers/music_handler.py

async def generate_music_title(text: str) -> str:
    """
    使用OpenAI模型生成歌曲标题
    
    参数:
    - text: 歌曲描述文本（提示词）
    
    返回:
    - 生成的歌曲标题
    """
    try:
        from models.chat import chatContent
        
        # 准备提示语（使用现有模板）
        formatted_prompt = generate_title_prompt.format(text=text)
        
        # 准备对话内容
        contents = [
            chatContent(role="user", content=formatted_prompt, files=None)
        ]
        
        # 调用ChatGPT生成标题（使用gpt-4o-mini模型）
        title = ""
        generator = chatServer.chat(
            model_name="gpt-4o-mini",
            contents=contents,
            systemPrompt="你是一个专业的音乐标题生成专家，请根据描述生成简短有力的歌曲标题",
            temperature=0.7
        )
        
        for chunk in generator:
            if isinstance(chunk, dict) and "input_tokens" in chunk:
                # 这是最后一个chunk，包含token统计，跳过
                continue
            # 拼接返回内容
            title += chunk if chunk else ""
        
        # 清理标题（去除多余空格和换行符）
        title = title.strip()
        
        # 如果标题为空，提供默认值
        if not title:
            title = "未命名歌曲"
            
        # 限制标题长度
        if len(title) > 6:
            title = title[:6]
            
        logger.info(f"AI生成的歌曲标题: {title}")
        return title
        
    except Exception as e:
        logger.error(f"生成歌曲标题失败: {e}", exc_info=True)
        # 发生错误时返回默认标题
        return "未命名歌曲"

class MusicGenerationRequest(BaseModel):
    text: str
    model_name: str = "InspireMusic-1.5B"
    chorus: str = "intro"
    output_sample_rate: int = 48000
    max_generate_audio_seconds: float = 30.0
    music_title: Optional[str] = None
    
    model_config = {"protected_namespaces": ()}

class MusicContinuationRequest(BaseModel):
    text: str
    audio_base64: str
    model_name: str = "InspireMusic-1.5B"
    chorus: str = "intro"
    output_sample_rate: int = 48000
    max_generate_audio_seconds: float = 30.0
    music_title: Optional[str] = None
    
    model_config = {"protected_namespaces": ()}

class MusicTaskResponse(BaseModel):
    code: str = "0000"
    data: Optional[Dict[str, Any]] = None  # 使用Dict代替特定模型，以支持通用格式
    msg: Optional[str] = None

# 新的历史记录响应模型
class MusicHistoryResponse(BaseModel):
    code: str = "0000"
    data: Optional[List[Dict[str, Any]]] = None
    msg: Optional[str] = None

@router.post("/generate", tags=["inspiremusic"], response_model=MusicTaskResponse)
@record_time(api_name="inspire_music_generate")
async def generate_music(
    req: MusicGenerationRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    音乐生成接口 - 创建生成任务
    """
    try:
        # 自动生成歌曲标题
        if not req.music_title:
            req.music_title = await generate_music_title(req.text)
            logger.info(f"已为用户 {user.username} 自动生成歌曲标题: {req.music_title}")
        
        # 任务参数
        params = req.model_dump()
        
        # 创建任务
        task, error_msg = await TaskManager.create_task(
            db=db,
            username=user.username,
            task_type=TaskType.MUSIC,
            action="GENERATE",
            task_params=params
        )
        
        # 检查任务是否因超限而未创建
        if task is None:
            raise ClientVisibleException(error_msg or "创建任务失败，请重试")
            
        # 转换为通用输出格式
        task_out = {
            "id": task.id,
            "taskid": task.taskid,
            "username": task.username,
            "status": task.status,
            "action": task.action,
            "submit_time": task.submit_time.isoformat() if task.submit_time else None,
            "queue_position": task.queue_position,
            # 将特定于音乐的参数从task_params提取出来
            "music_title": task.task_params.get("music_title", "未命名歌曲"),
            "music_type": task.task_params.get("model_name", "InspireMusic-1.5B"),
            "prompt": task.task_params.get("text", "")
        }
            
        # 返回任务信息
        return MusicTaskResponse(
            code="0000",
            data=task_out
        )
        
    except Exception as e:
        logger.error(f"创建生成任务失败 for user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("创建任务失败，请重试") from e

@router.post("/continue", tags=["inspiremusic"], response_model=MusicTaskResponse)
@record_time(api_name="inspire_music_continue")
async def continue_music(
    req: MusicContinuationRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    音乐续写接口 - 创建续写任务
    """
    try:
        # 自动生成歌曲标题
        if not req.music_title:
            req.music_title = await generate_music_title(req.text)
            logger.info(f"已为用户 {user.username} 自动生成续写歌曲标题: {req.music_title}")
        
        # 任务参数
        params = req.model_dump()
        
        # 创建任务
        task, error_msg = await TaskManager.create_task(
            db=db,
            username=user.username,
            task_type=TaskType.MUSIC,
            action="CONTINUE",
            task_params=params
        )
        
        # 检查任务是否因超限而未创建
        if task is None:
            raise ClientVisibleException("创建任务失败，请重试")
            
        # 转换为通用输出格式，与上面保持一致
        task_out = {
            "id": task.id,
            "taskid": task.taskid,
            "username": task.username,
            "status": task.status,
            "action": task.action,
            "submit_time": task.submit_time.isoformat() if task.submit_time else None,
            "queue_position": task.queue_position,
            # 将特定于音乐的参数从task_params提取出来
            "music_title": task.task_params.get("music_title", "未命名歌曲"),
            "music_type": task.task_params.get("model_name", "InspireMusic-1.5B"),
            "prompt": task.task_params.get("text", "")
        }
            
        # 返回任务信息
        return MusicTaskResponse(
            code="0000",
            data=task_out
        )
        
    except Exception as e:
        logger.error(f"创建续写任务失败 for user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("创建任务失败，请重试") from e

@router.get("/task_status/{taskid}", tags=["inspiremusic"], response_model=MusicTaskResponse)
@record_time(api_name="inspire_music_task_status")
async def get_task_status(
    taskid: str,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    查询任务状态
    """
    try:
        # 使用通用任务管理器查询任务
        task = await TaskManager.get_task_status(
            db=db,
            taskid=taskid,
            username=user.username
        )
        
        if not task:
            raise ClientVisibleException("任务不存在")
        
        # 构建输出数据结构
        task_out = {
            "id": task.id,
            "taskid": task.taskid,
            "username": task.username,
            "status": task.status,
            "action": task.action,
            "submit_time": task.submit_time.isoformat() if task.submit_time else None,
            "start_time": task.start_time.isoformat() if task.start_time else None,
            "finish_time": task.finish_time.isoformat() if task.finish_time else None,
            "queue_position": task.queue_position,
            "fail_reason": task.fail_reason,
            # 将特定于音乐的参数从task_params提取出来
            "music_title": task.task_params.get("music_title", "未命名歌曲"),
            "music_type": task.task_params.get("model_name", "InspireMusic-1.5B"),
            "prompt": task.task_params.get("text", "")
        }
        
        # 如果任务成功且有结果，解析结果JSON
        if task.status == TaskStatus.SUCCESS and task.task_result:
            task_out["audio_data"] = task.task_result
            # 兼容旧版，添加audio_url字段
            task_out["audio_url"] = json.dumps(task.task_result)
        elif task.resource_url:
            # 如果只有资源URL，创建简单的结果对象
            task_out["audio_data"] = {
                "url": task.resource_url,
                "type": "oss_url"
            }
            # 兼容旧版，添加audio_url字段
            task_out["audio_url"] = json.dumps({"url": task.resource_url, "type": "oss_url"})
        
        # 返回任务信息
        return MusicTaskResponse(
            code="0000",
            data=task_out
        )
        
    except Exception as e:
        logger.error(f"查询任务状态失败 for task {taskid}, user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("查询任务状态时发生内部错误") from e

@router.get("/history", tags=["inspiremusic"], response_model=MusicHistoryResponse)
async def get_music_history(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    title: Optional[str] = Query(None, description="歌曲标题（支持模糊搜索）"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的音乐生成历史记录（分页）
    支持按歌曲标题搜索
    """
    try:
        # 构建过滤条件
        filter_params = {}
        
        # 添加任务类型过滤
        tasks, total_count = await TaskManager.get_user_tasks(
            db=db,
            username=user.username,
            task_type=TaskType.MUSIC,
            page=page,
            size=size,
            filter_params=filter_params
        )
        
        processed_tasks = []
        for task in tasks:
            # 构建输出数据结构
            task_out = {
                "id": task.id,
                "taskid": task.taskid,
                "username": task.username,
                "status": task.status,
                "action": task.action,
                "submit_time": task.submit_time.isoformat() if task.submit_time else None,
                "start_time": task.start_time.isoformat() if task.start_time else None,
                "finish_time": task.finish_time.isoformat() if task.finish_time else None,
                "queue_position": task.queue_position,
                "fail_reason": task.fail_reason,
                # 将特定于音乐的参数从task_params提取出来
                "music_title": task.task_params.get("music_title", "未命名歌曲") if task.task_params else "未命名歌曲",
                "music_type": task.task_params.get("model_name", "InspireMusic-1.5B") if task.task_params else "InspireMusic-1.5B",
                "prompt": task.task_params.get("text", "") if task.task_params else ""
            }
            
            # 如果有标题过滤条件，检查是否匹配
            if title and task.task_params and "music_title" in task.task_params:
                music_title = task.task_params["music_title"]
                # 先尝试精确匹配
                if title != music_title:
                    # 再尝试模糊匹配
                    if title.lower() not in music_title.lower():
                        continue  # 既不精确匹配也不模糊匹配，跳过此任务
            
            # 如果任务成功且有结果，解析结果JSON
            if task.status == TaskStatus.SUCCESS and task.task_result:
                task_out["audio_data"] = task.task_result
                # 兼容旧版，添加audio_url字段
                task_out["audio_url"] = json.dumps(task.task_result)
            elif task.resource_url:
                # 如果只有资源URL，创建简单的结果对象
                task_out["audio_data"] = {
                    "url": task.resource_url,
                    "type": "oss_url"
                }
                # 兼容旧版，添加audio_url字段
                task_out["audio_url"] = json.dumps({"url": task.resource_url, "type": "oss_url"})
            
            processed_tasks.append(task_out)
            
        return MusicHistoryResponse(
            code="0000",
            data=processed_tasks
        )
        
    except Exception as e:
        logger.error(f"查询音乐历史记录失败 for user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("查询历史记录时发生内部错误") from e

@router.get("/prompts", tags=["inspiremusic"])
@record_time(api_name="inspire_music_get_prompts")
async def get_music_prompts(
    genre: Optional[str] = Query(None, description="音乐类型，例如：流行、摇滚、电子舞曲等"),
    user: User = Depends(get_request_user)
):
    """
    获取音乐提示词数据
    
    可选参数：
    - genre: 音乐类型，如果提供，则只返回该类型的提示词
    """
    try:
        return {
            "code": "0000",
            "data": music_prompt
        }
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise ClientVisibleException()
