import asyncio
import logging

from models.chat import chatContent
from typing import List
from service.google.chat import PROJECT_ID

from anthropic import AnthropicVertex
import httpx
import base64
from ..openai.prompts import get_system_prompt

logger = logging.getLogger(__name__)

# LOCATION = "europe-west1"
LOCATION = "us-east5"

client = AnthropicVertex(region=LOCATION, project_id=PROJECT_ID)


def load_image_from_url(image_url: str) -> str:
    return base64.b64encode(httpx.get(image_url).content).decode("utf-8")


def chat(
    model_name: str,
    contents: List[chatContent],
    systemPrompt: str = "",
    memory: str = "",
    temperature: float = 0.8,
    mcp: bool = False,
    web_search: bool = False,
    loop: asyncio.AbstractEventLoop = None,
):
    if not systemPrompt:
        systemPrompt = get_system_prompt(model_name, memory)
    messages = []
    if systemPrompt:
        messages.append(
            {"role": "user", "content": [{"type": "text", "text": systemPrompt}]}
        )
        messages.append(
            {"role": "assistant", "content": [{"type": "text", "text": "好的"}]}
        )
    role_sequence = "assistant"
    for r in contents:
        if r.role == role_sequence:
            continue
        role_sequence = r.role
        content = {
            "role": "user" if r.role == "user" else "assistant",
            "content": [{"type": "text", "text": r.content}],
        }
        if r.files:
            for f in r.files:
                if f.type == "image":
                    content["content"].append(
                        {
                            "type": f.type,
                            "source": {
                                "media_type": f.mimetype,
                                "data": load_image_from_url(f.url),
                                "type": "base64",
                            },
                        }
                    )
        messages.append(content)

    with client.messages.stream(
        max_tokens=4096,
        messages=messages,
        model=model_name,
    ) as stream:
        for text in stream.text_stream:
            yield text
        message = stream.get_final_message()
        yield (
            {
                "input_tokens": message.usage.input_tokens,
                "output_tokens": message.usage.output_tokens,
            }
        )
