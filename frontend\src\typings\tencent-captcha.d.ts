type CallbackRes<S> = {
  /** 验证结果，0：验证成功。2：用户主动关闭验证码。 */
  ret: number;
  /** 验证成功的票据，当且仅当 ret = 0 时 ticket 有值。 */
  ticket: string;
  /** 验证码应用 ID。 */
  appid: string;
  /** 自定义透传参数。 */
  bizState: S;
  /** 本次验证的随机串，后续票据校验时需传递该参数。 */
  randstr: string;
  /** 错误 code ，详情请参见 [回调函数 errorCode 说明](https://cloud.tencent.com/document/product/1110/36841#errorCode)。 */
  errorCode: number;
  /** 错误信息。 */
  errorMessage: string;
  /** 验证码校验接口耗时（ms） */
  verifyDuration: number;
  /** 用户操作校验成功耗时（ms） */
  actionDuration: number;
  /** 链路 sid */
  sid: string;
};

type CaptchaOptions<S> = {
  bizState?: S;
  enableDarkMode?: boolean | 'force';
  sdkOpts?: Record<string, unknown>;
  ready?: (res?: any) => void;
  needFeedBack?: boolean | string;
  loading?: boolean;
  userLanguage?: UserLanguage;
  type?: string;
  aidEncrypted?: string;
  showFn?: () => void;
};

type UserLanguage =
  | 'zh-cn'
  | 'zh-hk'
  | 'zh-tw'
  | 'en'
  | 'ar'
  | 'my'
  | 'fr'
  | 'de'
  | 'he'
  | 'hi'
  | 'id'
  | 'it'
  | 'ja'
  | 'ko'
  | 'lo'
  | 'ms'
  | 'pl'
  | 'pt'
  | 'ru'
  | 'es'
  | 'th'
  | 'tr'
  | 'vi'
  | 'fil'
  | 'ur';

type Callback<S> = (res: CallbackRes<S>) => void;

type Ticket = {
  CaptchaAppId: string;
  ticket: string;
};

declare class TencentCaptcha<S> {
  constructor(captchaAppId: string, callback: Callback<S>, options?: CaptchaOptions<S>);

  /** 显示验证码，可以反复调用。 */
  show(): void;

  /** 隐藏验证码，可以反复调用。 */
  destroy(): void;

  /** 获取验证成功后的 ticket。 */
  getTicket(): Ticket;
}
