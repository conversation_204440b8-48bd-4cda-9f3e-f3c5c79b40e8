<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { $t } from '@/locales';
import { fetchGetAllRoles, fetchGetMenuRole, postSetMenuRole } from '@/service/api';
defineOptions({
  name: 'MenuRoleModal'
});

interface Props {
  menuId: number;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

function closeModal() {
  visible.value = false;
}

const title = computed(() => $t('common.edit') + $t('page.manage.menu.setRole'));

const roleOption = ref<CommonType.Option<number>[]>([]);

const menuRole = ref<number[]>([]);

function handleSubmit() {
  postSetMenuRole(props.menuId, menuRole.value).then(res => {
    if (!res.error) {
      window.$message?.success?.($t('common.modifySuccess'));
      closeModal();
    }
  });
}

const getAllRole = () => {
  roleOption.value = [];
  fetchGetAllRoles().then(res => {
    if (res.data?.records) {
      res.data?.records.forEach(e => {
        const item: CommonType.Option<number> = { label: `${e.roleName}:${e.roleCode}`, value: e.id };
        roleOption.value.push(item);
      });
    }
  });
};
const getMenuRole = () => {
  menuRole.value = [];
  fetchGetMenuRole(props.menuId).then(res => {
    if (res.data?.records) {
      res.data?.records.forEach(e => {
        // 排除超级管理员
        menuRole.value.push(e.id);
      });
    }
  });
};
const init = () => {
  // roleUser.value = [];
  getAllRole();
  getMenuRole();
};

watch(visible, val => {
  if (val) {
    init();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-600px">
    <NTransfer v-model:value="menuRole" virtual-scroll :options="roleOption" source-filterable class="min-w-500px" />
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" class="mt-16px" @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
