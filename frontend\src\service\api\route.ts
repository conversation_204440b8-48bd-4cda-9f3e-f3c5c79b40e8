import { request } from '../request';

/** get constant routes */
export function fetchGetConstantRoutes() {
  // return request<Api.Route.MenuRoute[]>({ url: '/route/getConstantRoutes' });
  return request<Api.Route.MenuRoute[]>({ url: '/system/get_const_menu' });
}

/** get user routes */
export function fetchGetUserRoutes() {
  // return request<Api.Route.UserRoute>({ url: '/route/getUserRoutes' });
  return request<Api.Route.UserRoute>({ url: '/system/get_user_menu' });
}

/**
 * whether the route is exist
 *
 * @param routeName route name
 */
export function fetchIsRouteExist(routeName: string) {
  return request<boolean>({ url: '/route/isRouteExist', params: { routeName } });
}
