"""
以下代码依据均来自官方文档，没用 MCP 或者 SDK（也是 MCP）的原因是，MCP 返回的数据不是结构化的，而是自然语言。
https://cloud.baidu.com/doc/AppBuilder/s/pmaxd1hvy
"""
import json
import logging
from datetime import timedelta
from itertools import count
from typing import Literal, Any

from httpx import RequestError, HTTPStatusError, AsyncClient
from pydantic import BaseModel, Field

from config import app_settings

logger = logging.getLogger(__name__)

_ENDPOINT = "https://qianfan.baidubce.com/v2/ai_search/chat/completions"

_desc = """
执行搜索。

Args:
    query (str): 搜索查询关键词或短语，用于指定需要搜索的内容。支持自然语言查询，可以包含多个关键词。LLM 应该根据用户意图构建合适的查询语句，确保搜索结果的准确性和相关性。最佳实践：1) 使用简洁明确的关键词组合；2) 对于复杂查询，可以拆分为多个简单查询；3) 避免使用过于宽泛或模糊的词语。
    count (int, optional): 单次搜索最大返回数量，最大值为 50。
    search_recency_filter (str, optional): 根据网页发布时间进行筛选。week: 最近7天；month：最近30天；semiyear：最近180天；year：最近365天。
    search_domain_filter (list[str], optional): 置基于站点的过滤条件，对搜索到的结果按指定站点进行筛选，仅返回来自所设站点的内容。例如：设置 ["baidu.com"]，在搜索到的结果中仅返回来自 baidu.com 的搜索结果。
""".strip()


class WebSearchParams(BaseModel):
    query: str
    count: int | None = Field(le=50, ge=1)
    search_recency_filter: Literal["week", "month", "semiyear", "year"] | None = None
    search_domain_filter: list[str] | None = None


WEB_SEARCH_TOOL = {
    "type": "function",
    "function": {
        "name": "baidu_ai_search_api",
        "description": _desc,
        "parameters": WebSearchParams.model_json_schema(),
        "strict": True
    }
}

async def do_web_search(content: str):
    try:
        params = WebSearchParams.model_validate_json(content)
    except ValueError as e:
        logger.error(f"Invalid web search params: {e}")
        raise
    args: dict[str, Any] = dict(
        messages=[{
            "role": "user",
            "content": params.query,
        }],
        search_source="baidu_search_v2",
    )
    if params.count:
        args["resource_type_filter"] = [{"type": "web","top_k": params.count}]
    if params.search_recency_filter:
        args["search_recency_filter"] = params.search_recency_filter
    if params.search_domain_filter:
        args["search_domain_filter"] = params.search_domain_filter
    assert app_settings.baidu_appbuilder_api_key, "Baidu AppBuilder API key is not set."
    client = AsyncClient(timeout=timedelta(seconds=10).total_seconds())
    try:
        res = await client.post(
            url=_ENDPOINT,
            headers={
                "X-Appbuilder-Authorization": f"Bearer {app_settings.baidu_appbuilder_api_key}",
            },
            json=args,
        )
        res.raise_for_status()
    except RequestError as e:
        logger.error(f"Error while calling Baidu AppBuilder API: {e}")
        raise
    except HTTPStatusError as e:
        logger.error(f"Error while calling Baidu AppBuilder API, Status Code: {e.response.status_code}, Body: {e.response.text}")
        raise ValueError("Can not find any result from search engine.") from e
    finally:
        await client.aclose()
    data: dict[str, Any] = res.json()
    code = data.get('code', None)
    if code:
        logger.error(f"Error while calling Baidu AppBuilder API, Status Code: {code}, Body: {data.get('message', '')}")
        raise ValueError("Can not find any result from search engine.")
    references: list[dict[str, Any]] = data.get('references', [])
    if not references:
        raise ValueError("Can not find any result from search engine.")
    return references


def web_search_result_formatter():
    c = count(1)
    def formatter(items: list[dict[str, Any]]) -> str:
        result = []
        for item in items:
            result.append({
                "title": item.get('title', None) or "Untitled",
                "link": item.get('url', None) or '',
                "desc": item.get('content', None) or '',
                "icon": item.get('icon', None) or '',
                "media": item.get('media', None) or '',
                "refId": next(c)
            })
        return json.dumps(result, ensure_ascii=False)
    return formatter
