<!-- <script setup lang="ts">
import { onMounted, ref } from 'vue';
import Starback from 'starback';

declare module 'starback' {}

const props = defineProps<{
  type: 'dot' | 'line';
  quantity?: number;
  width?: number;
  height?: number;
  backgroundColor?: string | string[];
  showFps?: boolean;
  speed?: number;
  starSize?: number | [number, number];
  direction?: number;
  randomOpacity?: boolean | [number, number];
  starColor?: string;
  directionX?: number;
  directionY?: number;
  distanceX?: number;
  frequency?: number;
  slope?: { x: number; y: number };
  spread?: number;
}>();

const background = ref<HTMLCanvasElement | null>(null);
const starbackInstance = ref<Starback | null>(null);

onMounted(() => {
  if (background.value) {
    background.value.width = props.width || window.innerWidth;
    background.value.height = props.height || window.innerHeight;
    try {
      starbackInstance.value = new Starback(background.value, {
        type: props.type,
        width: props.width,
        height: props.height,
        backgroundColor: props.backgroundColor,
        quantity: props.quantity,
        showFps: props.showFps,
        speed: props.speed,
        starSize: props.starSize,
        direction: props.direction,
        randomOpacity: props.randomOpacity,
        starColor: props.starColor,
        directionX: props.directionX,
        directionY: props.directionY,
        distanceX: props.distanceX,
        frequency: props.frequency,
        slope: props.slope,
        spread: props.spread
      });
    } catch (error) {
      console.error('Error initializing Starback:', error);
    }
  } else {
    console.error('Background element is not a canvas');
  }
});
</script>

<template>
  <canvas ref="background" class="background"></canvas>


 <Background
    type="dot"
    :quantity="100"
    :width="800"
    :height="600"
    background-color="#000"
    :show-fps="false"
    :speed="0.5"
    :star-size="[0, 3]"
    :direction="225"
    :random-opacity="[0.1, 0.9]"
    star-color="#ffffff"
  />
</template>

<style scoped>
.background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
</style> -->
