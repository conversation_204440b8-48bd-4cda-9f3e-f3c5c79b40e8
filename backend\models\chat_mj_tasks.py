from utils.database import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Enum, DateTime, JSON
from models.pydanticBase import BaseModel
from enum import Enum as EnumType
from datetime import datetime
from typing import List, Dict, Optional


class ChatMjTasksStatus(str, EnumType):
  NOT_START = "NOT_START"
  SUBMITTED = "SUBMITTED"
  IN_PROGRESS = "IN_PROGRESS"
  FAILURE = "FAILURE"
  SUCCESS = "SUCCESS"
  MODAL = "MODAL"


class ChatMjTasksAction(str, EnumType):
  IMAGINE = "IMAGINE"
  UPSCALE = "UPSCALE"
  VARIATION = "VARIATION"
  REROLL = "REROLL"
  DESCRIBE = "DESCRIBE"
  BLEND = "BLEND"
  PAN = "PAN"


class ChatMjTasksChangeAction(str, EnumType):
  UPSCALE = "UPSCALE"
  VARIATION = "VARIATION"
  REROLL = "REROLL"


class ChatMjTask<PERSON><PERSON>ode<PERSON>(str, EnumType):
  MIDJOURNEY = "MIDJOURNEY"
  OPENAI = "OPENAI"
  FLUX = "FLUX"


class ChatMjTasksOut(BaseModel):
  """
    输出模型
    """
  id: int
  pid: int | None = None
  username: str
  taskid: str
  action: str
  status: str | None = None
  prompt: str | None = None
  prompt_en: str | None = None
  description: str | None = None
  state: str | None = None
  submit_time: datetime | None = None
  start_time: datetime | None = None
  finish_time: datetime | None = None
  image_url: str | None = None
  progress: str | None = None
  fail_reason: str | None = None
  uptime: datetime | None = None
  seed: str | None = None
  button: Optional[List[Dict]] = None
  channels: int = 0  # 默认值为0
  like_count: Optional[int] = 0  # 默认值为0
  is_like: int = 0
  model: str | None = None
  manufacturer: str
  prompt_img: str | None = None
  image_urls: str | None = None



class WorksTasksOut(BaseModel):
  id: int
  pid: int | None = None
  username: str
  taskid: str
  action: str
  status: str | None = None
  prompt: str
  image_url: str | None = None
  progress: str | None = None
  fail_reason: str | None = None
  total_page: int
  seed: str | None = None
  channels: int = 0  # 默认值为0
  like_count: Optional[int] = 0  # 默认值为0
  is_like: int = 0


class ChatMjTasks(Base):
  __tablename__ = "chat_mj_tasks"
  id = Column(Integer, primary_key=True, index=True)
  pid = Column(Integer)
  username = Column(String, nullable=False, index=True)
  taskid = Column(String, nullable=False, index=True)
  action = Column(Enum(ChatMjTasksAction), nullable=False)
  status = Column(Enum(ChatMjTasksStatus))
  prompt = Column(String, nullable=False)
  prompt_en = Column(String)
  description = Column(String)
  state = Column(String)
  submit_time = Column(DateTime)
  start_time = Column(DateTime)
  finish_time = Column(DateTime)
  image_url = Column(String)
  progress = Column(String)
  fail_reason = Column(String)
  uptime = Column(DateTime)
  seed = Column(String)
  buttons = Column(JSON)
  button = Column(JSON, comment="列buttons返回前端数据")
  channels = Column(Integer, default=0, nullable=False, comment="频道字段，默认值为0，不能为空")
  like_count = Column(Integer, default=0, nullable=True, comment="点赞数")
  manufacturer = Column(Enum(ChatMjTasksModel), default=ChatMjTasksModel.MIDJOURNEY, nullable=False)
  prompt_img = Column(String, comment="参考图，使用列表形式，适配多张参考图的情况")
  image_urls = Column(String, comment="用于存储多张图片的 URL，列表[]")
  model = Column(String, comment="模型名称")



chat_models = [
  {
    "label": "Midjourney", 
    "value": "MIDJOURNEY",
    "models": [
      {"value": " --v 6.1", "label": "Midjourney Model V6.1"},
      {"value": " --niji 6", "label": "Niji Model V6[ALPHA]"},
      {"value": " --v 7", "label": "Midjourney Model V7[ALPHA]"},
      {"value": " --v 6", "label": "Midjourney Model V6[ALPHA]"},
      {"value": " --v 5", "label": "Midjourney Model V5.2"},
      {"value": " --niji 5", "label": "Niji Model V5"},
      {"value": " --niji 4", "label": "Niji Model V4"},
      {"value": " --v 4", "label": "Midjourney Model V4"},
      {"value": " --v 3", "label": "Midjourney Model V3"},
      {"value": " --v 2", "label": "Midjourney Model V2"},
      {"value": " --v 1", "label": "Midjourney Model V1"}
    ]
  },
  {
    "label": "OpenAI",
    "value": "OPENAI", 
    "models": [
      {"value": "image-1", "label": "IMAGE-1"}
    ]
  }
  ,
  {
    "label": "Flux",
    "value": "FLUX",
    "models": [
      # {"value": "flux-kontext-pro", "label": "Flux-Kontext-Pro"}
      # {"value": "flux-kontext-max", "label": "Flux-Kontext-Max"}
      {"value": "flux_kontext_fp8_api", "label": "Flux-Kontext-Dev"}  # value 是comfyui的工作流文件名
    ]
  }
]

