"""
提供给 AI 助手使用生图工具
"""
import asyncio
import base64
import logging
from datetime import timedelta, datetime
from typing import Annotated, Literal

import nanoid
from pydantic import BaseModel, Field

from service.file import save_image_b64data
from .draw import upload_image_to_comfyui, replace_workflow_data, submit_task_to_comfyui, \
    query_task_status_from_comfyui, get_image_from_comfyui

logger = logging.getLogger(__name__)


prompt_description = '''
# 要求

- 使用中文进行描述。
- 描述应当尽量详尽，在一段话内将以下构图元素描述清楚，尽量接近人类自然语言。
- 注意并不是所有构图元素都需要用上，仅添加上相关部分即可。

## 构图元素
- 主体形象：你可以发挥自己的想象力，使用最华丽的词汇进行描述，包括对主体外观、眼睛、服装、体型和表情的描述，注意主体的形象应与氛围匹配。
- 场景：尽可能详细的描述一个场景，该场景的描述应与主体形象的意境相匹配。
- 氛围：这里填写使用的氛围词列表和期待，符合{主要内容}意境的词汇 。
- 镜头：这里填写使用的镜头，注意镜头视角的选择应有助于增强画面表现力。
- 照明：这里填写使用的照明词或类似的词条，请注意照明词条的选择应于主体形象、场景的意境相匹配 。
- 绘画风格：这里填写使用的绘画风格或类似的词条，请注意绘画风格的选择应与主体形象、场景、照明的意境匹配 。
- 参考画家：请根据指令的整体氛围、意境选择画风参考的画家。
- 画质：你可以选择 Detailed、Ultimate、Excellence、Masterpiece 或类似的词条。

# 输出示例

```
在阳光明媚的春日里，一片充满活力的绿色草地上，散布着雏菊、毛茛等五颜六色的小野花，背景中还立着一小段质朴的木栅栏。镜头以特写视角与一只毛茸茸的可爱小黄鸡视线齐平，将它的模样清晰展现：大大的黑色眼睛里满是好奇，小巧的橘色嘴巴搭配稚嫩的翅膀，天真无邪的脸上带着愉快的表情。明亮柔和的自然阳光洒在它身上，在绒毛上形成一层柔和的光晕，整个画面洋溢着愉快、温暖、活泼、宁静又充满生机的氛围。这幅数字绘画采用可爱卡通的童书插画风格，堪称画质卓越的杰作。
```
'''.strip()

aspect_ratio_map = {
    '1:1': (1024, 1024),
    '16:9': (1920, 1080),
    '9:16': (1080, 1920),
}
aspect_ratio_description = '''
生成图片时所使用的宽高比。可选值包括：
- 1:1
- 16:9
- 9:16

其中 1:1 的尺寸为 1024*1024，16:9 的尺寸为 1920*1080（横屏），9:16 的尺寸为 1080*1920（竖屏），默认使用 1:1。
'''.strip()


class GenParams(BaseModel):
    prompt: Annotated[str, Field(description=prompt_description)]
    image_url: Annotated[str | None, Field(description="若用户需要通过文字生成图片，则此项为空；若用户需要调整图片，则此项不能为空，应填入用户上传的图片 URL，或者是过往对话中生成的图片 URL")] = None
    aspect_ratio: Annotated[Literal['1:1', '16:9', '9:16'], Field(description=aspect_ratio_description)] = '1:1'


GEN_IMG_TOOL = {
    "type": "function",
    "function": {
        "name": "comfyui_image_generation",
        "description": "图片生成和调整工具，根据提示词及参考图片，实现文生图、 图生图和图片调整三种功能。",
        "parameters": GenParams.model_json_schema(),
        "strict": True
    }
}

TTI_TMPL = 'flux_kontext_text2img'
"""文生图工作流模板"""
ITI_TMPL = 'flux_kontext_fp8_api'
"""图生图工作流模板"""

POLLING_INTERVAL = timedelta(seconds=3)
"""轮询间隔时间"""

TIMEOUT = timedelta(minutes=5)
"""等待任务完成的时间"""

async def do_gen_img(content: str) -> list[str]:
    """执行图片生成任务"""
    try:
        params = GenParams.model_validate_json(content)
    except ValueError as e:
        logger.error(f"Invalid gen image params: {e}")
        raise

    replacements = {}
    aspect_ratio = aspect_ratio_map[params.aspect_ratio]
    if params.image_url:
        # 有图片，是图生图
        try:
            image_info = await upload_image_to_comfyui(params.image_url)
        except ValueError as e:
            logger.error(f"Error while uploading image to Comfyui: {e}")
            raise
        except Exception as e:
            logger.error(f"Error while uploading image to Comfyui: {e}")
            raise ValueError("Failed to process given image.") from e
        workflow_template = ITI_TMPL
        replacements["59"] = {"inputs.trans_text": params.prompt}
        replacements["27"] = {
            "inputs.width": aspect_ratio[0],
            "inputs.height": aspect_ratio[1],
        }
        if image_info['name']:
            replacements["41"] = {"inputs.image": image_info['name']}
        else:
            logger.error(f"Failed to upload image to Comfyui: {image_info}")
            raise ValueError("Failed to process given image.")
    else:
        # 没有图片，是文生图
        workflow_template = TTI_TMPL
        replacements["6"] = {"inputs.trans_text": params.prompt}
        replacements["15"] = {
            "inputs.width": aspect_ratio[0],
            "inputs.height": aspect_ratio[1],
        }

    # 工作流参数
    workflow_data = replace_workflow_data(workflow_template, replacements)
    try:
        submit_result = await submit_task_to_comfyui(workflow_data)
    except ValueError as e:
        logger.error(f"Error while submitting image generation task to Comfyui: {e}")
        raise
    except Exception as e:
        logger.error(f"Error while submitting image generation task to Comfyui: {e}")
        raise ValueError("Failed to generate image.") from e
    if submit_result and 'prompt_id' in submit_result:
        logger.info(f"Image generation task submitted successfully, prompt_id: {submit_result['prompt_id']}")
        comfyui_prompt_id = submit_result['prompt_id']
    else:
        logger.error(f"Failed to submit image generation task: {submit_result}")
        raise ValueError("Failed to generate image.")
    # 开始轮询
    is_first = True
    st = datetime.now()
    while datetime.now() - st < TIMEOUT:  # 不能超时
        if not is_first:
            # 如果不是第一次轮询，就等待一段时间
            await asyncio.sleep(POLLING_INTERVAL.total_seconds())
        is_first = False
        status_result = await query_task_status_from_comfyui(comfyui_prompt_id)
        if comfyui_prompt_id not in status_result:
            continue
        task_data = status_result[comfyui_prompt_id]
        if 'status' not in task_data:
            continue
        task_status = task_data.get('status', {})
        if task_status.get('status_str', '') == 'error':
            logger.error(f"Image generation task failed: {task_status}")
            raise ValueError("Failed to generate image.")
        if task_status.get('status_str', '') != 'success' or not task_status.get('completed', False):
            continue
        outputs = task_data.get('outputs', {})
        image_tasks = []
        for node_id, node_output in outputs.items():
            if 'images' in node_output and node_output['images']:
                for image_info in node_output['images']:
                    filename = image_info.get('filename')
                    if filename:
                        try:
                            # 获取图片数据
                            image_bytes = await get_image_from_comfyui(filename)
                        except Exception as e:
                            logger.error(f"Error while get image from Comfyui: {e}")
                            continue
                        if not image_bytes:
                            logger.error(f"Got an empty image from Comfyui: {filename}")
                            continue
                        base64_data = base64.b64encode(image_bytes).decode('utf-8')
                        oss_filename = f"comfyui_{nanoid.generate(size=10)}.png"
                        task = asyncio.create_task(asyncio.to_thread(save_image_b64data, base64_data, oss_filename))
                        image_tasks.append(task)
        if not image_tasks:
            logger.error("Failed to get any images from Comfyui.")
            raise ValueError("Failed to generate image.")
        urls = await asyncio.gather(*image_tasks, return_exceptions=True)
        result: list[str] = []
        for url in urls:
            if isinstance(url, BaseException) or not url:
                logger.error(f"Error while saving image to OSS: {url}")
                continue
            result.append(url)
        if result:
            return result
        else:
            logger.error("Failed to save any images to OSS.")
            raise ValueError("Failed to generate image.")
    logger.error("Timed out while waiting for image generation task to complete.")
    raise ValueError('Failed to generate image.')
