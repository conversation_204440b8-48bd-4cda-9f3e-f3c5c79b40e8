import { request } from '../request';

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(userName: string, password: string, ticket: string, randstr: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    data: {
      userName,
      password,
      ticket,
      randstr
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}

/** 钉钉登录 */
export function fetchDingTalkLogin() {
  return request<{ login_url: string; code: string }>({
    url: '/auth/dingding_login',
    method: 'get'
  });
}

/** token验证登录 */
export function fetchtokenLogin(token: string) {
  return request({
    url: '/auth/token_login',
    method: 'get',
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
}

export function fetchDingTalkcallback() {
  return request({
    url: '/auth/dingding_callback',
    method: 'get'
  });
}

export function fetchLogout() {
  return request({
    url: '/auth/logout',
    method: 'get'
  });
}

/** 用户注册 */
export function fetchRegister(data: {
  user_email: string;
  user_pwd: string;
  repeat_pwd: string;
  email_code: string;
  ticket: string;
  randstr: string;
}) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  });
}

/** 发送邮箱验证码 */
export function fetchSendEmailCode(data: {
  email: string;
  randstr: string;
  ticket: string;
  type: 'register' | 'reset';
}) {
  return request({
    url: '/auth/send_email_code',
    method: 'post',
    data
  });
}

/** 检查是否为办公室IP */
export function fetchCheckOfficeIp() {
  return request<{ allowed: number }>({
    url: '/auth/check_office_ip',
    method: 'get'
  });
}

/** 重置密码 */
export function fetchResetPassword(data: {
  user_email: string;
  user_pwd: string;
  repeat_pwd: string;
  email_code: string;
}) {
  return request({
    url: '/auth/reset_password',
    method: 'post',
    data
  });
}
