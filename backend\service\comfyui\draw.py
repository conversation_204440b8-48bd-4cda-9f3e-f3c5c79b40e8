import json
import os
import logging
import shutil
from typing import Dict, Any, Optional
from pathlib import Path
import aiohttp
import nanoid

from config import app_settings
from utils.image import detect_image_type, image_to_binary
from data.comfyui_workflow import get_workflow_file_path


logger = logging.getLogger(__name__)

comfyui_api_url = app_settings.comfyui_api_url


def _get_nested_value(data: Dict[str, Any], path: str) -> Any:
    """
    根据点号分隔的路径获取嵌套字典中的值
    
    Args:
        data: 目标字典
        path: 点号分隔的路径，如 'inputs.image'
        
    Returns:
        路径对应的值，如果路径不存在则返回None
        
    Example:
        _get_nested_value({"inputs": {"image": "test.png"}}, "inputs.image")
        # 返回: "test.png"
    """
    try:
        keys = path.split('.')
        current = data
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    except Exception as e:
        logger.warning(f"获取嵌套值时发生错误: {e}, 路径: {path}")
        return None


def _set_nested_value(data: Dict[str, Any], path: str, value: Any) -> bool:
    """
    根据点号分隔的路径设置嵌套字典中的值
    
    Args:
        data: 目标字典
        path: 点号分隔的路径，如 'inputs.image'
        value: 要设置的值
        
    Returns:
        True表示设置成功，False表示设置失败
        
    Example:
        _set_nested_value({"inputs": {"image": "old.png"}}, "inputs.image", "new.png")
        # 结果: {"inputs": {"image": "new.png"}}
    """
    try:
        keys = path.split('.')
        current = data
        # 导航到最后一个键的父级
        for key in keys[:-1]:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                logger.warning(f"路径中的键不存在或类型不正确: {key}, 完整路径: {path}")
                return False
        
        # 设置最后一个键的值
        final_key = keys[-1]
        if isinstance(current, dict):
            current[final_key] = value
            logger.debug(f"成功设置路径 {path} 的值为: {value}")
            return True
        else:
            logger.warning(f"目标对象不是字典类型，无法设置路径: {path}")
            return False
    except Exception as e:
        logger.error(f"设置嵌套值时发生错误: {e}, 路径: {path}, 值: {value}")
        return False


def _validate_workflow_file(filepath: str) -> bool:
    """
    验证工作流文件的有效性
    
    Args:
        filepath: JSON文件的完整路径
        
    Returns:
        True表示文件有效，False表示文件无效
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(filepath):
            logger.error(f"工作流文件不存在: {filepath}")
            return False
            
        # 检查文件是否可读
        if not os.access(filepath, os.R_OK):
            logger.error(f"工作流文件无法读取: {filepath}")
            return False
            
        # 尝试解析JSON
        with open(filepath, 'r', encoding='utf-8') as file:
            data = json.load(file)
            
        # 验证基本结构：应该是一个字典
        if not isinstance(data, dict):
            logger.error(f"工作流文件格式错误，根元素应该是字典: {filepath}")
            return False
            
        # 验证是否包含预期的节点结构
        for node_id, node_data in data.items():
            if not isinstance(node_data, dict):
                logger.warning(f"节点 {node_id} 的数据不是字典格式")
                continue
                
            # 检查节点是否包含基本字段
            if 'inputs' not in node_data:
                logger.warning(f"节点 {node_id} 缺少 'inputs' 字段")
            if 'class_type' not in node_data:
                logger.warning(f"节点 {node_id} 缺少 'class_type' 字段")
                
        logger.debug(f"工作流文件验证成功: {filepath}")
        return True
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}, 文件: {filepath}")
        return False
    except Exception as e:
        logger.error(f"验证工作流文件时发生错误: {e}, 文件: {filepath}")
        return False


def replace_workflow_data(
    filename: str, 
    replacements: Dict[str, Dict[str, Any]], 
    save_file: bool = False
) -> Dict[str, Any]:
    """
    替换ComfyUI工作流JSON数据中特定节点的输入值
    
    Args:
        filename: 工作流文件名（不含扩展名），如 "flux_kontext_fp8_api"
        replacements: 替换配置字典，格式为 {"node_id": {"field_path": new_value}}
        save_file: 是否保存修改后的文件到磁盘，默认False
        
    Returns:
        修改后的完整JSON数据
        
    Raises:
        ValueError: 参数验证失败
        FileNotFoundError: 工作流文件不存在
        json.JSONDecodeError: JSON文件格式错误
        
    Example:
        replacements = {
            "41": {"inputs.image": "new_image.png"},
            "59": {"inputs.trans_text": "新的翻译文本"}
        }
        result = replace_workflow_data("flux_kontext_fp8_api", replacements)
    """
    # 参数验证
    if not filename:
        raise ValueError("filename参数不能为空")
    
    # 清理文件名，移除可能的路径分隔符或扩展名
    filename = filename.strip()
    if '/' in filename or '\\' in filename:
        raise ValueError("filename不应包含路径分隔符")
    if filename.endswith('.json'):
        filename = filename[:-5]  # 移除.json扩展名
        
    if not replacements:
        logger.warning("replacements参数为空，将返回原始数据")
    
        
    # 验证replacements的结构
    for node_id, node_replacements in replacements.items():
        if not isinstance(node_id, str):
            raise ValueError(f"节点ID必须是字符串类型: {node_id}")
        if not isinstance(node_replacements, dict):
            raise ValueError(f"节点 {node_id} 的替换配置必须是字典类型")
        for field_path in node_replacements.keys():
            if not isinstance(field_path, str):
                raise ValueError(f"字段路径必须是字符串类型: {field_path}")
    
    logger.info(f"=== 开始ComfyUI工作流替换任务 ===")
    logger.info(f"文件名: {filename}")
    logger.info(f"替换节点数: {len(replacements)}")
    logger.info(f"保存文件: {save_file}")
    logger.debug(f"替换配置详情: {replacements}")
    
    # 获取文件路径（使用动态路径解析）
    file_path = get_workflow_file_path(filename)
    
    # 验证文件
    if not _validate_workflow_file(str(file_path)):
        raise FileNotFoundError(f"工作流文件验证失败: {file_path}")
    
    # 加载JSON文件
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            workflow_data = json.load(file)
        logger.debug(f"成功加载工作流文件: {file_path}")
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        raise json.JSONDecodeError(f"工作流文件JSON格式错误: {file_path}", e.doc, e.pos)
    except Exception as e:
        logger.error(f"加载工作流文件时发生错误: {e}")
        raise FileNotFoundError(f"无法加载工作流文件: {file_path}")
    
    # 执行替换操作
    successful_replacements = 0
    failed_replacements = 0
    
    for node_id, node_replacements in replacements.items():
        logger.debug(f"处理节点: {node_id}")
        
        # 检查节点是否存在
        if node_id not in workflow_data:
            logger.warning(f"节点 {node_id} 在工作流中不存在，跳过")
            failed_replacements += 1
            continue
            
        # 处理该节点的所有字段替换
        for field_path, new_value in node_replacements.items():
            logger.debug(f"替换节点 {node_id} 的字段 {field_path} 为: {new_value}")
            
            # 获取原始值用于日志记录
            old_value = _get_nested_value(workflow_data[node_id], field_path)
            
            # 执行替换
            if _set_nested_value(workflow_data[node_id], field_path, new_value):
                logger.info(f"成功替换节点 {node_id}.{field_path}: {old_value} -> {new_value}")
                successful_replacements += 1
            else:
                logger.error(f"替换失败 - 节点 {node_id}.{field_path}")
                failed_replacements += 1
    
    logger.info(f"替换操作完成 - 成功: {successful_replacements}, 失败: {failed_replacements}")
    
    # 可选的文件保存功能
    if save_file:
        try:
            # 创建备份文件名
            backup_path = file_path.with_suffix(file_path.suffix + '.backup')
            
            # 创建原文件的备份
            if file_path.exists():
                shutil.copy2(file_path, backup_path)
                logger.debug(f"创建备份文件: {backup_path}")
            
            # 保存修改后的文件
            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(workflow_data, file, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存修改后的工作流文件: {file_path}")
            
        except Exception as e:
            logger.error(f"保存文件时发生错误: {e}")
            # 尝试恢复备份文件
            if backup_path.exists():
                try:
                    shutil.copy2(backup_path, file_path)
                    logger.info(f"已从备份恢复原始文件: {file_path}")
                except Exception as restore_error:
                    logger.error(f"恢复备份文件时发生错误: {restore_error}")
            raise RuntimeError(f"保存工作流文件失败: {e}")
    
    logger.info(f"=== ComfyUI工作流替换任务完成 ===")
    logger.info(f"总计节点数: {len(workflow_data)}")
    logger.info(f"成功替换: {successful_replacements} 个字段")
    if failed_replacements > 0:
        logger.warning(f"失败替换: {failed_replacements} 个字段")
    
    return workflow_data
          
           
async def upload_image_to_comfyui(
    image_data: str, 
    # filename: Optional[str] = None,
    # subfolder: Optional[str] = ""
) -> Dict[str, Any]:
    """
    上传图片到ComfyUI服务器
    
    Args:
        image_data: 图片数据，支持base64编码或URL格式
        
    Returns:
        Dict[str, Any]: ComfyUI返回的上传结果，包含name、subfolder、type字段
        
    Raises:
        ValueError: 当参数验证失败时
        Exception: 当上传失败时
        
    Example:
        # 上传base64格式图片
        result = await upload_image_to_comfyui("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
        # 返回: {"name": "generated_filename.png", "subfolder": "", "type": "input"}
        
        # 上传URL格式图片
        result = await upload_image_to_comfyui("https://example.com/image.jpg", "custom_name.jpg")
        # 返回: {"name": "custom_name.jpg", "subfolder": "", "type": "input"}
    """
    
    # 参数验证
    if not image_data:
        raise ValueError("image_data参数不能为空")
    
    logger.info(f"=== 开始ComfyUI图片上传任务 ===")
    logger.info(f"图片数据长度: {len(image_data)}")
    
    try:
        # 检测图片数据类型
        image_type = detect_image_type(image_data)
        logger.debug(f"检测到图片数据类型: {image_type}")
        
        if image_type == "unknown":
            raise ValueError("无法识别的图片数据格式，仅支持URL或base64格式")
        
        # 转换为二进制数据
        logger.debug("开始转换图片数据为二进制格式")
        binary_data = await image_to_binary(image_data)
        logger.info(f"图片数据转换成功，二进制数据大小: {len(binary_data)} 字节")
        
        # 构建ComfyUI上传URL
        upload_url = f"{comfyui_api_url}/upload/image"
        logger.debug(f"ComfyUI上传URL: {upload_url}")
        
        # 创建FormData对象
        data = aiohttp.FormData()
        data.add_field(
            'image',
            binary_data,
        )   
        
        logger.debug("FormData构建完成，开始发送HTTP请求")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.post(upload_url, data=data) as response:
                # 验证HTTP状态码
                response.raise_for_status()
                logger.debug(f"HTTP请求成功，状态码: {response.status}")
                
                # 解析JSON响应
                response_data = await response.json()
                logger.debug(f"ComfyUI响应数据: {response_data}")
                
                # 验证响应数据格式
                if not isinstance(response_data, dict):
                    raise Exception("ComfyUI返回数据格式错误，期望为JSON对象")
                
                required_fields = ['name', 'subfolder', 'type']
                for field in required_fields:
                    if field not in response_data:
                        raise Exception(f"ComfyUI返回数据缺少必需字段: {field}")
                
                logger.info(f"图片上传成功: {response_data['name']}")
                logger.info(f"=== ComfyUI图片上传任务完成 ===")
                
                return response_data
    
    except aiohttp.ClientError as e:
        logger.error(f"图片上传失败: {str(e)}")
        raise Exception(f"图片上传失败，网络错误")
    
    except Exception as e:
        logger.error(f"图片上传失败: {str(e)}")
        raise Exception(f"图片上传失败")


async def submit_task_to_comfyui(
    prompt: Dict[str, Any],
    client_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    提交任务给ComfyUI服务器
    
    Args:
        prompt: 任务参数，必须是非空字典格式
        client_id: 可选的客户端ID，如果未提供将自动生成
        
    Returns:
        Dict[str, Any]: ComfyUI返回的任务提交结果，包含prompt_id、number、node_errors字段
        
    Raises:
        ValueError: 当参数验证失败时
        Exception: 当任务提交失败时
        
    Example:
        # 提交基本任务
        workflow_prompt = {"1": {"inputs": {"text": "hello world"}, "class_type": "TextNode"}}
        result = await submit_task_to_comfyui(workflow_prompt)
        # 返回: {"prompt_id": "784ecbe8-34e1-44c8-9830-8b082d56d040", "number": 3, "node_errors": {}}
        
        # 使用自定义client_id提交任务
        result = await submit_task_to_comfyui(workflow_prompt, "custom_client_id")
        # 返回: {"prompt_id": "784ecbe8-34e1-44c8-9830-8b082d56d040", "number": 3, "node_errors": {}}
    """
    
    # 参数验证
    if not prompt:
        raise ValueError("prompt参数不能为空")
    
    if not isinstance(prompt, dict):
        raise ValueError("prompt参数必须是字典类型")
    
    if client_id is not None and (not isinstance(client_id, str) or not client_id.strip()):
        raise ValueError("client_id参数必须是非空字符串类型")
    
    logger.info(f"=== 开始ComfyUI任务提交 ===")
    logger.info(f"Prompt节点数: {len(prompt)}")
    logger.info(f"自定义Client ID: {client_id is not None}")
    
    try:
        # 生成或使用提供的client_id
        if client_id is None:
            generated_client_id = nanoid.generate()
            logger.debug(f"自动生成Client ID: {generated_client_id}")
        else:
            generated_client_id = client_id.strip()
            logger.debug(f"使用提供的Client ID: {generated_client_id}")
        
        # 构建ComfyUI任务提交URL
        submit_url = f"{comfyui_api_url}/prompt"
        logger.debug(f"ComfyUI提交URL: {submit_url}")
        
        # 准备请求数据
        request_data = {
            "client_id": generated_client_id,
            "prompt": prompt
        }
        logger.debug(f"请求数据准备完成，Client ID: {generated_client_id}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.post(
                submit_url, 
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                # 验证HTTP状态码
                response.raise_for_status()
                logger.debug(f"HTTP请求成功，状态码: {response.status}")
                
                # 解析JSON响应
                response_data = await response.json()
                logger.debug(f"ComfyUI响应数据: {response_data}")
                
                # 验证响应数据格式
                if not isinstance(response_data, dict):
                    raise Exception("ComfyUI返回数据格式错误，期望为JSON对象")
                
                # 验证必需字段
                required_fields = ['prompt_id', 'number', 'node_errors']
                missing_fields = []
                for field in required_fields:
                    if field not in response_data:
                        missing_fields.append(field)
                
                if missing_fields:
                    raise Exception(f"ComfyUI返回数据缺少必需字段: {', '.join(missing_fields)}")
                
                logger.info(f"任务提交成功，Prompt ID: {response_data['prompt_id']}, 队列编号: {response_data['number']}")
                
                # 检查节点错误
                if response_data['node_errors']:
                    logger.warning(f"任务包含节点错误: {response_data['node_errors']}")
                else:
                    logger.debug("任务提交无节点错误")
                
                logger.info(f"=== ComfyUI任务提交完成 ===")
                
                return response_data
    
    except aiohttp.ClientError as e:
        logger.error(f"任务提交失败 - 网络错误: {str(e)}")
        raise Exception(f"任务提交失败，网络连接错误")
    
    except Exception as e:
        logger.error(f"任务提交失败: {str(e)}")
        raise Exception(f"任务提交失败")


async def query_task_status_from_comfyui(prompt_id: str) -> Dict[str, Any]:
    """
    查询ComfyUI任务执行状态
    
    Args:
        prompt_id: ComfyUI任务的提示ID
        
    Returns:
        Dict[str, Any]: ComfyUI历史记录API的完整响应数据
        - 如果任务未完成，返回空字典 {}
        - 如果任务完成，返回包含prompt_id为key的字典，包含prompt、outputs、status、meta等信息
        
    Raises:
        ValueError: 当参数验证失败时
        Exception: 当状态查询失败时
        
    Example:
        # 查询进行中的任务
        result = await query_task_status_from_comfyui("784ecbe8-34e1-44c8-9830-8b082d56d040")
        # 返回: {}
        
        # 查询已完成的任务
        result = await query_task_status_from_comfyui("784ecbe8-34e1-44c8-9830-8b082d56d040")
        # 返回: {"784ecbe8-34e1-44c8-9830-8b082d56d040": {"prompt": [], "outputs": {...}, "status": {...}, "meta": {...}}}
    """
    
    # 参数验证
    if not prompt_id:
        raise ValueError("prompt_id参数不能为空")
    
    # 清理prompt_id，移除可能的空白字符
    cleaned_prompt_id = prompt_id.strip()
    if not cleaned_prompt_id:
        raise ValueError("prompt_id参数不能为空白字符串")
    
    logger.info(f"=== 开始ComfyUI任务状态查询 ===")
    logger.info(f"Prompt ID: {cleaned_prompt_id}")
    
    try:
        # 构建ComfyUI历史记录查询URL
        history_url = f"{comfyui_api_url}/history/{cleaned_prompt_id}"
        logger.debug(f"ComfyUI历史记录查询URL: {history_url}")
        
        # 发送HTTP GET请求
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.get(history_url) as response:
                # 验证HTTP状态码
                response.raise_for_status()
                logger.debug(f"HTTP请求成功，状态码: {response.status}")
                
                # 解析JSON响应
                response_data = await response.json()
                logger.debug(f"ComfyUI响应数据: {response_data}")
                
                # 验证响应数据格式
                if not isinstance(response_data, dict):
                    raise Exception("ComfyUI返回数据格式错误，期望为JSON对象")
                
                # 判断任务状态
                if not response_data:
                    # 空字典表示任务仍在进行中
                    logger.info(f"任务状态: 进行中 (队列中或执行中)")
                    logger.info(f"=== ComfyUI任务状态查询完成 ===")
                    return response_data
                elif cleaned_prompt_id in response_data:
                    # 包含prompt_id的数据表示任务已完成
                    task_data = response_data[cleaned_prompt_id]
                    
                    # 提取状态信息用于日志记录
                    if 'status' in task_data and 'status_str' in task_data['status']:
                        status_str = task_data['status']['status_str']
                        completed = task_data['status'].get('completed', False)
                        logger.info(f"任务状态: {status_str}, 完成状态: {completed}")
                    else:
                        logger.info(f"任务状态: 已完成 (状态信息不完整)")
                    
                    # 提取输出信息用于日志记录
                    if 'outputs' in task_data:
                        outputs_count = len(task_data['outputs'])
                        logger.info(f"输出节点数: {outputs_count}")
                        
                        # 记录图片输出信息
                        total_images = 0
                        for node_id, node_output in task_data['outputs'].items():
                            if 'images' in node_output:
                                images_count = len(node_output['images'])
                                total_images += images_count
                                logger.debug(f"节点 {node_id} 输出图片数: {images_count}")
                        
                        if total_images > 0:
                            logger.info(f"总计输出图片数: {total_images}")
                    
                    logger.info(f"=== ComfyUI任务状态查询完成 ===")
                    return response_data
                else:
                    # 意外情况：有数据但不包含expected prompt_id
                    logger.warning(f"响应数据中不包含期望的prompt_id: {cleaned_prompt_id}")
                    logger.info(f"=== ComfyUI任务状态查询完成 ===")
                    return response_data
    
    except aiohttp.ClientError as e:
        logger.error(f"任务状态查询失败 - 网络错误: {str(e)}")
        raise Exception(f"任务状态查询失败，网络连接错误")
    
    except Exception as e:
        logger.error(f"任务状态查询失败: {str(e)}")
        raise Exception(f"任务状态查询失败")


async def get_image_from_comfyui(
    filename: str,
    image_type: str = "output"
) -> bytes:
    """
    获取ComfyUI生成的图片二进制数据
    
    Args:
        filename: ComfyUI服务器上的图片文件名，必填字符串
        image_type: 图片类型标识符，默认为"output"
        
    Returns:
        bytes: 图片的二进制数据
        
    Raises:
        ValueError: 当参数验证失败时
        Exception: 当图片获取失败时
        
    Example:
        # 获取默认输出类型的图片
        image_data = await get_image_from_comfyui("ComfyUI_00001_.png")
        # 返回: b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR...'
        
        # 获取指定类型的图片
        image_data = await get_image_from_comfyui("image.jpg", "input")
        # 返回: b'\xff\xd8\xff\xe0\x00\x10JFIF...'
    """
    
    # 参数验证
    if not filename:
        raise ValueError("filename参数不能为空")
    
    # 清理filename，移除可能的空白字符
    cleaned_filename = filename.strip()
    if not cleaned_filename:
        raise ValueError("filename参数不能为空白字符串")
    
    
    logger.info(f"=== 开始ComfyUI图片获取任务 ===")
    logger.info(f"文件名: {cleaned_filename}")
    logger.info(f"图片类型: {image_type}")
    
    try:
        # 构建ComfyUI图片获取URL
        view_url = f"{comfyui_api_url}/view"
        logger.debug(f"ComfyUI图片获取URL: {view_url}")
        
        # 准备查询参数
        params = {
            "filename": cleaned_filename,
            "type": image_type
        }
        logger.debug(f"查询参数: {params}")
        
        # 发送HTTP GET请求
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.get(view_url, params=params) as response:
                # 检查HTTP状态码
                if response.status == 404:
                    logger.error(f"图片文件不存在: {cleaned_filename}")
                    raise Exception("指定的图片文件不存在")
                elif response.status == 403:
                    logger.error(f"访问图片文件权限不足: {cleaned_filename}")
                    raise Exception("访问图片文件权限不足")
                
                # 验证其他HTTP状态码
                response.raise_for_status()
                logger.debug(f"HTTP请求成功，状态码: {response.status}")
                
                # 读取二进制数据
                image_data = await response.read()
                logger.debug(f"成功读取图片数据，大小: {len(image_data)} 字节")
                
                # 验证数据是否为空
                if not image_data:
                    raise Exception("获取到的图片数据为空")
                
                logger.info(f"图片获取成功，文件大小: {len(image_data)} 字节")
                logger.info(f"=== ComfyUI图片获取任务完成 ===")
                
                return image_data
    
    except aiohttp.ClientError as e:
        logger.error(f"图片获取失败 - 网络错误: {str(e)}")
        raise Exception(f"图片获取失败，网络连接错误")
    
    except Exception as e:
        # 如果异常已经是我们定制的消息，直接重新抛出
        if str(e) in ["指定的图片文件不存在", "访问图片文件权限不足", "获取到的图片数据为空"]:
            logger.error(f"图片获取失败: {str(e)}")
            raise e
        else:
            logger.error(f"图片获取失败: {str(e)}")
            raise Exception(f"图片获取失败")

