<script setup lang="ts">
import { computed, h, onBeforeUnmount, ref, watch } from 'vue';
import { NButton, NPopover, useMessage } from 'naive-ui';
import { $t } from '@/locales';
import {
  fetchFilePresets,
  fetchSupportedLanguages,
  fetchSupportedModels,
  readExcel,
  // translateExcel,
  translateStream
} from '@/service/api/text';
// import type { TranslateParams } from '@/service/api/text';
import SvgIcon from '@/components/custom/svg-icon.vue';
import EditDraw from '../../presetmange/editDraw.vue';

// 消息提示
const message = useMessage();
const formData = new FormData(); // 用于记录用户上传的翻译文件
const isformData = ref<boolean>(false);

// 是否显示编辑预设抽屉
const showEditDrawer = ref<boolean>(false);

// 翻译数据表格
interface TableRow {
  key: string;
  source: string;
  target: string;
  translation: string;
  rowspan?: number;
}

const tableData = ref<TableRow[]>([]);

// 使用预设选项
const usePresets = ref<boolean>(false);

// 添加流式翻译控制
const streamController = ref<{ cancel: () => void } | null>(null);
// 当前正在翻译的源文本索引
const currentSourceIndex = ref<number>(0);
// 当前正在翻译的语言索引
const currentLanguageIndex = ref<number>(0);
// 待翻译的源文本和语言组合
const pendingTranslations = ref<{ source: string; language: string }[]>([]);

// 翻译类型选择
const targetLanguages = ref<{ label: string; value: string }[]>([]);
const selectedTargets = ref<string | string[]>([]);

// 预置游戏名词选择
const presetOptions = ref<{ label: string; value: string }[]>([{ label: '默认', value: 'Default' }]);
const presetSelected = ref<string>('Default');

// 模型选择选项
const modelOptions = ref<{ label: string; value: string }[]>([]);
const selectedModel = ref<string>('');

// 加载状态
const loading = ref<boolean>(false);
// 文件名
const fileName = ref<string>('');
// 原始文本数据
const sourceTexts = ref<string[]>([]);
// 翻译任务状态
const translationStatus = ref<{ [key: string]: boolean }>({});
// 当前处理中的语言
const currentLanguage = ref<string>('');

// 提前声明函数类型，解决循环引用问题
type HandleStreamMessageFn = (messageData: string) => void;
type TranslateNextItemFn = () => Promise<void>;

// 前向声明，避免TypeScript错误
let translateNextItem: TranslateNextItemFn;

// 获取预设名称
async function getPresetNames() {
  const response = await fetchFilePresets();
  if (response.data) {
    const presetFiles = response.data.map((file: { gamename: string; gamecode: string }) => ({
      label: file.gamename,
      value: file.gamecode
    }));
    presetOptions.value = [{ label: '默认', value: 'Default' }, ...presetFiles];
  }
}

// 获取支持的语言列表
async function getSupportedLanguages() {
  try {
    const response = await fetchSupportedLanguages();
    if (response.data) {
      targetLanguages.value = response.data;
      if (targetLanguages.value.length > 0) {
        // 初始化为数组，确保默认选中第一个语言
        if (!Array.isArray(selectedTargets.value) || selectedTargets.value.length === 0) {
          selectedTargets.value = [targetLanguages.value[0].value];
        }
      }
    }
  } catch (error) {
    message.error('获取支持的语言列表失败');
  }
}

// 获取支持的模型列表
async function getSupportedModels() {
  try {
    const response = await fetchSupportedModels();
    if (response.data) {
      modelOptions.value = response.data;
      if (modelOptions.value.length > 0) {
        selectedModel.value = modelOptions.value[0].value;
      }
    }
  } catch (error) {
    message.error('获取支持的模型列表失败');
  }
}

// 初始化数据
async function initData() {
  await Promise.all([getPresetNames(), getSupportedLanguages(), getSupportedModels()]);
}

// 组件初始化时加载必要数据
initData();

// 监听目标语言选择变化，重建表格结构
watch(
  () => selectedTargets.value,
  newValue => {
    // 确保至少选择一个语言
    if (Array.isArray(newValue) && newValue.length === 0 && targetLanguages.value.length > 0) {
      selectedTargets.value = [targetLanguages.value[0].value];
      return;
    }

    // 只有在已上传文件且有源文本数据时才重建表格
    if (isformData.value && sourceTexts.value.length > 0) {
      rebuildTableStructure();
    }
  },
  { deep: true }
);

// 查看预设内容
function handleViewPresets() {
  if (presetSelected.value === '默认') {
    message.error($t('page.text.pleaseChooseGame'));
    return;
  }
  showEditDrawer.value = true;
}

// 改进保存翻译结果的方式，使用语言代码而非标签作为键的一部分
function saveCurrentTranslations() {
  const currentTranslations = new Map<string, string>();

  tableData.value.forEach(row => {
    if (!row.translation || row.translation.trim() === '') return;

    // 查找语言代码
    const targetLanguageCode = getLanguageCodeByLabel(row.target);
    if (!targetLanguageCode) return;

    // 使用"原文-语言代码"作为键，确保语言切换时能正确匹配
    const key = `${row.source}-${targetLanguageCode}`;
    currentTranslations.set(key, row.translation);

    // 同时维护翻译状态
    translationStatus.value[key] = true;
  });

  return currentTranslations;
}

// 根据语言标签查找对应的语言代码
function getLanguageCodeByLabel(label: string): string | undefined {
  const language = targetLanguages.value.find(lang => lang.label === label);
  return language?.value;
}

// 根据语言代码查找对应的语言标签
function getLanguageLabelByCode(code: string): string | undefined {
  const language = targetLanguages.value.find(lang => lang.value === code);
  return language?.label;
}

// 重建表格结构以匹配当前选择的目标语言
function rebuildTableStructure() {
  // 保存当前表格中的翻译结果（改用语言代码作为键的一部分）
  const currentTranslations = saveCurrentTranslations();

  // 清空表格数据
  tableData.value = [];
  const targets = Array.isArray(selectedTargets.value) ? selectedTargets.value : [selectedTargets.value];

  if (targets.length > 1) {
    // 多目标语言模式
    buildMultiLanguageTableStructurePreservingTranslations(sourceTexts.value, targets, currentTranslations);
  } else {
    // 单目标语言模式
    buildSingleLanguageTableStructurePreservingTranslations(sourceTexts.value, targets, currentTranslations);
  }
}

// 构建多语言表格结构时保留翻译结果
function buildMultiLanguageTableStructurePreservingTranslations(
  texts: string[],
  targets: string | string[],
  currentTranslations: Map<string, string>
) {
  const targetsList = Array.isArray(targets) ? targets : [targets];
  if (targetsList.length === 0) return;

  texts.forEach(text => {
    // 获取第一个目标语言
    const firstLang = targetsList[0];
    const firstLangLabel = getLanguageLabelByCode(firstLang) || String(firstLang);

    // 检查是否有已有的翻译（使用语言代码）
    const firstRowKey = `${text}-${firstLang}`;
    const firstRowTranslation = currentTranslations.get(firstRowKey) || '';

    // 添加主行，带有rowspan属性
    tableData.value.push({
      key: `${text}-${firstLang}`,
      source: text,
      target: firstLangLabel,
      translation: firstRowTranslation,
      rowspan: targetsList.length
    });

    // 为剩余目标语言添加行（从第二个语言开始）
    if (targetsList.length > 1) {
      targetsList.slice(1).forEach((lang, _i) => {
        const langLabel = getLanguageLabelByCode(lang) || String(lang);

        // 检查是否有已有的翻译（使用语言代码）
        const rowKey = `${text}-${lang}`;
        const rowTranslation = currentTranslations.get(rowKey) || '';

        tableData.value.push({
          key: `${text}-${lang}`,
          source: text,
          target: langLabel,
          translation: rowTranslation
        });
      });
    }
  });
}

// 构建单语言表格结构时保留翻译结果
function buildSingleLanguageTableStructurePreservingTranslations(
  texts: string[],
  targets: string | string[],
  currentTranslations: Map<string, string>
) {
  const targetsList = Array.isArray(targets) ? targets : [targets];
  if (targetsList.length === 0) return;

  const targetLang = targetsList[0];
  const langLabel = getLanguageLabelByCode(targetLang) || '';

  texts.forEach((text, index) => {
    // 检查是否有已有的翻译（使用语言代码）
    const rowKey = `${text}-${targetLang}`;
    const rowTranslation = currentTranslations.get(rowKey) || '';

    tableData.value.push({
      key: `${text}-${index}`,
      source: text,
      target: langLabel,
      translation: rowTranslation
    });
  });
}

// 删除上传的文件
function removeFile() {
  formData.delete('file');
  isformData.value = false;
  tableData.value = [];
  fileName.value = '';
  sourceTexts.value = [];
  translationStatus.value = {};
}

// 匹配行和语言代码的辅助函数
function matchRowWithLanguageCode(row: TableRow, text: string, languageCode: string): boolean {
  if (row.source !== text) return false;

  // 尝试通过目标语言标签找到语言代码
  const rowLanguageCode = getLanguageCodeByLabel(row.target);
  return rowLanguageCode === languageCode;
}

// 更新表格中已有行的翻译内容
function updateTableDataWithTranslation(
  text: string,
  languageCode: string,
  languageLabel: string,
  translation: string
) {
  // 在表格中查找匹配的行
  if (Array.isArray(selectedTargets.value) && selectedTargets.value.length > 1) {
    // 多目标语言模式 - 查找匹配行
    let found = false;

    // 方法1：尝试通过原文和语言标签精确匹配
    let rowIndex = tableData.value.findIndex(row => {
      return row.source === text && row.target === languageLabel;
    });

    // 方法2：如果方法1找不到，尝试通过原文和语言代码匹配（检查所有行）
    if (rowIndex < 0) {
      rowIndex = tableData.value.findIndex(row => matchRowWithLanguageCode(row, text, languageCode));
    }

    // 方法3：如果还是找不到，尝试只匹配原文和任意语言
    if (rowIndex < 0) {
      // 找到该原文的所有行
      const relatedRows = tableData.value.filter(row => row.source === text);
      if (relatedRows.length > 0) {
        // 若有找到相关行，尝试找到匹配语言的行
        const matchingRow =
          relatedRows.find(row => matchRowWithLanguageCode(row, text, languageCode)) || relatedRows[0]; // 如果没找到匹配语言的行，使用第一行

        // 取行的索引
        rowIndex = tableData.value.findIndex(row => row === matchingRow);
      }
    }

    // 如果找到了匹配行，更新翻译
    if (rowIndex >= 0) {
      tableData.value[rowIndex].translation = translation;
      found = true;
    }

    // 找不到匹配行，记录日志
    if (!found) {
      console.log(`找不到匹配行: 文本="${text}", 目标语言="${languageLabel}" (代码: ${languageCode})`);
      // 打印当前表格中与该原文相关的所有行，帮助调试
      const relatedRows = tableData.value.filter(row => row.source === text);
      console.log('相关行:', JSON.stringify(relatedRows));
    }
  } else {
    // 单目标语言模式 - 只需要匹配原文
    const rowIndex = tableData.value.findIndex(row => row.source === text);

    if (rowIndex >= 0) {
      // 找到对应行，更新翻译
      tableData.value[rowIndex].translation = translation;
    } else {
      // 找不到，记录日志
      console.log(`找不到匹配行: 文本="${text}"`);
    }
  }
}

// 处理流式翻译消息
const handleStreamMessage: HandleStreamMessageFn = (messageData: string) => {
  try {
    // 解析JSON消息
    const data = JSON.parse(messageData);

    // 获取目标语言、翻译内容和完成状态
    const { language, text, done, error } = data;

    if (error) {
      message.error(`翻译错误`);
      loading.value = false;
      return;
    }

    // 获取当前处理的翻译项
    if (currentSourceIndex.value >= pendingTranslations.value.length) {
      console.error(
        '索引越界：currentSourceIndex',
        currentSourceIndex.value,
        '超出了pendingTranslations范围',
        pendingTranslations.value.length
      );
      loading.value = false;
      return;
    }

    const currentItem = pendingTranslations.value[currentSourceIndex.value];
    if (!currentItem) {
      console.error('无法获取当前翻译项', currentSourceIndex.value, pendingTranslations.value);
      loading.value = false;
      return;
    }

    const currentSource = currentItem.source;

    // 检查语言是否匹配
    if (language !== currentItem.language) {
      console.warn(`语言不匹配: 期望 ${currentItem.language}, 实际 ${language}`);
    }

    // 查找对应语言的标签
    const languageLabel = getLanguageLabelByCode(language) || language;

    // 更新表格数据中的翻译
    updateTableDataWithTranslation(currentSource, language, languageLabel, text);

    // 如果翻译完成
    if (done) {
      // 标记该翻译完成 - 使用语言代码作为键的一部分
      translationStatus.value[`${currentSource}-${language}`] = true;

      // 检查是否还有待翻译的内容
      if (currentSourceIndex.value < pendingTranslations.value.length - 1) {
        // 移动到下一个项目
        currentSourceIndex.value++;

        // 防止索引越界
        if (currentSourceIndex.value >= pendingTranslations.value.length) {
          // 所有翻译完成
          loading.value = false;
          message.success('翻译完成');
          currentLanguage.value = '';
          return;
        }

        // 翻译下一个
        translateNextItem();
      } else {
        // 所有翻译完成
        loading.value = false;
        message.success('翻译完成');
        currentLanguage.value = '';
      }
    }
  } catch (error) {
    console.error('处理流式消息失败:', error);
  }
};

// 翻译下一个待翻译的项
translateNextItem = async () => {
  // 防止索引越界
  if (currentSourceIndex.value >= pendingTranslations.value.length) {
    console.error('索引越界，停止翻译');
    loading.value = false;
    return;
  }

  const currentItem = pendingTranslations.value[currentSourceIndex.value];
  if (!currentItem) {
    console.error('无法获取当前翻译项，索引可能无效', currentSourceIndex.value);
    loading.value = false;
    return;
  }

  const targetLanguage = currentItem.language;
  const sourceText = currentItem.source;
  const gamecode = presetSelected.value;
  const model = selectedModel.value;

  // 找到当前语言的标签
  const languageLabel = getLanguageLabelByCode(targetLanguage) || targetLanguage;
  currentLanguage.value = languageLabel;

  try {
    const controller = await translateStream(
      {
        userinput: sourceText,
        target_language: targetLanguage,
        gamecode,
        model,
        use_presets: usePresets.value
      },
      handleStreamMessage,
      error => {
        console.error('翻译错误:', error);
        message.error('翻译失败，请重试');
        loading.value = false;
      },
      () => {
        console.log(`${targetLanguage} 翻译完成`);
      },
      () => {
        console.log(`开始 ${targetLanguage} 翻译`);
      }
    );

    if (controller) {
      streamController.value = controller;
    }
  } catch (error) {
    loading.value = false;
    message.error('翻译失败，请重试');
  }
};

// 停止翻译
const stopTranslation = () => {
  if (streamController.value) {
    streamController.value.cancel();
    streamController.value = null;
    loading.value = false;
  }
};

// 在组件销毁前取消流式请求
onBeforeUnmount(() => {
  stopTranslation();
});

// 修改开始翻译函数，使用语言代码而非标签来检查翻译状态
async function startTranslation() {
  if (!isformData.value) {
    message.error('请先上传文件');
    return;
  }

  if (sourceTexts.value.length === 0) {
    message.error('没有可翻译的内容');
    return;
  }

  // 获取选中的目标语言
  const targets = Array.isArray(selectedTargets.value) ? selectedTargets.value : [selectedTargets.value];

  if (targets.length === 0) {
    // 如果没有选择任何目标语言，默认选择第一个语言
    if (targetLanguages.value.length > 0) {
      selectedTargets.value = [targetLanguages.value[0].value];
      message.info('自动选择了第一个目标语言');
    } else {
      message.error('请至少选择一种目标语言');
      return;
    }
  }

  // 检查翻译行数限制
  const totalRows = sourceTexts.value.length * targets.length;
  if (totalRows > 200) {
    message.error('批量翻译最多支持200行翻译，请减少文本数量或目标语言数量');
    return;
  }

  // 如果当前表格结构与选择的目标语言不匹配，则需要重建表格
  const needsRebuild = checkIfTableNeedsRebuild();
  if (needsRebuild) {
    // 重建表格结构以匹配当前选择的目标语言
    rebuildTableStructure();
  }

  // 收集翻译状态信息，确定哪些项目需要翻译
  const itemsNeedingTranslation: { source: string; language: string }[] = [];

  // 对每个源文本和目标语言组合进行检查
  for (const source of sourceTexts.value) {
    for (const language of targets) {
      // 直接使用语言代码和原文构建键
      const translationKey = `${source}-${language}`;

      // 查找表格中是否已有该语言的翻译结果
      const isTranslated = translationStatus.value[translationKey] === true;

      // 如果未翻译或翻译不完整，添加到待翻译列表
      if (!isTranslated) {
        itemsNeedingTranslation.push({
          source,
          language
        });
      }
    }
  }

  // 如果没有需要翻译的项目，直接返回
  if (itemsNeedingTranslation.length === 0) {
    message.success('翻译完成');
    return;
  }

  loading.value = true;
  // 注意：这里不重置翻译状态，只添加新的状态

  // 准备待翻译的项目列表
  pendingTranslations.value = itemsNeedingTranslation;

  // 重置索引
  currentSourceIndex.value = 0;
  currentLanguageIndex.value = 0;

  // 开始翻译第一个项
  translateNextItem();
}

// 检查当前表格结构是否与选择的目标语言匹配
function checkIfTableNeedsRebuild() {
  if (tableData.value.length === 0) return true;

  const targets = Array.isArray(selectedTargets.value) ? selectedTargets.value : [selectedTargets.value];

  // 多目标语言模式
  if (targets.length > 1) {
    // 检查第一个原文是否有足够的目标语言行
    const firstSource = sourceTexts.value[0];
    if (!firstSource) return false;

    const matchingRows = tableData.value.filter(row => row.source === firstSource);
    // 如果匹配的行数与目标语言数不同，需要重建
    return matchingRows.length !== targets.length;
  }
  // 单目标模式，检查是否有多行
  return tableData.value.length !== sourceTexts.value.length;
}

// 计算属性：判断是否为英文
const isEnglishTarget = computed(() => {
  if (Array.isArray(selectedTargets.value)) {
    return selectedTargets.value.includes('English');
  }
  return selectedTargets.value === 'English';
});

// 处理重新生成单行翻译
const regenerateRowTranslation = async (sourceText: string, targetLabel: string) => {
  // 查找该语言对应的语言代码
  const languageCode = getLanguageCodeByLabel(targetLabel);
  if (!languageCode) {
    message.error('无法找到语言信息');
    return;
  }

  // 显示加载状态
  loading.value = true;
  currentLanguage.value = targetLabel;

  // 重置该语言的翻译状态
  const translationKey = `${sourceText}-${languageCode}`;
  translationStatus.value[translationKey] = false;

  // 设置只翻译这一种语言和文本
  currentSourceIndex.value = 0;
  pendingTranslations.value = [
    {
      source: sourceText,
      language: languageCode
    }
  ];

  // 开始翻译
  const gamecode = presetSelected.value;
  const model = selectedModel.value;

  try {
    const controller = await translateStream(
      {
        userinput: sourceText,
        target_language: languageCode,
        gamecode,
        model,
        use_presets: usePresets.value
      },
      handleStreamMessage,
      error => {
        console.error('重新翻译错误:', error);
        // message.error('重新翻译失败，请重试');
        loading.value = false;
        currentLanguage.value = '';
      },
      () => {
        // console.log(`${languageCode} 重新翻译完成`);
        // 翻译完成后更新状态
        loading.value = false;
        // message.success(`${targetLabel} 重新翻译成功`);
        currentLanguage.value = '';
      },
      () => {
        // console.log(`开始 ${languageCode} 重新翻译`);
      }
    );

    if (controller) {
      streamController.value = controller;
    }
  } catch (error) {
    loading.value = false;
    // message.error('重新翻译失败，请重试');
    currentLanguage.value = '';
  }
};

// 数据表格列定义
const columns = computed(() => {
  // 基础列配置 - 无论单目标还是多目标都显示
  const baseColumns = [
    {
      title: '原文',
      key: 'source',
      width: '35%',
      // 自定义渲染函数处理行合并
      rowSpan: (row: TableRow) => {
        // 多目标语言模式时使用rowspan属性
        if (Array.isArray(selectedTargets.value) && selectedTargets.value.length > 1) {
          return row.rowspan || 1;
        }
        // 单目标模式时不进行合并
        return 1;
      },
      render: (row: TableRow) => {
        return row.source;
      }
    },
    {
      title: '目标',
      key: 'target',
      width: '15%'
    },
    {
      title: '翻译',
      key: 'translation',
      width: '50%',
      render: (row: TableRow) => {
        // 获取语言代码
        const languageCode = getLanguageCodeByLabel(row.target);

        // 检查翻译是否完成
        const translationKey = `${row.source}-${languageCode}`;
        const isDone = translationKey && translationStatus.value[translationKey] === true;

        // 创建重新翻译按钮（仅在翻译完成时显示）
        const regenerateButton = isDone
          ? h(
              NButton,
              {
                text: true,
                class: 'regenerate-button text-4',
                onClick: (e: MouseEvent) => {
                  e.stopPropagation();
                  regenerateRowTranslation(row.source, row.target);
                },
                title: '重新翻译',
                disabled: loading.value
              },
              { default: () => h(SvgIcon, { icon: 'mdi:refresh' }) }
            )
          : null;

        return h(
          NPopover,
          {
            trigger: 'hover',
            placement: 'top'
          },
          {
            trigger: () => {
              const content = [h('div', { style: 'white-space: pre-wrap; word-break: break-word;' }, row.translation)];

              // 只有翻译完成才显示重新翻译按钮
              if (isDone) {
                content.push(h('div', { class: 'action-buttons-wrapper mt-2' }, [regenerateButton]));
              }

              return h('div', { class: 'translation-cell flex justify-start flex-col' }, content);
            },
            default: () => h('div', { style: 'max-width: 300px;' }, row.source)
          }
        );
      }
    }
  ];

  return baseColumns;
});

// 导出表格为CSV
function downloadCsv() {
  // 根据当前模式确定列
  let exportColumns;

  if (Array.isArray(selectedTargets.value) && selectedTargets.value.length > 1) {
    exportColumns = [
      { title: '原文', key: 'source' },
      { title: '目标', key: 'target' },
      { title: '翻译', key: 'translation' }
    ];
  } else {
    exportColumns = [
      { title: '原文', key: 'source' },
      { title: '翻译', key: 'translation' }
    ];
  }

  // 创建CSV内容
  let csvContent = `data:text/csv;charset=utf-8,\uFEFF${exportColumns.map(col => col.title).join(',')}\n`;

  // 多目标模式下需要特殊处理合并单元格
  if (Array.isArray(selectedTargets.value) && selectedTargets.value.length > 1) {
    // 按原文分组处理数据
    const groupedBySource: { [source: string]: TableRow[] } = {};

    // 分组收集数据
    tableData.value.forEach(row => {
      if (!groupedBySource[row.source]) {
        groupedBySource[row.source] = [];
      }
      groupedBySource[row.source].push(row);
    });

    // 为每组数据生成CSV行
    Object.keys(groupedBySource).forEach(source => {
      const rows = groupedBySource[source];
      rows.forEach(row => {
        // 将引号包裹的内容替换为双引号，防止CSV格式错误
        const safeSource = `"${row.source.replace(/"/g, '""')}"`;
        const safeTarget = `"${row.target.replace(/"/g, '""')}"`;
        const safeTranslation = `"${row.translation.replace(/"/g, '""')}"`;

        csvContent += `${safeSource},${safeTarget},${safeTranslation}\n`;
      });
    });
  } else {
    // 单目标模式直接处理
    tableData.value.forEach(row => {
      const values = exportColumns.map(column => {
        const value = row[column.key as keyof TableRow] || '';
        // 将引号包裹的内容替换为双引号，防止CSV格式错误
        return `"${String(value).replace(/"/g, '""')}"`;
      });
      csvContent += `${values.join(',')}\n`;
    });
  }

  const encodedUri = encodeURI(csvContent);
  const link = document.createElement('a');
  link.setAttribute('href', encodedUri);
  link.setAttribute('download', `translated-data.csv`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 下载示例Excel表格
const downloadExampleCsv = () => {
  const link = document.createElement('a');
  link.setAttribute('href', '/proxy-default/data/static/translate/example.xlsx');
  link.setAttribute('download', 'example-template.xlsx');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 上传文件处理 - 仅解析原文，不自动填充翻译
const handleUpload = async ({ file }: { file: any }) => {
  const allowedExtensions = ['xlsx', 'csv'];
  const actualFile = file.file;
  const fileExtension = actualFile.name.split('.').pop()?.toLowerCase();
  loading.value = true;

  if (!allowedExtensions.includes(fileExtension!)) {
    loading.value = false;
    message.error($t('page.text.uploadExcel'));
    return;
  }

  formData.delete('file'); // 清除之前的文件
  formData.append('file', actualFile);
  fileName.value = actualFile.name;

  try {
    const response = await readExcel(formData);

    if (response.data) {
      // 存储原始文本数据
      sourceTexts.value = response.data.text || [];

      // 如果没有选择任何目标语言，默认选中第一个
      if (
        (!Array.isArray(selectedTargets.value) || selectedTargets.value.length === 0) &&
        targetLanguages.value.length > 0
      ) {
        selectedTargets.value = [targetLanguages.value[0].value];
      }

      // 保存当前已有的翻译结果
      const currentTranslations = saveCurrentTranslations();

      // 清空表格数据
      tableData.value = [];

      // 如果是多目标语言模式，预构建合并单元格的表格结构
      if (Array.isArray(selectedTargets.value) && selectedTargets.value.length > 1) {
        // 多目标语言模式
        buildMultiLanguageTableStructurePreservingTranslations(
          sourceTexts.value,
          selectedTargets.value,
          currentTranslations
        );
      } else {
        // 单目标语言模式，简单结构
        buildSingleLanguageTableStructurePreservingTranslations(
          sourceTexts.value,
          selectedTargets.value,
          currentTranslations
        );
      }

      // 设置状态
      isformData.value = true;
      loading.value = false;
      message.success($t('page.text.uploadSuccess'));
    } else {
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
    message.error($t('page.text.uploadError'));
  }
};
</script>

<template>
  <div class="batch-translation">
    <NCard class="h-full">
      <!-- 工具栏 -->
      <NFlex gap="12" vertical class="mb-4">
        <!-- 目标语言选择 - 独占一行 -->
        <NInputGroup class="w-full">
          <NButton type="info" class="label-button">目标</NButton>
          <NSelect
            v-model:value="selectedTargets"
            :options="targetLanguages"
            multiple
            max-tag-count="responsive"
            class="flex-select"
            :clearable="selectedTargets.length > 1"
          />
        </NInputGroup>

        <!-- 其他控件使用wrap布局 -->
        <NFlex gap="12" wrap>
          <!-- 模型选择 -->
          <NInputGroup class="input-group">
            <NButton type="info" class="label-button">模型</NButton>
            <NSelect v-model:value="selectedModel" :options="modelOptions" class="flex-select" />
          </NInputGroup>

          <!-- 预置游戏名词选择 -->
          <NSelect
            v-if="presetOptions.length > 1"
            v-model:value="presetSelected"
            :options="presetOptions"
            class="preset-select"
          />

          <!-- 预设按钮 -->
          <NTooltip trigger="hover">
            <template #trigger>
              <NButton :disabled="!isEnglishTarget" @click="handleViewPresets">预设</NButton>
            </template>
            <span v-if="isEnglishTarget">查看/编辑 预设内容</span>
            <span v-else>目前仅支持英文预设</span>
          </NTooltip>

          <NCheckbox v-model:checked="usePresets" class="ml-2">使用预设</NCheckbox>

          <!-- 文件上传 -->
          <NFlex class="upload-container">
            <NTooltip trigger="hover">
              <template #trigger>
                <NButton v-if="isformData" type="warning" @click="removeFile">取消上传</NButton>
                <NUpload v-else :show-file-list="false" accept=".xlsx,.csv" @change="handleUpload">
                  <NButton>上传文件</NButton>
                </NUpload>
              </template>
              <p v-if="isformData">取消文件上传</p>
              <p v-else>请上传需要翻译的Excel表格</p>
            </NTooltip>

            <NTooltip trigger="hover">
              <template #trigger>
                <NButton text class="help-btn" @click="downloadExampleCsv">
                  <SvgIcon icon="ph:question-bold" />
                </NButton>
              </template>
              点击下载示例表格
            </NTooltip>
          </NFlex>

          <!-- 开始翻译按钮 -->
          <NButton type="primary" :loading="loading" :disabled="!isformData" @click="startTranslation">
            开始翻译
          </NButton>
        </NFlex>
      </NFlex>

      <!-- 翻译结果表格 -->
      <NDataTable
        :columns="columns"
        :data="tableData"
        :row-key="row => row.key"
        flex-height
        class="data-table-height"
        max-height="1000"
        :bordered="true"
        :single-line="false"
      />

      <!-- 导出按钮 -->
      <NFlex justify="end" class="mt-3 w-full">
        <NButtonGroup>
          <NButton type="primary" @click="downloadCsv">
            <template #icon>
              <SvgIcon icon="material-symbols:download" />
            </template>
            下载表格
          </NButton>
        </NButtonGroup>
      </NFlex>
    </NCard>

    <!-- 预设编辑抽屉 -->
    <NDrawer v-model:show="showEditDrawer" placement="right" resizable width="80em">
      <NDrawerContent>
        <EditDraw :selected-game-code="presetSelected" />
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.batch-translation {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.input-group {
  max-width: 220px;
  display: flex;
}

.label-button {
  width: 70px;
  padding: 0 8px;
  flex-shrink: 0;
}

.flex-select {
  flex: 1;
  min-width: 60px;
}

.preset-select {
  max-width: 180px;
  width: 100%;
}

.upload-container {
  gap: 0 !important;
  align-items: flex-start;
  flex-wrap: nowrap !important;
}

.help-btn {
  width: 24px;
  height: 34px;
  padding: 0;
  display: flex;
  align-items: start;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-weight: 500;
  color: var(--primary-color);
}

:deep(.n-data-table .n-data-table-td) {
  vertical-align: middle;
}

.input-group-full {
  width: 100%;
  display: flex;
}

.n-checkbox {
  align-items: center;
}

.data-table-height {
  height: calc(100vh - 280px);
}

/* 添加重新生成按钮样式 */
.regenerate-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 4px !important;
  margin: 0 4px !important;
  min-width: 32px !important;
}

/* 翻译单元格样式 */
.translation-cell {
  position: relative;
  width: 100%;
  min-height: 30px;
}

/* 按钮容器样式 */
.action-buttons-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
}
</style>
