import type { AxiosProgressEvent } from 'axios';
import { request } from '../request';
export interface UploadResult {
  file_path: string;
}
/** get role list */
export function postUploadFile(type: string, data: FormData, callback: (arg: AxiosProgressEvent) => void) {
  const url = `/common/upload/${type}`;
  return request<UploadResult>({
    url,
    method: 'post',
    data,
    onUploadProgress: progressEvent => callback(progressEvent)
  });
}

/** get role list */
export function ocrupload(type: string, data: FormData, callback: (arg: AxiosProgressEvent) => void) {
  const url = `/imgocr/upload_to_remote/${type}`;
  return request<UploadResult>({
    url,
    method: 'post',
    data,
    onUploadProgress: progressEvent => callback(progressEvent)
  });
}

/** 腾讯云临时身份令牌 */
export function getTempCredentials() {
  const url = `/cos/get_temp_credentials`;
  return request({
    url,
    method: 'get'
  });
}
