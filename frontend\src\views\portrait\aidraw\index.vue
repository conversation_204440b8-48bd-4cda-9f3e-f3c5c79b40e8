<script setup lang="ts">
import { ref } from 'vue';
import type { SelectGroupOption, SelectOption } from 'naive-ui';
const resolutionOption: CommonType.Option[] = [
  { label: '1024*1024', value: '1024*1024' },
  { label: '1080*1080', value: '1080*1080' }
];
const controllOption: CommonType.Option[] = [
  { label: '参考', value: '参考' },
  { label: '轮廓', value: '轮廓' },
  { label: '结构', value: '结构' },
  { label: '换脸', value: '换脸' }
];
const controllerSopt1 = ref([0.2, 0.7]);
// const controllerSopt2 = ref([0.2, 0.7]);
// const controllerSopt3 = ref([0.2, 0.7]);
// const controllerSopt4 = ref([0.2, 0.7]);
const lorasOtion: Array<SelectOption | SelectGroupOption> = [
  {
    type: 'group',
    label: '功能',
    key: '功能',
    children: [
      {
        label: 'add-detail-xl.safetensors',
        value: 'add-detail-xl.safetensors'
      },
      {
        label: '手部修复ClearHands_v2.0.safetensors',
        value: '手部修复ClearHands_v2.0.safetensors'
      }
    ]
  },
  {
    type: 'group',
    label: '概念滑块',
    key: '概念滑块',
    children: [
      {
        label: 'age.bin',
        value: 'age.bin'
      },
      {
        label: 'cartoon_style.bin',
        value: 'cartoon_style.bin'
      },
      {
        label: 'chubby.bin',
        value: 'chubby.bin'
      },
      {
        label: 'clay_style.bin',
        value: 'clay_style.bin'
      },
      {
        label: 'cluttered_room.bin',
        value: 'cluttered_room.bin'
      },
      {
        label: 'curlyhair.bin',
        value: 'curlyhair.bin'
      },
      {
        label: 'dark_weather.bin',
        value: 'dark_weather.bin'
      },
      {
        label: 'eyebrow.bin',
        value: 'eyebrow.bin'
      },
      {
        label: 'eyesize.bin',
        value: 'eyesize.bin'
      },
      {
        label: 'festive.bin',
        value: 'festive.bin'
      },
      {
        label: 'fix_hands.bin',
        value: 'fix_hands.bin'
      },
      {
        label: 'long_hair.bin',
        value: 'long_hair.bin'
      },
      {
        label: 'muscular.bin',
        value: 'muscular.bin'
      },
      {
        label: 'pixar_style.bin',
        value: 'pixar_style.bin'
      },
      {
        label: 'professional.bin',
        value: 'professional.bin'
      },
      {
        label: 'repair_slider.bin',
        value: 'repair_slider.bin'
      },
      {
        label: 'sculpture_style.bin',
        value: 'sculpture_style.bin'
      },
      {
        label: 'smiling.bin',
        value: 'smiling.bin'
      },
      {
        label: 'stylegan_latent1.bin',
        value: 'stylegan_latent1.bin'
      },
      {
        label: 'stylegan_latent2.bin',
        value: 'stylegan_latent2.bin'
      },
      {
        label: 'suprised_look.bin',
        value: 'suprised_look.bin'
      },
      {
        label: 'tropical_weather.bin',
        value: 'tropical_weather.bin'
      },
      {
        label: 'winter_weather.bin',
        value: 'winter_weather.bin'
      }
    ]
  },
  {
    type: 'group',
    label: '自制',
    key: '自制',
    children: [
      {
        label: '20231128-1701143004686-0006.safetensors',
        value: '20231128-1701143004686-0006.safetensors'
      },
      {
        label: '20231128-1701143004686-0008.safetensors',
        value: '20231128-1701143004686-0008.safetensors'
      },
      {
        label: '20231130-1701339648865-0006.safetensors',
        value: '20231130-1701339648865-0006.safetensors'
      },
      {
        label: '20231130-1701339648865-0007.safetensors',
        value: '20231130-1701339648865-0007.safetensors'
      },
      {
        label: '20231130-1701339648865-0008.safetensors',
        value: '20231130-1701339648865-0008.safetensors'
      },
      {
        label: '20231130-1701339648865-0009.safetensors',
        value: '20231130-1701339648865-0009.safetensors'
      }
    ]
  },
  {
    type: 'group',
    label: '自制',
    key: '自制',
    children: [
      {
        label: '20231128-1701143004686-0006.safetensors',
        value: '20231128-1701143004686-0006.safetensors'
      },
      {
        label: '20231128-1701143004686-0008.safetensors',
        value: '20231128-1701143004686-0008.safetensors'
      },
      {
        label: '20231130-1701339648865-0006.safetensors',
        value: '20231130-1701339648865-0006.safetensors'
      },
      {
        label: '20231130-1701339648865-0007.safetensors',
        value: '20231130-1701339648865-0007.safetensors'
      },
      {
        label: '20231130-1701339648865-0008.safetensors',
        value: '20231130-1701339648865-0008.safetensors'
      },
      {
        label: '20231130-1701339648865-0009.safetensors',
        value: '20231130-1701339648865-0009.safetensors'
      }
    ]
  },
  {
    type: 'group',
    label: '风格',
    key: '风格',
    children: [
      {
        label: 'fashigirl-v6-sdxl-5ep-resize.safetensors',
        value: 'fashigirl-v6-sdxl-5ep-resize.safetensors'
      },
      {
        label: 'pixel-art-xl-v1.1.safetensors',
        value: 'pixel-art-xl-v1.1.safetensors'
      },
      {
        label: 'SDXL_FILM_PHOTOGRAPHY_STYLE_BetaV0.4.safetensors',
        value: 'SDXL_FILM_PHOTOGRAPHY_STYLE_BetaV0.4.safetensors'
      },
      {
        label: 'Ukiyo-e Art.safetensors',
        value: 'Ukiyo-e Art.safetensors'
      }
    ]
  },
  {
    type: 'group',
    label: '默认',
    key: '默认',
    children: [
      {
        label: 'None.safetensors',
        value: 'None.safetensors'
      },
      {
        label: 'sd_xl_offset_example-lora_1.0.safetensors',
        value: 'sd_xl_offset_example-lora_1.0.safetensors'
      }
    ]
  }
];
</script>

<template>
  <div class="flex-row-stretch gap-16px">
    <NGrid cols="24" x-gap="12" class="h-full" item-responsive responsive="screen">
      <NGi span="24 m:8 ">
        <NCard class="h-full">
          <NTabs type="line" animated class="h-full overflow-hidden">
            <NTabPane name="oasis" tab="设定">
              <NForm label-placement="left" size="small">
                <NGrid :cols="24" :x-gap="5">
                  <NFormItemGi :span="24" label="优先级" path="radioGroupValue">
                    <NRadioGroup name="radiogroup2">
                      <NRadio value="Radio 1">极速优先</NRadio>
                      <NRadio value="Radio 2">速度优先</NRadio>
                      <NRadio value="Radio 3">质量优先</NRadio>
                    </NRadioGroup>
                  </NFormItemGi>
                  <NFormItemGi :span="24" label="种子" path="inputValue">
                    <NInput placeholder="Input" />
                  </NFormItemGi>
                  <NFormItemGi :span="24" label="种子" path="inputValue">
                    <NSlider :step="0.1" />
                  </NFormItemGi>
                  <NFormItemGi :span="24" label="指导量表" path="inputValue">
                    <NSlider :step="0.1" />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="FreeU图像优化" path="inputValue">
                    <NSwitch />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="	跳过控制预处理" path="inputValue">
                    <NSwitch />
                  </NFormItemGi>

                  <NFormItemGi :span="12" label="分辨率" path="selectValue">
                    <NSelect placeholder="Select" :options="resolutionOption" class="w-5/6"></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="出图数" path="selectValue">
                    <NInputNumber :min="1" :max="2" />
                  </NFormItemGi>
                </NGrid>
              </NForm>
            </NTabPane>
            <NTabPane name="the beatles" tab="风格" class="max-h-full overflow-auto">
              <NGrid cols="5" :x-gap="5" :y-gap="5">
                <NGi v-for="i in 10" :key="i" span="1" class="relative aspect-square">
                  <NCheckbox class="custom-checkbox">
                    <NImage
                      preview-disabled
                      class="absolute left-0 top-0"
                      src="https://xxfpfg-gg.gdsre.cn/images/202407/d2e44739953046066f6a030a9e358763.png"
                    />
                    <NEllipsis class="absolute bottom-0 left-0 scale-80 text-xs text-white tracking-widest">
                      停滞的海浪
                    </NEllipsis>
                  </NCheckbox>
                </NGi>
              </NGrid>
            </NTabPane>
            <NTabPane name="jay chou" tab="模型">
              <NForm label-placement="left" size="small">
                <NGrid :cols="24" :x-gap="5">
                  <NFormItemGi :span="12" label="SDXL Base Model" label-placement="top" path="selectValue">
                    <NSelect placeholder="Select" :options="resolutionOption" class="w-5/6"></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="SDXL Refiner" label-placement="top" path="selectValue">
                    <NSelect placeholder="Select" :options="resolutionOption" class="w-5/6"></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="24" label="种子" path="inputValue">
                    <NSlider :step="0.1" />
                  </NFormItemGi>

                  <NFormItemGi :span="12" label="SDXL LoRA 1" label-placement="top" path="selectValue">
                    <NSelect
                      placeholder="Select"
                      :options="lorasOtion"
                      :consistent-menu-width="false"
                      size="small"
                      class="w-10/12"
                    ></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="Weight " label-placement="top" path="inputValue">
                    <NSlider :step="0.1" :min="-2" :max="2" />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="SDXL LoRA 2" label-placement="top" path="selectValue">
                    <NSelect
                      placeholder="Select"
                      :options="lorasOtion"
                      :consistent-menu-width="false"
                      size="small"
                      class="w-10/12"
                    ></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="Weight " label-placement="top" path="inputValue">
                    <NSlider :step="0.1" :min="-2" :max="2" />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="SDXL LoRA 3" label-placement="top" path="selectValue">
                    <NSelect
                      placeholder="Select"
                      :options="lorasOtion"
                      :consistent-menu-width="false"
                      size="small"
                      class="w-10/12"
                    ></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="Weight " label-placement="top" path="inputValue">
                    <NSlider :step="0.1" :min="-2" :max="2" />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="SDXL LoRA 4" label-placement="top" path="selectValue">
                    <NSelect
                      placeholder="Select"
                      :options="lorasOtion"
                      :consistent-menu-width="false"
                      size="small"
                      class="w-10/12"
                    ></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="Weight " label-placement="top" path="inputValue">
                    <NSlider :step="0.1" :min="-2" :max="2" />
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="SDXL LoRA 5" label-placement="top" path="selectValue">
                    <NSelect
                      placeholder="Select"
                      :options="lorasOtion"
                      :consistent-menu-width="false"
                      size="small"
                      class="w-10/12"
                    ></NSelect>
                  </NFormItemGi>
                  <NFormItemGi :span="12" label="Weight " label-placement="top" path="inputValue">
                    <NSlider :step="0.1" :min="-2" :max="2" />
                  </NFormItemGi>
                </NGrid>
              </NForm>
            </NTabPane>
            <NTabPane name="controll" tab="控制">
              <NGrid :cols="24" :x-gap="20" :y-gap="20">
                <NGi :span="12" :show-label="false">
                  <NUpload
                    action="https://www.mocky.io/v2/5e4bafc63100007100d8b70f"
                    list-type="image-card"
                    :multiple="false"
                    :max="1"
                    :data="{ sloat: '1' }"
                    :default-upload="false"
                    accept=".jpg,.jpeg,.png"
                  >
                    点击上传
                  </NUpload>
                </NGi>
                <NGi :span="12" :show-label="false" class="flex-col justify-around">
                  <NFormItemGi label="类型" label-placement="left" :show-feedback="false">
                    <NSelect placeholder="Select" :options="controllOption" size="small" class="mt-1 w-full"></NSelect>
                  </NFormItemGi>
                  <NFormItemGi label="时机" label-placement="left" :show-feedback="false">
                    <NSlider v-model:value="controllerSopt1" range :step="0.001" :min="0" :max="1" />
                  </NFormItemGi>
                  <NFormItemGi label="权重" label-placement="left" :show-feedback="false">
                    <NSlider :step="0.1" :min="0" :max="2" />
                  </NFormItemGi>
                </NGi>
                <NDivider />
              </NGrid>
            </NTabPane>
          </NTabs>
        </NCard>
      </NGi>
      <NGi span="24 m:16">
        <NCard class="h-full">
          <NImage
            class="h-200px w-200px"
            src="https://xxfpfg-gg.gdsre.cn/images/example/example/f35fdf57d63ebab536682b333a3f79c1.jpeg?imageMogr2/thumbnail/300x"
          />
          <template #footer>
            <NCollapse default-expanded-names="1" accordion class="mb-2">
              <NCollapseItem title="正向提示词" name="1">
                <NInput type="textarea"></NInput>
                <template #header-extra>
                  <NButton @click.stop="">按钮</NButton>
                </template>
              </NCollapseItem>
              <NCollapseItem title="反向提示词" name="2">
                <NInput type="textarea"></NInput>
              </NCollapseItem>
            </NCollapse>

            <NButton type="primary" class="h-60px w-full" size="large">生成</NButton>
          </template>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style lang="scss" scoped>
:deep(.custom-checkbox) {
  // padding-left: 20px;
  .n-checkbox-box-wrapper {
    position: absolute !important;
    left: 0.3rem;
    top: 0.3rem;
    z-index: 999 !important;
  }
}
:deep(.n-upload) {
  .n-upload-file-list {
    grid-template-columns: unset;
    .n-upload-file,
    .n-upload-trigger {
      width: 100% !important;
      height: 150px !important;
    }
    .n-upload-file {
      .n-upload-file-info {
        .n-upload-file-info__thumbnail {
          .n-image {
            img {
              object-fit: cover !important;
            }
          }
        }
      }
    }
  }
}
</style>
