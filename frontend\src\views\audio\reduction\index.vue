<script setup lang="ts">
import { computed, h, onMounted, reactive, ref } from 'vue';
import type { DataTableColumns, UploadCustomRequestOptions } from 'naive-ui';
import { useLoadingBar, useMessage } from 'naive-ui';
import { getHistory, getReductionStatus, postReduction, postUploadFile } from '@/service/api';
import { audioBufferToBlobUrl, audioBufferToFile, blobToBase64, fileToArrayBuffer } from '@/utils/audio';
import { generateUUID } from '@/utils/common';

const message = useMessage();
const wrapperRef = ref<HTMLElement>();
const loadingBar = useLoadingBar();
const uploadLoading = ref(false);
const loading = ref(false);

const audioBlobUrls = ref<string[]>([]);
const fileNames = ref<string[]>([]);
const emptyFileNames = computed(() => fileNames.value.length === 0);
const isUploadDisabled = computed(() => audioBlobUrls.value.length > 8);

const customRequest = async ({ file, data, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  if (isUploadDisabled.value) {
    message.error('最多只能上传8个音频文件');
    onError();
    return;
  }

  loadingBar.start();
  uploadLoading.value = true;

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }

  try {
    let audioFile: File;
    let blobUrl: string;

    if (file.type === 'video/mp4') {
      const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
      const audioCtx = new AudioContext();
      const audioBuffer = await audioCtx.decodeAudioData(buffer);
      const uuid = generateUUID();
      const newFileName = `${uuid}.wav`;
      audioFile = audioBufferToFile(audioBuffer, newFileName);
      blobUrl = await audioBufferToBlobUrl(audioBuffer);
    } else {
      audioFile = file.file as File;
      blobUrl = URL.createObjectURL(file.file as Blob);
    }

    formData.append('file', audioFile);

    const res = await postUploadFile('separate', formData, progressEvent => {
      if (progressEvent.progress) {
        onProgress({ percent: progressEvent!.progress * 100 });
      }
    });

    console.log(res);
    audioBlobUrls.value.push(blobUrl);
    fileNames.value.push(file.name);
    onFinish();
  } catch (error) {
    console.log(error);
    onError();
  } finally {
    loadingBar.finish();
    uploadLoading.value = false;
  }
};

const onRemove = (index: number) => {
  audioBlobUrls.value.splice(index, 1);
  fileNames.value.splice(index, 1);
};

// const removeFile = () => {
//   // show-file-list
//   audioBlobUrl.value = '';
//   fileName.value = '';
// };

// const jobid = ref<string>('');

interface HistoryItem {
  taskid: string;
  status: string;
  create_time: string;
  uptime: string;
  audio_url: string;
}

const columns: DataTableColumns<HistoryItem> = [
  {
    title: '任务',
    key: 'taskid',
    minWidth: 300,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    minWidth: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'create_time',
    minWidth: 180,
    align: 'center'
  },
  {
    title: '更新时间',
    key: 'uptime',
    minWidth: 180,
    align: 'center'
  },
  {
    title: '预览',
    key: 'audio_url',
    render: (row: HistoryItem) => {
      return row.audio_url ? h('audio', { src: row.audio_url, controls: true, style: `max-width: 19em` }) : '无预览';
    },
    minWidth: 210,
    align: 'center'
  }
];

const historyData = ref<HistoryItem[]>([]);
// const totalPages = ref(1);
// const currentPage = ref(1);
// const pageSize = ref(8);
const pagination = reactive({
  page: 1,
  pageSize: 8,
  pageCount: 1
});

const formatTime = (time: string) => {
  return time.replace('T', ' ');
};

const formatStatus = (status: string) => {
  switch (status) {
    case 'Submitted':
      return '任务提交';
    case 'Running':
      return '任务运行中';
    case 'Success':
      return '任务完成';
    case 'Failed':
      return '任务失败';
    case 'Pause':
      return '任务暂停';
    case 'Cancel':
      return '任务取消';
    default:
      return '执行异常';
  }
};

const fetchHistory = async (page: number) => {
  loading.value = true;
  try {
    const res = await getHistory(page, pagination.pageSize);
    if (res.data) {
      historyData.value = res.data.history.map((item: HistoryItem) => ({
        ...item,
        create_time: formatTime(item.create_time),
        uptime: formatTime(item.uptime),
        status: formatStatus(item.status)
      }));
      pagination.pageCount = res.data.total_pages;
    } else {
      message.error('获取历史任务失败');
    }
  } catch (error) {
    console.error('请求历史任务出错', error);
    message.error('请求历史任务出错');
  } finally {
    loading.value = false;
  }
};

const pollReductionStatus = (jobId: string) => {
  const interval = setInterval(async () => {
    try {
      const res = await getReductionStatus(jobId);
      if (res.data) {
        const state = formatStatus(res.data.state);
        const audioUrl = res.data.audio;

        const taskIndex = historyData.value.findIndex(item => item.taskid === jobId);

        if (taskIndex !== -1) {
          // 更新表格中的数据
          historyData.value[taskIndex].status = state;
          historyData.value[taskIndex].uptime = formatTime(res.data.update_time);
          if (audioUrl) {
            historyData.value[taskIndex].audio_url = audioUrl;
          }
        } else {
          // 如果表格中没有这个 taskid，则添加一行数据
          fetchHistory(1);
        }

        if (state === '任务完成' || state === '任务失败' || state === '任务暂停' || state === '任务取消') {
          clearInterval(interval); // 任务完成、失败、暂停或取消时停止轮询
        }
      }
    } catch (error) {
      console.error('轮询任务状态时出错', error);
      clearInterval(interval); // 出错时停止轮询
    }
  }, 3000);
};

// eslint-disable-next-line no-await-in-loop
const toReduceNoise = async () => {
  if (fileNames.value.length === 0) {
    message.error('未上传音频');
    return;
  }
  if (historyData.value.some(item => item.status === '任务提交' || item.status === '任务运行中')) {
    message.info('当前任务运行中');
    return;
  }
  loading.value = true;
  try {
    // 使用 for 循环依次处理每个音频文件
    for (let index = 0; index < fileNames.value.length; index++) {
      const fileName = fileNames.value[index];
      // eslint-disable-next-line no-await-in-loop
      const audioBlob = await fetch(audioBlobUrls.value[index]).then(res => res.blob());
      // eslint-disable-next-line no-await-in-loop
      const base64Audio = await blobToBase64(audioBlob);
      let fileNameValue = fileName;
      if (!fileNameValue.endsWith('.mp3')) {
        fileNameValue = fileNameValue.replace(/\.\w+$/, '.wav');
      }
      const reductionData: Api.Audio.Reduction = {
        audio_data: base64Audio,
        original_filename: fileNameValue
      };
      try {
        // eslint-disable-next-line no-await-in-loop
        const res = await postReduction(reductionData);
        if (res.data) {
          const newJobId = res.data.JobId;
          console.log('jobid:', newJobId);
          message.success(`音频 ${fileNameValue} 降噪任务提交成功`);
          pollReductionStatus(newJobId); // 轮询任务状态
        } else {
          message.error(`音频 ${fileNameValue} 提交失败`);
        }
      } catch (error) {
        console.error('Error in postReduction:', error);
        message.error(`音频 ${fileNameValue} 处理过程中发生错误`);
      }
    }
  } catch (error) {
    console.error('Error in reduction process:', error);
    message.error('降噪任务处理过程中发生错误');
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchHistory(page);
};

onMounted(() => {
  fetchHistory(pagination.page).then(() => {
    historyData.value.forEach(item => {
      if (item.status === '任务提交' || item.status === '任务运行中') {
        pollReductionStatus(item.taskid);
      }
    });
  });
});

// 表格行高
const ROW_HEIGHT = 5.65;
const rowProps = () => {
  return {
    style: `height: ${ROW_HEIGHT}em;`
  };
};
</script>

<template>
  <NGrid :cols="24" :x-gap="20">
    <!-- 左侧面板占 8 列 -->
    <NGridItem :span="8">
      <NCard class="h-full">
        <div ref="wrapperRef" class="h-full flex-col">
          <NFlex vertical class="relative h-full">
            <NScrollbar class="max-h-98">
              <NGrid cols="20" :x-gap="10">
                <NGridItem span="24">
                  <NSpin :show="uploadLoading">
                    <NUpload
                      :custom-request="customRequest"
                      accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
                      multiple
                      :show-file-list="false"
                      :disabled="isUploadDisabled"
                      class="mt-5 px-5"
                    >
                      <NUploadDragger>
                        <NText>{{ $t('page.media.form.text.dndVideoOrAudio') }}</NText>
                        <NP depth="3"></NP>
                        <div class="grid min-h-54px items-center justify-center">
                          <div v-for="(url, index) in audioBlobUrls" :key="index">
                            <audio :src="url" controls class="mb-3 min-w-full"></audio>
                          </div>
                        </div>
                        <div class="n-upload-file-list pt-3">
                          <template v-if="emptyFileNames">
                            <div>{{ $t('page.media.form.text.noFileSelected') }}</div>
                          </template>
                          <template v-else>
                            <div
                              v-for="(fileName, index) in fileNames"
                              :key="index"
                              class="n-upload-file n-upload-file--success-status n-upload-file--text-type"
                            >
                              <div class="n-upload-file-info flex items-center justify-between">
                                <div class="n-upload-file-info__name">
                                  <span>{{ fileName }}</span>
                                  <NButton text class="ml-2" @click.stop="onRemove(index)">
                                    {{ $t('page.media.form.btn.del') }}
                                  </NButton>
                                </div>
                              </div>
                            </div>
                          </template>
                        </div>
                      </NUploadDragger>
                    </NUpload>
                  </NSpin>
                </NGridItem>
              </NGrid>
            </NScrollbar>

            <div class="sider-bottom mt-30">
              <NAlert type="info" class="alert-container" title="使用说明">
                <NText>1.最多支持8个音频、视频文件批量操作。</NText>
                <br />
                <NText>2.右侧板块为您的历史任务展示</NText>
                <br />
                <NText>3.如当前存在运行中任务，需等待该任务完成</NText>
                <br />
                <NText>4.请等待检查所有提交的任务均显示提交成功</NText>
              </NAlert>
              <div class="button-container">
                <NButton type="info" class="generate-button" :disabled="isUploadDisabled" @click="toReduceNoise">
                  生成
                </NButton>
              </div>
            </div>
          </NFlex>
          <NLoadingBarProvider :to="wrapperRef" container-style="position: absolute;"></NLoadingBarProvider>
        </div>
      </NCard>
    </NGridItem>

    <!-- 右侧面板占 16 列 -->
    <NGridItem :span="16">
      <NCard class="h-full">
        <NDataTable
          remote
          :bordered="true"
          :columns="columns"
          :data="historyData"
          :loading="loading"
          :pagination="pagination"
          :row-key="row => row.taskid"
          class="mt-5"
          :row-props="rowProps"
          @update:page="handlePageChange"
        />
      </NCard>
    </NGridItem>
  </NGrid>
</template>

<style scoped lang="scss">
.prosess {
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audioBox :deep(.n-card__content) {
  display: flex;
  justify-content: center;
}

:deep(.n-data-table__pagination) {
  display: flex;
  justify-content: center;
}

.sider-bottom {
  width: 100%;
  padding: 0 1rem;
  position: relative;
  bottom: 0;

  .alert-container {
    min-height: 8rem;
    width: 100%;
    height: auto;
  }

  .button-container {
    padding: 1rem 0;

    .generate-button {
      width: 100%;
      min-height: 3rem;
      height: auto;
    }
  }
}
</style>
