<script setup lang="ts">
import { ref } from 'vue';
import type { CSSProperties } from 'vue';
import { useThemeStore } from '@/store/modules/theme';
import Loading from './loading.vue';

const themeStore = useThemeStore();

// 控制是否显示加载组件
const showLoading = ref(true);

// 测试样式
const containerStyle: CSSProperties = {
  padding: '20px',
  display: 'flex',
  flexDirection: 'column',
  gap: '20px'
};

// 切换主题
function toggleTheme() {
  themeStore.toggleThemeScheme();
}

// 切换显示状态
function toggleLoading() {
  showLoading.value = !showLoading.value;
}
</script>

<template>
  <div :style="containerStyle">
    <h2>加载组件测试</h2>

    <div style="display: flex; gap: 10px; margin-bottom: 20px">
      <button @click="toggleTheme">切换{{ themeStore.darkMode ? '亮色' : '暗色' }}主题</button>
      <button @click="toggleLoading">{{ showLoading ? '隐藏' : '显示' }}加载动画</button>
    </div>

    <div>
      <h3>默认样式</h3>
      <Loading :show="showLoading" />
    </div>

    <div>
      <h3>自定义大小和文本</h3>
      <Loading :show="showLoading" width="150" height="150" text="请稍候" />
    </div>

    <div>
      <h3>自定义动画时间</h3>
      <Loading
        :show="showLoading"
        width="150"
        height="150"
        text="SLOWER"
        :primary-duration="4"
        :secondary-duration="3"
        :text-duration="3"
      />
    </div>

    <div>
      <h3>自定义颜色</h3>
      <Loading
        :show="showLoading"
        width="150"
        height="150"
        text="CUSTOM"
        :custom-colors="{
          primaryPath: '#ff5555',
          secondaryPath: '#55ff55',
          text: '#5555ff'
        }"
      />
    </div>

    <div>
      <h3>百分比尺寸</h3>
      <div style="width: 200px; height: 200px; border: 1px solid #ccc">
        <Loading :show="showLoading" width="100%" height="100%" text="100%" />
      </div>
    </div>
  </div>
</template>
