import logging

from qcloud_cos import CosConfig, CosS3Client
from qcloud_cos.cos_exception import CosClient<PERSON><PERSON>r, CosServiceError

from config import app_settings
from .base import get_secret
import json
from tencentcloud.common.credential import Credential
from tencentcloud.sts.v20180813.sts_client import StsClient
from tencentcloud.sts.v20180813.models import GetFederationTokenRequest
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
import asyncio

logger = logging.getLogger(__name__)
secret = get_secret()
region = app_settings.tencent_cos_region
scheme = "https"  # 指定使用 http/https 协议来访问 COS，默认为 https，可不填

config = CosConfig(
  Region=region, SecretId=secret["id"], SecretKey=secret["key"], Scheme=scheme
)

client = CosS3Client(config)

bucket = app_settings.tencent_cos_bucket


def upload_file(file, cos_path, expires=None):
  if client.object_exists(Bucket=bucket, Key=cos_path):
    return app_settings.tencent_cos_host + cos_path
  # 重试N次
  success = False
  fn = cos_path.split("/")[-1]
  for i in range(0, 3):
    try:
      response = client.upload_file(
        Bucket=bucket,
        Key=cos_path,
        LocalFilePath=file,
        Metadata={
          'Content-Disposition': f'attachment; filename="{fn}"',
        }
      )
      success = True
      logger.info("upload file success")
      logger.info(response)
      break
    except CosClientError or CosServiceError as e:
      logger.error(e)

  if not success:
    raise Exception("upload file failed")

  return app_settings.tencent_cos_host + cos_path


def set_lifecycle(lifecycle_rules):
    """
    设置存储桶的生命周期配置
    
    Args:
        lifecycle_rules (dict): 生命周期规则配置，例如：
            {
                'Rule': [
                    {
                        'ID': 'rule1',
                        'Filter': {'Prefix': 'test/'},
                        'Status': 'Enabled',
                        'Expiration': {'Days': 100},
                        'Transition': [{'Days': 30, 'StorageClass': 'Standard_IA'}],
                        'AbortIncompleteMultipartUpload': {'DaysAfterInitiation': 7}
                    }
                ]
            }
            
    Returns:
        dict: 设置生命周期的响应结果
        
    Raises:
        Exception: 设置生命周期失败时抛出异常
    """
    try:
        response = client.put_bucket_lifecycle(
            Bucket=bucket,
            LifecycleConfiguration=lifecycle_rules
        )
        logger.info("Set bucket lifecycle successfully")
        return response
    except (CosClientError, CosServiceError) as e:
        logger.error(f"Failed to set bucket lifecycle: {str(e)}")
        raise Exception(f"设置生命周期失败: {str(e)}")


def get_lifecycle():
    """
    查询存储桶的生命周期配置
    
    Returns:
        dict: 存储桶的生命周期配置
        
    Raises:
        Exception: 查询生命周期失败时抛出异常
    """
    try:
        response = client.get_bucket_lifecycle(
            Bucket=bucket
        )
        logger.info("Get bucket lifecycle successfully")
        return response
    except (CosClientError, CosServiceError) as e:
        logger.error(f"Failed to get bucket lifecycle: {str(e)}")
        raise Exception(f"查询生命周期失败: {str(e)}")


def delete_lifecycle():
    """
    删除存储桶的生命周期配置
    
    Returns:
        dict: 删除生命周期的响应结果
        
    Raises:
        Exception: 删除生命周期失败时抛出异常
    """
    try:
        response = client.delete_bucket_lifecycle(
            Bucket=bucket
        )
        logger.info("Delete bucket lifecycle successfully")
        return response
    except (CosClientError, CosServiceError) as e:
        logger.error(f"Failed to delete bucket lifecycle: {str(e)}")
        raise Exception(f"删除生命周期失败: {str(e)}")


async def move_object(src_key, dest_key, src_bucket=None, dest_bucket=None, src_region=None):
    """
    移动对象（先复制后删除）
    
    Args:
        src_key (str): 源对象的路径（Key）
        dest_key (str): 目标对象的路径（Key）
        src_bucket (str, optional): 源存储桶名称。默认为None，表示使用配置的默认存储桶。
        dest_bucket (str, optional): 目标存储桶名称。默认为None，表示使用配置的默认存储桶。
        src_region (str, optional): 源对象所在区域。默认为None，表示使用配置的默认区域。
        
    Returns:
        str: 移动后的对象URL
        
    Raises:
        Exception: 移动对象失败时抛出异常
    """
    # 设置默认值
    if src_bucket is None:
        src_bucket = bucket
    if dest_bucket is None:
        dest_bucket = bucket
    if src_region is None:
        src_region = region
    
    # 首先检查源对象是否存在
    source_exists = await object_exists(src_key, src_bucket)
    if not source_exists:
        logger.warning(f"源对象不存在: {src_key}，直接返回目标路径URL")
        return app_settings.tencent_cos_host + dest_key
    
    # 检查目标对象是否已存在
    dest_exists = await object_exists(dest_key, dest_bucket)
    if dest_exists:
        logger.info(f"目标对象已存在: {dest_key}，无需复制")
        # 尝试删除源对象（如果源对象存在）
        try:
            await asyncio.to_thread(
                client.delete_object,
                Bucket=src_bucket,
                Key=src_key
            )
            logger.info(f"删除原对象成功: {src_key}")
        except (CosClientError, CosServiceError) as e:
            logger.error(f"删除原对象失败: {str(e)}")
        return app_settings.tencent_cos_host + dest_key
    
    # 复制操作
    copy_success = False
    for i in range(0, 3):  # 重试3次
        try:
            # 使用 asyncio.to_thread 包装同步操作为异步操作
            response = await asyncio.to_thread(
                client.copy,
                Bucket=dest_bucket,
                Key=dest_key,
                CopySource={
                    'Bucket': src_bucket,
                    'Key': src_key,
                    'Region': src_region
                }
            )
            copy_success = True
            logger.info(f"复制对象成功: {src_key} -> {dest_key}")
            logger.info(response)
            break
        except (CosClientError, CosServiceError) as e:
            logger.error(f"复制对象失败 (尝试 {i+1}/3): {str(e)}")
            if i == 2:  # 最后一次尝试
                raise Exception(f"移动对象失败: 复制步骤失败 - {str(e)}")
    
    # 只有在复制成功后才删除原对象
    if copy_success:
        try:
            # 使用 asyncio.to_thread 包装同步操作为异步操作
            await asyncio.to_thread(
                client.delete_object,
                Bucket=src_bucket,
                Key=src_key
            )
            logger.info(f"删除原对象成功: {src_key}")
        except (CosClientError, CosServiceError) as e:
            logger.error(f"删除原对象失败: {str(e)}")
            # 即使删除失败，复制已经成功，所以不抛出异常，但记录日志
            logger.warning("注意: 对象已被复制，但原对象删除失败。可能需要手动清理。")
    
    # 返回新对象的URL
    return app_settings.tencent_cos_host + dest_key


async def object_exists(key, bucket_name=None):
    """
    检查对象是否存在于存储桶中
    
    Args:
        key (str): 对象的路径（Key）
        bucket_name (str, optional): 存储桶名称。默认为None，表示使用配置的默认存储桶。
        
    Returns:
        bool: 如果对象存在返回True，否则返回False
    """
    if bucket_name is None:
        bucket_name = bucket
        
    try:
        # 使用 asyncio.to_thread 包装同步操作为异步操作
        exists = await asyncio.to_thread(
            client.object_exists,
            Bucket=bucket_name,
            Key=key
        )
        return exists
    except (CosClientError, CosServiceError) as e:
        logger.error(f"检查对象是否存在失败: {str(e)}")
        # 发生错误时返回False
        return False


# 显式导出 client 和 bucket
__all__ = ["client", "bucket", "region", "generate_temp_credentials", "set_lifecycle", "get_lifecycle", "delete_lifecycle", "move_object", "object_exists", "upload_file"]


def generate_temp_credentials(duration_seconds=7200):
    """
    生成临时密钥令牌，用于客户端直接上传文件到COS
    
    Args:
        duration_seconds (int, optional): 临时密钥有效期，单位秒。默认为7200（2小时）
        
    Returns:
        dict: 包含临时密钥信息的字典，格式如下：
            {
                "credentials": {
                    "tmpSecretId": "临时密钥ID",
                    "tmpSecretKey": "临时密钥Key",
                    "sessionToken": "临时令牌"
                },
                "expiration": "过期时间",
                "startTime": "开始时间",
                "requestId": "请求ID"
            }
            
    Raises:
        Exception: 生成临时密钥失败时抛出异常
    """
    try:
        # 获取永久密钥
        secret = get_secret()
        
        # 初始化STS客户端
        cred = Credential(secret["id"], secret["key"])
        sts_client = StsClient(cred, region)
        
        # 从bucket名称中提取APPID
        # 假设 bucket 格式为 'name-appid'
        bucket_name_parts = bucket.split('-')
        if len(bucket_name_parts) < 2 or not bucket_name_parts[-1].isdigit():
            logger.error(f"Invalid bucket name format: {bucket}. Expected 'name-appid'. Cannot extract APPID.")
            raise ValueError(f"无效的存储桶名称格式: {bucket}。应为 '名称-APPID' 格式，无法提取 APPID。")
        appid = bucket_name_parts[-1]
        
        # 设置权限策略
        policy = {
            'version': '2.0',
            'statement': [
                {
                    'action': [
                        'name/cos:PutObject',
                        'name/cos:PostObject',
                        'name/cos:InitiateMultipartUpload',
                        'name/cos:ListMultipartUploads',
                        'name/cos:ListParts',
                        'name/cos:UploadPart',
                        'name/cos:CompleteMultipartUpload',
                        "name/cos:PutBucketCORS",
                        "name/cos:PutBucketLifecycle",
                        "name/cos:GetBucketLifecycle",
                        "name/cos:DeleteBucketLifecycle"
                    ],
                    'effect': 'allow',
                    'resource': [
                       # f'qcs::cos:ap-hongkong:uid/1259109643:ai-1259109643/*'
                        f'qcs::cos:{region}:uid/{appid}:{bucket}/*',
                        # 添加对存储桶自身的权限
                        f'qcs::cos:{region}:uid/{appid}:{bucket}'
                    ]
                }
            ]
        }
        
        
        # 创建请求对象并设置参数
        request = GetFederationTokenRequest()
        request.Name = 'cos-sts-python'
        request.Policy = json.dumps(policy)
        request.DurationSeconds = duration_seconds
        
        # 请求临时密钥
        response = sts_client.GetFederationToken(request)
        
        logger.info("Generated temporary credentials successfully")
        # 将响应对象转换为字典格式，同时保留原始字段和文档格式
        response_dict = {            
            # 保留原始字段格式，便于与SDK兼容
            "Credentials": {
                "Token": response.Credentials.Token,
                "TmpSecretId": response.Credentials.TmpSecretId,
                "TmpSecretKey": response.Credentials.TmpSecretKey
            },
            "ExpiredTime": response.ExpiredTime,
            "Expiration": response.Expiration,
            "RequestId": response.RequestId
        }
        return response_dict
        
    except TencentCloudSDKException as e:
        logger.error(f"Failed to generate temporary credentials: {str(e)}")
        raise Exception(f"生成临时密钥失败: {str(e)}")
