from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, case, join, desc, asc
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import date, datetime, timezone, timedelta
import pytz
from models.assets import AssetOut, Assets, AssetType
from models.users import User, get_request_user, get_request_role
from utils.database import get_db
from models.users import User

import logging

from utils.exceptions import ClientVisibleException

logger = logging.getLogger(__name__)

router = APIRouter()

class StatisticsResponse(BaseModel):
    total: int
    image_count: int
    video_count: int
    audio_count: int

@router.get("/statistics", tags=["asset"], response_model=Dict[str, Any])
async def get_asset_report_statistics(
    startDate: Optional[str] = Query(None, description="开始日期时间 (ISO格式)"),
    endDate: Optional[str] = Query(None, description="结束日期时间 (ISO格式)"),
    company: Optional[str] = Query(None, description="公司名称"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    # 基础查询
    query = select(
        func.count().label("total"),
        func.sum(case((Assets.type == AssetType.IMAGE, 1), else_=0)).label("image_count"),
        func.sum(case((Assets.type == AssetType.VIDEO, 1), else_=0)).label("video_count"),
        func.sum(case((Assets.type == AssetType.AUDIO, 1), else_=0)).label("audio_count")
    ).select_from(Assets)
    
    # 设置本地时区
    local_tz = pytz.timezone('Asia/Shanghai')

    # 应用日期过滤
    if startDate:
        try:
            # 解析ISO格式UTC时间
            start_datetime_utc = datetime.fromisoformat(startDate.replace('Z', '+00:00'))
            # 转换为本地时间
            start_datetime_local = start_datetime_utc.astimezone(local_tz)
            # 使用本地时间查询数据库
            logger.info(f"Using start_datetime_local: {start_datetime_local}")
            query = query.where(Assets.create_time >= start_datetime_local)
        except ValueError as e:
            logger.error(f"Invalid startDate format: {startDate}, error: {str(e)}")
            raise ClientVisibleException(f"Invalid startDate format: {startDate}")
    
    if endDate:
        try:
            # 解析ISO格式UTC时间
            end_datetime_utc = datetime.fromisoformat(endDate.replace('Z', '+00:00'))
            # 转换为本地时间
            end_datetime_local = end_datetime_utc.astimezone(local_tz)
            # 使用本地时间查询数据库
            logger.info(f"Using end_datetime_local: {end_datetime_local}")
            query = query.where(Assets.create_time <= end_datetime_local)
        except ValueError as e:
            logger.error(f"Invalid endDate format: {endDate}, error: {str(e)}")
            raise ClientVisibleException(f"Invalid endDate format: {endDate}")
    
    # 应用公司过滤
    if company:
        # 创建子查询获取指定公司的用户ID
        user_ids = select(User.id).where(User.company == company).scalar_subquery()
        
        # 在主查询中应用子查询过滤
        query = query.where(Assets.user_id.in_(user_ids))
    
    # 执行查询
    result = await db.execute(query)
    stats = result.fetchone()
    
    # 处理结果可能为空的情况
    if not stats:
        return {
            "code": "0000",
            "data": {
                "total": 0,
                "image_count": 0,
                "video_count": 0,
                "audio_count": 0
            }
        }
    
    # 确保结果中的 None 值转换为 0
    response_data = {
        "total": stats.total or 0,
        "image_count": stats.image_count or 0,
        "video_count": stats.video_count or 0,
        "audio_count": stats.audio_count or 0
    }
    
    return {
        "code": "0000",
        "data": response_data
    }

class UserAssetItem(BaseModel):
    company: str
    username: str
    total_assets: int
    image_count: int
    video_count: int
    audio_count: int

class UserAssetsResponse(BaseModel):
    total: int
    page: int
    pageSize: int
    users: List[UserAssetItem]

@router.get("/user_assets", tags=["asset"], response_model=Dict[str, Any])
async def get_asset_report_user_assets(
    startDate: Optional[str] = Query(None, description="开始日期时间 (ISO格式)"),
    endDate: Optional[str] = Query(None, description="结束日期时间 (ISO格式)"),
    company: Optional[str] = Query(None, description="公司名称"),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(10, ge=1, le=100, description="每页条数"),
    sortField: Optional[str] = Query(None, description="排序字段"),
    sortOrder: Optional[str] = Query("asc", description="排序方向 (asc/desc)"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    # 设置本地时区
    local_tz = pytz.timezone('Asia/Shanghai')
    
    # 构建统计查询 - 需要按用户分组
    count_query = select(func.count().label("total")).select_from(
        select(Assets.user_id)
        .join(User, User.id == Assets.user_id)
        .group_by(Assets.user_id)
        .subquery()
    )
    
    # 构建用户资产聚合查询
    query = select(
        User.id,
        User.company,
        User.nickname.label("username"),
        func.count(Assets.id).label("total_assets"),
        func.sum(case((Assets.type == AssetType.IMAGE, 1), else_=0)).label("image_count"),
        func.sum(case((Assets.type == AssetType.VIDEO, 1), else_=0)).label("video_count"),
        func.sum(case((Assets.type == AssetType.AUDIO, 1), else_=0)).label("audio_count")
    ).select_from(Assets).join(User, User.id == Assets.user_id).group_by(User.id, Assets.user_id, User.company, User.nickname)
    
    # 应用日期过滤
    if startDate:
        try:
            # 解析ISO格式UTC时间
            start_datetime_utc = datetime.fromisoformat(startDate.replace('Z', '+00:00'))
            # 转换为本地时间
            start_datetime_local = start_datetime_utc.astimezone(local_tz)
            logger.info(f"Using start_datetime_local: {start_datetime_local}")
            query = query.where(Assets.create_time >= start_datetime_local)
            count_query = count_query.where(Assets.create_time >= start_datetime_local)
        except ValueError as e:
            logger.error(f"Invalid startDate format: {startDate}, error: {str(e)}")
            raise ClientVisibleException(f"Invalid startDate format: {startDate}")
    
    if endDate:
        try:
            # 解析ISO格式UTC时间
            end_datetime_utc = datetime.fromisoformat(endDate.replace('Z', '+00:00'))
            # 转换为本地时间
            end_datetime_local = end_datetime_utc.astimezone(local_tz)
            logger.info(f"Using end_datetime_local: {end_datetime_local}")
            query = query.where(Assets.create_time <= end_datetime_local)
            count_query = count_query.where(Assets.create_time <= end_datetime_local)
        except ValueError as e:
            logger.error(f"Invalid endDate format: {endDate}, error: {str(e)}")
            raise ClientVisibleException(f"Invalid endDate format: {endDate}")
    
    # 应用公司过滤
    if company:
        query = query.where(User.company == company)
        count_query = count_query.where(User.company == company)
    
    # 应用排序
    valid_sort_fields = {
        "total_assets": "total_assets",
        "image_count": "image_count", 
        "video_count": "video_count",
        "audio_count": "audio_count",
        "username": "username",
        "company": "company"
    }
    
    # 默认排序字段为总资产数
    if sortField and sortField in valid_sort_fields:
        sort_column = valid_sort_fields[sortField]
        if sortOrder and sortOrder.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
    else:
        # 默认按总资产数降序排列
        query = query.order_by(desc("total_assets"))
    
    # 执行总数查询
    total_result = await db.execute(count_query)
    total_count = total_result.scalar() or 0
    
    # 应用分页
    offset = (page - 1) * pageSize
    query = query.offset(offset).limit(pageSize)
    
    # 执行分页查询
    result = await db.execute(query)
    user_assets = result.fetchall()
    
    # 格式化结果
    users_data = []
    for row in user_assets:
        users_data.append({
            "id": row.id,
            "company": row.company or "",
            "username": row.username or "",
            "total_assets": row.total_assets or 0,
            "image_count": row.image_count or 0,
            "video_count": row.video_count or 0,
            "audio_count": row.audio_count or 0
        })
    
    # 构建响应
    response_data = {
        "total": total_count,
        "page": page,
        "pageSize": pageSize,
        "users": users_data
    }
    
    return {
        "code": "0000",
        "data": response_data
    }


class AssetsPaginatedData(BaseModel):
    """资产分页数据模型"""
    records: List[AssetOut]
    current: int
    size: int
    total: int


class AssetsPaginatedResponse(BaseModel):
    """资产分页响应模型"""
    code: str = "0000"
    msg: str = "success"
    data: AssetsPaginatedData


@router.get("/get_user_assets", response_model=AssetsPaginatedResponse, tags=["asset"])
async def get_user_assets(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    type: str = Query(AssetType.IMAGE, description="资产类型筛选，使用'all'返回所有类型"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
    iscollect: Optional[int] = Query(None, description="是否收藏筛选，使用'1'返回收藏的资产，不提供则返回所有资产"),
    userid: int = Query(..., description="要查询的用户ID"),
) -> AssetsPaginatedResponse:
    """
    超级管理员获取指定用户的资产信息
    
    Args:
        page: 页码，从1开始
        size: 每页大小，范围1-100
        type: 资产类型筛选，默认为IMAGE，使用'all'返回所有类型
        user: 当前登录用户
        db: 数据库会话
        iscollect: 是否收藏筛选，未提供时返回所有资产，使用'1'返回收藏的资产
        userid: 要查询的用户ID
        
    Returns:
        包含用户资产分页数据的响应
    """
    try:
        # 获取当前用户的角色
        roles = await get_request_role(user, db=db)
        # 检查当前用户是否为超级管理员
        is_super_admin = 'super_admin' in roles
        
        if not is_super_admin:
            raise ClientVisibleException("权限不足")
        
        async with db as session:
            # 构建查询条件
            base_query = select(Assets).filter(Assets.user_id == userid)
            count_query = select(func.count()).select_from(Assets).filter(Assets.user_id == userid)
            
            # 如果不是请求所有类型，则按类型筛选
            if type.upper() != "ALL":
                try:
                    asset_type = AssetType(type.upper())
                    base_query = base_query.filter(Assets.type == asset_type)
                    count_query = count_query.filter(Assets.type == asset_type)
                except ValueError:
                    # 如果传入的类型不是有效的AssetType，则默认使用IMAGE类型
                    base_query = base_query.filter(Assets.type == AssetType.IMAGE)
                    count_query = count_query.filter(Assets.type == AssetType.IMAGE)
            
            # 根据收藏状态筛选，仅当明确提供参数时
            if iscollect is not None:
                if iscollect == 1:
                    base_query = base_query.filter(Assets.iscollect == 1)
                    count_query = count_query.filter(Assets.iscollect == 1)
                elif iscollect == 0:
                    base_query = base_query.filter(Assets.iscollect == 0)
                    count_query = count_query.filter(Assets.iscollect == 0)
            
            # 获取总数
            count_result = await session.execute(count_query)
            total = count_result.scalar()
            
            # 获取分页数据，按创建时间降序排列
            data_query = base_query.order_by(Assets.create_time.desc()).offset((page - 1) * size).limit(size)
            data_result = await session.execute(data_query)
            assets = data_result.scalars().all()
            
            # 转换为输出模型
            records = [AssetOut.model_validate(asset) for asset in assets]
            
            return AssetsPaginatedResponse(
                code="0000",
                msg="success",
                data=AssetsPaginatedData(
                    records=records,
                    current=page,
                    size=size,
                    total=total
                )
            )
            
    except Exception as e:
        logger.error(f"获取用户资产失败: {str(e)}")
        raise ClientVisibleException("获取用户资产失败") from e
