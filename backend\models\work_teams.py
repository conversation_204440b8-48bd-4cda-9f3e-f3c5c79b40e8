from sqlalchemy import Column, Integer, String, TIMESTAMP
from utils.database import Base


class WorkTeams(Base):
  __tablename__ = "work_teams"

  id = Column(Integer, primary_key=True, index=True, autoincrement=True)
  team_name = Column(String(32), nullable=False, comment="团队名称")
  team_code = Column(String(12), nullable=False, comment="团队编码")
  update_time = Column(TIMESTAMP, default="CURRENT_TIMESTAMP", onupdate="CURRENT_TIMESTAMP", nullable=True,
                       comment="更新时间")
  email = Column(String(32), nullable=False, comment="邮箱")

  def __repr__(self):
    return f"<WorkTeams(id={self.id}, team_name={self.team_name}, team_code={self.team_code})>"
