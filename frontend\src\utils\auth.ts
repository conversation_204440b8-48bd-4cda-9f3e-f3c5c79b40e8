import { useAuthStore } from '@/store/modules/auth';
import { localStg } from '@/utils/storage';

/**
 * 检查HTTP响应的认证状态
 *
 * @param response - Fetch API的Response对象
 * @returns 是否为认证错误
 */
export function isAuthError(response: Response): boolean {
  // 检查HTTP状态码
  if (response.status === 401 || response.status === 403) {
    return true;
  }
  return false;
}

/**
 * 检查后端业务错误码是否为认证相关错误
 *
 * @param errorCode - 后端返回的错误码
 * @returns 是否为认证错误
 */
export function isBackendAuthError(errorCode: string): boolean {
  // 获取环境变量中配置的登出错误码
  const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
  const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
  const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];

  return (
    logoutCodes.includes(errorCode) || modalLogoutCodes.includes(errorCode) || expiredTokenCodes.includes(errorCode)
  );
}

/**
 * 处理认证错误，清除认证状态并跳转到登录页
 *
 * @param redirectPath - 可选的重定向路径
 */
export function handleAuthError(redirectPath?: string): void {
  const authStore = useAuthStore();

  // 清除认证状态
  authStore.resetStore();

  // 构建登录页URL，包含重定向参数
  const currentPath = redirectPath || window.location.pathname + window.location.search;
  const loginUrl = currentPath !== '/login' ? `/login?redirect=${encodeURIComponent(currentPath)}` : '/login';

  // 使用硬重定向，确保页面状态完全重置
  window.location.href = loginUrl;
}

/**
 * 检查并处理SSE响应的认证状态
 *
 * @param response - Fetch API的Response对象
 * @returns 如果是认证错误返回true，否则返回false
 */
export function checkAndHandleSSEAuth(response: Response): boolean {
  if (isAuthError(response)) {
    handleAuthError();
    return true;
  }
  return false;
}

/**
 * 验证当前token是否存在
 *
 * @returns token字符串或null
 */
export function getCurrentToken(): string | null {
  return localStg.get('token');
}

/**
 * 检查当前是否已登录
 *
 * @returns 是否已登录
 */
export function isLoggedIn(): boolean {
  const token = getCurrentToken();
  return Boolean(token);
}
