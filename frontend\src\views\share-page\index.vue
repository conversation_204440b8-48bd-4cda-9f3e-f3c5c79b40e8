<script setup lang="ts">
import { computed, onMounted, ref, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import useClipboard from 'vue-clipboard3';
import logo from '@/assets/svg-icon/logo_long.svg';
import type { ShareInfo } from '@/service/api/share';
import { ShareType, fetchShare } from '@/service/api/share';
import { ParamsFillBack, encodeParams } from '@/utils/paramsFillBack';
import { $t } from '@/locales';
import { downloadFile } from '@/utils/common';

const route = useRoute();

// 分享信息，null 表示分享信息不存在
const shareInfo = ref<ShareInfo | null>(null);
// 确定也是是否在加载中
const loading = ref(false);

// 用于处理临时分享数据
const tempShareData = ref<any>(null);

onMounted(async () => {
  // 加载分享信息
  loading.value = true;

  // 检查是否是临时分享链接
  const isTemp = route.query.temp === 'true';
  const tempData = route.query.data as string | undefined;

  if (isTemp && tempData) {
    try {
      // 解析临时分享数据
      const decodedData = JSON.parse(decodeURIComponent(tempData));

      // 智能检测媒体类型
      let shareType = ShareType.IMAGE;
      if (decodedData.type) {
        // 如果数据中包含类型信息，直接使用
        shareType = decodedData.type === 'VIDEO' ? ShareType.VIDEO : ShareType.IMAGE;
      } else if (decodedData.result && decodedData.result.url) {
        // 如果没有类型信息，通过URL检测
        const url = decodedData.result.url.toLowerCase();
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mkv'];
        const isVideo = videoExtensions.some(ext => url.includes(ext));
        shareType = isVideo ? ShareType.VIDEO : ShareType.IMAGE;
      }

      tempShareData.value = {
        share_key: `temp-${Date.now()}`,
        share_type: shareType,
        params: {
          prompt: decodedData.prompt || '',
          prompt_img: decodedData.prompt_img || []
        },
        result: decodedData.result || { url: '' }
      };
      loading.value = false;
      return;
    } catch (e) {
      console.error('解析临时分享数据失败:', e);
      // 如果解析失败，继续尝试加载正常的分享信息
    }
  }

  // 正常分享信息逻辑
  let share_key = route.query.key;
  if (!share_key) {
    loading.value = false;
    return;
  }
  if (Array.isArray(share_key)) {
    share_key = share_key[0];
  }
  try {
    shareInfo.value = await fetchShare(share_key as string);
  } finally {
    loading.value = false;
  }
});

const resultUrl = computed<string | null>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value) {
    return tempShareData.value.result.url || null;
  }

  // 使用正常分享数据
  if (shareInfo.value === null) {
    return null;
  }
  const url = shareInfo.value.result.url;
  if (!url) {
    return null;
  }
  return url.toString() || null;
});

// 添加计算属性：明确判断当前是否为视频资产
const isVideoAsset = computed<boolean>(() => {
  if (tempShareData.value) {
    // 优先使用明确设置的类型
    if (tempShareData.value.share_type === ShareType.VIDEO) {
      return true;
    }

    // 如果类型不明确，则通过URL检测
    if (resultUrl.value) {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mkv'];
      return videoExtensions.some(ext => resultUrl.value?.toLowerCase().includes(ext));
    }
  }

  // 使用正常分享数据
  return shareInfo.value?.share_type === ShareType.VIDEO || false;
});

const prompt = computed<string>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value && tempShareData.value.params.prompt) {
    return tempShareData.value.params.prompt;
  }

  // 使用正常分享数据
  if (shareInfo.value === null) {
    return '';
  }
  const p = shareInfo.value.params.prompt;
  if (!p) {
    return '';
  }
  return p.toString();
});

const promptImg = computed<string[]>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value && tempShareData.value.params.prompt_img) {
    const img = tempShareData.value.params.prompt_img;
    if (!img) {
      return [];
    }
    if (!Array.isArray(img)) {
      return [img.toString()];
    }
    return img;
  }

  // 使用正常分享数据
  if (shareInfo.value === null) {
    return [];
  }
  const img = shareInfo.value.params.prompt_img;
  if (!img) {
    return [];
  }
  if (!Array.isArray(img)) {
    return [img.toString()];
  }
  return img;
});

const downloading = ref(false);

const handleDownload = async () => {
  if (!resultUrl.value) {
    return;
  }
  downloading.value = true;
  try {
    // 确定文件类型，使用isVideoAsset计算属性判断
    const fileExt = isVideoAsset.value ? 'mp4' : 'png';
    const ext = resultUrl.value.split('.').pop()?.split('?')[0] || fileExt;
    const customFileName = `result.${ext}`;

    // 使用通用下载函数
    await downloadFile(resultUrl.value, customFileName);
  } catch (error) {
    console.error('下载文件失败:', error);
  } finally {
    downloading.value = false;
  }
};

const message = useMessage();
const { toClipboard } = useClipboard();

const handleCopy = async (field: 'prompt' | 'shareUrl') => {
  let text = '';
  switch (field) {
    case 'prompt':
      text = prompt.value;
      break;
    case 'shareUrl':
      text = location.href || '';
      break;
    default:
      break;
  }
  try {
    await toClipboard(text);
    message.success($t('page.share-page.copySuccess'));
  } catch (e) {
    console.error(e);
    message.error($t('page.share-page.copyError'));
  }
};

const router = useRouter();

// 重命名handleMakeSame为handleMakeSameImage以保持一致性
const handleMakeSameImage = () => {
  // 首先检查是否有临时分享数据
  if (tempShareData.value) {
    router.push({
      path: '/portrait/midjourney',
      query: {
        [ParamsFillBack]: encodeParams(tempShareData.value.params)
      }
    });
    return;
  }

  // 使用正常分享数据
  switch (shareInfo.value?.share_type) {
    case ShareType.IMAGE:
      router.push({
        path: '/portrait/midjourney',
        query: {
          [ParamsFillBack]: encodeParams(toRaw(shareInfo.value.params))
        }
      });
      break;
    case ShareType.VIDEO:
      router.push({
        path: '/video/framepack',
        query: {
          [ParamsFillBack]: encodeParams(toRaw(shareInfo.value.params))
        }
      });
      break;
    default:
      break;
  }
};

const handleCreateVideo = () => {
  // 检查临时分享数据
  if (tempShareData.value) {
    const params = { ...tempShareData.value.params };
    params.prompt_img = resultUrl.value as string;
    router.push({
      path: '/video/framepack',
      query: {
        [ParamsFillBack]: encodeParams(params)
      }
    });
    return;
  }

  // 使用正常分享数据
  if (!shareInfo.value) {
    return;
  }
  const params = toRaw(shareInfo.value.params);
  params.prompt_img = resultUrl.value as string;
  router.push({
    path: '/video/framepack',
    query: {
      [ParamsFillBack]: encodeParams(params)
    }
  });
};
</script>

<template>
  <div class="h-screen max-w-screen flex flex-col p-8">
    <header>
      <img :src="logo" alt="Logo" class="h-8" />
      <div v-if="tempShareData" class="mt-2 rounded-md bg-amber-50 p-2 text-amber-800">
        <small>这是一个临时分享链接，仅在当前会话有效</small>
      </div>
    </header>
    <main class="mt-8 flex flex-col grow gap-12 lg:flex-row">
      <div class="flex items-center justify-center lg:grow">
        <div class="aspect-16/9 max-w-[90%] w-full flex justify-center overflow-hidden rounded-lg bg-black">
          <NSkeleton v-if="loading" :sharp="false" height="100%" width="100%" />
          <img
            v-else-if="
              (shareInfo && shareInfo.share_type === ShareType.IMAGE && resultUrl) ||
              (tempShareData && tempShareData.share_type === ShareType.IMAGE && resultUrl)
            "
            class="h-full"
            :src="resultUrl"
            alt="Result"
          />
          <video
            v-else-if="
              (shareInfo && shareInfo.share_type === ShareType.VIDEO && resultUrl) ||
              (tempShareData && tempShareData.share_type === ShareType.VIDEO && resultUrl)
            "
            controls
            class="h-full w-full"
            :src="resultUrl"
          />
        </div>
      </div>
      <div class="flex flex-col grow lg:w-72 lg:grow-0">
        <div class="flex items-center justify-between">
          <NH5 class="mb-0 font-semibold">{{ $t('page.share-page.prompt') }}</NH5>
          <NButton size="tiny" @click="handleCopy('prompt')">
            <template #icon>
              <SvgIcon icon="solar:copy-bold" />
            </template>
            {{ $t('page.share-page.copy') }}
          </NButton>
        </div>
        <div class="mb-10 mt-4 grow">
          <NScrollbar class="h-full max-h-96">
            <NSkeleton v-if="loading" :text="true" :sharp="false" :repeat="8" />
            <NSkeleton v-if="loading" :text="true" :sharp="false" class="w-[60%]" />
            <NText v-else class="leading-6">
              {{ prompt }}
            </NText>
          </NScrollbar>
          <div class="mt-4">
            <NSpace v-if="loading">
              <NSkeleton :sharp="false" class="h-16 w-10" />
            </NSpace>
            <NImageGroup v-else>
              <NSpace>
                <NImage v-for="img of promptImg" :key="img" :src="img" class="h-16 rounded" alt="Prompt Image" />
              </NSpace>
            </NImageGroup>
          </div>
        </div>
        <div>
          <div class="mt-2 flex gap-2">
            <NPopover trigger="hover" :show-arrow="false" placement="top" class="w-59">
              <template #trigger>
                <NButton type="primary" class="flex-1">
                  <template #icon>
                    <SvgIcon v-if="loading" icon="gg:spinner" class="animate-spin" />
                    <SvgIcon v-else icon="mingcute:ai-fill" />
                  </template>
                  {{ $t('page.share-page.createSame') }}
                </NButton>
              </template>
              <div class="popover-buttons">
                <!-- 如果是图片类型，显示两个选项 -->
                <template v-if="!isVideoAsset">
                  <NButton block @click="handleMakeSameImage">
                    <template #icon>
                      <SvgIcon icon="humbleicons:image" />
                    </template>
                    生成图片
                  </NButton>
                  <NButton block class="mt-2" @click="handleCreateVideo">
                    <template #icon>
                      <SvgIcon icon="ri:film-ai-line" />
                    </template>
                    生成视频
                  </NButton>
                </template>
                <!-- 如果是视频类型，只显示一个选项 -->
                <template v-else>
                  <NButton block @click="handleCreateVideo">
                    <template #icon>
                      <SvgIcon icon="ri:film-ai-line" />
                    </template>
                    生成视频
                  </NButton>
                </template>
              </div>
            </NPopover>

            <NButton secondary :disabled="downloading" @click="handleDownload">
              <template #icon>
                <SvgIcon v-if="downloading || loading" icon="gg:spinner" class="animate-spin" />
                <SvgIcon v-else icon="material-symbols:download-rounded" />
              </template>
            </NButton>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
/* 弹出菜单按钮样式 */
.popover-buttons {
  width: 100%;
}

/* 弹出的操作菜单样式 */
.popover-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 100px;

  :deep(.n-button) {
    text-align: left;
    padding: 6px 12px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
