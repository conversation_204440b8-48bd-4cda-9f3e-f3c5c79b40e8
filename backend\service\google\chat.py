import asyncio
import logging

import vertexai
from vertexai.generative_models import GenerativeModel, Content, Part, Image
import json
from typing import List
from models.chat import chatContent
import requests

PROJECT_ID = "235319866038"
LOCATION = "asia-southeast1"

logger = logging.getLogger(__name__)

vertexai.init(project=PROJECT_ID, location=LOCATION)


def load_image_from_url(image_url: str) -> Image:
    response = requests.get(image_url)
    image_bytes = response.content
    return Image.from_bytes(image_bytes)


def chat(
    model_name: str,
    contents: List[chatContent],
    systemPrompt: str = "",
    memory: str = "",
    temperature: float = 0.8,
    mcp: bool = False,
    web_search: bool = False,
    loop: asyncio.AbstractEventLoop = None,
):
    model = GenerativeModel(model_name)
    history = []
    if systemPrompt:
        history.append(Content(parts=[Part.from_text(systemPrompt)], role="user"))
        history.append(Content(parts=[Part.from_text("好的")], role="model"))

    role_sequence = "model"
    for r in contents:
        # 保持 user->model 的顺序
        if r.role == role_sequence:
            continue
        role_sequence = r.role
        parts = []
        text_part = Part.from_text(r.content)
        parts.append(text_part)
        # parts.append(
        #     Part.from_uri(
        #         uri="gs://ai-20240418/chat/202404/937c0b211372061392c077156c188f84.mp4",
        #         mime_type="video/mp4",
        #     )
        # )
        if r.files:
            for f in r.files:
                if f.type == "image":
                    image_part = Part.from_image(load_image_from_url(f.url))
                    parts.append(image_part)
                elif f.type == "video":
                    video_part = Part.from_uri(uri=f.gs_uri, mime_type=f.mimetype)
                    parts.append(video_part)
        content = Content(parts=parts, role=r.role)
        history.append(content)

    # 要发送的消息
    content = history.pop()
    ChatSession = model.start_chat(history=history)
    response = ChatSession.send_message(content=content, stream=True)
    for chunk in response:
        logger.debug("get chunk: %s" % json.dumps(chunk.to_dict()))
        yield chunk.text

    if "usage_metadata" in chunk.to_dict():
        yield (
            {
                "input_tokens": chunk.usage_metadata.prompt_token_count,
                "output_tokens": chunk.usage_metadata.candidates_token_count,
            }
        )
