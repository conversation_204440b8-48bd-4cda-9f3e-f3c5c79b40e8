from datetime import datetime
from enum import StrEnum
from typing import Annotated, Self, Literal

from pydantic import BaseModel, Field, HttpUrl

from models.audio_synthesis import Status


class PretrainedToneCategory(BaseModel):
    id: Annotated[str, Field(description="分类标识")]
    name: Annotated[str, <PERSON>(description="分类名称")]
    children: Annotated[list[Self] | None, Field(default=None, description="子分类")]


class ToneType(StrEnum):
    CUSTOM = "custom"
    PRETRAINED = "pretrained"


class ModelFunction(StrEnum):
    FAST_CLONE = "fast_clone"
    """GPT-SoVITS 极速复刻 + CosyVoice 3S 极速复刻"""

    COSY_PRETRAINED = "cosy_pretrained"
    """CosyVoice 预训练音色"""

    CHAT_TTS = "chat_tts"
    """Chat TTS 语音合成"""

    GPT_SOVITS_TTS = "gpt_sovits_tts"
    """GPT-SoVITS 语音合成"""

    VOLCENGINE = "volcengine"
    """火山语音"""


# 预训练模型的模型名称和功能名称，名称用于逻辑判断，改动前请注意
pretrained_model_function_desc: dict[ModelFunction, tuple[str, str]] = {
    ModelFunction.COSY_PRETRAINED: ('CosyVoice', '预训练音色'),
    ModelFunction.CHAT_TTS: ('Chat TTS', '语音合成'),
    ModelFunction.GPT_SOVITS_TTS: ('GPT-SoVITS', '语音合成'),
    ModelFunction.VOLCENGINE: ('Volcengine', '语音合成'),
}

# 极速复刻模型的名称和功能名称，名称用于逻辑判断，改动前请注意
fast_clone_model_function_desc: list[tuple[str, str]] = [
    ("GPT-SoVITS", "极速复刻"),
    ("CosyVoice", "极速复刻"),
]


class Tone(BaseModel):
    tone_type: Annotated[ToneType, Field(description="音色类型")]
    tone_id: Annotated[int | str, Field(description="音色 ID，如果 tone_type 是 CUSTOM，则填入音色库 ID，如果是 PRETRAINED，就是音色的名称")]
    tone_name: Annotated[str, Field(description="音色名称")]
    description: Annotated[str, Field(description="音色描述")]
    prompt_text: Annotated[str, Field(description="音色对应文本")]
    model_function: Annotated[ModelFunction, Field(description="模型功能")]
    is_favorite: Annotated[bool, Field(description="是否收藏")]
    url: Annotated[HttpUrl, Field(description="音色预览 URL")]
    gender: Annotated[Literal[0, 1] | None, Field(description='性别')]
    lang: Annotated[str | None, Field(description='语言')]


class ModifyFavoriteStatusReq(BaseModel):
    tone_type: Annotated[ToneType, Field(description="音色类型")]
    is_favorite: Annotated[bool, Field(description="是否收藏")]


class ParamSeg(BaseModel):
    tone_type: Annotated[ToneType, Field(description="音色类型")]
    tone_id: Annotated[int | str, Field(description="音色 ID，如果 tone_type 是 CUSTOM，则填入音色库 ID，如果是 PRETRAINED，就是音色的名称")]
    model_function: Annotated[ModelFunction, Field(description="模型功能")]
    text: Annotated[str, Field(description="待合成文本", min_length=1)]
    speed: Annotated[float, Field(description="语速", ge=0.5, le=2.0, multiple_of=0.1)] = 1
    pitch: Annotated[int, Field(description="声调", ge=-12, le=12, multiple_of=1)] = 0


class CreateTaskReq(BaseModel):
    segs: Annotated[list[ParamSeg], Field(default_factory=list, min_length=1)]


class CreateTaskRes(BaseModel):
    task_ids: Annotated[list[int], Field(default_factory=list)]


class ParamSegOut(ParamSeg):
    tone_name: Annotated[str, Field(description="音色名称")]
    model_name: Annotated[str, Field(description="模型名称")]
    function_name: Annotated[str, Field(description="模型功能名称")]


class ParamsOut(BaseModel):
    segs: Annotated[list[ParamSegOut], Field(default_factory=list)]


class TaskItem(BaseModel):
    id: Annotated[int, Field(description="任务标识")]
    params: Annotated[ParamsOut, Field(description="生成音频的参数")]
    url: Annotated[HttpUrl | Literal[''], Field(description="生成的音频 URL")]
    duration: Annotated[int, Field(description="生成的音频时长")]
    create_time: Annotated[datetime, Field(description="生成时间")]
    status: Annotated[Status, Field(description="任务状态")]

