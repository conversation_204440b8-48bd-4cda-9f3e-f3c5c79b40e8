<script setup lang="ts">
import { defineEmits, ref, watch } from 'vue';

const props = defineProps<{
  models: Array<{ label: string; value: string; desc: string }>;
  title: string;
  model?: string;
  isMobile?: boolean;
}>();

// 定义 emit
const emit = defineEmits<{
  (event: 'update:model', value: string): void;
  (event: 'chatAdded'): void;
  (event: 'openSidebar'): void;
}>();

// 默认模型
const selectedValue = ref(props.model || props.models[1]?.value);

// 监听 selectedValue 的变化并向父组件传递
watch(selectedValue, newValue => {
  emit('update:model', newValue);
});

// 监听 props.model 的变化并更新 selectedValue
watch(
  () => props.model,
  newModel => {
    if (newModel && newModel !== selectedValue.value) {
      selectedValue.value = newModel;
    }
  }
);

// 监听 props.models 的变化并更新 selectedValue
watch(
  () => props.models,
  newModels => {
    if (!props.model) {
      const newValue = newModels[1]?.value;
      selectedValue.value = newValue;
      emit('update:model', newValue);
    }
  },
  { immediate: true }
);

// 处理用户选择模型
function handleModelSelect(value: string) {
  selectedValue.value = value;
}

function emitAdd() {
  emit('chatAdded');
}

function openSidebar() {
  emit('openSidebar');
}
</script>

<template>
  <NFlex class="w-full px-4" align="center">
    <NText v-if="isMobile" class="text-2xl" @click="openSidebar">
      <SvgIcon icon="ic:outline-menu" />
    </NText>
    <NFlex class="flex-grow" :justify="isMobile ? 'center' : 'start'" align="center">
      <NSelect
        v-model:value="selectedValue"
        class="mx-2 w-38"
        :options="props.models"
        label-field="label"
        value-field="value"
        @update:value="handleModelSelect"
      />
      <NText v-if="!isMobile">{{ title }}</NText>
    </NFlex>
    <NText v-if="isMobile" class="text-2xl" @click="emitAdd">
      <SvgIcon icon="ci:message-plus-alt" />
    </NText>
  </NFlex>
</template>

<style scoped></style>
