<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { NButton, NFlex, NSlider, useMessage } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useThemeStore } from '@/store/modules/theme';

interface Props {
  audioUrl: string;
  audioTitle: string;
  autoPlay?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'ended'): void;
  (e: 'previousTrack'): void;
  (e: 'nextTrack'): void;
  (e: 'playStatusChange', isPlaying: boolean): void;
}>();

const themeStore = useThemeStore();
const message = useMessage();

// 主题相关计算属性
const backgroundColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(250, 250, 252, 1)' : 'rgba(24, 24, 28, 1)';
});

const borderColor = computed(() => {
  return !themeStore.darkMode ? 'rgb(239, 239, 245)' : 'rgba(255, 255, 255, 0.09)';
});

const textColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(31, 34, 37, 1)' : 'rgba(255, 255, 255, 0.9)';
});

// 播放状态相关
const audioRef = ref<HTMLAudioElement | null>(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const volume = ref(50);
const progress = ref(0);
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');

// 格式化时间为 mm:ss 格式
const formatTime = (time: number): string => {
  if (Number.isNaN(time) || time < 0) return '00:00';
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const formattedCurrentTime = computed(() => formatTime(currentTime.value));
const formattedDuration = computed(() => formatTime(duration.value));

// 音频控制方法
const togglePlay = () => {
  if (!audioRef.value || hasError.value || !props.audioUrl) return;

  if (isPlaying.value) {
    audioRef.value.pause();
  } else {
    const playPromise = audioRef.value.play();
    if (playPromise !== undefined) {
      isLoading.value = true;
      playPromise
        .then(() => {
          isPlaying.value = true;
          isLoading.value = false;
          emit('playStatusChange', true);
        })
        .catch(error => {
          console.error('播放失败:', error);
          isPlaying.value = false;
          isLoading.value = false;
          hasError.value = true;
          errorMessage.value = '播放失败，请重试';
          message.error('播放失败，请重试');
          emit('playStatusChange', false);
        });
    }
  }
};

// 切换到上一首
const handlePrevious = () => {
  if (hasError.value || !props.audioUrl) return;
  emit('previousTrack');
};

// 切换到下一首
const handleNext = () => {
  if (hasError.value || !props.audioUrl) return;
  emit('nextTrack');
};

// 进度条拖动控制
const handleProgressChange = (value: number) => {
  if (!audioRef.value || hasError.value || !props.audioUrl) return;
  const newTime = (value / 100) * duration.value;
  audioRef.value.currentTime = newTime;
  currentTime.value = newTime;
};

// 音量控制
const handleVolumeChange = (value: number) => {
  if (!audioRef.value || !props.audioUrl) return;
  volume.value = value;
  audioRef.value.volume = value / 100;
};

// 音频事件处理
const handleTimeUpdate = () => {
  if (!audioRef.value || !props.audioUrl) return;
  currentTime.value = audioRef.value.currentTime;
  progress.value = (currentTime.value / duration.value) * 100 || 0;
};

const handleLoadedMetadata = () => {
  if (!audioRef.value || !props.audioUrl) return;
  duration.value = audioRef.value.duration;
  audioRef.value.volume = volume.value / 100;
  hasError.value = false;
  errorMessage.value = '';
};

const handleEnded = () => {
  if (!props.audioUrl) return;
  isPlaying.value = false;
  emit('playStatusChange', false);
  emit('ended');
};

const handleError = (event: Event) => {
  if (!props.audioUrl) return;
  console.error('音频加载错误:', event);
  isPlaying.value = false;
  isLoading.value = false;
  hasError.value = true;
  errorMessage.value = '音频加载失败';
};

// 存储音量设置
const saveVolumeSettings = () => {
  try {
    localStorage.setItem('musicPlayerVolume', volume.value.toString());
  } catch (error) {
    console.error('保存音量设置失败:', error);
  }
};

// 加载音量设置
const loadVolumeSettings = () => {
  try {
    const savedVolume = localStorage.getItem('musicPlayerVolume');
    if (savedVolume) {
      volume.value = Number.parseInt(savedVolume, 10);
      if (audioRef.value) {
        audioRef.value.volume = volume.value / 100;
      }
    }
  } catch (error) {
    console.error('加载音量设置失败:', error);
  }
};

// 监听 audioUrl 变化，重置播放
watch(
  () => props.audioUrl,
  newUrl => {
    // 如果URL为空，直接返回，不执行其他逻辑
    if (!newUrl || newUrl === '') {
      isPlaying.value = false;
      currentTime.value = 0;
      duration.value = 0;
      progress.value = 0;
      return;
    }

    // 重置状态
    currentTime.value = 0;
    progress.value = 0;
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    // 如果音频元素已经存在，尝试加载和播放
    if (audioRef.value) {
      // 加载新音频
      audioRef.value.load();

      // 只有在autoPlay为true时才自动播放
      if (props.autoPlay) {
        // 延迟一点点再播放，避免加载请求冲突
        setTimeout(() => {
          if (audioRef.value) {
            const playPromise = audioRef.value.play();
            if (playPromise !== undefined) {
              playPromise
                .then(() => {
                  isPlaying.value = true;
                  isLoading.value = false;
                })
                .catch(error => {
                  console.error('自动播放失败:', error);
                  isPlaying.value = false;
                  isLoading.value = false;
                  // 不要立即设置错误状态，因为可能是由于浏览器自动播放策略导致的
                });
            }
          }
        }, 100);
      }
    }
  },
  { immediate: true }
);

// 组件挂载和卸载时的处理
onMounted(() => {
  loadVolumeSettings();
});

onBeforeUnmount(() => {
  if (isPlaying.value && audioRef.value) {
    audioRef.value.pause();
  }
  saveVolumeSettings();
});
</script>

<template>
  <div class="player-container mt-4 w-full">
    <!-- 音频元素 -->
    <audio
      ref="audioRef"
      :src="audioUrl"
      @timeupdate="handleTimeUpdate"
      @loadedmetadata="handleLoadedMetadata"
      @ended="handleEnded"
      @play="
        isPlaying = true;
        emit('playStatusChange', true);
      "
      @pause="
        isPlaying = false;
        emit('playStatusChange', false);
      "
      @error="handleError"
      @waiting="isLoading = true"
      @canplay="isLoading = false"
    ></audio>

    <!-- 播放器标题 -->
    <div v-if="audioTitle" class="audio-title mb-2">
      <NText>{{ audioTitle }}</NText>
    </div>
    <div v-else class="audio-title mb-2">
      <NText class="text-placeholder">请选择歌曲播放</NText>
    </div>

    <!-- 模拟进度条 -->
    <NSlider
      class="progress-slider mb-2"
      :value="progress"
      :step="0.1"
      :min="0"
      :max="100"
      :disabled="hasError || !audioUrl"
      @update:value="handleProgressChange"
    />

    <NFlex justify="space-between" align="center" class="player-controls">
      <span class="time-display">{{ formattedCurrentTime }}</span>

      <NFlex align="center" justify="center" class="control-buttons">
        <NButton quaternary circle class="control-btn" :disabled="hasError" @click="handlePrevious">
          <SvgIcon icon="icon-park-outline:go-start" />
        </NButton>

        <NButton quaternary circle class="play-btn mx-2" :disabled="hasError || !audioUrl" @click="togglePlay">
          <SvgIcon v-if="isLoading" icon="line-md:loading-twotone-loop" />
          <SvgIcon v-else :icon="isPlaying ? 'icon-park-outline:pause' : 'icon-park-outline:play'" />
        </NButton>

        <NButton quaternary circle class="control-btn" :disabled="hasError" @click="handleNext">
          <SvgIcon icon="icon-park-outline:go-end" />
        </NButton>
      </NFlex>

      <NFlex align="center">
        <span class="time-display ml-2">{{ formattedDuration }}</span>
      </NFlex>

      <NFlex align="center">
        <NButton quaternary circle class="volume-btn">
          <SvgIcon :icon="volume === 0 ? 'icon-park-outline:mute' : 'ant-design:sound-filled'" />
        </NButton>
        <NSlider
          class="volume-slider w-[80px]"
          :value="volume"
          :step="1"
          :min="0"
          :max="100"
          @update:value="handleVolumeChange"
        />
      </NFlex>
    </NFlex>

    <!-- 错误信息 -->
    <div v-if="hasError" class="error-message mt-2">
      <NText type="error">{{ errorMessage }}</NText>
    </div>
  </div>
</template>

<style scoped lang="scss">
.player-container {
  border-radius: 8px;
  padding: 12px;
  background-color: v-bind('backgroundColor');
  border: 1px solid v-bind('borderColor');
}

.audio-title {
  font-weight: 500;
  margin-bottom: 8px;
  padding: 0 8px;
}

.text-placeholder {
  opacity: 0.5;
  font-style: italic;
}

.progress-slider {
  margin-bottom: 12px;
}

.player-controls {
  padding: 0 8px;
}

.control-btn {
  font-size: 18px;
}

.play-btn {
  font-size: 32px;
}

.volume-btn {
  font-size: 18px;
}

.time-display {
  font-size: 12px;
  color: v-bind('textColor');
  opacity: 0.8;
}

.time-display.ml-2 {
  margin-left: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.error-message {
  font-size: 12px;
  text-align: center;
  padding: 4px 0;
}
</style>
