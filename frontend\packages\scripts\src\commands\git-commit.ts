import path from 'node:path';
import { readFileSync } from 'node:fs';
import { prompt } from 'enquirer';
import { bgRed, green, red, yellow } from 'kolorist';
import { execCommand } from '../shared';
import type { CliOption } from '../types';

interface PromptObject {
  types: string;
  scopes: string;
  description: string;
}

/**
 * Git commit with Conventional Commits standard
 *
 * @param gitCommitTypes
 * @param gitCommitScopes
 */
export async function gitCommit(
  gitCommitTypes: CliOption['gitCommitTypes'],
  gitCommitScopes: CliOption['gitCommitScopes']
) {
  const typesChoices = gitCommitTypes.map(([value, msg]) => {
    const nameWithSuffix = `${value}:`;

    const message = `${nameWithSuffix.padEnd(12)}${msg}`;

    return {
      name: value,
      message
    };
  });

  const scopesChoices = gitCommitScopes.map(([value, msg]) => ({
    name: value,
    message: `${value.padEnd(30)} (${msg})`
  }));

  const result = await prompt<PromptObject>([
    {
      name: 'types',
      type: 'select',
      message: 'Please select a type',
      choices: typesChoices
    },
    {
      name: 'scopes',
      type: 'select',
      message: 'Please select a scope',
      choices: scopesChoices
    },
    {
      name: 'description',
      type: 'text',
      message: `Please enter a description (add prefix ${yellow('!')} to indicate breaking change)`
    }
  ]);

  const breaking = result.description.startsWith('!') ? '!' : '';

  const description = result.description.replace(/^!/, '').trim();

  const commitMsg = `${result.types}(${result.scopes})${breaking}: ${description}`;

  await execCommand('git', ['commit', '-m', commitMsg], { stdio: 'inherit' });
}

/** Git commit message verify */
export async function gitCommitVerify() {
  // 获取 Git 根目录
  const gitPath = await execCommand('git', ['rev-parse', '--show-toplevel']);

  // 获取提交消息路径
  const gitMsgPath = path.join(gitPath, '.git', 'COMMIT_EDITMSG');

  // 读取提交消息
  const rawCommitMsg = readFileSync(gitMsgPath, 'utf8').trim();

  // 获取提交消息的第一行（提交标题）
  const commitHeader = rawCommitMsg.split('\n')[0];

  // 正则表达式匹配 Conventional Commits 格式，支持中文范围和描述
  const REG_EXP = /^(?<type>[a-z]+)(?:\((?<scope>[a-zA-Z0-9_\u4E00-\u9FA5-]+)\))?(?<breaking>!)?: (?<description>.+)$/i;

  // 格式化提交消息头
  const formattedCommitHeader = formatCommitMessage(commitHeader);

  // 验证提交消息头是否符合格式
  if (!REG_EXP.test(formattedCommitHeader)) {
    throw new Error(
      `${bgRed(' 错误 ')} ${red('提交信息必须符合 Conventional Commits 标准！')}\n\n${yellow(
        '您的提交信息为：'
      )} ${red(formattedCommitHeader)}\n\n${green(
        '正确格式为：<type>(<scope>): <description>\n例如：feat(界面): 添加按钮悬停效果'
      )}\n\n${yellow(
        '建议：如果无法确定类型和范围，可以使用默认类型 `misc`，例如：misc: 智能抠图资源调用切换到初心'
      )}\n\n${green(
        '建议使用命令 `pnpm commit` 来生成符合 Conventional Commits 标准的提交信息。\n了解更多，请访问：https://conventionalcommits.org'
      )}`
    );
  }

  console.log(green('✅ 提交信息符合 Conventional Commits 标准！'));
}

/**
 * 格式化提交消息：自动补充默认类型（如无）
 *
 * @param message 原始提交消息
 * @returns 格式化后的提交消息
 */
function formatCommitMessage(message: string): string {
  const REG_EXP = /^(?<type>[a-z]+)(?:\((?<scope>[a-zA-Z0-9_\u4E00-\u9FA5-]+)\))?(?<breaking>!)?: (?<description>.+)$/i;

  if (!REG_EXP.test(message)) {
    // 如果未匹配，自动补充默认类型
    return `misc: ${message}`;
  }

  return message;
}
