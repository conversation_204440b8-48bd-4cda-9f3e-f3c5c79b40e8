<script lang="ts" setup>
import { ref, watch } from 'vue';
interface Props {
  src: string;
}
const props = defineProps<Props>();
const audio = ref<HTMLAudioElement | null>(null);
const isBase64 = ref(false);
const isPlaying = ref(false);
const canplay = ref(false);
const currentTime = ref(0);
const duration = ref(0);

const togglePlay = () => {
  if (!audio.value) return;
  if (isPlaying.value) {
    audio.value.pause();
  } else {
    audio.value.play();
  }
  isPlaying.value = !isPlaying.value;
};

const updateTime = () => {
  if (audio.value) {
    currentTime.value = audio.value.currentTime;
  }
};

const updateDuration = () => {
  if (audio.value && audio.value.readyState >= HTMLMediaElement.HAVE_CURRENT_DATA) {
    duration.value = audio.value.duration;
  }
};
// :on-update:value="changeProgress"
// const changeProgress = (value: number) => {
//   console.log('changeProgress', value);
//   if (audio.value) {
//     audio.value.currentTime = value;
//     currentTime.value = value;
//   }
// };

const formatTime = (time: number) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60)
    .toString()
    .padStart(2, '0');
  return `${minutes}:${seconds}`;
};

const downloadAudio = () => {
  const link = document.createElement('a');
  link.href = props.src;
  link.download = 'audio.mp3';
  link.click();
};
const ended = () => {
  isPlaying.value = false;
};
const handelCanplay = () => {
  console.log('handelCanplay');
  canplay.value = true;
  isPlaying.value = false;
};
watch(
  () => props.src,
  src => {
    if (src.startsWith('data:audio')) {
      isBase64.value = true;
    }
    if (audio.value) {
      audio.value.load(); // 当音频源改变时重新加载音频
    }
  }
);
</script>

<template>
  <div class="audio-player bg w-auto bg-gray-100 px-1 py-2 dark:bg-[#303033]">
    <audio
      ref="audio"
      @timeupdate="updateTime"
      @loadedmetadata="updateDuration"
      @ended="ended"
      @canplay="handelCanplay"
    >
      <source :src="props.src" />
      Your browser does not support the audio element.
    </audio>
    <div class="w-full flex flex-row items-center">
      <NButton text class="mr-2" :disabled="!canplay" @click="togglePlay">
        <SvgIcon
          :icon="isPlaying ? 'fluent:pause-32-filled' : 'fluent:play-32-filled'"
          class="icon h-6 w-6 text-primary-color"
        ></SvgIcon>
      </NButton>
      <NEllipsis class="time-container min-w-20">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</NEllipsis>
      <NSlider v-model:value="currentTime" :step="10" :min="0" :max="duration" />
      <NButton text class="ml-2" :disabled="!canplay" @click="downloadAudio">
        <SvgIcon icon="line-md:download-loop" class="h-8 w-8 text-primary-color"></SvgIcon>
      </NButton>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audio-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: var(--n-border-radius);
}

.controls {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.time-container {
  color: var(--n-text-color);
}
// :deep(.n-slider-handles) {
// .n-slider-handle-wrapper {
//   .n-slider-handle {
//     height: 6px;
//     width: 6px;
//   }
// }
// display: none;
// }
</style>
