<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import { useCreditStore } from '@/store/modules/credit';
import { AiCapacity } from '@/utils/AiCapacity';
import { fetchGetUserCreditLog } from '@/service/api/user';

const capacityMap = new Map<string, string>([
  [AiCapacity.IMAGE_GENERATION, '图像生成'],
  [AiCapacity.VIDEO_GENERATION, '视频生成']
]);

const formatCapacity = (value: string) => {
  return capacityMap.get(value) || value;
};

const creditStore = useCreditStore();
const { creditInfo } = storeToRefs(creditStore);

// 控制模态框显示
const showModal = ref(false);
// 加载状态
const loading = ref(false);
// 分页参数
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);

// 积分记录数据
const creditDetails = ref<any[]>([]);

// 加载更多数据
async function loadMore() {
  if (!hasMore.value || loading.value) return;

  loading.value = true;
  try {
    const { data } = await fetchGetUserCreditLog(page.value, pageSize.value);
    if (data?.records?.length) {
      if (page.value === 1) {
        creditDetails.value = data.records;
      } else {
        creditDetails.value.push(...data.records);
      }

      // 如果返回的数据数量小于pageSize，说明没有更多数据了
      if (data.records.length < pageSize.value) {
        hasMore.value = false;
      } else {
        page.value++;
      }
    } else {
      hasMore.value = false;
    }
  } finally {
    loading.value = false;
  }
}

// 初始化数据
function handleClick() {
  showModal.value = true;
  page.value = 1;
  creditDetails.value = [];
  hasMore.value = true;
  loadMore();
}

// 格式化积分显示
function formatCredit(credit: number) {
  return credit >= 0 ? `+${credit}` : `${credit}`;
}

// 滚动加载处理
const onScroll = (e: { target: any }) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target;
  if (scrollHeight - scrollTop - clientHeight < 50) {
    loadMore();
  }
};
</script>

<template>
  <ButtonIcon :tooltip-content="$t('common.credit')" @click="handleClick">
    <div class="flex items-center justify-center gap-2">
      <SvgIcon icon="octicon:feed-star-16" class="text-18px" />
      <NText class="text-16px">{{ creditInfo.total_credit }}</NText>
    </div>
  </ButtonIcon>

  <NModal v-model:show="showModal" preset="card" title="积分记录" class="w-550px">
    <NScrollbar class="max-h-55vh" @scroll="onScroll">
      <div class="credit-list">
        <div v-for="item in creditDetails" :key="item.createtime" class="credit-item">
          <div class="credit-content">
            <div class="credit-info">
              <div v-if="item.matter" class="credit-title">
                <span>{{ item.matter }}</span>
                <span v-if="item.capacity">&nbsp;-&nbsp;{{ formatCapacity(item.capacity) }}</span>
              </div>
              <div v-else class="credit-title">-</div>
              <div class="time">{{ item.createtime.replace('T', ' ') }}</div>
            </div>
            <div class="credit-amount" :class="item.credit >= 0 ? 'increase' : 'decrease'">
              {{ formatCredit(item.credit) }}
            </div>
          </div>
          <NDivider />
        </div>
        <NEmpty v-if="creditDetails.length === 0" description="暂无积分记录" />
        <NFlex v-if="loading" justify="center">
          <NSpin />
        </NFlex>
        <div v-if="!hasMore && creditDetails.length > 0" class="text-center">
          <NText class="text-xs">没有更多数据了 ~</NText>
        </div>
      </div>
    </NScrollbar>
  </NModal>
</template>

<style scoped>
.credit-list {
  padding: 16px;
}

.credit-item {
  margin-bottom: 16px;
}

.credit-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.credit-info {
  flex: 1;
}

.credit-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.time {
  font-size: 14px;
  color: #999;
}

.credit-amount {
  font-size: 20px;
  font-weight: 600;
}

.increase {
  color: #2dc373;
}

.decrease {
  color: #7389e3;
}

:deep(.n-divider) {
  margin: 16px 0;
  color: #999;
}
</style>
