<script setup lang="ts">
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { computed, reactive, watch } from 'vue';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { convertBlobToBase64 } from '@/utils/image';
import { Capacity, capacityOp } from '@/utils/capacity';
import { fetchCreateModel, fetchUpdateModel } from '@/service/api';
import type { FlatResponseData } from '~/packages/axios/src';
import publishersMap from './publishersMap';

interface Props {
  operateType: NaiveUI.TableOperateType;
  rowData?: Api.SystemManage.Model | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

function closeDrawer() {
  visible.value = false;
}

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.manage.model.addModel'),
    edit: $t('page.manage.model.editModel')
  };
  return titles[props.operateType];
});

const { formRef, validate, restoreValidation } = useNaiveForm();

type Model = Omit<Api.SystemManage.Model, 'id' | 'capacity' | 'icon'> & { capacity: string[]; icon: UploadFileInfo[] };

const createDefaultModel = () => ({
  name: '',
  label: '',
  icon: [],
  description: '',
  publishers: '',
  capacity: [],
  status: false
});

const convertImgToB64 = async ({ file }: UploadCustomRequestOptions) => {
  if (!file.file) return;
  model.icon[0].url = await convertBlobToBase64(file.file);
};

const model = reactive<Model>(createDefaultModel());

function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData, {
      capacity: capacityOp.toText(props.rowData.capacity),
      icon: [
        {
          id: props.rowData.name,
          name: props.rowData.name,
          status: 'finished',
          url: props.rowData.icon
        }
      ]
    });
  } else {
    Object.assign(model, createDefaultModel());
  }
}

const rules: Record<keyof Model, App.Global.FormRule> = {
  name: {
    required: true
  },
  label: {
    required: false
  },
  icon: {
    required: true
  },
  description: {
    required: false
  },
  publishers: {
    required: true
  },
  capacity: {
    required: true
  },
  status: {
    required: true
  }
};

async function handleSubmit() {
  await validate();
  const payload = {
    ...model,
    capacity: capacityOp.toNumber(model.capacity.map(item => Capacity[item as keyof typeof Capacity])),
    icon: model.icon[0]!.url!
  };
  let res: FlatResponseData;
  switch (props.operateType) {
    case 'add':
      res = await fetchCreateModel(payload);
      break;
    case 'edit':
      if (!props.rowData?.id) return;
      res = await fetchUpdateModel(props.rowData.id, payload);
      break;
  }
  if (!res.error) {
    closeDrawer();
    emit('submitted');
    return true;
  }
  return false;
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const publishersOptions = Object.entries(publishersMap).map(([key, value]) => ({
  label: value,
  value: key
}));

const capacityOptions = Object.keys(Capacity)
  .filter(key => isNaN(Number(key)))
  .map(key => ({
    label: key,
    value: key
  }));
</script>

<template>
  <NModal
    v-model:show="visible"
    display-directive="show"
    :width="360"
    :title="title"
    preset="dialog"
    :native-scrollbar="false"
    closable
    :positive-text="$t('common.confirm')"
    :negative-text="$t('common.cancel')"
    class="min-w-1000px"
    @positive-click="handleSubmit"
    @negative-click="closeDrawer"
  >
    <NScrollbar class="max-h-500px pr-5" :size="5" :x-scrollable="false">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-align="right" label-width="80">
        <NFormItem :label="$t('page.manage.model.name')" path="name">
          <NInput v-model:value="model.name" :placeholder="$t('page.manage.model.form.name')" />
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.label')" path="label">
          <NInput v-model:value="model.label" :placeholder="$t('page.manage.model.form.label')" />
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.icon')" path="icon">
          <NUpload
            v-model:file-list="model.icon"
            :custom-request="convertImgToB64"
            :max="1"
            list-type="image-card"
            accept=".png,.jpg,.jpeg"
          >
            <SvgIcon icon="material-symbols:add-rounded" />
          </NUpload>
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.description')" path="description">
          <NInput
            v-model:value="model.description"
            type="textarea"
            :placeholder="$t('page.manage.model.form.description')"
          />
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.publishers')" path="publishers">
          <NSelect v-model:value="model.publishers" :options="publishersOptions" />
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.capacity')" path="capacity">
          <NSelect v-model:value="model.capacity" multiple :options="capacityOptions" />
        </NFormItem>

        <NFormItem :label="$t('page.manage.model.status')" path="status">
          <NSwitch v-model:value="model.status">
            <template #checked>ON</template>
            <template #unchecked>OFF</template>
          </NSwitch>
        </NFormItem>
      </NForm>
    </NScrollbar>
  </NModal>
</template>
