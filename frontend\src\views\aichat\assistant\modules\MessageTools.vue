<script setup lang="ts">
import {NTag} from 'naive-ui';
import {computed} from 'vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import {Capacity, capacityOp} from '@/utils/capacity';

const props = defineProps<{
  isMobile?: boolean;
  models: Array<{ label: string; value: string; desc: string; icon: string; capacity: number }>;
  currentModel?: string;
  isLoading?: boolean;
  message?: string;
}>();

// 检测是否包含工具调用
const containsToolCall = computed(() => {
  const checklist: string[] = ['<aichat-img-generating>'];
  if (!props.message) {
    return false;
  }
  return checklist.some(item => props.message!.includes(item));
});

const getCanSwitchModel = (modelName: string) => {
  if (!containsToolCall.value) {
    return true;
  }
  const modelObj = props.models.find(item => item.value === modelName);
  if (!modelObj) {
    return false;
  }
  return capacityOp.has(modelObj.capacity, Capacity.TOOL_CALL);
};

const modelOptions = computed(() =>
  props.models.map(item => ({
    label: item.label,
    value: item.value,
    icon: item.icon,
    disabled: !getCanSwitchModel(item.value)
  }))
);

const emit = defineEmits<{
  (e: 'regenerate'): void;
  (e: 'modelChange', selectedModel: string): void;
  (e: 'copyText'): void;
}>();
</script>

<template>
  <NFlex align="center" class="mt-2">
    <NSelect
      v-if="isMobile"
      size="small"
      :options="modelOptions"
      :default-value="currentModel"
      class="w-37"
      @update:value="(value: string) => emit('modelChange', value)"
    />
    <div v-else>
      <NTag
        v-for="item in modelOptions"
        :key="item.value"
        :bordered="currentModel !== item.value"
        :type="currentModel === item.value ? 'info' : 'default'"
        size="small"
        class="mr-0.5"
        :class="[
          isLoading || item.disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer',
          currentModel === item.value ? 'current-model' : ''
        ]"
        @click="() => !isLoading && !item.disabled && emit('modelChange', item.value)"
      >
        {{ item.label }}
        <template #avatar>
          <NAvatar :src="item.icon" class="bg-white" />
        </template>
      </NTag>
    </div>
    <NText
      class="cursor-pointer text-lg"
      :class="{ 'opacity-60 cursor-not-allowed': isLoading }"
      @click="() => emit('copyText')"
    >
      <SvgIcon icon="ri:file-copy-2-line" class="text-sm" />
    </NText>
    <NText
      class="cursor-pointer text-lg"
      :class="{ 'opacity-60 cursor-not-allowed': isLoading }"
      @click="() => !isLoading && emit('regenerate')"
    >
      <SvgIcon icon="ri:restart-line" class="text-sm" />
    </NText>
  </NFlex>
</template>
