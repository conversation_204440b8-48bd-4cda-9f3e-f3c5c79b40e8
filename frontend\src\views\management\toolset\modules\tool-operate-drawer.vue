<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import type { UploadInst } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'GameOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: 'add' | 'edit';
  /** the edit row data */
  rowData?: any | null;
}

interface UploadFileInfo {
  id: string;
  name?: string;
  status?: 'pending' | 'uploading' | 'error' | 'finished' | 'removed';
  batchId?: string | null;
  file?: File | null;
  fullPath?: string | null;
  percentage?: number | null;
  thumbnailUrl?: string | null;
  type?: string | null;
  url?: string | null;
}

const props = defineProps<Props>();

type SubmitModel = {
  name: string;
  type: string;
  description?: string;
  main_function?: string;
  url?: string;
  recommendation_rating?: number;
  is_paid?: boolean;
  is_available_in_china?: boolean;
  origin_country?: string;
  image_url?: string;
  document?: string;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '新增游戏' : '编辑信息';
});

type Model = {
  name: string;
  type: string;
  description?: string;
  main_function?: string;
  url?: string;
  recommendation_rating?: number;
  is_paid?: boolean;
  is_available_in_china?: boolean;
  origin_country?: string;
  image_url?: string;
  document?: string;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    type: '',
    description: '',
    main_function: '',
    url: '',
    recommendation_rating: 0,
    is_paid: false,
    is_available_in_china: true,
    origin_country: '',
    image_url: '',
    document: ''
  };
}

type RuleKey = 'name' | 'type';

// const { patternRules } = useFormRules();

const rules: Record<RuleKey, any> = {
  name: defaultRequiredRule,
  type: defaultRequiredRule
};

const uploadRef = ref<UploadInst | null>(null);

function resetUpload() {
  if (uploadRef.value) {
    uploadRef.value.clear();
  }
}

function closeDrawer() {
  visible.value = false;
  resetUpload();
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  resetUpload();

  // console.log('Model after assigning default values:', props.rowData);

  if (props.operateType === 'edit' && props.rowData) {
    const rowData = { ...props.rowData };
    Object.assign(model, rowData);
    // console.log('Model after assigning rowData:', model);
  }
}

const imagePreview = ref<string | null>(null);

async function handleSubmit() {
  await validate();

  const submitModel = {
    ...model,
    image_url: imagePreview.value ? imagePreview.value : model.image_url
  };

  closeDrawer();
  emit('submitted', submitModel);
}

// 下拉框的选项
const type_options = [
  { label: '聊天', value: '聊天' },
  { label: '绘图', value: '绘图' },
  { label: '写作工具', value: '写作工具' },
  { label: '办公工具', value: '办公工具' },
  { label: '设计工具', value: '设计工具' },
  { label: '视频', value: '视频' },
  { label: '其他', value: '其他' },
  { label: '编程工具', value: '编程工具' },
  { label: '音乐', value: '音乐' }
];

watch(visible, newVal => {
  // console.log('visible changed:', newVal);
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});

const disableUpload = ref(false);
const disableInput = ref(false);

watch(
  () => model.image_url,
  newVal => {
    if (newVal) {
      disableUpload.value = true;
      disableInput.value = false;
    } else {
      disableUpload.value = false;
    }
  }
);

function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => {
      if (e.target && e.target.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(file);
  });
}

async function handleImageUpload(fileList: UploadFileInfo[]) {
  if (!fileList || fileList.length === 0) return;

  const item = fileList[0];
  if (item.file) {
    try {
      const fullBase64 = await readFileAsDataURL(item.file);
      imagePreview.value = fullBase64;
      disableInput.value = true;
      // console.log('image_url changed to', model.image_url);
    } catch (error) {
      console.error('Failed to read file:', error);
    }
  }
}

function handleRemove() {
  model.image_url = '';
  imagePreview.value = '';
  disableInput.value = false;
  // console.log('image_url removed');
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="AI工具" path="name">
          <NInput v-model:value="model.name" placeholder="请输入工具名称" :disabled="props.operateType === 'edit'" />
        </NFormItem>
        <NFormItem label="工具类型" path="type">
          <NSelect v-model:value="model.type" :options="type_options" placeholder="请选择类型" />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput v-model:value="model.description" type="textarea" placeholder="请输入工具简介" rows="3" />
        </NFormItem>
        <NFormItem label="主要功能" path="main_function">
          <NInput v-model:value="model.main_function" type="textarea" placeholder="请输入工具主要功能" rows="5" />
        </NFormItem>
        <NFormItem label="使用地址" path="url">
          <NInput v-model:value="model.url" placeholder="请输入工具使用地址" />
        </NFormItem>
        <NFormItem label="推荐指数" path="recommendation_rating">
          <NRate v-model:value="model.recommendation_rating" clearable />
        </NFormItem>
        <NSpace class="mb-4">
          <NFormItem path="is_paid" class="flex items-center space-x-4">
            <label class="w-20">是否收费</label>
            <NCheckbox v-model:checked="model.is_paid"></NCheckbox>
          </NFormItem>
          <NFormItem path="is_available_in_china" class="flex items-center space-x-4">
            <label class="w-20">国内可用</label>
            <NCheckbox v-model:checked="model.is_available_in_china"></NCheckbox>
          </NFormItem>
        </NSpace>
        <NFormItem label="来源" path="origin_country">
          <NInput v-model:value="model.origin_country" placeholder="请输入工具来源" />
        </NFormItem>
        <NFormItem label="图片url地址" path="image_url">
          <NInput
            v-model:value="model.image_url"
            placeholder="请输入图片url地址"
            :disabled="disableInput"
            @update:value="
              value => {
                model.image_url = value;
                if (!value) {
                  imagePreview = '';
                  disableInput = false;
                }
              }
            "
          />
        </NFormItem>
        <NFormItem label="上传图片" path="image_url">
          <NUpload
            ref="uploadRef"
            list-type="image-card"
            accept="image/*"
            :max="1"
            :disabled="disableUpload"
            @update:file-list="handleImageUpload"
            @remove="handleRemove"
          />
        </NFormItem>
        <NFormItem label="工具使用说明" path="document">
          <NInput v-model:value="model.document"></NInput>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
