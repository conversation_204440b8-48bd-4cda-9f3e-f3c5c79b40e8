import logging
import json
import httpx
import re
from base64 import b64encode
from typing import Dict, Any
from urllib.parse import urlparse

from config import app_settings

logger = logging.getLogger(__name__)


# 环境变量
AI_SERVER = app_settings.ai_server


# 检测字符串是否为URL的辅助函数
def is_url(string: str) -> bool:
    """
    检测字符串是否为URL
    
    参数:
    - string: 要检测的字符串
    
    返回:
    - 如果是URL则返回True，否则返回False
    """
    try:
        # 使用正则表达式匹配URL
        url_pattern = re.compile(
            r'^(?:http|https)://'  # http:// 或 https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # 域名
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
            r'(?::\d+)?'  # 可选的端口
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        # 使用urlparse检查
        result = urlparse(string)
        # 修改：确保scheme必须是http或https，并且必须有netloc(域名部分)
        has_valid_scheme = result.scheme in ['http', 'https']
        has_netloc = bool(result.netloc)
        
        # 只有满足正则表达式验证或同时有有效scheme和netloc时才返回True
        return bool(url_pattern.match(string)) or (has_valid_scheme and has_netloc)
    except:
        return False


# URL转Base64的辅助函数
async def url_to_base64(url: str) -> str:
    """
    将URL指向的音频文件转换为Base64编码的字符串
    
    参数:
    - url: 音频文件的URL
    
    返回:
    - Base64编码的字符串，不包含前缀
    """
    try:
        # 确保URL有协议前缀
        if not url.startswith(('http://', 'https://')):
            # 默认添加https前缀
            url = 'https://' + url
            logger.info(f"URL缺少协议前缀，已添加https://前缀: {url}")
            
        logger.info(f"开始从URL获取并转换为base64: {url}")
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(url)
            response.raise_for_status()
            
            # 获取内容并转换为Base64
            content = response.content
            base64_encoded = b64encode(content).decode('utf-8')
            
            logger.info(f"URL成功转换为base64，长度: {len(base64_encoded)}")
            return base64_encoded
    except Exception as e:
        logger.error(f"URL转Base64出错: {e}", exc_info=True)
        raise ValueError(f"无法从URL获取音频数据: {str(e)}")


async def generate_music_title(text: str) -> str:
    """
    使用OpenAI模型生成歌曲标题
    
    参数:
    - text: 歌曲描述文本（提示词）
    
    返回:
    - 生成的歌曲标题
    """
    try:
        # 动态导入必要的模块
        from models.chat import chatContent
        chatServer = __import__("service.openai.chat", fromlist=["chat"])
        from app.audio.musciPrompt import generate_title_prompt
        
        # 准备提示语
        formatted_prompt = generate_title_prompt.format(text=text)
        
        # 准备对话内容
        contents = [
            chatContent(role="user", content=formatted_prompt, files=None)
        ]
        
        # 调用ChatGPT生成标题（使用gpt-4o-mini模型）
        title = ""
        generator = chatServer.chat(
            model_name="gpt-4o-mini",
            contents=contents,
            systemPrompt="你是一个专业的音乐标题生成专家，请根据描述生成简短有力的歌曲标题",
            temperature=0.7
        )
        
        for chunk in generator:
            if isinstance(chunk, dict) and "input_tokens" in chunk:
                # 这是最后一个chunk，包含token统计，跳过
                continue
            # 拼接返回内容
            title += chunk if chunk else ""
        
        # 清理标题（去除多余空格和换行符）
        title = title.strip()
        
        # 如果标题为空，提供默认值
        if not title:
            title = "未命名歌曲"
            
        # 限制标题长度
        if len(title) > 6:
            title = title[:6]
            
        logger.info(f"AI生成的歌曲标题: {title}")
        return title
        
    except Exception as e:
        logger.error(f"生成歌曲标题失败: {e}", exc_info=True)
        # 发生错误时返回默认标题
        return "未命名歌曲"


async def handle_music_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理音乐生成任务
    
    参数:
    - task_data: 任务数据，包含以下字段:
      - db_task_id: 数据库任务ID
      - taskid: 任务ID
      - username: 用户名
      - action: 任务动作
      - params: 任务参数
      - db: 数据库会话
    
    返回:
    - 处理结果字典，包含以下字段:
      - success: 处理是否成功
      - resource_url: 资源URL（如果成功）
      - result: 详细结果（如果成功）
      - error: 错误信息（如果失败）
    """
    # 解析任务数据
    db_task_id = task_data.get("db_task_id")
    taskid = task_data.get("taskid")
    username = task_data.get("username")
    action = task_data.get("action")
    params = task_data.get("params")
    db = task_data.get("db")
    
    try:
        logger.info(f"处理音乐任务: TaskID={taskid}, Action={action}")
        
        # 准备API调用参数
        api_endpoint = None
        payload = {}
        
        # 根据动作类型设置不同的API端点和参数
        if action == "GENERATE":
            api_endpoint = f"{AI_SERVER}/inspiremusic/generate"
            payload = {
                "text": params.get("text"),
                "model_name": params.get("model_name", "InspireMusic-1.5B"),
                "chorus": params.get("chorus", "intro"),
                "output_sample_rate": params.get("output_sample_rate", 48000),
                "max_generate_audio_seconds": params.get("max_generate_audio_seconds", 30.0),
            }
        elif action == "CONTINUE":
            api_endpoint = f"{AI_SERVER}/inspiremusic/continue"
            
            # 获取音频数据参数
            audio_data = params.get("audio_base64")
            
            # 检测音频数据是否为URL，如果是则转换为Base64
            if is_url(audio_data):
                try:
                    logger.info(f"检测到音频数据为URL，即将转换: {audio_data}")
                    audio_data = await url_to_base64(audio_data)
                    logger.info(f"URL成功转换为Base64")
                except Exception as url_err:
                    logger.error(f"URL转Base64出错: {url_err}", exc_info=True)
                    return {
                        "success": False,
                        "error": f"URL转换失败: {str(url_err)}"
                    }
            
            payload = {
                "text": params.get("text"),
                "audio_base64": audio_data,
                "model_name": params.get("model_name", "InspireMusic-1.5B"),
                "chorus": params.get("chorus", "intro"),
                "output_sample_rate": params.get("output_sample_rate", 48000),
                "max_generate_audio_seconds": params.get("max_generate_audio_seconds", 30.0),
            }
        else:
            return {
                "success": False,
                "error": f"不支持的任务动作: {action}"
            }
        
        # 调用AI服务
        logger.info(f"向 {api_endpoint} 发送请求 for task {taskid}")
        async with httpx.AsyncClient(timeout=300.0) as client:  # 5分钟超时
            response = await client.post(api_endpoint, json=payload)
            response.raise_for_status()  # 抛出4xx/5xx错误
            
            # 处理成功响应
            response_data = response.json()
            logger.info(f"任务 {taskid} 外部API调用成功: {response_data}")
            
            api_result_data = response_data.get("data")
            if not api_result_data or "url" not in api_result_data:
                raise ValueError("外部API响应格式无效，缺少 'data' 或 'url' 字段")
            
            # 将结果打包成JSON字符串
            result_json = {
                "url": api_result_data.get("url"),
                "type": api_result_data.get("type", "oss_url"),
                "duration": api_result_data.get("duration"),
                "sample_rate": api_result_data.get("sample_rate")
            }
            
            # 返回成功结果
            return {
                "success": True,
                "resource_url": api_result_data.get("url"),
                "result": result_json
            }
            
    except httpx.TimeoutException as timeout_err:
        logger.error(f"任务 {taskid} 调用外部API超时: {timeout_err}")
        logger.error(f"调用详情 - URL: {api_endpoint}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        
        return {
            "success": False,
            "error": f"调用外部API超时: {str(timeout_err)}"
        }
    except httpx.HTTPStatusError as http_err:
        logger.error(f"任务 {taskid} 调用外部API失败 (HTTP Status): {http_err.response.status_code} - {http_err.response.text}")
        logger.error(f"调用详情 - URL: {api_endpoint}, 状态码: {http_err.response.status_code}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        logger.error(f"响应头: {dict(http_err.response.headers)}")
        
        return {
            "success": False,
            "error": f"外部API返回错误状态 {http_err.response.status_code}: {http_err.response.text[:200]}"
        }
    except Exception as e:
        logger.error(f"处理任务 {taskid} 时发生内部错误: {e}", exc_info=True)
        if 'api_endpoint' in locals() and 'payload' in locals():
            logger.error(f"调用详情 - URL: {api_endpoint}, 参数: {json.dumps(payload, ensure_ascii=False)[:500]}")
        
        return {
            "success": False,
            "error": f"处理任务时发生内部错误: {str(e)}"
        } 