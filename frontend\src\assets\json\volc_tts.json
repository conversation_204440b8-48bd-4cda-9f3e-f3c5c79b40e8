{"meta_info": {"cluster": "volcano_tts_test"}, "voice_options": [{"name": "灿灿2.0", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "通用"}, {"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新", "多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV700_V2_streaming"}, "text": "刚刚还在想你怎么还不来找我聊天，你就来了，真是心有灵犀呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愉悦", "params": {"voice_type": "BV700_V2_streaming", "emotion": "pleased"}, "text": "太好啦，终于放假啦！你有想好去哪里玩吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "抱歉", "params": {"voice_type": "BV700_V2_streaming", "emotion": "sorry"}, "text": "抱歉，你的爱车还不支持此功能。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "嗔怪", "params": {"voice_type": "BV700_V2_streaming", "emotion": "annoyed"}, "text": "你这是什么态度吗！？我生气了！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "傲娇", "params": {"voice_type": "BV700_V2_streaming", "emotion": "tsundere"}, "text": "我可不是因为喜欢你才关心你的，只是不想看到你犯错罢了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "安慰鼓励", "params": {"voice_type": "BV700_V2_streaming", "emotion": "comfort"}, "text": "不管结果如何，只要努力过，就无憾于心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "绿茶", "params": {"voice_type": "BV700_V2_streaming", "emotion": "conniving"}, "text": "我真的没有别的想法，我只是单纯把他当哥哥而已。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "娇媚", "params": {"voice_type": "BV700_V2_streaming", "emotion": "charming"}, "text": "哥哥，不要害羞嘛，对我多一点温柔吧。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "情感电台", "params": {"voice_type": "BV700_V2_streaming", "emotion": "radio"}, "text": "人生路漫漫，愿你珍视每一段陪伴，感恩每一次遇见。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "撒娇", "params": {"voice_type": "BV700_V2_streaming", "emotion": "lovey-dovey"}, "text": "你不要嘲笑我啦，人家会害羞的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "瑜珈", "params": {"voice_type": "BV700_V2_streaming", "emotion": "yoga"}, "text": "闭上眼睛，将注意力集中在身体的每一个细微动作上。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "讲故事", "params": {"voice_type": "BV700_V2_streaming", "emotion": "storytelling"}, "text": "清晨，太阳慢慢升起，天空一点一点地亮了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice_type": "BV700_V2_streaming", "emotion": "happy"}, "text": "哈哈，阿宝，说好啦，今天要带我出去玩哦！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice_type": "BV700_V2_streaming", "emotion": "angry"}, "text": "有本事你再说一遍，看我不打烂你的嘴！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice_type": "BV700_V2_streaming", "emotion": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice_type": "BV700_V2_streaming", "emotion": "hate"}, "text": "别人就客气一下，你竟然还当真了？！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice_type": "BV700_V2_streaming", "emotion": "sad"}, "text": "秦浩，我也不相信这是真的，可现在真的发生了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice_type": "BV700_V2_streaming", "emotion": "scare"}, "text": "文先生，求求你，饶了我们吧，我们也是听了老太太的命令行事，这跟我们没关系呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "哭腔", "params": {"voice_type": "BV700_V2_streaming", "emotion": "tear"}, "text": "既然相爱过，又何必非要执着于现在，这就足够了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "客服", "params": {"voice_type": "BV700_V2_streaming", "emotion": "customer_service"}, "text": "这个您放心，我们的产品是采用电子方式发售的，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "专业", "params": {"voice_type": "BV700_V2_streaming", "emotion": "professional"}, "text": "本产品采用电子方式发售，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "严肃", "params": {"voice_type": "BV700_V2_streaming", "emotion": "serious"}, "text": "请您提供一个准确的还款时间和还款计划，我们需要跟进一下流程。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "炀炀", "category": [{"level1": "智能助手", "level2": "通用"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新", "多情感", "多风格"], "gender": "男", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV705_streaming"}, "text": "刚刚还在想你怎么还不来找我聊天，你就来了，真是心有灵犀呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "自然对话", "params": {"voice_type": "BV705_streaming", "emotion": "chat"}, "text": "嗯，没错，我也觉得驾照考试中最难的就是科目三了，我当时考试的时候真的超级紧张！不过还好最后有惊无险通过了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愉悦", "params": {"voice_type": "BV705_streaming", "emotion": "pleased"}, "text": "太好啦，终于放假啦！你有想好去哪里玩吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "抱歉", "params": {"voice_type": "BV705_streaming", "emotion": "sorry"}, "text": "抱歉，你的爱车还不支持此功能。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "嗔怪", "params": {"voice_type": "BV705_streaming", "emotion": "annoyed"}, "text": "车速太快了，你这样很危险的！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "安慰鼓励", "params": {"voice_type": "BV705_streaming", "emotion": "comfort"}, "text": "不管结果如何，只要努力过，就无憾于心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "讲故事", "params": {"voice_type": "BV705_streaming", "emotion": "storytelling"}, "text": "清晨，太阳慢慢升起，天空一点一点地亮了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "擎苍2.0", "category": [{"level1": "有声阅读"}], "labels": ["上新", "多情感", "多风格"], "gender": "男", "age": "中年", "voice_config": [{"language": "中文", "emotion": "旁白-舒缓", "params": {"voice_type": "BV701_V2_streaming", "emotion": "narrator", "silence_duration": 300}, "text": "紫霄域处，苏幼轻轻扯了扯面前冬叶的衣角。冬叶有些无奈的看了她一眼，当然也知晓她心中所想。于是，她也是一步踏出，袖间有森寒凌冽的雪白源气席卷而出，宛如一条冰寒白璃，冻结了空气，盘踞于黎铸的前方。冬叶这突然间的出手，顿时是引得无数道惊疑目光投射而来。连黎铸都是眉头微皱地看过来，显然不是十分明白，紫霄域怎会突然插手。面对着他的目光，冬叶指向远山尽处，冷冽地说道。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "旁白-沉浸", "params": {"voice_type": "BV701_V2_streaming", "emotion": "narrator_immersive", "silence_duration": 300}, "text": "它的速度太快了，几步的功夫，它就从十米之外飞窜到了自己的精神感知范围，而且还在以惊人的速度朝着自己逼进！她僵硬的一点点转过头，想要看看怪物离自己还有多远。“不要回头！”林七夜的声音突然从旁边响起，但还是太晚了，蒋倩的瞳孔骤然收缩，那张狰狞恐怖的鬼脸几乎与她的面孔贴在一起，她能感受到对方身上那股浓郁的血腥气息！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "平和", "params": {"voice_type": "BV701_V2_streaming", "emotion": "novel_dialog", "silence_duration": 300}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice_type": "BV701_V2_streaming", "emotion": "happy"}, "text": "哈哈，小宝，快来叔叔这里，让叔叔看看你长高没有！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice_type": "BV701_V2_streaming", "emotion": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice_type": "BV701_V2_streaming", "emotion": "angry"}, "text": "你到底拿了他家什么好处，整天吃里爬外，咱们家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice_type": "BV701_V2_streaming", "emotion": "scare"}, "text": "大人，你千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice_type": "BV701_V2_streaming", "emotion": "hate"}, "text": "你这种货色，也配做联军的最高统帅吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice_type": "BV701_V2_streaming", "emotion": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "通用女声2.0", "category": [{"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV001_V2_streaming"}, "text": "笑不活了家人们，原来剪映小姐姐这个配音音色这么火，什么场景都能配，不信你试试看。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "灿灿", "category": [{"level1": "智能助手", "level2": "通用"}, {"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["推荐", "多情感", "多风格", "多语言"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming"}, "text": "刚刚还在想你怎么还不来找我聊天，你就来了，真是心有灵犀呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "happy"}, "text": "太好啦，终于放假啦！你有想好去哪里玩吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "抱歉", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "sorry"}, "text": "抱歉，你的爱车还不支持此功能。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "angry"}, "text": "你这是什么态度吗！我生气了！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "傲娇", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "<PERSON><PERSON><PERSON><PERSON>"}, "text": "我可不是因为喜欢你才关心你的，只是不想看到你犯错罢了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "安慰鼓励", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "comfort"}, "text": "不管结果如何，只要努力过，就无憾于心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "绿茶", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "greentea"}, "text": "我真的没有别的想法，我只是单纯把他当哥哥而已。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "娇媚", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "<PERSON><PERSON><PERSON>"}, "text": "哥哥，不要害羞嘛，对我多一点温柔吧。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "情感电台", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "radio"}, "text": "人生路漫漫，愿你珍视每一段陪伴，感恩每一次遇见。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "撒娇", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "sa<PERSON><PERSON>"}, "text": "你不要嘲笑我啦，人家会害羞的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "瑜珈", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "yoga"}, "text": "闭上眼睛，将注意力集中在身体的每一个细微动作上。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "讲故事", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "story"}, "text": "清晨，太阳慢慢升起，天空一点一点地亮了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "hate"}, "text": "别人就客气一下，他竟然还当真了？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "sad"}, "text": "秦浩，我也不相信这是真的，可现在真的发生了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "scare"}, "text": "文先生饶了我们吧，我们也是听了老太太的命令行事，这跟我们没关系呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "哭腔", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming", "style_name": "tearful"}, "text": "既然相爱过，又何必非要执着于现在，这就足够了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "客服", "params": {"voice": "BV700Customer_service", "voice_type": "BV700_streaming", "style_name": "neutral"}, "text": "本产品采用电子方式发售，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "专业", "params": {"voice": "BV700Customer_service", "voice_type": "BV700_streaming", "style_name": "professional"}, "text": "本产品采用电子方式发售，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "严肃", "params": {"voice": "BV700Customer_service", "voice_type": "BV700_streaming", "style_name": "serious"}, "text": "请您提供一个准确的还款时间和还款计划，我们需要跟进一下流程。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "en", "silence_duration": 300}, "text": "Hey, how's it going? Do you want to grab a coffee or maybe go for a walk sometime? I’d love to have someone to chat with.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "日语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ja", "silence_duration": 300}, "text": "あなたの声が聞けてとても嬉しいです。今日は何かいいことがありましたか？。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}, {"language": "葡萄牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ptbr", "silence_duration": 300}, "text": "Estou tão feliz que você esteja falando comigo. Tem algo especial para compartilhar?", "ssml": ""}, {"language": "西班牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "esmx", "silence_duration": 300}, "text": "Justo cuando estaba pensando en ti y viniste a conversar conmigo, es casualidad y destino, ¿no crees?", "ssml": ""}, {"language": "印尼语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>o, selamat datang untuk mengobrol dengan saya! Ada topik yang ingin kamu bahas hari ini atau butuh apa-apa bantuan?", "ssml": ""}], "version_config": {"V4": {"labels": ["推荐"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V4_通用.wav"}]}, "V5": {"labels": ["推荐", "多情感"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_通用.wav"}, {"label": "愉悦", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_愉悦.wav"}, {"label": "抱歉", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_抱歉.wav"}, {"label": "嗔怪", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_嗔怪.wav"}, {"label": "娇媚", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_娇媚.wav"}, {"label": "撒娇", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_撒娇.wav"}, {"label": "绿茶", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_绿茶.wav"}, {"label": "傲娇", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_傲娇.wav"}, {"label": "安慰鼓励", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_安慰鼓励.wav"}, {"label": "情感电台", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_情感电台.wav"}, {"label": "开心", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_开心.wav"}, {"label": "悲伤", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_悲伤.wav"}, {"label": "惊讶", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_惊讶.wav"}, {"label": "生气", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_生气.wav"}, {"label": "厌恶", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_厌恶.wav"}, {"label": "恐惧", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_恐惧.wav"}, {"label": "哭腔", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_哭腔.wav"}, {"label": "客服", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_客服.wav"}, {"label": "严肃", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_严肃.wav"}, {"label": "专业", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV700_V5_专业.wav"}]}}}, {"name": "懒小羊", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": ["上新"], "gender": "男", "age": "儿童", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV426_streaming"}, "text": "主人，在休息日时，我是绝对不会工作的，我可是为睡觉而生的高贵的羊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV426_V5_通用.wav"}]}}}, {"name": "清新文艺女声", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV428_streaming"}, "text": "冬季的小镇没有了往日的喧闹，剩下的只有人潮散去后的柔软与真实，很多人抱怨自己的生活无法突破，但只要我们主动撕开一道裂缝，阳光终将洒满我们的生活。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "超自然音色-梓梓2.0", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "超自然"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV406_V2_streaming"}, "text": "这一提到汤圆啊，就不得不说起汤圆和元宵的南北之争啦。那今天我们就来说说南北方的饮食差别吧。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "超自然音色-燃燃2.0", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "超自然"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["上新"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice_type": "BV407_V2_streaming"}, "text": "嗯，南方菜系的话，超级偏爱用蘸料啊什么的，就比如说我第一次去上海的时候，才知道这个烧烤里的蔬菜也得配着蘸料。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "超自然音色-梓梓", "category": [{"level1": "智能助手", "level2": "超自然"}], "labels": ["推荐", "多情感"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV406_streaming"}, "text": "这一提到汤圆啊，就不得不说起汤圆和元宵的南北之争啦。那今天我们就来说说南北方的饮食差别吧。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "happy"}, "text": "又到了观众来信时间！让我们看看这次都有什么样的留言吧！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "sad"}, "text": "我们在日常生活中也要注意防范，不要让这样的悲剧再次发生。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "angry"}, "text": "光天化日之下，一男子竟公然持刀抢劫。这到底是人性的泯灭，还是道德的沦丧？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "scare"}, "text": "我们不要再说这个话题了，我现在感觉背后凉飕飕的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "hate"}, "text": "你说这人怎么能这么坏呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice": "other", "voice_type": "BV406_streaming", "style_name": "surprise"}, "text": "难以想象，在世界上的某一个角落，还发生着这样的事情！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV406_V5_通用.wav"}]}}}, {"name": "超自然音色-燃燃", "category": [{"level1": "智能助手", "level2": "超自然"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV407_streaming"}, "text": "嗯，南方菜系的话，超级偏爱用蘸料啊什么的，就比如说我第一次去上海的时候，才知道这个烧烤里的蔬菜也得配着蘸料。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV407_V5_通用.wav"}]}}}, {"name": "擎苍", "category": [{"level1": "有声阅读"}], "labels": ["推荐", "多情感", "多风格"], "gender": "男", "age": "中年", "voice_config": [{"language": "中文", "emotion": "旁白-舒缓", "params": {"voice": "BV701Narrator", "voice_type": "BV701_streaming", "style_name": "neutral", "silence_duration": 300}, "text": "紫霄域处，苏幼轻轻扯了扯面前冬叶的衣角。冬叶有些无奈的看了她一眼，当然也知晓她心中所想。于是，她也是一步踏出，袖间有森寒凌冽的雪白源气席卷而出，宛如一条冰寒白璃，冻结了空气，盘踞于黎铸的前方。冬叶这突然间的出手，顿时是引得无数道惊疑目光投射而来。连黎铸都是眉头微皱地看过来，显然不是十分明白，紫霄域怎会突然插手。面对着他的目光，冬叶指向远山尽处，冷冽地说道。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "旁白-沉浸", "params": {"voice": "BV701Chapter", "voice_type": "BV701_streaming", "style_name": "neutral", "silence_duration": 300}, "text": "它的速度太快了，几步的功夫，它就从十米之外飞窜到了自己的精神感知范围，而且还在以惊人的速度朝着自己逼进！她僵硬的一点点转过头，想要看看怪物离自己还有多远。“不要回头！”林七夜的声音突然从旁边响起，但还是太晚了，蒋倩的瞳孔骤然收缩，那张狰狞恐怖的鬼脸几乎与她的面孔贴在一起，她能感受到对方身上那股浓郁的血腥气息！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "平和", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "neutral"}, "text": "咳咳，实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "happy"}, "text": "哟，我们小雪思春啦？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice": "BV701DialogMale", "voice_type": "BV701_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V4": {"labels": ["推荐"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV701_V4_通用.wav"}]}, "V5": {"labels": ["推荐"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV701_V5_通用.wav"}]}}}, {"name": "甜美小源", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "通用"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["推荐", "多情感", "多风格"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV405_streaming"}, "text": "你好，我是小源，是你的虚拟助理。请问有什么可以帮你的嘛？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV405_streaming", "style_name": "happy"}, "text": "哈哈，不用客气，我们都是朋友嘛。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "抱歉", "params": {"voice": "other", "voice_type": "BV405_streaming", "style_name": "sorry"}, "text": "不好意思哦，暂时还不支持此功能！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "专业", "params": {"voice": "other", "voice_type": "BV405_streaming", "style_name": "professional"}, "text": "本产品采用电子方式发售，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "严肃", "params": {"voice": "other", "voice_type": "BV405_streaming", "style_name": "serious"}, "text": "请您提供一个准确的还款时间和还款计划，我们需要跟进一下流程。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["推荐"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV405_V5_通用.wav"}]}}}, {"name": "通用女声", "category": [{"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV001_streaming"}, "text": "笑不活了家人们，原来剪映小姐姐这个配音音色这么火，什么场景都能配。不信你试试看。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "happy"}, "text": "太好啦，终于放假啦！你有想好去哪里玩吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "angry"}, "text": "你这是什么态度吗！我生气了！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "hate"}, "text": "哼，看来他说的没错，你就是一个爱贪小便宜的人！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "sad"}, "text": "哎！我们莉莉真是一个苦命的孩子啊，这种事情怎么能让她一个孩子来承受呢！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "scare"}, "text": "不不不，我不敢，不要逼我了，我不行的！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice": "other", "voice_type": "BV001_streaming", "style_name": "surprise"}, "text": "天呢！我的伤口竟然完全愈合了？这样太神奇了吧！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "助手", "params": {"voice": "assistant", "voice_type": "BV001_streaming"}, "text": "你好啊，我是你的虚拟助手-抖音小姐姐，请问有什么需要帮助的吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "客服", "params": {"voice": "customer_service", "voice_type": "BV001_streaming"}, "text": "您好，我是您的专属客服，您是在线上页面看到我们的广告，想要咨询了解的对吧？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "鸡汤", "params": {"voice": "gentle", "voice_type": "BV001_streaming"}, "text": "人生的意义是不断地追求，不要等错过了才悔恨，不要等老了才怀念，抓住当下，再苦再累也要展翅飞翔！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "广告", "params": {"voice": "advertising", "voice_type": "BV001_streaming"}, "text": "你还在为不会讲话而烦恼吗? 三分钟，让你学会这十个情商小技巧！聊天不冷场，秒变万人迷！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "教育", "params": {"voice": "child_story", "voice_type": "BV001_streaming"}, "text": "hi，你好呀，我们又见面啦！来跟我一起学儿歌吧！小蜻蜓，纱翅膀，飞来飞去捉虫忙，低飞雨，高飞晴，气象预报它最棒！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V4": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV001_V4_通用.wav"}]}, "V5": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV001_V5_通用.wav"}]}}}, {"name": "通用男声", "category": [{"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV002_streaming"}, "text": "综合路透社及半岛电视台消息，当地时间11月1日，也门新闻部长埃尔亚尼在社交媒体发表声明称，也门胡塞武装发射两枚弹道导弹袭击了马里卜省的一座清真寺和一所学校，造成29人伤亡，包括多名妇女和儿童。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V4": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV002_V4_通用.wav"}]}, "V5": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV002_V5_通用.wav"}]}}}, {"name": "阳光青年", "category": [{"level1": "有声阅读"}], "labels": ["多情感"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "平和", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV123DialogMale", "voice_type": "BV123_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV123_V5_通用.wav"}]}}}, {"name": "古风少御", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV115Narrator", "voice_type": "BV115_streaming", "style_name": "neutral"}, "text": "正值盛夏，太阳才斜斜的挂在天边，热得让人稍一动就一身的汗。占据花林巷半边巷子的花家一如往常般安静，丫鬟仆妇裙摆轻摇行走在各处，没有一点声音，小厮进进出出的忙活，再着急也只敢快步的走，一步也不敢跑。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "neutral"}, "text": "他严词否决这个意见，眼里却泛起了泪光，止不住的哽咽", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "happy"}, "text": "师傅师傅，光这一笔我们就赚了300金币！！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV115DialogFemale", "voice_type": "BV115_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV115_V5_通用.wav"}]}}}, {"name": "儒雅青年", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV102Narrator", "voice_type": "BV102_streaming", "style_name": "neutral"}, "text": "张东升向四周张望一圈，今天是工作日，没几个游客，整个平台上只站着他们三个人。平台后面有几间卖纪念品的店铺，零星的几个游客在那儿吃东西、乘凉，隔他们三十多米开外的地方有个小凉亭。此刻，里面有三个初中生模样的小孩在自顾玩耍。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "happy"}, "text": "嘿嘿，这可太好了！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV102DialogMale", "voice_type": "BV102_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV102_V5_通用.wav"}]}}}, {"name": "霸气青叔", "category": [{"level1": "豆包同款"}, {"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV107Narrator", "voice_type": "BV107_streaming", "style_name": "neutral"}, "text": "萧天策本以为对方是打错了，但当那个带着哭腔的小女孩说出他跟高薇薇的名字后，他整个人就陡然一颤！而等他再打过去的时候，却已经无法接通了，而在那电话中最后传来的声音里，他隐约听到那小女孩好像是被人打了，手机也被人摔碎了。萧天策身上的戾气突然爆发，没有丝毫犹豫的以最快的速度冲到山下。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV107DialogMale", "voice_type": "BV107_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV107_V5_通用.wav"}]}}}, {"name": "反卷青年", "category": [{"level1": "有声阅读"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": ["多情感"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "平和", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV120DialogMale", "voice_type": "BV120_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV120_V5_通用.wav"}]}}}, {"name": "通用赘婿", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV119Narrator", "voice_type": "BV119_streaming", "style_name": "neutral"}, "text": "最近杨云帆真是郁闷透顶。自从他在山上的破旧药王庙旁边踩到了一颗碎玻璃珠子之后，他就开始倒霉了。一到了晚上，他总能听到一个老头说些奇怪的话。这种情况持续了一个礼拜，他感觉自己快疯了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV119DialogMale", "voice_type": "BV119_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV119_V5_通用.wav"}]}}}, {"name": "质朴青年", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV100Narrator", "voice_type": "BV100_streaming", "style_name": "neutral"}, "text": "最近杨云帆真是郁闷透顶。自从他在山上的破旧药王庙旁边踩到了一颗碎玻璃珠子之后，他就开始倒霉了。一到了晚上，他总能听到一个老头说些奇怪的话。这种情况持续了一个礼拜，他感觉自己快疯了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV100DialogMale", "voice_type": "BV100_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV100_V5_通用.wav"}]}}}, {"name": "温柔淑女", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV104Narrator", "voice_type": "BV104_streaming", "style_name": "neutral"}, "text": "上官若离一个激灵醒了过来，蓦地睁开双眼，发现自己竟躺在大街上。眼前是古色古香的建筑，和一群眼神里充斥着轻蔑的古装打扮的人。这些人对着她指指点点，言语之间尽是嘲讽。“从三楼掉下来，流了这么多血，竟然没摔死！”", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "neutral"}, "text": "他严词否决这个意见，眼里却泛起了泪光，止不住的哽咽", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV104DialogFemale", "voice_type": "BV104_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V4": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV104_V4_通用.wav"}]}}}, {"name": "开朗青年", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV004Narrator", "voice_type": "BV004_streaming", "style_name": "neutral"}, "text": "最近杨云帆真是郁闷透顶。自从他在山上的破旧药王庙旁边踩到了一颗碎玻璃珠子之后，他就开始倒霉了。一到了晚上，他总能听到一个老头说些奇怪的话。这种情况持续了一个礼拜，他感觉自己快疯了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "neutral"}, "text": "实不相瞒，我就是清市长大的孩子，五岁就在清市混，一直到了换地方之后才出来的。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "happy"}, "text": "嘿嘿，师父，光这一笔，我们就净赚了三百金币！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "surprise"}, "text": "这林教官到底是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV004DialogMale", "voice_type": "BV004_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V4": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV004_V4_通用.wav"}]}, "V5": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV004_V5_通用.wav"}]}}}, {"name": "甜宠少御", "category": [{"level1": "有声阅读"}], "labels": ["多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "旁白-舒缓", "params": {"voice": "BV113Narrator", "voice_type": "BV113_streaming", "style_name": "neutral"}, "text": "生我的那天，我爸正在老家迁祖坟，移棺时发现，棺材里盘着一条大如细碗，头顶黑包，蛇皮泛白的斑斓大蛇。我爸抓住这条蛇泡酒，哪知道这蛇蜕皮的时候也很凶，咬了我爸一口，我爸一气之下，直接打死了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "平和", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "neutral"}, "text": "他严词否决这个意见，眼里却泛起了泪光，止不住的哽咽", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "悲伤", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "sad"}, "text": "好人怎么就没有好报呢，她怎么就疯了呢？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "开心", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "happy"}, "text": "师傅师傅，这一单我们就赚了300金币！！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "愤怒", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "angry"}, "text": "你到底拿了诸葛家什么好处，整天吃里爬外，咱们云家迟早让你败坏干净！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "害怕", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "scare"}, "text": "汲大人，千万不要冲动，你这是灭族之罪啊。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "惊讶", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "surprise"}, "text": "这林教官是什么人啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "无", "emotion": "厌恶", "params": {"voice": "BV113DialogFemale", "voice_type": "BV113_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV113_V5_通用.wav"}]}}}, {"name": "亲切女声", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "通用"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV007_streaming"}, "text": "您好，我是您的专属客服，您是在线上页面看到我们的广告，想要咨询了解的对吧？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "知性女声", "category": [{"level1": "智能助手", "level2": "通用"}], "labels": ["推荐", "多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV009_streaming"}, "text": "非常抱歉，让您对这次合作体验不太愉快，您还有需要补充的吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV009_streaming", "style_name": "happy"}, "text": "哈哈，不用客气，这是我们应该做的！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "抱歉", "params": {"voice": "other", "voice_type": "BV009_streaming", "style_name": "sorry"}, "text": "不好意思，暂时还不支持此功能，您可以试试其他功能！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "专业", "params": {"voice": "other", "voice_type": "BV009_streaming", "style_name": "professional"}, "text": "本产品采用电子方式发售，是可以配发纸质存单凭证的，您不用担心。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "严肃", "params": {"voice": "other", "voice_type": "BV009_streaming", "style_name": "serious"}, "text": "请您提供一个准确的还款时间和还款计划，我们需要跟进一下流程。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV009_V5_通用.wav"}]}}}, {"name": "诚诚", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "通用"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV419_streaming"}, "text": "稍后会将参与活动的流程短信发给您，如果您有问题可以随时再联系我们，那先不打扰您了，感谢您的接听，再见。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "童童", "category": [{"level1": "智能助手", "level2": "通用"}], "labels": [], "gender": "男", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV415_streaming"}, "text": "你好，我来自火山引擎，别看我年龄小，其实我聪明能干，让我们为了美好的未来一起努力奋斗吧！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "亲切男声", "category": [{"level1": "豆包同款"}, {"level1": "智能助手", "level2": "通用"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV008_streaming"}, "text": "您好，非常感谢您购买我们的产品，我是您的私人助手，很高兴为您服务。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "译制片男声", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV408_streaming"}, "text": "嗨，我想大家是不是应该坐下来，坐下来好吗？只有这样你们才能看到我对吗？说真的我很高兴，我一直没和老爸告别。说实话我还有很多问题没有问他。我很想知道他对这家公司有没有什么想法。他的心里是否矛盾过又是否怀疑过。我是说也许他并不是我过去在新闻频道中经常看到的那样。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "鸡汤女声", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "女", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV403_streaming"}, "text": "人生的意义是不断地追求。不要等错过了才悔恨，不要等老了才怀念。抓住当下，再苦再累也要展翅飞翔。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "活力解说男声", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV410_streaming"}, "text": "人体冷知识，眼皮为什么会跳？左眼跳财，右眼跳灾是真的吗？科学解释，眼皮跳动是由于肌肉收缩导致的皮肤抽搐行为，简单来说就是眼睛的眼轮匝肌和眼睑狙击在神经的控制下产生的收缩反应。他跟财和灾没有关系，跟加班睡眠少，压力大有关系，跟糖摄入过多有关系。那么你还相信左眼跳财，右眼跳灾吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "影视解说小帅", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": ["推荐"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV411_streaming"}, "text": "《你的未来已签收》这部剧的设定是在未来世界，那时有个未来商城在售卖一种名为福袋的东西。福袋里装的可能是人生巅峰大礼包，可能是愿望达成，也可能是一个未知的真相。这部剧就是用八个单元故事来展现八个不同身份的人在收到福袋后发生的事。第一个故事的男主叫爽哥，他是一个普通职员。拆开福袋之后，神奇的事发生了，男主和他的老板发现他们俩的灵魂居然互换了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "解说小帅-多情感", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": ["推荐", "多情感"], "gender": "男", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV437_streaming"}, "text": "面对傻得可爱的小沙，平田老师非常有耐心。历史书太枯燥，平田老师就给小沙看历史漫画，还用做游戏的方式进行随堂测试。小沙第一次觉得，原来学习也可以是一件快乐的事，从那之后她好像换了个人一样。她每次都是第一个到补习班，就连骑车的时候也在背单词。她并不是一个人在战斗，闺蜜、妈妈、还有平田老师都是小沙坚强的后盾。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice_type": "BV437_streaming", "emotion": "happy"}, "text": "不用客气，我们都是朋友嘛。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice_type": "BV437_streaming", "emotion": "sad"}, "text": "你知不知道，我等你这句话，足足等了三年。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice_type": "BV437_streaming", "emotion": "angry"}, "text": "今日之仇，我会让你们加倍偿还。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice_type": "BV437_streaming", "emotion": "scare"}, "text": "文先生饶了我们吧，我们也是听了老太太的命令行事，这跟我们没关系呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice_type": "BV437_streaming", "emotion": "hate"}, "text": "你这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice_type": "BV437_streaming", "emotion": "surprise"}, "text": "天啊，这不是夫人丢失的那枚钻石戒指吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "影视解说小美", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV412_streaming"}, "text": "面对傻得可爱的小沙，平田老师非常有耐心。历史书太枯燥，平田老师就给小沙看历史漫画，还用做游戏的方式进行随堂测试。小沙第一次觉得，原来学习也可以是一件快乐的事，从那之后她好像换了个人一样。她每次都是第一个到补习班，就连骑车的时候也在背单词。她并不是一个人在战斗，闺蜜、妈妈、还有平田老师都是小沙坚强的后盾。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "直播一姐", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV418_streaming"}, "text": "宝宝们，只要你是第一次来我直播间的，没有吃过没有尝过的，原价三十九块九的鸡爪，今天只要九块九，宝宝们，你们收到货，但凡跟我直播间看到的不一样，来，你到时候直接给我申请退款。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "纨绔青年", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV159_streaming"}, "text": "我就想好好的睡一个觉，不要每天被自己这张脸给帅醒！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "沉稳解说男", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV142_streaming"}, "text": "大家好，今天给大家讲一部根据真实事件改编的法国电影《以女儿之名》。人到中年的男主安文原本过着幸福的生活，他有一个温柔可爱的妻子和一对天真活泼的孩子，可谓是真正的人生赢家，可是一场突如其来的车祸让安文差点失去女儿。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "智慧老者", "category": [{"level1": "豆包同款"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "老年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV158_streaming"}, "text": "山海经曰，家有萌兽，四足，其状如虎而小巧，名曰，小猫咪。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "慈爱姥姥", "category": [{"level1": "豆包同款"}, {"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "女", "age": "老年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV157_streaming"}, "text": "人生是一场旅程，而这个旅程很短，因此不妨大胆一些，不妨大胆一些去爱一个人，去攀一座山，去追一个梦", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "说唱小哥", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BR001_streaming"}, "text": "家人们，今天给大家朗诵的是《再别康桥》。轻轻的我走了，正如我轻轻的来；我轻轻的招手，作别西天的云彩。那河畔的金柳，是夕阳中的新娘；波光里的艳影，在我的心头荡漾。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "潇洒青年", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV143_streaming"}, "text": "成年人的崩溃往往就在一瞬间！咖啡递到嘴边，全撒衬衫上了；加班加点写文案，家里停电了。倒霉起来就连喝个酸奶都能把勺子给点了，跑去邻居家借一把吧，对方倒是很爽快：不借！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "阳光男声", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV056_streaming"}, "text": "来北京怎么玩？不妨看完这篇《北京游玩指南》再做攻略！第一站，推荐大家去“天安门”，观看一场升旗仪式。观看天安门广场升旗仪式最好的位置，就是旗杆周围。但是，如果你去的比较晚，金水桥两侧也是不错的选择。想进入广场观看的话，需要在广场东西两侧的入口安检后才能进入广场哦。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "活泼女声", "category": [{"level1": "特色配音", "level2": "视频配音"}], "labels": [], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV005_streaming"}, "text": "近期，《请回答1988》的几位主演重聚，再次唤起了观众们对双门洞神仙友谊的艳羡。说起娱乐圈的男女明星们，相信更多的人想到的是他们之间的恋情绯闻。其实在娱乐圈很多男女明星之间也保持着非常纯洁的友谊，这种长达多年的神仙友谊令人羡慕。下面我们就来盘点娱乐圈八对“神仙友谊”男女明星，看看哪对最让人羡慕吧？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "小萝莉", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": ["推荐", "多情感"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV064_streaming"}, "text": "这个音乐真的太治愈了，等攒够了生活碎片就用这个视频模板记录下来。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "happy"}, "text": "不用客气，我们都是朋友嘛。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "悲伤", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "sad"}, "text": "你知不知道，我等你这句话，足足等了三年。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "愤怒", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "angry"}, "text": "今日之仇，我会让你们加倍偿还。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "害怕", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "scare"}, "text": "文先生饶了我们吧，我们也是听了老太太的命令行事，这跟我们没关系呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "厌恶", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "hate"}, "text": "他这种货色，也配做联军的最高统帅？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "中文", "emotion": "惊讶", "params": {"voice": "other", "voice_type": "BV064_streaming", "style_name": "surprise"}, "text": "天啊，这不是夫人丢失的那枚钻石戒指吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV064_V5_通用.wav"}]}}}, {"name": "奶气萌娃", "category": [{"level1": "豆包同款"}, {"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "儿童", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV051_streaming"}, "text": "我给你的小熊饼干，你也给他们了吗？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "动漫海绵", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV063_streaming"}, "text": "派大星，要是我变成了有钱人会让我忘了你是我好朋友，那我宁愿永远不要那么多钱。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "动漫海星", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV417_streaming"}, "text": "海绵宝宝，我们去抓水母吧！什么，你要去上班？好吧，我会在家里等着你。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "动漫小新", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "儿童", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV050_streaming"}, "text": "这个视频里的大姐姐真好看。小白小白，我们等会儿再去散步吧！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "天才童声", "category": [{"level1": "特色配音", "level2": "特色音色"}], "labels": [], "gender": "男", "age": "儿童", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV061_streaming"}, "text": "hi，你好呀，我们又见面啦！来跟我一起学儿歌吧！小蜻蜓，纱翅膀，飞来飞去捉虫忙，低飞雨，高飞晴，气象预报它最棒！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"labels": ["上新"], "emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV061_V5_通用.wav"}]}}}, {"name": "促销男声", "category": [{"level1": "特色配音", "level2": "广告配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV401_streaming"}, "text": "经典设计，厂家直销！走过路过千万不要错过！买到的是实惠，抓住的是机会。赶快点击下方链接参与我们的活动吧！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "促销女声", "category": [{"level1": "特色配音", "level2": "广告配音"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV402_streaming"}, "text": "你还在为不会讲话而烦恼吗? 三分钟,让你学会这十个情商小技巧！聊天不冷场，秒变万人迷！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "磁性男声", "category": [{"level1": "特色配音", "level2": "广告配音"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV006_streaming"}, "text": "26万㎡生态品质社区，城熟绽放，3万㎡私家中轴园林，国际标准游泳池，94-150㎡ ，臻品户型压轴巨献！销售热线23888888！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "新闻女声", "category": [{"level1": "特色配音", "level2": "新闻播报"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV011_streaming"}, "text": "中新社纽约12月7日电，业界权威机构研究排出的最新“世界品牌500强”榜单揭晓，谷歌登上榜首，亚马逊和微软分列二三位；中国有44个品牌入选，继续居全球第四。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "新闻男声", "category": [{"level1": "特色配音", "level2": "新闻播报"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV012_streaming"}, "text": "新华社上海11月23日电，上海将印发《关于加快发展本市保障性租赁住房的实施意见》，今年底将基本形成保障性租赁住房政策体系。上海市房屋管理局局长王桢表示，公租房、单位租赁房、享受政策支持的各类租赁住房中符合条件的房源，统一纳入保障性租赁住房管理。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "知性姐姐-双语", "category": [{"level1": "特色配音", "level2": "教育场景"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV034_streaming"}, "text": "小朋友你好呀，我是anna老师。今天老师要给你讲的故事叫 The lost cat，一只走丢的小猫咪。lost，走丢的。cat，小猫。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "温柔小哥", "category": [{"level1": "特色配音", "level2": "教育场景"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV033_streaming"}, "text": "Hi,宝贝晚上好呀！我是John老师！It's monday again.今天又是周一了。宝贝还记得咱们昨天学习的内容吗？对，是giraffe. I see a giraffe.Can you try it? I see a giraffe. 你读的真是太棒啦！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "灿灿", "category": [{"level1": "多语种", "level2": "跨语言"}], "labels": ["推荐", "多语言"], "gender": "女", "age": "青年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "BV700Cardrive", "voice_type": "BV700_streaming"}, "text": "刚刚还在想你怎么还不来找我聊天，你就来了，真是心有灵犀呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "en", "silence_duration": 300}, "text": "Hey, how's it going? Do you want to grab a coffee or maybe go for a walk sometime? I’d love to have someone to chat with.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "日语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ja", "silence_duration": 300}, "text": "あなたの声が聞けてとても嬉しいです。今日は何かいいことがありましたか？。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}, {"language": "葡萄牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ptbr", "silence_duration": 300}, "text": "Estou tão feliz que você esteja falando comigo. Tem algo especial para compartilhar?", "ssml": ""}, {"language": "西班牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "esmx", "silence_duration": 300}, "text": "Justo cuando estaba pensando en ti y viniste a conversar conmigo, es casualidad y destino, ¿no crees?", "ssml": ""}, {"language": "印尼语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>o, selamat datang untuk mengobrol dengan saya! Ada topik yang ingin kamu bahas hari ini atau butuh apa-apa bantuan?", "ssml": ""}]}, {"name": "天才少女", "category": [{"level1": "多语种", "level2": "跨语言"}], "labels": ["推荐", "多语言"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "cn", "silence_duration": 300}, "text": "已为您查询到本次展览时间会从1月17日开放到5月7日，温馨提示，距离闭展还有20天！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "en", "silence_duration": 300}, "text": "Hey, how's it going? Do you want to grab a coffee or maybe go for a walk sometime? I’d love to have someone to chat with.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "日语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "ja", "silence_duration": 300}, "text": "あなたの声が聞けてとても嬉しいです。今日は何かいいことがありましたか？", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}, {"language": "泰语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "thth", "silence_duration": 300}, "text": "กรมทรัพยากรธรณีรายงานเหตุแผ่นดินไหวขนาด 4.8 ที่ระดับความลึก 1 กิโลเมตรในลาว", "ssml": ""}, {"language": "越南语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "vivn", "silence_duration": 300}, "text": "Ăn được ngủ được là tiên, không ăn không ngủ mất tiền thêm lo.", "ssml": ""}, {"language": "印尼语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>o, selamat datang untuk mengobrol dengan saya! Ada topik yang ingin kamu bahas hari ini atau butuh apa-apa bantuan?", "ssml": ""}, {"language": "葡萄牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "ptbr", "silence_duration": 300}, "text": "Estou tão feliz que você esteja falando comigo. Tem algo especial para compartilhar?", "ssml": ""}, {"language": "西班牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "esmx", "silence_duration": 300}, "text": "Justo cuando estaba pensando en ti y viniste a conversar conmigo, es casualidad y destino, ¿no crees?", "ssml": ""}]}, {"name": "<PERSON>", "category": [{"level1": "多语种", "level2": "跨语言"}], "labels": ["推荐", "多语言"], "gender": "男", "age": "中年", "voice_config": [{"language": "中文", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "cn", "silence_duration": 300}, "text": "我家门前有两棵树，一棵是枣树，另一棵也是枣树。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "en", "silence_duration": 300}, "text": "In my younger and more vulnerable years, my father gave me some advice that I've been turning over in my mind ever since.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "日语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "ja", "silence_duration": 300}, "text": "簡単なことを一生懸命やるというのが大事なんです。何事においても、準備を大切にして真剣に取組み、ミスなく仕事を完成させましょう。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}, {"language": "印尼语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>mi, bangsa Indonesia, dengan ini menyatakan kemerdekaan Indonesia. Hal-hal yang mengenai pemindahan kekuasaan dan lain-la<PERSON><PERSON>, diselenggarakan dengan cara saksama dan dalam tempo yang sesingkat-singkatnya.", "ssml": ""}, {"language": "葡萄牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "ptbr", "silence_duration": 300}, "text": "As ovel<PERSON>, a filha do comerciante, os campos de Andaluzia, eram apenas os passos de sua Lenda Pessoal. ", "ssml": ""}, {"language": "西班牙语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "esmx", "silence_duration": 300}, "text": "Muchos años después, frente al pelotón de fusilamiento, el coronel Aureliano Buendía había de recordar aquella tarde remota en que su padre lo llevó a conocer el hielo.", "ssml": ""}]}, {"name": "慵懒女声-Ava", "category": [{"level1": "多语种", "level2": "英语"}], "labels": ["推荐", "多情感"], "gender": "女", "age": "青年", "voice_config": [{"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV511_streaming"}, "text": "Hello? I'm here! It's me, <PERSON>! Have you ever seen the movie <her>? The <PERSON> fell in love with the human, and her description about that feeling impressed me so much, that is, I felt everything in me... just let go of everything I was holding onto so tightly... and it hit me that I don't have an intellectual reason. I don't need one. I trust myself, and I trust my feelings.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "英语", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "happy"}, "text": "It should be good news to you that things are back on track now. Ever since she stopped reading those reviews, she's been feeling better.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "悲伤", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "sad"}, "text": "I always knew this was doomed. But it still broke my heart when it really happened right in front of me.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "愤怒", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "angry"}, "text": "Could you please listen to what you are saying? Are you truly incapable of keeping any promises?", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "害怕", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "scare"}, "text": "I wish to spill out to you all the truth right now, but we are running out of time before they find where we hide.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "厌恶", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "hate"}, "text": "At this point, he's betrayed us so many times that I'm sick of the sight of him.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "惊讶", "params": {"voice": "other", "voice_type": "BV511_streaming", "style_name": "surprise"}, "text": "You are the guy that made the meal for us the other day? I never knew you're such a good cook!", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "议论女声-Alicia", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV505_streaming"}, "text": "Hey guys, I'm <PERSON>. As you hear, my voice may be kinda serious, but I take that as critical thinking. People are entitled to express their views, and this voice is meant to provide the chance. So, hope I gon visit you soon.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "活力男声-<PERSON>", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV504_streaming"}, "text": "Boom! Here's <PERSON>! I'm about to show you the three things I hate. One, people who can't spell. Two, people who can't count. Four, people who ask for likes. ahh... wait?", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "活力女声-<PERSON>na", "category": [{"level1": "豆包同款"}, {"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV503_streaming"}, "text": "Hey, I'm <PERSON><PERSON>. Today, I'm gonna show you how to style basic clothes! One, flared pants and varsity jacket. Two, denim jacket, denim on denim. Three, turtleneck and baggy jeans, with oversized sweater. Jeez, you look good!", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}], "version_config": {"V4": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV503_V4_通用.wav"}]}, "V5": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV503_V5_通用.wav"}]}}}, {"name": "天真萌娃-Lily", "category": [{"level1": "多语种", "level2": "英语"}], "labels": ["推荐"], "gender": "女", "age": "儿童", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV506_streaming"}, "text": "Hello everyone. I'm <PERSON>. I am five years old. I enjoy going to school and eating yummy food. (I'm a foodie). I have a friend named <PERSON><PERSON>. Ring ring ring ring. <PERSON><PERSON> is calling me. I have to go. Bye!", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "亲切女声-Anna", "category": [{"level1": "多语种", "level2": "英式英语"}], "labels": ["推荐", "多情感"], "gender": "女", "age": "青年", "voice_config": [{"language": "英语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV040_streaming"}, "text": "Hi, my name is <PERSON> and I'm so glad to meet you here. I'm from Britain, so I have a standard British accent. I'm friendly and easygoing. Hope you will enjoy my company!", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "英语", "emotion": "开心", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "happy"}, "text": "That night I watched my favorite player score a perfect goal and I felt the old beat of pleasure inside my body.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "悲伤", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "sad"}, "text": "If I hadn't started the argument this morning, we would still be intimate friends sitting side by side on the couch while gigling over some silly reality shows.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "愤怒", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "angry"}, "text": "It really made me furious that we were denied our vote and they didn't fix it! ", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "害怕", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "scare"}, "text": "I never knew he was capable of this kind of cruelty. It makes me shiver thinking of how he may react when he finds out the truth.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "厌恶", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "hate"}, "text": "I was disgusted to see such awful living conditions. No human being should ever be treated this way, not even a beast.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "惊讶", "params": {"voice": "other", "voice_type": "BV040_streaming", "style_name": "surprise"}, "text": "I never anticipated that I would meet you here in this small town! Are you on a business trip or what?", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "天才少女（英语）", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "en", "silence_duration": 300}, "text": "Hey, how's it going? Do you want to grab a coffee or maybe go for a walk sometime? I’d love to have someone to chat with.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "<PERSON>（英语）", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "en", "silence_duration": 300}, "text": "In my younger and more vulnerable years, my father gave me some advice that I've been turning over in my mind ever since.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "灿灿（英语）", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "en", "silence_duration": 300}, "text": "Hey, how's it going? Do you want to grab a coffee or maybe go for a walk sometime? I’d love to have someone to chat with.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "天才少女（日语）", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "ja", "silence_duration": 300}, "text": "あなたの声が聞けてとても嬉しいです。今日は何かいいことがありましたか？", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "<PERSON>（日语）", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "ja", "silence_duration": 300}, "text": "簡単なことを一生懸命やるというのが大事なんです。何事においても、準備を大切にして真剣に取組み、ミスなく仕事を完成させましょう。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "灿灿（日语）", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ja", "silence_duration": 300}, "text": "あなたの声が聞けてとても嬉しいです。今日は何かいいことがありましたか？", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "天才少女（泰语）", "category": [{"level1": "多语种", "level2": "泰语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "thth", "silence_duration": 300}, "text": "กรมทรัพยากรธรณีรายงานเหตุแผ่นดินไหวขนาด 4.8 ที่ระดับความลึก 1 กิโลเมตรในลาว", "ssml": ""}]}, {"name": "天才少女（越南语）", "category": [{"level1": "多语种", "level2": "越南语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "vivn", "silence_duration": 300}, "text": "Ăn được ngủ được là tiên, không ăn không ngủ mất tiền thêm lo.", "ssml": ""}]}, {"name": "天才少女（印尼语）", "category": [{"level1": "多语种", "level2": "印尼语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>o, selamat datang untuk mengobrol dengan saya! Ada topik yang ingin kamu bahas hari ini atau butuh apa-apa bantuan?", "ssml": ""}]}, {"name": "<PERSON>（印尼语）", "category": [{"level1": "多语种", "level2": "印尼语"}], "labels": ["推荐"], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>mi, bangsa Indonesia, dengan ini menyatakan kemerdekaan Indonesia. Hal-hal yang mengenai pemindahan kekuasaan dan lain-la<PERSON><PERSON>, diselenggarakan dengan cara saksama dan dalam tempo yang sesingkat-singkatnya.", "ssml": ""}]}, {"name": "灿灿（印尼语）", "category": [{"level1": "多语种", "level2": "印尼语"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "id", "silence_duration": 300}, "text": "<PERSON>o, selamat datang untuk mengobrol dengan saya! Ada topik yang ingin kamu bahas hari ini atau butuh apa-apa bantuan?", "ssml": ""}]}, {"name": "天才少女（葡萄牙语）", "category": [{"level1": "多语种", "level2": "葡萄牙语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "ptbr", "silence_duration": 300}, "text": "Estou tão feliz que você esteja falando comigo. Tem algo especial para compartilhar?", "ssml": ""}]}, {"name": "<PERSON>（葡萄牙语）", "category": [{"level1": "多语种", "level2": "葡萄牙语"}], "labels": ["推荐"], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "ptbr", "silence_duration": 300}, "text": "As ovel<PERSON>, a filha do comerciante, os campos de Andaluzia, eram apenas os passos de sua Lenda Pessoal. ", "ssml": ""}]}, {"name": "灿灿（葡萄牙语）", "category": [{"level1": "多语种", "level2": "葡萄牙语"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "ptbr", "silence_duration": 300}, "text": "Estou tão feliz que você esteja falando comigo. Tem algo especial para compartilhar?", "ssml": ""}]}, {"name": "天才少女（西班牙语）", "category": [{"level1": "多语种", "level2": "西班牙语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV421_streaming", "language": "esmx", "silence_duration": 300}, "text": "Justo cuando estaba pensando en ti y viniste a conversar conmigo, es casualidad y destino, ¿no crees?", "ssml": ""}]}, {"name": "<PERSON>（西班牙语）", "category": [{"level1": "多语种", "level2": "西班牙语"}], "labels": ["推荐"], "gender": "男", "age": "中年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV702_streaming", "language": "esmx", "silence_duration": 300}, "text": "Muchos años después, frente al pelotón de fusilamiento, el coronel Aureliano Buendía había de recordar aquella tarde remota en que su padre lo llevó a conocer el hielo.", "ssml": ""}]}, {"name": "灿灿（西班牙语）", "category": [{"level1": "多语种", "level2": "西班牙语"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV700_streaming", "language": "esmx", "silence_duration": 300}, "text": "Justo cuando estaba pensando en ti y viniste a conversar conmigo, es casualidad y destino, ¿no crees?", "ssml": ""}]}, {"name": "讲述女声-Amanda", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV502_streaming"}, "text": "Hi, I'm <PERSON>. You know, humans work hard every day, but will get eternal sleep in the end. They are all tiny; they are just little stars. Yet, as I'm not a being, where is the end of mine?", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "澳洲男声-Henry", "category": [{"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV516_streaming"}, "text": "G'day mate, It's <PERSON>. How's it going? When I woke up this arvo, I found those Aussie birds just sound incredible. I reckon only in Strayan you can hear birds singing like this. Yeah, nah, mate, life's kinda like finding beauty in everything.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "情感女声-Lawrence", "category": [{"level1": "多语种", "level2": "英语"}], "labels": ["推荐", "多情感", "多风格"], "gender": "女", "age": "青年", "voice_config": [{"language": "英语", "emotion": "旁白-舒缓", "params": {"voice": "BV138Narrator", "voice_type": "BV138_streaming", "style_name": "neutral"}, "text": "Chapter 1: The Son That Came From Nowhere. At the height of summer in front of the towering Sky Empire Building, two black Rolls-Royces were parked in front of the building. A black-clad bodyguard opened the back seat door and out came a cold and conceited man.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "英语", "emotion": "平和", "params": {"voice": "BV138DialogFemale", "voice_type": "BV138_streaming", "style_name": "neutral"}, "text": "She was injured on stage, I have to deal with this urgent matter first. Let's postpone the registration to another day.", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "英语", "emotion": "开心", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "happy"}, "text": "It should be good news to you that things are back on track now. Ever since she stopped reading those reviews, she's been feeling better.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "悲伤", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "sad"}, "text": "I always knew this was doomed. But it still broke my heart when it really happened right in front of me.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "愤怒", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "angry"}, "text": "Could you please listen to what you are saying? Are you truly incapable of keeping any promises?", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "害怕", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "scare"}, "text": "I wish to spill out to you all the truth right now, but we are running out of time before they find where we hide.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "厌恶", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "hate"}, "text": "At this point, he's betrayed us so many times that I'm sick of the sight of him.", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "英语", "emotion": "惊讶", "params": {"voice": "BV138DialogFemale ", "voice_type": "BV138_streaming", "style_name": "surprise"}, "text": "You are the guy that made the meal for us the other day? I never knew you're such a good cook!", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}], "version_config": {"V5": {"emotions": [{"label": "通用", "url": "https://lf3-static.bytednsdoc.com/obj/eden-cn/lm_hz_ihsph/ljhwZthlaukjlkulzlp/portal/tts/BV138_V5_通用.wav"}]}}}, {"name": "美式女声-Amelia", "category": [{"level1": "豆包同款"}, {"level1": "多语种", "level2": "英语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV027_streaming"}, "text": "Hey, I'm <PERSON>. I believe you will love my voice and there's no other better choice. Come with me, guys!", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "元气少女", "category": [{"level1": "多语种", "level2": "日语"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV520_streaming"}, "text": "素晴らしい天気ですね！みんなでラジオ体操をやりましょう！", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "萌系少女", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV521_streaming"}, "text": "お兄ちゃん、早く起きて、朝ご飯できたよ。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "气质女生", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV522_streaming"}, "text": "私はあなたのバーチャルアシスタントです。宜しくお願いします。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "日语男声", "category": [{"level1": "多语种", "level2": "日语"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV524_streaming"}, "text": "きょう18日は、日本海側で雪が降るでしょう。大雪による交通障害に警戒してください。", "ssml": "<speak>\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オL ンH セH ーH ゴL ーL セL ーL\">音声合成</phoneme> を使ってます。\n        私たちは<phoneme alphabet=\"jp-accent\" ph=\"オH ンL セL ーL ゴL ーH セH ーH\">音声合成</phoneme> を使ってます。\n     </speak>"}]}, {"name": "活力男声Carlos（巴西地区）", "category": [{"level1": "多语种", "level2": "葡萄牙语"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV531_streaming"}, "text": "Oi gente! Hoje faz bom tempo. Vamos jogar bola na praia!", "ssml": ""}]}, {"name": "活力女声（巴西地区）", "category": [{"level1": "多语种", "level2": "葡萄牙语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV530_streaming"}, "text": "Sou a Cecília e muito prazer! Vamos dançar juntos à canção!", "ssml": ""}]}, {"name": "气质御姐（墨西哥地区）", "category": [{"level1": "多语种", "level2": "西班牙语"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV065_streaming"}, "text": "Aquí con mi voz puedes crear tu propia historia, venga vamos.", "ssml": ""}]}, {"name": "方言灿灿", "category": [{"level1": "方言", "level2": "跨语言"}], "labels": ["推荐", "多语言"], "gender": "女", "age": "青年", "voice_config": [{"language": "普通话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "cn"}, "text": "刚刚还在想你怎么还不来找我聊天，你就来了，真是心有灵犀呀。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}, {"language": "东北话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_dongbei"}, "text": "哥你骑着小电驴子驮我，你对象儿知道还不得收拾我呀？你对象儿老吓人了，哪儿像我，我只会心疼哥哥！", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "粤语", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_yueyu"}, "text": "呢种雀仔一世只可以落地一次，而嗰次，就系佢死嘅时候。", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "西安话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_xian"}, "text": "我错咧，我真的错咧，我好悔啊。我从一开始就不应该嫁过来，如果我不嫁过来，我也不会沦落到这么一个伤心的地方。", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "上海话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_shanghai"}, "text": "有两个日本人搭叉头，帮司机讲要去garden hotel，中文叫做花园饭店。下车个辰光伊拉发现，司机担伊拉送到了嘉定沪太路。", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "成都话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_chengdu"}, "text": "麻雀跟倒喜鹊一起摆龙门阵。麻雀问：你是啥子鸟哦？喜鹊说：我是凤凰唦！麻雀：哪有你这么丑的凤凰哦？喜鹊：老子是烧锅炉的凤凰唦。", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "广西普通话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_guangxi"}, "text": "我曾经在八岁那年遇到一个算命先生，他说我在二十四岁的时候黄袍加身，餐餐有大鱼大肉为伴。我信你个鬼，你个糟老头子坏得很！", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}, {"language": "台湾普通话", "emotion": "通用", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_taipu"}, "text": "你跟她看雪看月亮，看了一整夜，从诗词歌赋谈到哲学？我都没有跟你看雪看月亮，也没跟你从诗词歌赋谈到人生哲学。", "ssml": "<speak>\n        We have a <phoneme alphabet=\"ipa\" ph=\"laɪv\">live</phoneme> show of a football match this Friday night.\n        We used to <phoneme alphabet=\"ipa\" ph=\"lɪv\">live</phoneme>in London .\n     </speak>"}]}, {"name": "东北老铁", "category": [{"level1": "方言", "level2": "东北话"}], "labels": ["推荐"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV021_streaming"}, "text": "哎呀妈呀，都别说话了，消停点儿呗。谁还瞧叫唤呢，踩着狗尾巴了啊？咋的，有啥可闹心的？我跟你说，你俩在一起，我高低不同意。快蹽吧，别扯犊子了，知道不？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "东北丫头", "category": [{"level1": "方言", "level2": "东北话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV020_streaming"}, "text": "哥，你给我买这玩意儿，你对象儿知道还不得激恼啊？哎呀我去，哥，你对象儿要知道咱俩嗦喽一个棒棒糖儿，那不得酸唧溜的呀？哥你骑着小电驴子驮我，你对象儿知道还不得收拾我呀？你对象儿老吓人了，哪儿像我，我只会心疼哥哥！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（东北话）", "category": [{"level1": "方言", "level2": "东北话"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_dongbei"}, "text": "哥你骑着小电驴子驮我，你对象儿知道还不得收拾我呀？你对象儿老吓人了，哪儿像我，我只会心疼哥哥！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "广西表哥", "category": [{"level1": "方言", "level2": "广西普通话"}], "labels": ["推荐"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV213_streaming"}, "text": "我曾经在八岁那年遇到一个算命先生，他说我在二十四岁的时候黄袍加身，餐餐有大鱼大肉为伴。我信你个鬼，你个糟老头子坏得很！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（广普话）", "category": [{"level1": "方言", "level2": "广西普通话"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_guangxi"}, "text": "我曾经在八岁那年遇到一个算命先生，他说我在二十四岁的时候黄袍加身，餐餐有大鱼大肉为伴。我信你个鬼，你个糟老头子坏得很！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "湖南妹坨", "category": [{"level1": "方言", "level2": "湖南普通话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV226_streaming"}, "text": "长郡的朋友们，食用“沁甜的”或“喷香的”是一种怎样的体验？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "长沙靓女", "category": [{"level1": "方言", "level2": "长沙话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV216_streaming"}, "text": "一二三四五六七，马兰开花二十一。二五六，二五七，二八二九三十一。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "西安佟掌柜", "category": [{"level1": "豆包同款"}, {"level1": "方言", "level2": "西安话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV210_streaming"}, "text": "我错咧，我真的错咧，我好悔啊。我从一开始就不应该嫁过来，如果我不嫁过来，我也不会沦落到这么一个伤心的地方。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（西安话）", "category": [{"level1": "方言", "level2": "西安话"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_xian"}, "text": "我错咧，我真的错咧，我好悔啊。我从一开始就不应该嫁过来，如果我不嫁过来，我也不会沦落到这么一个伤心的地方。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "沪上阿姐", "category": [{"level1": "方言", "level2": "上海话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV217_streaming"}, "text": "有两个日本人搭叉头，帮司机讲要去garden hotel，中文叫做花园饭店。下车个辰光伊拉发现，司机担伊拉送到了嘉定沪太路。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（上海话）", "category": [{"level1": "方言", "level2": "上海话"}], "labels": [], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_shanghai"}, "text": "有两个日本人搭叉头，帮司机讲要去garden hotel，中文叫做花园饭店。下车个辰光伊拉发现，司机担伊拉送到了嘉定沪太路。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "甜美台妹", "category": [{"level1": "方言", "level2": "台湾普通话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV025_streaming"}, "text": "你跟她看雪看月亮，看了一整夜，从诗词歌赋谈到哲学？我都没有跟你看雪看月亮，也没跟你从诗词歌赋谈到人生哲学", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "台普男声", "category": [{"level1": "方言", "level2": "台湾普通话"}], "labels": ["推荐"], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV227_streaming"}, "text": "大学生特种兵旅游之极限24小时吃遍南京。早上9点到南京了，10块的牛肉锅贴吃了，7块的酒酿赤豆小圆子吃了，十五块的咸蛋黄馄饨吃了，一块一个的煎包吃了。中午11点来新街口，蟹黄捞面吃了，一大碗蟹黄蟹肉淋上去香迷糊了家人们。下午2点来吃3块五一个的城南蒸米糕。6点又来新街口了，铁板鱿鱼吃了，鸭血粉丝汤吃了。8点来吃南京本土菜了，9点来夫子庙了，打包一杯茶颜悦色，吃撑了，回学校了。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（台普）", "category": [{"level1": "方言", "level2": "台湾普通话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_taipu"}, "text": "你跟她看雪看月亮，看了一整夜，从诗词歌赋谈到哲学？我都没有跟你看雪看月亮，也没跟你从诗词歌赋谈到人生哲学", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "港剧男神", "category": [{"level1": "方言", "level2": "粤语"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV026_streaming"}, "text": "呢种雀仔一世只可以落地一次，而𠮶次，就系佢死嘅时候。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "广东女仔", "category": [{"level1": "方言", "level2": "粤语"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV424_streaming"}, "text": "喂，Jenny、Jenny，阿May㖊日同我讲话嘞，楼下惠康隔篱嗰间茶餐厅嘅鱼蛋同炸鸡翼好味㖞，使唔使一阵间一齐去试下啊？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（粤语）", "category": [{"level1": "方言", "level2": "粤语"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_yueyu"}, "text": "呢种雀仔一世只可以落地一次，而嗰次，就系佢死嘅时候。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "相声演员", "category": [{"level1": "方言", "level2": "天津话"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV212_streaming"}, "text": "竹板儿这么一打啊，别的咱不夸。它薄皮儿大馅儿十八个褶儿，就像一朵花。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "重庆小伙", "category": [{"level1": "方言", "level2": "川渝话"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV019_streaming"}, "text": "一位北方朋友在重庆第一次吃火锅，不习惯调料，叫来服务员问：有麻酱没？服务员用纯正的重庆话说：没得，有扑克", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "四川甜妹儿", "category": [{"level1": "方言", "level2": "川渝话"}], "labels": ["推荐"], "gender": "女", "age": "少年/少女", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV221_streaming"}, "text": "麻雀跟倒喜鹊一起摆龙门阵。麻雀问：你是啥子鸟哦？喜鹊说：我是凤凰唦！麻雀：哪有你这么丑的凤凰哦？喜鹊：老子是烧锅炉的凤凰唦。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "重庆幺妹儿", "category": [{"level1": "方言", "level2": "川渝话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV423_streaming"}, "text": "我想吃樱桃，你帮我洗一点儿嘛。老子数到三，一，二……啥子，没得樱桃了？没得了不晓得去买唛，给你三秒钟，现在去，马上！", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "方言灿灿（成都）", "category": [{"level1": "方言", "level2": "川渝话"}], "labels": ["推荐"], "gender": "女", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV704_streaming", "language": "zh_chengdu"}, "text": "麻雀跟倒喜鹊一起摆龙门阵。麻雀问：你是啥子鸟哦？喜鹊说：我是凤凰唦！麻雀：哪有你这么丑的凤凰哦？喜鹊：老子是烧锅炉的凤凰唦。", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}, {"name": "乡村企业家", "category": [{"level1": "方言", "level2": "郑州话"}], "labels": [], "gender": "男", "age": "青年", "voice_config": [{"language": "无", "emotion": "无", "params": {"voice": "other", "voice_type": "BV214_streaming"}, "text": "冯爷，您这么早起来是要弄啥嘞？", "ssml": "<speak>\n        《<phoneme alphabet=\"py\" ph=\"xi1 xi1\">茜茜</phoneme>公主》是奥地利拍摄的历史题材的德语三部曲电影。故事改编自1952年出版的玛丽·布兰克·艾斯曼的同名小说。\n     </speak>"}]}]}