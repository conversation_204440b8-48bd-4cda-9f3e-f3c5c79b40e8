<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import type { ComponentPublicInstance } from 'vue';
import type { TreeSelectOption, UploadCustomRequestOptions } from 'naive-ui';
import { useLoadingBar } from 'naive-ui';
import role_tree_json from '@/assets/json/gpt_sovits/role.json';
import role_voice_json from '@/assets/json/gpt_sovits/voice.json';
import { postExtractSubtitle, postTTS, postUploadFile } from '@/service/api';
import { downloadFile, generateUUID } from '@/utils/common';
import { audioBufferToBlobUrl, audioBufferToFile, fileToArrayBuffer } from '@/utils/audio';
const role_tree = role_tree_json as TreeSelectOption[];
const role_voice = role_voice_json as { [key: string]: CommonType.Option[] };

const textInput = ref<ComponentPublicInstance | null>(null);

// const loading = useLoadingBar();
// const loadingBar = useLoadingBar();
// const uploadLoading = ref(false);
const loading = ref(false);
const extract_loading = ref(false);
const createDefaultModel = (): Api.Audio.TextToSpeech => {
  return {
    text: '',
    model: '',
    role: '丁真',
    prompt_audio: '',
    voice: '',
    text_seed: 0,
    audio_seed: 0,
    custom_voice: '',
    prompt_text: '',
    language: '中文',
    file_path: '',
    timbre: '',
    tone: '[oral_2]',
    prompt_oral: 0,
    prompt_laugh: 0,
    prompt_break: 0,
    prompt_oral_status: false,
    prompt_laugh_status: false,
    prompt_break_status: false,
    temperature: 0.1,
    top_k: 20,
    top_p: 0.1
  };
};
const audioBlobUrl = ref<string>('');
const ttsAudioBlobUrl = ref<string>('');
const model: Api.Audio.TextToSpeech = reactive(createDefaultModel());

const langOption: CommonType.Option[] = [
  { label: '中文', value: '中文' },
  { label: '英文', value: '英文' },
  { label: '日文', value: '日文' }
];
const audioOption = ref<CommonType.Option[]>([]);
const initDefault = () => {
  console.log('initDefault');
  if (model.role === '自定义') {
    model.prompt_audio = '';
    model.prompt_text = '';
  } else if (model.role) {
    audioOption.value = [{ label: '自定义', value: 'custom' }, ...(role_voice[model.role] || [])];

    if (audioOption.value.length >= 2) {
      model.prompt_audio = audioOption.value[1].value;
      model.prompt_text = audioOption.value[1].label;
      audioBlobUrl.value = `/proxy-default/data/voice/model/${model.prompt_audio}`;
    }
  }
};
watch(
  () => model.role,
  () => {
    initDefault();
  }
);
watch(
  () => model.prompt_audio,
  () => {
    const item = audioOption.value.find(e => e.value === model.prompt_audio);
    if (item) {
      model.prompt_text = item.label;
      audioBlobUrl.value = `/proxy-default/data/voice/model/${model.prompt_audio}`;
    }
  },
  { immediate: false }
);

onMounted(() => {
  initDefault();
});
const make = async () => {
  loading.value = true;
  ttsAudioBlobUrl.value = '';
  const res = await postTTS(model);
  if (res && res.data) {
    ttsAudioBlobUrl.value = `${res.data.b64}`;
  }
  loading.value = false;
};
const loadingBar = useLoadingBar();
const uploadLoading = ref(false);
const fileName = ref<string>('');

const emptyFileName = computed(() => fileName.value === '');
const customromptAudio = computed(() => model.prompt_audio === 'custom');
const extract = async () => {
  try {
    const params: Api.Media.Subtitle = {
      file_path: model.file_path,
      prompt_audio_b64: '',
      model: 'Gpt-4o mini',
      format: 'text',
      gamecode: '默认',
      translate: ''
    };
    model.prompt_text = '';
    extract_loading.value = true;
    postExtractSubtitle(params)
      .then(res => {
        if (res.data) {
          model.prompt_text = res.data.text;
        }
      })
      .finally(() => {
        extract_loading.value = false;
      });
  } catch (error) {
    console.log(error);
    window.$message?.error('音频转化失败,请上传音频文件');
  }
};
const customRequestRefAudio = async ({ file, data, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  // console.log(file);
  loadingBar.start();
  uploadLoading.value = true;

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }
  // formData.append(file.name, file.file as File);
  // 如果是视频文件
  // if xxx='mp4' || mov
  // 如果是视频，先转音频
  if (file.type === 'video/mp4') {
    // mp3=>audio/mpeg
    // mp4=>video/mp4
    const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // 创建一个AudioContext
    const audioCtx = new AudioContext();
    // arrayBuffer转audioBuffer
    const audioBuffer = await audioCtx.decodeAudioData(buffer);
    const uuid = generateUUID();
    const newFileName = `${uuid}.wav`;
    const audioFile = audioBufferToFile(audioBuffer, newFileName);
    formData.append('file', audioFile as File);
    const blobUrl = await audioBufferToBlobUrl(audioBuffer);
    audioBlobUrl.value = blobUrl;
  } else {
    if (file.file) {
      const blobUrl = URL.createObjectURL(file!.file);
      audioBlobUrl.value = blobUrl;
    }
    formData.append('file', file.file as File);
  }

  postUploadFile('separate', formData, progressEvent => {
    if (progressEvent.progress) {
      onProgress({ percent: progressEvent!.progress * 100 });
    }
  })
    .then(res => {
      console.log(res);
      if (res.data) model.file_path = res.data.file_path;
      fileName.value = file.name;
      extract();
      onFinish();
    })
    .catch(error => {
      console.log(error);
      onError();
    })
    .finally(() => {
      loadingBar.finish();
      uploadLoading.value = false;
    });
};
const onRemove = () => {
  audioBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};
const removeFile = () => {
  audioBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};

const onUpdatedRole = (value: string) => {
  console.log('onUpdatedRole');
  model.role = value;
};

// 添加音频控制相关的状态
const resultAudio = ref<HTMLAudioElement>();

// 下载音频文件
const downloadAudio = (url: string) => {
  if (!url) {
    // message.error('暂无音频文件');
    return;
  }
  const fn = fileName.value.replace(/\.[^/.]+$/, '');
  downloadFile(url, `${fn}_tts.wav`);
};

// 设置音频播放速率
const setPlaybackRate = (rate: number) => {
  if (resultAudio.value) {
    resultAudio.value.playbackRate = rate;
  }
};
</script>

<template>
  <NScrollbar class="w-full">
    <div class="h-[calc(100vh-13em)] flex gap-16px">
      <!-- 左侧操作区域 -->
      <div class="panel-container shrink-0">
        <NCard class="h-full">
          <NSpace vertical justify="space-around">
            <NGrid cols="3" :x-gap="10">
              <NGridItem span="3">
                <NInput
                  ref="textInput"
                  v-model:value="model.text"
                  type="textarea"
                  placeholder="请输入要合成的文本"
                  class="min-h-200px"
                />
              </NGridItem>
            </NGrid>
            <NGrid :cols="1" class="p-2">
              <NGi>
                <div class="grid grid-cols-2 gap-4">
                  <!-- 左侧表单 -->
                  <div class="form-container">
                    <NFormItem :show-feedback="false" label="角色" label-placement="left" class="mb-3">
                      <NCascader
                        v-model:value="model.role"
                        :options="role_tree"
                        show-path
                        filterable
                        virtual-scroll
                        expand-trigger="click"
                        :on-update:value="onUpdatedRole"
                        class="w-full"
                      />
                    </NFormItem>
                    <NFormItem :show-feedback="false" label="语言" label-placement="left" class="mb-3">
                      <NSelect v-model:value="model.language" :options="langOption" size="medium" class="w-full" />
                    </NFormItem>
                    <NFormItem :show-feedback="false" label="参考" label-placement="left" class="mb-3">
                      <NSelect v-model:value="model.prompt_audio" :options="audioOption" size="medium" class="w-full" />
                    </NFormItem>
                  </div>

                  <!-- 右侧音频和文本 -->
                  <div class="audio-container">
                    <div v-if="!customromptAudio" class="audio-player-container mb-3">
                      <audio :src="audioBlobUrl" controls class="h-[34px] w-full"></audio>
                    </div>
                    <NSpin v-else :show="uploadLoading" class="mb-3">
                      <NUpload
                        class="relative w-full"
                        action=""
                        :custom-request="customRequestRefAudio"
                        accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
                        :on-remove="onRemove"
                        :show-file-list="false"
                      >
                        <NUploadDragger>
                          <NButton v-if="!emptyFileName" text class="absolute right-1 top-1" @click.stop="removeFile">
                            <SvgIcon icon="f7:delete-left" class="h-5 w-5" />
                          </NButton>
                          <div class="grid min-h-54px items-center justify-center">
                            <audio :src="audioBlobUrl" controls class="w-full"></audio>
                          </div>
                        </NUploadDragger>
                      </NUpload>
                    </NSpin>
                    <NInput
                      ref="textInput"
                      v-model:value="model.prompt_text"
                      type="textarea"
                      :rows="2"
                      placeholder=""
                      class="custom-input w-full"
                    >
                      <template #suffix>
                        <div class="h-7 w-full flex justify-end">
                          <NButton
                            v-if="customromptAudio"
                            size="tiny"
                            dashed
                            :loading="extract_loading"
                            @click.stop="extract"
                          >
                            提取
                          </NButton>
                        </div>
                      </template>
                    </NInput>
                  </div>
                </div>
              </NGi>
            </NGrid>
            <NSpace justify="center" class="mt-3">
              <NButton type="info" :loading="loading" class="h-45px w-60 p-3" @click="make">生成</NButton>
            </NSpace>
          </NSpace>
        </NCard>
      </div>

      <!-- 右侧结果展示区域 -->
      <div class="panel-container">
        <NCard class="h-full">
          <div class="flex flex-col gap-2">
            <NSpin :show="loading">
              <audio ref="resultAudio" :src="ttsAudioBlobUrl" controls class="w-full"></audio>
            </NSpin>
            <div class="flex justify-center">
              <NButtonGroup>
                <NButton size="small" @click="downloadAudio(ttsAudioBlobUrl)">
                  <template #icon>
                    <SvgIcon icon="mdi:download" />
                  </template>
                  下载
                </NButton>
                <NButton size="small" @click="setPlaybackRate(0.5)">0.5x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.0)">1.0x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.5)">1.5x</NButton>
              </NButtonGroup>
            </div>
          </div>
        </NCard>
      </div>
    </div>
  </NScrollbar>
</template>

<style scoped lang="scss">
:deep(.custom-input) {
  .n-input-wrapper {
    display: flex;
    flex-direction: column;
  }
}

// 添加响应式布局样式
.form-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.audio-container {
  display: flex;
  flex-direction: column;
}

.audio-player-container {
  height: 34px;
  display: flex;
  align-items: center;
}

:deep(.n-form-item) {
  margin-bottom: 8px;

  .n-form-item-label {
    min-width: 40px;
    text-align: right;
    padding-right: 8px;
  }
}

:deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;

  .n-card-header {
    flex-shrink: 0;
  }

  .n-card__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
}

// 左右均等布局样式
.panel-container {
  width: 50%;
  max-width: 820px;
  padding: 0 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  @media screen and (max-width: 1200px) {
    max-width: 600px;
  }
}

@media screen and (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .panel-container {
    width: 100%;
    max-width: 100%;
  }
}
</style>
