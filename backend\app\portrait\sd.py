import logging
import random
import httpx
from fastapi import APIRouter, Depends, BackgroundTasks
from config import app_settings
from models.users import User, get_request_user
from utils.exceptions import ClientVisibleException
from utils.redis import redis
import json
import asyncio
from utils.database import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
from collections import defaultdict

router = APIRouter()
logger = logging.getLogger(__name__)

CUDA = app_settings.cuda

# 固定端口（持续运行的服务）
fixed_ports = [7903, 7904]  # server1 和 server2
# 动态端口（可动态启动/停止的服务）
dynamic_ports = [7901, 7902]  # server3 和 server4

# 空闲 CPU 使用率阈值（低于该值被视为空闲）
IDLE_THRESHOLD = 10.0
# 每次检查的间隔（秒）
CHECK_INTERVAL = 5
# 10 分钟内的最大空闲计数
MAX_IDLE_TIME = 120  # 600 秒 / 5 秒
# 最大失败次数
MAX_FAILURES = 10

monitor_tasks = defaultdict(asyncio.Task)
monitor_tasks_lock = asyncio.Lock()

async def check_cpu_usage(port: int) -> float:
    """获取指定端口的 CPU 使用率"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.post(
                f"{CUDA}:5003/sd/get_cpu",
              # f"http://172.17.0.253:5005/sd/get_cpu",
                json={"port": port}
            )
            response.raise_for_status()
            data = response.json()
            cpu_usage = float(data.get("cpu_usage", 0))
            logger.info(f"端口 {port} 的 CPU 使用率：{cpu_usage}")
            return cpu_usage
    except Exception as e:
        logger.error(f"获取 CPU 使用率失败（端口 {port}）：{e}")
        return None


async def stop_service(port: int) -> bool:
  """停止指定端口的服务，根据端口类型调用不同的接口"""
  try:
    if port in fixed_ports:
      return True
    async with httpx.AsyncClient(timeout=10) as client:
      response = await client.post(
        f"http://172.17.0.253:5005/sd/stop_webui",
        json={"port": port}
      )
    response.raise_for_status()
    data = response.json()
    return data.get("stu") == 0
  except Exception as e:
    logger.error(f"停止服务失败（端口 {port}）：{e}")
    return False


async def monitor_cpu_usage(port: int, redis_client):
  """
  监控指定端口的 CPU 使用率，空闲超时后停止服务或重置状态。

  参数：
      port: 要监控的端口号
      redis_client: Redis 客户端实例
  """
  failure_count = 0

  try:
    while True:
      # 从 Redis 获取服务列表
      sdserver_list = await redis_client.exec("lrange", "sdserver", 0, -1)
      sdserver = [json.loads(item) for item in sdserver_list]

      # 在列表中查找端口信息
      port_info = next((item for item in sdserver if item['port'] == port), None)
      if not port_info:
        logger.warning(f"端口信息不存在 {port}，停止监控。")
        break

      # 检查 CPU 使用率
      cpu_usage = await check_cpu_usage(port)

      # 如果获取 CPU 使用率失败
      if cpu_usage is None:
        failure_count += 1
        logger.debug(f"无法获取端口 {port} 的 CPU 使用率。失败计数：{failure_count}/{MAX_FAILURES}")

        if failure_count >= MAX_FAILURES:
          logger.warning(f"端口 {port} 达到最大失败次数，处理服务。")
          if port in fixed_ports:
            # 对于固定端口，直接重置状态
            logger.info(f"固定端口 {port}，重置状态，不停止服务。")
            # 重置端口信息
            port_info.update({
              "freeTime": 0,
              "user": "",
              "company": "",
              "status": 1,
              "redirect_url": ""
            })
          else:
            # 停止动态端口的服务
            await stop_service(port)
            # 重置端口信息
            port_info.update({
              "freeTime": 0,
              "user": "",
              "company": "",
              "status": 1,
              "redirect_url": ""
            })
          index = sdserver.index(port_info)
          await redis_client.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
          logger.info(f"端口 {port} 已重置 freeTime 为 0，并清除其他数据。")
          break
      else:
        # 成功获取 CPU 使用率后重置失败计数器
        failure_count = 0

        # 检查 CPU 使用率是否低于空闲阈值
        if cpu_usage < IDLE_THRESHOLD:
          port_info["freeTime"] += 1
          logger.info(f"端口 {port} 空闲计数：{port_info['freeTime']}")
        else:
          port_info["freeTime"] = 0

        # 检查空闲时间是否超过最大值并停止服务或重置状态
        if port_info["freeTime"] >= MAX_IDLE_TIME:
          logger.info(f"端口 {port} 空闲超时，处理服务。")
          if port in fixed_ports:
            # 对于固定端口，直接重置状态
            logger.info(f"固定端口 {port}，重置状态，不停止服务。")
            # 重置端口信息
            port_info.update({
              "freeTime": 0,
              "user": "",
              "company": "",
              "status": 1,
              "redirect_url": ""
            })
          else:
            # 停止动态端口的服务
            await stop_service(port)
            # 重置端口信息
            port_info.update({
              "freeTime": 0,
              "user": "",
              "company": "",
              "status": 1,
              "redirect_url": ""
            })
          index = sdserver.index(port_info)
          await redis_client.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
          break

        # 更新 Redis 列表中的端口信息
        index = sdserver.index(port_info)
        await redis_client.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))

      # 等待下次检查
      await asyncio.sleep(CHECK_INTERVAL)

  except Exception as e:
    logger.error(f"监控任务异常（端口 {port}）：{e}")
    try:
      # 重置端口信息
      port_info.update({
        "freeTime": 0,
        "user": "",
        "company": "",
        "status": 1,
        "redirect_url": ""
      })
      index = sdserver.index(port_info)
      await redis_client.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
    except Exception as ex:
      logger.error(f"无法更新 Redis 数据（端口 {port}）：{ex}")

@router.get("/start_webui")
async def start_webui(
    background_tasks: BackgroundTasks,
    port: int = None,  # 接受前端传入的端口
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    try:
        # 查询数据库以获取对应 user.username 的公司和昵称
        result = await db.execute(
          select(User.company, User.nickname).where(User.username == user.username)
        )
        user_data = result.first()
        if not user_data:
          raise ClientVisibleException("用户信息不存在")
        user_company, user_nickname = user_data

        # 从 Redis 获取服务列表
        sdserver_list = await redis.exec("lrange", "sdserver", 0, -1)
        sdserver = [json.loads(item) for item in sdserver_list]

        if port in fixed_ports:
            # 固定端口，直接拼接链接，无需请求启动接口
            constructed_url = f"http://172.17.0.253:{port}"
            status = 2  # 状态为 繁忙

            # 更新 Redis 中的端口信息
            port_info = None
            index = None
            for idx, item in enumerate(sdserver):
                if item['port'] == port:
                    port_info = item
                    index = idx
                    break

            if port_info:
                # 更新已有的端口信息
                port_info.update({
                    "freeTime": 0,
                    "redirect_url": constructed_url,
                    "status": status,
                    "user": user_nickname,
                    "company": user_company
                })
                await redis.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
            else:
                # 添加新的端口信息
                port_info = {
                    "port": port,
                    "freeTime": 0,
                    "redirect_url": constructed_url,
                    "status": status,
                    "user": user_nickname,
                    "company": user_company
                }
                await redis.exec("rpush", "sdserver", json.dumps(port_info, sort_keys=True))

            # 启动 CPU 监控任务
            background_tasks.add_task(monitor_cpu_usage, port, redis)

            # 返回重定向响应
            return {
                "code": "0000",
                "data": {
                    "redirect_url": constructed_url,
                    "stu": status
                }
            }

        else:
            # 未传入固定端口，按正常流程启动动态端口服务
            # 请求 GPU 显存信息
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(
                    f"{CUDA}:5003/sd/gpu_memory"
                    # f"http://172.17.0.253:5005/sd/gpu_memory"
                )
                response.raise_for_status()
                gpu_memory = response.json().get("gpu_memory", {})
                free_memory = gpu_memory.get("free_memory", 0)

            # 检查可用显存是否小于 4GB
            if free_memory < 4.0:
                return {
                    "code": "0000",
                    "data": {
                        "msg": "资源不足，启动失败"
                    },
                  "msg": "资源不足，启动失败"
                }

            # 获取已使用的端口列表
            used_ports = [item['port'] for item in sdserver if item['status'] != 1]

            # 如果所有动态端口都在使用中，返回提示
            if len([p for p in used_ports if p in dynamic_ports]) == len(dynamic_ports):
                raise ClientVisibleException("资源已满，请稍后重试")

            # 随机选择一个未使用的动态端口
            available_ports = list(set(dynamic_ports) - set(used_ports))
            selected_port = random.choice(available_ports)

            # 请求 CUDA 接口启动 WebUI
            async with httpx.AsyncClient(timeout=90) as client:
                response = await client.post(
                    f"{CUDA}:5003/sd/start_webui",
                  # f"http://172.17.0.253:5005/sd/start_webui",
                    json={
                        "port": selected_port,
                        "company": user_company
                    }
                )
                response.raise_for_status()
                data = response.json()

                logger.debug(f"start webui info:{data}")

                if data.get("stu") != 2:
                    raise ClientVisibleException("生成失败，请重试")

                # 拼接链接
                constructed_url = f"http://172.17.0.253:{selected_port}"
                status = data.get("stu", 2)

            # 更新 Redis 中的端口信息
            port_info = None
            index = None
            for idx, item in enumerate(sdserver):
                if item['port'] == selected_port:
                    port_info = item
                    index = idx
                    break

            if port_info:
                # 更新已有的端口信息
                port_info.update({
                    "freeTime": 0,
                    "redirect_url": constructed_url,
                    "status": status,
                    "user": user_nickname,
                    "company": user_company
                })
                await redis.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
            else:
                # 添加新的端口信息
                port_info = {
                    "port": selected_port,
                    "freeTime": 0,
                    "redirect_url": constructed_url,
                    "status": status,
                    "user": user_nickname,
                    "company": user_company
                }
                await redis.exec("rpush", "sdserver", json.dumps(port_info, sort_keys=True))

                # 启动 CPU 监控任务并保存任务对象
            async with monitor_tasks_lock:
              task = asyncio.create_task(monitor_cpu_usage(port, redis))
              monitor_tasks[port] = task

            # 返回重定向响应
            return {
                "code": "0000",
                "data": {
                    "redirect_url": constructed_url,
                    "stu": status
                }
            }

    except httpx.HTTPStatusError as e:
        raise ClientVisibleException("生成失败，请重试") from e
    except Exception as e:
        logger.error(f"启动 WebUI 失败：{e}")
        raise ClientVisibleException("资源机异常，请通知管理员修复") from e


@router.get("/start_fixed_webui")
async def start_fixed_webui(
    background_tasks: BackgroundTasks,
    port: int,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    if port not in fixed_ports:
        raise ClientVisibleException("无效端口，请重试")

    try:
        # 请求 CUDA 接口获取 URL
        async with httpx.AsyncClient(timeout=90) as client:
            response = await client.post(
                f"{CUDA}:5003/sd/generate_url",
              # f"http://172.17.0.253:5005/sd/generate_url",
                json={"port": port}
            )
            response.raise_for_status()
            data = response.json()
            public_url = data.get("url", "")
            status = data.get("stu", 1)

        # 更新 Redis 中的端口信息
        sdserver_list = await redis.exec("lrange", "sdserver", 0, -1)
        sdserver = [json.loads(item) for item in sdserver_list]

        port_info = next((item for item in sdserver if item['port'] == port), None)
        if port_info:
            # 更新已有的端口信息
            port_info.update({
                "freeTime": 0,
                "redirect_url": public_url,
                "status": status,
                "user": user.username,
                "company": ""
            })
            index = sdserver.index(port_info)
            await redis.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))
        else:
            # 初始化端口信息
            port_info = {
                "port": port,
                "freeTime": 0,
                "redirect_url": public_url,
                "status": status,
                "user": user.username,
                "company": ""
            }
            await redis.exec("rpush", "sdserver", json.dumps(port_info, sort_keys=True))

        # 启动 CPU 监控任务
        background_tasks.add_task(monitor_cpu_usage, port, redis)

        # 返回重定向响应
        return {
            "code": "0000",
            "data": {
                "redirect_url": public_url,
                "status": status
            }
        }

    except Exception as e:
        logger.error(f"启动固定端口 WebUI 失败：{e}")
        raise ClientVisibleException("资源机异常，请通知管理员修复") from e


class StopWebUIRequest(BaseModel):
  port: int


@router.post("/stop_webui")
async def stop_webui_endpoint(
    request: StopWebUIRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    port = request.port
    # 查询数据库以获取对应 user.username 的公司和昵称
    result = await db.execute(
        select(User.nickname).where(User.username == user.username)
    )
    user_data = result.first()
    if not user_data:
        raise ClientVisibleException("用户信息未找到")
    user_nickname = user_data[0]

    # 更新 Redis 中的端口信息
    sdserver_list = await redis.exec("lrange", "sdserver", 0, -1)
    sdserver = [json.loads(item) for item in sdserver_list]
    port_info = next((item for item in sdserver if item['port'] == port), None)

    # 判断用户是否与 port_info 中的 user 一致
    if port_info and port_info.get("user") != user_nickname:
        # 如果不是同一用户，则判断 freeTime 是否小于 4
        if port_info.get("freeTime") < 4:
            logger.info(
                f"用户 {user_nickname} 尝试停止非自己创建的服务（端口 {port} 用户:{port_info.get('user')}）。"
            )
            raise ClientVisibleException("用户正在创作中，请稍等。")

    try:
        if port not in dynamic_ports + fixed_ports:
            raise ClientVisibleException("无效端口，请重试")

        # 停止服务
        if await stop_service(port):
            logger.info(f"端口 {port} 成功停止并清理。")

            # 取消对应的后台任务
            async with monitor_tasks_lock:
                task = monitor_tasks.get(port)
                if task:
                    task.cancel()
                    del monitor_tasks[port]
                    logger.info(f"已取消端口 {port} 的 CPU 监控任务。")

            if port_info:
                # 重置端口信息
                port_info.update({
                    "freeTime": 0,
                    "user": "",
                    "company": "",
                    "status": 1,
                    "redirect_url": ""
                })
                index = sdserver.index(port_info)
                await redis.exec("lset", "sdserver", index, json.dumps(port_info, sort_keys=True))

            return {"code": "0000", "message": f"端口 {port} 已停止"}
        else:
            logger.error(f"无法停止服务（端口 {port}）。")
            raise ClientVisibleException("停止失败，请重试")

    except Exception as e:
        logger.error(f"停止服务异常（端口 {port}）：{e}")
        raise ClientVisibleException("资源机异常，请通知管理员修复") from e

@router.get("/gpu_memory")
async def get_cuda_memory():
    """
    查询 GPU 显存使用情况
    """
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(
                f"{CUDA}:5003/sd/gpu_memory"
                # f"http://172.17.0.253:5005/sd/gpu_memory"
            )
            response.raise_for_status()
            # 从接口返回的 gpu_memory 字段中提取数据
            gpu_memory = response.json().get("gpu_memory", {})
            # 总显存
            total_memory = gpu_memory.get("total_memory", 0)
            # 已用显存
            used_memory = gpu_memory.get("used_memory", 0)
            # 空闲显存
            free_memory = gpu_memory.get("free_memory", 0)

            return {
                "code": "0000",
                "data": {
                    "total_memory": total_memory,
                    "used_memory": used_memory,
                    "free_memory": free_memory
                }
            }
    except Exception as e:
        logger.error(f"获取 GPU 显存信息失败：{e}")
        raise ClientVisibleException("资源机异常") from e

@router.get("/get_servers_status")
async def get_servers_status():
    """
    获取四个服务的状态
    """
    try:
        # 从 Redis 获取服务列表
        sdserver_list = await redis.exec("lrange", "sdserver", 0, -1)
        sdserver = [json.loads(item) for item in sdserver_list]

        # 端口与服务器名称的映射
        servers = {
            "server1": 7903,
            "server2": 7904,
            "server3": 7901,
            "server4": 7902
        }

        data = {}

        for server_name, port in servers.items():
            # 查找对应端口的信息
            port_info = next((item for item in sdserver if item['port'] == port), None)
            if port_info:
                # 获取状态
                data[server_name] = {
                  'stu': port_info.get('status', 0),
                  'user': port_info.get('user', ''),
                  'freetime': port_info.get('freeTime', 0)
                }  # 如果有记录，返回 status，0代表获取异常，redis记录问题
            else:
                # 如果没有记录，stu 返回 1
                data[server_name] = {'stu': 1}

        # 返回结果
        return {"code": "0000", "data": data}

    except Exception as e:
        logger.error(f"获取服务状态异常：{e}")
        raise ClientVisibleException("资源机异常") from e
