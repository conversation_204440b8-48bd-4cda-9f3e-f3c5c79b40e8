<script setup lang="ts">
import { computed, ref, watch } from 'vue';

// 定义props类型接口
interface VideoInfoCardProps {
  isGenerating?: boolean;
  progress?: number;
  videoUrl?: string;
  imageUrl?: string;
  prompt?: string;
  taskId?: string;
  secondLength?: number;
  status?: string;
  failReason?: string;
  queuePosition?: number;
  taskParams?: any;
  submitTime?: string;
  promptMediaUrl?: string;
  model?: string;
}

// 使用类型声明和默认值
const props = withDefaults(defineProps<VideoInfoCardProps>(), {
  isGenerating: false,
  progress: 0,
  videoUrl: '',
  imageUrl: '',
  prompt: '创意描述',
  taskId: '',
  secondLength: 5,
  status: 'submitted',
  failReason: '',
  queuePosition: 0,
  taskParams: () => ({}),
  submitTime: '',
  promptMediaUrl: '',
  model: 'framepack'
});

// 定义emit发送事件给父组件
const emit = defineEmits<{
  (
    e: 'generateSimilar',
    params: { imageUrl: string; prompt: string; secondLength: number; hasImage: boolean; model: string }
  ): void;
  // (e: 'download', url: string): void;
  (e: 'cancel', taskId: string): void;
  (e: 'delete', taskId: string): void;
}>();

// 存储最高进度值的ref
const highestProgress = ref<number>(0);

// 监听progress变化，更新最高进度值
watch(
  () => props.progress,
  newValue => {
    if (newValue > highestProgress.value) {
      highestProgress.value = newValue;
    }
  },
  { immediate: true }
);

// 计算标签，包含模型名称和生成秒数
const tags = computed(() => {
  return [props.model, `${props.secondLength} s`];
});

// 计算提交时间格式化
const formattedSubmitTime = computed(() => {
  if (!props.submitTime) return '';
  try {
    return new Date(props.submitTime).toLocaleString();
  } catch (e) {
    return props.submitTime;
  }
});

// 默认占位图URL
const defaultPlaceholderImage =
  'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202505/64c7ca42cb11a33215346f73eea52c16_20250528205813.png';

// 计算当前显示的图片
const displayImageUrl = computed(() => {
  // 检查是否有接口返回的图片数据
  if (props.promptMediaUrl && props.promptMediaUrl.trim() !== '') {
    return props.promptMediaUrl;
  }

  // 如果没有promptMediaUrl，但有task_params.image_base64，则使用它
  if (props.taskParams?.image_base64 && props.taskParams.image_base64.trim() !== '') {
    return props.taskParams.image_base64;
  }

  // 如果两者都没有，则使用默认占位图
  return defaultPlaceholderImage;
});

// 计算稳定的进度值，确保进度不会减少
const stableProgress = computed((): number => {
  return highestProgress.value;
});

// 点击生成同款按钮，触发父组件的方法
const onGenerateSimilar = () => {
  // 判断是否有图片
  const hasImage = Boolean(props.taskParams?.image_base64 && props.taskParams.image_base64.trim() !== '');

  emit('generateSimilar', {
    imageUrl: displayImageUrl.value,
    prompt: props.prompt,
    secondLength: props.secondLength,
    hasImage,
    model: props.model
  });
};

// 点击取消按钮，触发取消事件
const onCancel = () => {
  if (props.taskId) {
    emit('cancel', props.taskId);
  }
};

// 点击删除按钮，触发删除事件
const onDelete = () => {
  if (props.taskId) {
    emit('delete', props.taskId);
  }
};

// 点击下载按钮，触发下载事件
// const onDownload = () => {
//   if (props.videoUrl) {
//     emit('download', props.videoUrl);
//   }
// };
</script>

<template>
  <div class="w-full flex gap-5">
    <!-- 左侧视觉展示部分 -->
    <div class="relative h-80 w-2/3 overflow-hidden rounded-lg">
      <template v-if="isGenerating || status === 'submitted' || status === 'in_progress' || status === 'not_start'">
        <!-- 背景图片（模糊效果） -->
        <div
          class="bg-blur absolute inset-0 bg-cover bg-center"
          :style="{ backgroundImage: `url(${displayImageUrl})` }"
        ></div>

        <!-- 根据状态显示不同UI -->
        <div class="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white">
          <!-- 排队中状态 -->
          <template v-if="status === 'submitted' || status === 'not_start'">
            <div class="flex flex-col items-center">
              <SvgIcon icon="ph:hourglass-medium" class="mb-4 text-5xl text-white/80" />
              <div v-if="queuePosition > 0" class="text-center">
                <div class="mb-1 font-bold">正在排队中</div>
                <div>前方还有 {{ queuePosition }} 个任务</div>
              </div>
              <div v-else class="text-center">
                <div class="mb-1 font-bold">正在排队中</div>
                <div>即将开始处理</div>
              </div>
            </div>
          </template>

          <!-- 生成中状态 -->
          <template v-else>
            <div class="mb-4 w-64">
              <NProgress
                type="line"
                :percentage="stableProgress"
                :color="{ stops: ['#E3F2FD', '#2080f0'] }"
                processing
                indicator-placement="inside"
              />
            </div>
            <div class="mt-2 text-white/90">内容生成中，退出页面不影响生成进度</div>
          </template>
        </div>
      </template>
      <template v-else-if="status === 'failure'">
        <!-- 生成失败：显示背景图片（模糊效果）和错误信息 -->
        <div
          class="bg-blur absolute inset-0 bg-cover bg-center"
          :style="{ backgroundImage: `url(${displayImageUrl})` }"
        ></div>

        <!-- 显示错误信息 -->
        <div class="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white">
          <div class="flex flex-col items-center">
            <SvgIcon icon="material-symbols:error-outline" class="mb-2 text-5xl text-red-400" />
            <div class="text-center text-red-300">
              <div class="mb-1 font-bold">生成失败</div>
              <div class="max-w-md">{{ failReason || '未知错误' }}</div>
            </div>
          </div>
        </div>
      </template>
      <template v-else-if="status === 'canceled'">
        <!-- 用户取消：显示背景图片（模糊效果）和取消信息 -->
        <div
          class="bg-blur absolute inset-0 bg-cover bg-center"
          :style="{ backgroundImage: `url(${displayImageUrl})` }"
        ></div>

        <!-- 显示取消信息 -->
        <div class="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white">
          <div class="flex flex-col items-center">
            <SvgIcon icon="material-symbols:cancel" class="mb-2 text-5xl text-yellow-400" />
            <div class="text-center text-yellow-300">
              <div class="mb-1 font-bold">任务已取消</div>
              <div class="max-w-md">用户取消任务</div>
            </div>
          </div>
        </div>
      </template>
      <template v-else-if="status === 'success' && videoUrl">
        <!-- 只有在状态为success且有视频URL时才渲染视频 -->
        <video :src="videoUrl" controls class="h-full w-full object-contain"></video>
      </template>
    </div>

    <!-- 右侧信息部分 -->
    <div class="h-80 w-1/3 flex flex-col justify-between gap-2 pb-1">
      <!-- 标题和描述部分 -->
      <div class="flex flex-col gap-2">
        <div>
          <NText class="mr-5 text-lg font-medium">视频描述</NText>
          <NText depth="3" class="mt-2 text-xs">{{ formattedSubmitTime }}</NText>
        </div>

        <!-- 缩略图 -->
        <div v-if="props.taskParams?.image_base64">
          <NImage :src="displayImageUrl" class="rounded-md" :width="85" :height="85" object-fit="cover" />
        </div>

        <div>
          <NEllipsis :line-clamp="5" class="mt-1 text-sm text-gray-500">{{ prompt }}</NEllipsis>
        </div>

        <!-- 标签 -->
        <div class="mt-2 flex gap-2">
          <NTag v-for="tag in tags" :key="tag" type="info" size="small">
            {{ tag }}
          </NTag>
        </div>
      </div>

      <!-- 按钮部分 - 固定在底部 -->
      <!-- 取消状态 -->
      <div v-if="status !== 'success' && status !== 'canceled' && status !== 'failure'" class="flex gap-2">
        <NButton type="primary" size="small" secondary @click="onCancel">
          <template #icon>
            <SvgIcon icon="material-symbols:cancel" />
          </template>
          {{ status === 'in_progress' ? '停止创作' : '取消排队' }}
        </NButton>
      </div>

      <!-- 操作按钮 - 在成功状态显示 -->
      <div v-if="status === 'success'" class="flex gap-2">
        <NButton type="primary" size="small" secondary @click="onGenerateSimilar">
          <template #icon>
            <SvgIcon icon="solar:star-fall-broken" />
          </template>
          生成同款
        </NButton>

        <NPopconfirm @positive-click="onDelete">
          <template #trigger>
            <NButton type="primary" size="small" secondary>
              <template #icon>
                <SvgIcon icon="material-symbols:delete" />
              </template>
              删除
            </NButton>
          </template>
          确认删除这个视频记录吗？删除后无法恢复。
        </NPopconfirm>
      </div>

      <!-- 失败和取消状态的操作按钮 -->
      <div v-if="status === 'failure' || status === 'canceled'" class="flex gap-2">
        <NButton type="primary" size="small" secondary @click="onDelete">
          <template #icon>
            <SvgIcon icon="material-symbols:delete" />
          </template>
          删除
        </NButton>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 用于复杂样式 */
.bg-blur {
  filter: blur(20px);
}

/* 自定义flex增长因子 */
// .flex-grow-2 {
//   flex-grow: 2;
// }

:deep(.n-image) {
  justify-content: flex-start !important;
}
</style>
