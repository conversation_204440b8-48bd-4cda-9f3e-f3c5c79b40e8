import COS from 'cos-js-sdk-v5';
import { getTempCredentials } from '@/service/api/upload';

// COS实例
let cosInstance: COS | null = null;
// 凭证过期时间戳
let credentialsExpireTime: number = 0;

// COS配置
export interface CosConfig {
  Bucket: string;
  Region: string;
  BaseUrl?: string; // COS访问域名
}

// COS临时凭证
export interface CosCredentials {
  TmpSecretId: string;
  TmpSecretKey: string;
  Token: string;
}

// 临时凭证API响应
interface CredentialsResponse {
  code: string;
  data: {
    Credentials: {
      TmpSecretId: string;
      TmpSecretKey: string;
      Token: string;
    };
    ExpiredTime: number;
    Expiration: string;
    RequestId: string;
  };
  msg: string;
}

// 默认的COS配置
let cosConfig: CosConfig = {
  Bucket: 'ai-1259109643',
  Region: 'ap-hongkong',
  BaseUrl: 'https://xxfpfg-gg.gdsre.cn/'
};

/**
 * 初始化COS实例
 *
 * @param config COS配置
 */
export async function initCOS(config: CosConfig): Promise<COS> {
  cosConfig = { ...cosConfig, ...config };

  try {
    // 获取临时凭证
    const res = (await getTempCredentials()) as unknown as CredentialsResponse;

    if (!res.data) {
      throw new Error('获取临时凭证失败: 未返回数据');
    }

    const { data } = res;

    if (!data.Credentials) {
      throw new Error('获取临时凭证失败: 凭证数据格式错误');
    }

    const { Credentials } = data;

    // 验证必要字段
    if (!Credentials.TmpSecretId || !Credentials.TmpSecretKey || !Credentials.Token) {
      throw new Error('临时凭证不完整: 缺少必要字段');
    }

    // 保存凭证过期时间
    credentialsExpireTime = data.ExpiredTime;

    // 创建COS实例
    cosInstance = new COS({
      SecretId: Credentials.TmpSecretId,
      SecretKey: Credentials.TmpSecretKey,
      SecurityToken: Credentials.Token,
      ExpiredTime: data.ExpiredTime
    });

    return cosInstance;
  } catch (error) {
    console.error('初始化COS实例失败:', error);
    throw error;
  }
}

/**
 * 检查凭证是否过期
 *
 * @returns 是否过期
 */
function isCredentialsExpired(): boolean {
  // 安全边界：提前30秒视为过期，避免临界点问题
  const safetyMargin = 30; // 30秒
  const currentTime = Math.floor(Date.now() / 1000); // 当前时间戳（秒）

  return !credentialsExpireTime || currentTime + safetyMargin >= credentialsExpireTime;
}

/** 获取COS实例，如果不存在或凭证已过期则初始化 */
export async function getCosInstance(config?: CosConfig): Promise<COS> {
  // 检查实例不存在或凭证已过期
  if (!cosInstance || isCredentialsExpired()) {
    if (config) {
      return initCOS(config);
    }
    return initCOS(cosConfig);
  }
  return cosInstance;
}

/**
 * 上传文件到COS
 *
 * @param file 要上传的文件
 * @param key 存储路径，如：'folder/filename.ext'
 * @param onProgress 上传进度回调
 * @returns 上传成功后的文件URL
 */
export async function uploadToCOS(file: File, key: string, onProgress?: (progress: number) => void): Promise<string> {
  try {
    // 确保COS实例已初始化且凭证有效
    const cos = await getCosInstance(cosConfig);

    return new Promise((resolve, reject) => {
      cos.uploadFile(
        {
          Bucket: cosConfig.Bucket,
          Region: cosConfig.Region,
          Key: key,
          Body: file,
          SliceSize: 1024 * 1024 * 5, // 5MB分片
          onProgress: info => {
            onProgress?.(Math.floor(info.percent * 100));
          }
        },
        (err, _data) => {
          if (err) {
            console.error('上传文件失败:', err);

            // 提供更详细的错误信息
            let errorMessage = '上传文件失败';

            if (err.message) {
              if (err.message.includes('CORS')) {
                errorMessage = `CORS跨域请求被阻止，请检查COS存储桶CORS配置是否正确。建议在腾讯云控制台配置允许来源: ${
                  window.location.origin
                }`;
              } else if (err.message.includes('Network Error')) {
                errorMessage = '网络错误，请检查网络连接是否正常';
              } else if (err.message.includes('timeout')) {
                errorMessage = '上传超时，请检查网络状况或文件大小';
              } else if (err.message.includes('Bucket not exist')) {
                errorMessage = `存储桶 ${cosConfig.Bucket} 不存在，请检查配置`;
              } else if (err.message.includes('Access Denied')) {
                errorMessage = '访问被拒绝，请检查临时凭证权限是否正确';
              } else {
                errorMessage = `上传失败: ${err.message}`;
              }
            }

            const enhancedError = new Error(errorMessage);
            Object.assign(enhancedError, err);
            reject(enhancedError);
            return;
          }

          // 构建文件URL
          const url = `${cosConfig.BaseUrl || ''}${key}`;
          resolve(url);
        }
      );
    });
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
}

/**
 * 生成唯一的文件名
 *
 * @param file 上传的文件
 */
export function generateCosKey(file: File): string {
  const ext = file.name.split('.').pop() || '';
  const date = new Date();
  const ymd = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`;
  const random = Math.floor(Math.random() * 1000000)
    .toString()
    .padStart(6, '0');

  return `file/24_hours/${ymd}/${ymd}_${random}.${ext}`;
}

export default {
  initCOS,
  getCosInstance,
  uploadToCOS,
  generateCosKey
};
