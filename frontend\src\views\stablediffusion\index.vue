<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const url = ref('');
let intervalId: number | null = null;

// Cookie 获取函数
const getCookie = (name: string): string | null => {
  const nameEQ = `${encodeURIComponent(name)}=`;
  const ca = document.cookie.split(';');
  for (let c of ca) {
    c = c.trim();
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length));
    }
  }
  return null;
};

// 检查是否在登录页面
const isLoginPage = () => {
  return route.path.includes('/login');
};

onMounted(() => {
  // 从 Cookie 中读取 url
  const sessionUrl = getCookie('url');
  if (sessionUrl) {
    url.value = sessionUrl;
  } else {
    // 如果没有 url，重定向到指定链接
    window.location.href = 'https://aihub.igamebuy.com/';
  }

  // 仅在非登录页面时启用定时检查
  if (!isLoginPage()) {
    intervalId = window.setInterval(() => {
      const currentUrl = getCookie('url');
      if (!currentUrl) {
        // 增加友好提示
        window.$message?.warning('页面状态已更新，即将刷新...');
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    }, 10000);
  }
});

onUnmounted(() => {
  // 清除定时器
  if (intervalId !== null) {
    clearInterval(intervalId);
  }
});
</script>

<template>
  <!-- 仅当 url 存在时才渲染 iframe -->
  <iframe v-if="url" :src="url" class="h-screen w-full"></iframe>
</template>

<style scoped></style>
