<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { $t } from '@/locales';
import { fetchGetRoleUser, fetchGetUserList, postSetRoleUser } from '@/service/api';
defineOptions({
  name: 'RoleUserModal'
});

interface Props {
  /** the roleId */
  roleId: number;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

function closeModal() {
  visible.value = false;
}

const title = computed(() => $t('common.edit') + $t('page.manage.role.roleUser'));

const roleUserOption = ref<CommonType.Option<number>[]>([]);

const roleUser = ref<number[]>([]);

function handleSubmit() {
  postSetRoleUser(props.roleId, roleUser.value).then(() => {
    window.$message?.success?.($t('common.modifySuccess'));
    closeModal();
  });
}

const getAllUser = () => {
  roleUserOption.value = [];
  fetchGetUserList({ size: 99999 }).then(res => {
    if (res.data?.records) {
      res.data?.records.forEach(e => {
        const item: CommonType.Option<number> = { label: `${e.nickname}:${e.username}`, value: e.id };
        roleUserOption.value.push(item);
      });
    }
  });
};
const getRoleUser = () => {
  roleUser.value = [];
  fetchGetRoleUser(props.roleId).then(res => {
    if (res.data?.records) {
      res.data?.records.forEach(e => {
        roleUser.value.push(e.id);
      });
    }
  });
};
const init = () => {
  getAllUser();
  getRoleUser();
};

watch(visible, val => {
  if (val) {
    init();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-600px">
    <NTransfer
      v-model:value="roleUser"
      virtual-scroll
      :options="roleUserOption"
      source-filterable
      class="min-w-500px"
    />
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" class="mt-16px" @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
