import logging

import redis.asyncio as asRedis

from config import app_settings

logger = logging.getLogger(__name__)

pool = asRedis.ConnectionPool(
    host=app_settings.redis_host, port=app_settings.redis_port, db=0
)


def get_redis():
    client = asRedis.Redis(connection_pool=pool)
    return client


class redis:
    @classmethod
    async def get(cls, key):
        try:
            client = get_redis()
            return await client.get(key)
        finally:
            await client.aclose()

    @classmethod
    async def set(cls, key, value, ex=None, exat=None):
        try:
            client = get_redis()
            return await client.set(key, value, ex=ex, exat=exat)
        finally:
            await client.aclose()

    @classmethod
    async def exec(cls, method, *args, **kwargs):
        try:
            client = get_redis()
            Method = getattr(client, method)
            return await Method(*args, **kwargs)
        finally:
            await client.aclose()

    @classmethod
    async def delete(cls, key):
        try:
            client = get_redis()
            return await client.delete(key)
        finally:
            await client.aclose()

    @classmethod
    async def lpush(cls, key, *values):
        try:
            client = get_redis()
            return await client.lpush(key, *values)
        finally:
            await client.aclose()

    @classmethod
    async def brpop(cls, key, timeout=0):
        try:
            client = get_redis()
            return await client.brpop(key, timeout=timeout)
        finally:
            await client.aclose()

    @classmethod
    async def xadd(cls, stream, data):
        try:
            client = get_redis()
            # if isinstance(data, str):
            #     data = json.loads(data)
            return await client.xadd(stream, data)
        finally:
            await client.aclose()

    @classmethod
    async def xread_group(cls, group_name, consumer_name, streams, count=1, block=0):
        try:
            client = get_redis()
            return await client.xreadgroup(group_name, consumer_name, streams, count=count, block=block)
        finally:
            await client.aclose()

    @classmethod
    async def xack(cls, stream, group_name, *ids):
        try:
            client = get_redis()
            return await client.xack(stream, group_name, *ids)
        finally:
            await client.aclose()

    @classmethod
    async def xgroup_create(cls, stream, group_name, id="0", mkstream=False):
        try:
            client = get_redis()
            return await client.xgroup_create(stream, group_name, id=id, mkstream=mkstream)
        finally:
            await client.aclose()

    @classmethod
    async def incr(cls, key):
      try:
        client = get_redis()
        return await client.incr(key)
      finally:
        await client.aclose()

    @classmethod
    async def expire(cls, key, time):
      try:
        client = get_redis()
        return await client.expire(key, time)
      finally:
        await client.aclose()

