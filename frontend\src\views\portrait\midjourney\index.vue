<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import {
  change,
  fluxComfyUIImage,
  fluxPanImage,
  getGptImageTask,
  getMyTasks,
  getSearchTasks,
  getTaskState,
  gptImage,
  imagine,
  jobAction,
  panImage
} from '@/service/api/midjourney';
import type { ChatMjTask } from '@/service/api/midjourney';
import CardContent from './modules/card-content.vue';
import SystemMenu from './modules/system-menu.vue';
import MjChannels from './modules/mj-channels.vue';

// 图片历史记录，用于存储每个任务的图片列表
// const imageHistoryMap = new Map<number, string[]>();

const tasks = ref<ChatMjTask[]>([]);
// 当前选择的平台（默认为midjourney）
const currentPlatform = ref('midjourney');
const page = ref(1);
const pageSize = ref(5);
const hasMore = ref(true);
const loading = ref(false);
const message = useMessage();
// 搜索
const searchtasks = ref<ChatMjTask[]>([]);
const searchInput = ref('');

// 记录选中的频道ID，默认0表示私人频道
// const selectedChannelId = ref<number>(0);

// 作品集
const router = useRouter();

// 把当前选中的频道 ID 保存在父组件（默认 0：私人频道）
const currentChannelId = ref(0);

// 定时器管理系统 - 存储活动的轮询定时器
const pollingTimers = new Map<number, NodeJS.Timeout>();

// 清理单个定时器
const clearPollingTimer = (taskId: number) => {
  const timerId = pollingTimers.get(taskId);
  if (timerId) {
    clearInterval(timerId);
    pollingTimers.delete(taskId);
    console.log(`已停止轮询任务: ${taskId}`);
  }
};

// 清理所有定时器
const clearAllPollingTimers = () => {
  pollingTimers.forEach((timerId, taskId) => {
    clearInterval(timerId);
    console.log(`已清理定时器 - 任务ID: ${taskId}`);
  });
  pollingTimers.clear();
  console.log('所有轮询定时器已清理');
};

const goToWorks = () => {
  router.push({ name: 'portrait_mjworks' });
};

// 辅助函数：处理图片URL数组
const processImageUrls = (imageUrls: any): string[] | null => {
  if (!imageUrls) return null;

  try {
    if (typeof imageUrls === 'string') {
      const parsed = JSON.parse(imageUrls);
      return Array.isArray(parsed) ? parsed : [imageUrls as string];
    } else if (Array.isArray(imageUrls)) {
      return imageUrls;
    }
  } catch (e) {
    return [imageUrls as unknown as string];
  }
  return null;
};

// 辅助函数：从localStorage恢复期望数量
const getExpectedCountForTask = (task: ChatMjTask): number => {
  // 首先检查任务本身是否包含expected_count
  if ((task as any).expected_count !== undefined) {
    return (task as any).expected_count;
  }

  // 如果任务没有expected_count，尝试从localStorage恢复
  try {
    const storedData = localStorage.getItem(`openai-progress-${task.taskid}`);
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      if (parsedData && parsedData.num) {
        return parsedData.num;
      }
    }
  } catch (e) {
    console.error('从localStorage恢复expected_count失败:', e);
  }

  return 1; // 默认值
};

// 格式化数据
const formatTaskData = (task: ChatMjTask) => {
  const [date, timestamp] = task.uptime.split(' ');

  // 处理各种图片URL字段
  const processedImageUrls = processImageUrls(task.image_urls);
  const processedPromptImg = processImageUrls(task.prompt_img) || [];
  const processedPromptImgOss = processImageUrls(task.prompt_img_oss) || [];
  const expectedCount = getExpectedCountForTask(task);

  const formattedTask = {
    ...task,
    date,
    timestamp,
    taskid: Number(task.taskid)
  };

  // 设置处理后的图片数组
  if (processedImageUrls) {
    (formattedTask as any).image_urls = processedImageUrls;
  }
  (formattedTask as any).prompt_img = processedPromptImg;
  (formattedTask as any).prompt_img_oss = processedPromptImgOss;
  (formattedTask as any).expected_count = expectedCount;

  return formattedTask as ChatMjTask;
};

// 定义占位图URL常量
const PLACEHOLDER_IMAGE_URL = 'https://i.postimg.cc/52YJ745k/AI-2048.png';

// 定义表单数据类型
interface FormValue {
  model: string;
  prompt: string;
  originalPrompt: string;
  image: string[];
  selectedPrompts: { param: string; weight: number }[];
  stylize: number | null;
  quality: number | null;
  chaos: number | null;
  seed: number | null;
  aspect: string | null;
  iw: number | null;
  repeat: number | null;
  exclude: string;
  parameters: Record<string, string | number>;
  platform: string;
  num: number;
}

const createTaskFromResponse = (data: ChatMjTask): ChatMjTask =>
  ({
    id: data.id,
    pid: data.pid,
    username: data.username,
    taskid: Number(data.taskid),
    action: data.action,
    status: data.status,
    prompt: data.prompt,
    prompt_en: data.prompt_en,
    description: data.description,
    state: data.state,
    submit_time: data.submit_time,
    start_time: data.start_time,
    finish_time: data.finish_time,
    image_url: data.image_url,
    progress: data.progress,
    fail_reason: data.fail_reason,
    uptime: data.uptime,
    seed: data.seed,
    button: data.button,
    channels: data.channels,
    like_count: data.like_count,
    is_like: data.is_like,
    model: data.model,
    manufacturer: data.manufacturer,
    ...((data as any).expected_count !== undefined
      ? { expected_count: (data as any).expected_count }
      : { expected_count: 1 })
  }) as ChatMjTask;

// 轮询查看生成图片状态
const pollTaskState = (taskid: number) => {
  // 如果已经有该任务的定时器，先清理
  clearPollingTimer(taskid);

  const pollingInterval = 3000; // 轮询间隔：3秒

  const poll = async () => {
    try {
      const taskStateResponse = await getTaskState(taskid);

      if (taskStateResponse.data) {
        const updatedTask = formatTaskData(taskStateResponse.data);
        const taskIndex = tasks.value.findIndex(task => task.taskid === taskid);

        if (taskIndex !== -1) {
          tasks.value[taskIndex] = updatedTask;
        }

        const { status, progress } = taskStateResponse.data;

        // 如果任务已完成或失败，则停止轮询
        if (status === 'SUCCESS' || status === 'FAILURE' || progress === '100%') {
          clearPollingTimer(taskid);
        }
      }
    } catch (error) {
      console.error(`轮询任务 ${taskid} 状态时发生错误:`, error);
      // 如果连续出错，可能需要停止轮询避免无限重试
    }
  };

  // 使用 setInterval 替代递归 setTimeout
  const timerId = setInterval(poll, pollingInterval);

  // 存储定时器 ID 以便后续清理
  pollingTimers.set(taskid, timerId);

  // 立即执行一次检查
  poll();
};

// 轮询查看OpenAI生成图片状态
const pollGptTaskState = (taskid: number, existingImages?: string[], requestedNum?: number) => {
  // 如果已经有该任务的定时器，先清理
  clearPollingTimer(taskid);

  const pollingInterval = 3000; // 轮询间隔：3秒

  // 获取存储在localStorage中的进度数据
  const getStoredProgress = () => {
    const storedData = localStorage.getItem(`openai-progress-${taskid}`);
    return storedData ? JSON.parse(storedData) : null;
  };

  // 存储进度数据到localStorage
  const storeProgress = (task: ChatMjTask, existingImgs?: string[], num?: number) => {
    localStorage.setItem(
      `openai-progress-${taskid}`,
      JSON.stringify({
        taskid: task.taskid,
        start_time: task.start_time,
        status: task.status,
        lastUpdated: new Date().toISOString(),
        // 存储图片数组，包括占位图状态
        image_urls: existingImgs || [],
        // 存储请求的图片数量
        num: num || 1,
        // 新增：存储最后一次收到的后端图片数量
        lastReceivedCount: task.image_urls && Array.isArray(task.image_urls) ? task.image_urls.length : 0
      })
    );
  };

  // 从API响应中提取图片数组
  const extractApiImages = (task: ChatMjTask): string[] => {
    if (!task.image_urls) return [];

    try {
      if (typeof task.image_urls === 'string') {
        const parsedImages = JSON.parse(task.image_urls);
        if (Array.isArray(parsedImages) && parsedImages.length > 0) {
          return parsedImages;
        }
      } else if (Array.isArray(task.image_urls) && task.image_urls.length > 0) {
        return task.image_urls;
      }
    } catch (e) {
      console.error('解析image_urls失败:', e);
    }
    return [];
  };

  // 处理单图替换逻辑
  const handleSingleImageReplacement = (newImageUrl: string, existingImgs: string[]): string[] => {
    // 检查是否有占位图，如果有则替换
    const placeholderIndices = existingImgs
      .map((img, idx) => (img === PLACEHOLDER_IMAGE_URL ? idx : -1))
      .filter(idx => idx !== -1);

    if (placeholderIndices.length > 0) {
      // 有占位图，创建新数组并替换第一个占位图
      const result = [...existingImgs];
      result[placeholderIndices[0]] = newImageUrl;
      return result;
    }

    // 默认情况：添加新图片到现有数组前面
    return [newImageUrl, ...existingImgs];
  };

  // 处理未完成任务的占位图逻辑
  const handleInProgressPlaceholders = (existingImgs: string[], requiredPlaceholders: number): string[] => {
    // 计算当前占位图数量
    const placeholderCount = existingImgs.filter(img => img === PLACEHOLDER_IMAGE_URL).length;

    // 如果已有足够占位图，保持现有数组不变
    if (placeholderCount >= requiredPlaceholders) {
      return existingImgs;
    }

    // 需要添加更多占位图
    if (placeholderCount > 0) {
      // 已有一些占位图，添加缺少的部分
      const additionalPlaceholders = Array(requiredPlaceholders - placeholderCount).fill(PLACEHOLDER_IMAGE_URL);
      return [...existingImgs, ...additionalPlaceholders];
    }

    // 没有占位图，创建所需数量
    const placeholders = Array(requiredPlaceholders).fill(PLACEHOLDER_IMAGE_URL);
    return [...placeholders, ...existingImgs];
  };

  // 处理已完成任务的图片返回逻辑
  const handleCompletedTaskImages = (
    updatedTask: ChatMjTask,
    existingImgs: string[],
    storedProgress: any
  ): string[] => {
    // 如果任务已完成但没有image_url，保持现有状态
    if (!updatedTask.image_url && (!updatedTask.image_urls || !Array.isArray(updatedTask.image_urls))) {
      return existingImgs;
    }

    // 获取API返回的图片数组
    const apiReturnedImages = extractApiImages(updatedTask);

    // 如果API返回了有效图片数组
    if (apiReturnedImages.length > 0) {
      // 检查是否有增加图片（对比localStorage中的lastReceivedCount）
      if (storedProgress?.lastReceivedCount && apiReturnedImages.length > storedProgress.lastReceivedCount) {
        // 有新图片，但保留占位图
        const placeholderCount = existingImgs.filter(img => img === PLACEHOLDER_IMAGE_URL).length;
        if (placeholderCount > 0) {
          // 保留placeholder，在apiReturnedImages前添加相应数量的占位图
          const placeholdersToAdd = Array(placeholderCount).fill(PLACEHOLDER_IMAGE_URL);
          return [...placeholdersToAdd, ...apiReturnedImages];
        }
      }
      return apiReturnedImages;
    }

    // 处理单图情况
    return handleSingleImageReplacement(updatedTask.image_url || '', existingImgs);
  };

  // 处理API返回的图片数据 - 优化版本
  const processApiImages = (updatedTask: ChatMjTask, existingImgs: string[]): string[] => {
    // 获取存储的进度数据
    const storedProgress = getStoredProgress();

    // 确定应该保持的占位图数量
    let requiredPlaceholders = requestedNum || 1;
    if (storedProgress?.num) {
      requiredPlaceholders = Math.max(requiredPlaceholders, storedProgress.num);
    }

    // 如果任务尚未完成，确保保留占位图
    if (updatedTask.status !== 'SUCCESS' || updatedTask.progress !== '100%') {
      return handleInProgressPlaceholders(existingImgs, requiredPlaceholders);
    }

    // 任务已完成，处理返回的图片
    return handleCompletedTaskImages(updatedTask, existingImgs, storedProgress);
  };

  // 检查是否应该停止轮询
  const checkShouldStopPolling = (updatedTask: ChatMjTask, expectedNum?: number) => {
    const previousStored = getStoredProgress();
    const expectedCount = expectedNum || previousStored?.num || 1;

    // 获取有效图片数量（排除占位图和空URL）
    let processedImages = [];
    if (updatedTask.image_urls && Array.isArray(updatedTask.image_urls)) {
      processedImages = updatedTask.image_urls;
    } else if (typeof updatedTask.image_urls === 'string') {
      try {
        processedImages = JSON.parse(updatedTask.image_urls);
      } catch (e) {
        processedImages = [];
      }
    }
    const validImages = processedImages.filter((url: string) => url !== PLACEHOLDER_IMAGE_URL && url !== '');

    // 添加详细轮询日志
    console.log(
      `轮询检查TaskID ${taskid}: 已生成${validImages.length}/${expectedCount}张图片，状态:${updatedTask.status}`
    );

    // 判断是否应该停止轮询
    const shouldStop = validImages.length >= expectedCount || updatedTask.status === 'FAILURE';

    if (shouldStop) {
      if (validImages.length >= expectedCount) {
        console.log(`任务${taskid}已完成：生成了${validImages.length}张图片，达到期望数量${expectedCount}`);
      } else {
        console.log(`任务${taskid}失败，停止轮询`);
      }
      localStorage.removeItem(`mjProgress-${taskid}`);
      clearPollingTimer(taskid);
    }

    return shouldStop;
  };

  // 处理API任务状态更新
  const updateTaskFromApi = async () => {
    try {
      const taskStateResponse = await getGptImageTask(String(taskid));
      if (!taskStateResponse.data) return false;

      const originalTask = taskStateResponse.data;

      // 创建任务的副本，以便修改
      const updatedTask = { ...originalTask };

      // 对于OPENAI任务，简化进度逻辑
      // 只使用任务状态来决定进度：SUCCESS=100%，其他=0%
      updatedTask.progress = updatedTask.status === 'SUCCESS' ? '100%' : '0%';

      // 获取任务索引
      const taskIndex = tasks.value.findIndex(task => task.taskid === taskid);

      // 如果找到任务
      if (taskIndex !== -1) {
        // 读取上一次的存储数据
        const previousStored = getStoredProgress();

        // 格式化任务数据
        const formattedTask = formatTaskData(updatedTask);

        // 处理existingImages参数
        let finalImages: string[] = existingImages || [];

        // 如果之前有存储的图片数据，优先使用它们而非传入的existingImages
        if (previousStored && previousStored.image_urls && Array.isArray(previousStored.image_urls)) {
          finalImages = previousStored.image_urls;
        }

        // 处理图片数组并更新任务数据
        const processedImages = processApiImages(updatedTask, finalImages);
        (formattedTask as any).image_urls = processedImages;

        // 存储进度数据（包括处理后的图片数组）
        storeProgress(updatedTask, processedImages, requestedNum || previousStored?.num || 1);

        // 更新任务 - 使用不可变更新模式替代直接修改
        const newTasks = [...tasks.value];
        newTasks[taskIndex] = formattedTask;

        // 创建一个批量更新函数，将多个状态更改合并为一次更新
        const batchUpdate = () => {
          tasks.value = newTasks;
        };

        // 使用微任务确保状态更新在当前执行栈完成后进行
        Promise.resolve().then(batchUpdate);
      }

      // 检查轮询停止条件：基于图片数量和期望数量，而非简单状态判断
      return checkShouldStopPolling(updatedTask, requestedNum);
    } catch (error) {
      console.error(`轮询任务 ${taskid} 状态时发生错误:`, error);
      return false;
    }
  };

  // 添加占位图
  const addPlaceholderImage = (task: ChatMjTask) => {
    // 如果任务未完成且没有存储的图片数组，添加占位图
    let currentImages: string[] = [];

    // 如果已有图片，添加到数组
    if (task.image_urls && Array.isArray(task.image_urls)) {
      currentImages = [...task.image_urls];
    } else if (task.image_url) {
      currentImages = [task.image_url];
    }

    // 检查第一张图是否已经是占位图
    if (currentImages.length === 0 || currentImages[0] !== PLACEHOLDER_IMAGE_URL) {
      // 创建包含占位图的新图片数组 (占位图放在第一位)
      const updatedImagesWithPlaceholder = [PLACEHOLDER_IMAGE_URL, ...currentImages];

      // 更新任务的图片数组 - 返回新对象而非修改原对象
      return {
        ...task,
        image_urls: updatedImagesWithPlaceholder
      };
    }

    // 如果不需要修改，返回原始任务对象
    return task;
  };

  // 更新任务的图片数组
  const updateTaskImages = (task: ChatMjTask, storedProgress: any) => {
    // 创建任务的副本，避免直接修改
    let updatedTask = { ...task };

    // 恢复存储的图片数组，包括占位图状态
    if (storedProgress.image_urls && Array.isArray(storedProgress.image_urls) && storedProgress.image_urls.length > 0) {
      updatedTask = {
        ...updatedTask,
        image_urls: [...storedProgress.image_urls]
      };
    } else if (task.status !== 'SUCCESS' && task.progress !== '100%') {
      // 使用addPlaceholderImage返回的新对象
      updatedTask = addPlaceholderImage(updatedTask);
    }

    return updatedTask;
  };

  // 从本地存储更新任务状态
  const updateTaskFromStorage = () => {
    const storedProgress = getStoredProgress();
    if (!storedProgress) return;

    const taskIndex = tasks.value.findIndex(task => task.taskid === taskid);
    if (taskIndex === -1) return;

    // 获取当前任务的副本
    const task = { ...tasks.value[taskIndex] };

    // 创建更新后的任务对象
    const updatedTask = {
      ...task,
      start_time: storedProgress.start_time,
      status: storedProgress.status,
      // 对于OPENAI任务，简化进度逻辑
      // 只使用任务状态来决定进度：SUCCESS=100%，其他=0%
      progress: task.status === 'SUCCESS' ? '100%' : '0%'
    };

    // 恢复存储的图片数组
    const taskWithImages = updateTaskImages(updatedTask, storedProgress);

    // 使用不可变更新模式更新任务列表
    const newTasks = [...tasks.value];
    newTasks[taskIndex] = taskWithImages;

    // 使用微任务确保状态更新在当前执行栈完成后进行
    Promise.resolve().then(() => {
      tasks.value = newTasks;
    });
  };

  // 主轮询函数
  const poll = async () => {
    // 1. 从API更新任务状态
    const shouldStop = await updateTaskFromApi();
    if (shouldStop) return;

    // 2. 从本地存储更新任务状态（作为备份）
    updateTaskFromStorage();
  };

  // 使用 setInterval 替代递归 setTimeout
  const timerId = setInterval(poll, pollingInterval);

  // 存储定时器 ID 以便后续清理
  pollingTimers.set(taskid, timerId);

  // 立即执行一次检查
  poll();
};

// 生成图片，提交请求
const handleGenerate = async (formValue: FormValue, num?: number) => {
  searchtasks.value = [];
  const prompt = formValue.prompt; // 最终生成的prompt

  if (!prompt) {
    message.error('提示词不能为空');
    return;
  }

  loading.value = true;

  try {
    let response;
    // 根据平台选择不同的API
    if (formValue.platform === 'openai') {
      // OpenAI平台，使用最终生成的prompt（包含比例参数）
      response = await gptImage({
        prompt,
        image_urls: formValue.image,
        num: formValue.num,
        proportion: (formValue as any).proportion
      } as any);
    } else {
      // Midjourney平台，使用最终生成的prompt
      response = await imagine({
        prompt,
        base64Array: formValue.image,
        channels: currentChannelId.value,
        proportion: (formValue as any).proportion
      } as any);
    }

    if (response.data && response.data.taskid) {
      const newTask: ChatMjTask = createTaskFromResponse(response.data);

      // 设置期望生成数量
      const imageCount = num || formValue.num || 1;
      (newTask as any).expected_count = imageCount;

      tasks.value.unshift(newTask); // 将新任务添加到任务列表的顶部

      message.success('开始图片创作,请耐心等待');

      // 根据模型类型选择不同的轮询函数
      if (newTask.manufacturer === 'OPENAI' || newTask.manufacturer === 'FLUX') {
        // 如果是OPENAI或FLUX模型，检查num参数，创建多个占位容器
        // 创建imageCount个占位容器图片，所有占位容器共享同一个taskid
        if (imageCount > 1) {
          // 创建图片数组
          const imageUrls = Array(imageCount).fill(PLACEHOLDER_IMAGE_URL);

          // 为任务设置图片数组
          (newTask as any).image_urls = imageUrls;

          // 传递占位图数组和num参数
          pollGptTaskState(Number(newTask.taskid), imageUrls, imageCount);
        } else {
          // 单图生成，传递num = 1
          pollGptTaskState(Number(newTask.taskid), undefined, 1);
        }
      } else {
        pollTaskState(Number(newTask.taskid));
      }
    } else if (response.data && response.data.fail_reason !== null && response.data.fail_reason !== '') {
      message.error('创作失败');
    }
  } catch (error) {
    // message.error('请求过程中发生错误');
    // console.error('Error during image generation:', error);
  } finally {
    loading.value = false;
  }
};

// 提交绘画变形请求
const handleAction = async ({ action, index, taskId }: { action: string; index: number | 'none'; taskId: number }) => {
  searchtasks.value = [];
  message.success('开始创作,请耐心等待');
  const response = await change({
    taskid: taskId,
    action,
    index: index === 'none' ? undefined : index,
    channels: currentChannelId.value
  });

  if (response.data && response.data.taskid) {
    const newTask: ChatMjTask = createTaskFromResponse(response.data);

    tasks.value.unshift(newTask); // 将新任务添加到任务列表的顶部

    // 根据模型类型选择不同的轮询函数
    if (newTask.manufacturer === 'OPENAI' || newTask.manufacturer === 'FLUX') {
      pollGptTaskState(Number(newTask.taskid), undefined, 1);
    } else {
      pollTaskState(Number(newTask.taskid));
    }
  } else if (response.data && response.data.fail_reason !== null && response.data.fail_reason !== '') {
    message.error('创作失败');
  }
};

const isLoading = ref(false); // 请求锁

// 获取用户历史记录
const loadTasks = async () => {
  if (isLoading.value) return; // 如果请求正在进行中，直接返回
  isLoading.value = true; // 设置请求正在进行

  const response = await getMyTasks(page.value, pageSize.value, currentChannelId.value);
  if (response.data) {
    const formattedTasks = response.data.map(formatTaskData);
    tasks.value.push(...formattedTasks);

    isLoading.value = false;

    if (response.data.length < pageSize.value) {
      hasMore.value = false;
    }
    // 检查未完成的任务，启动轮询
    formattedTasks.forEach(task => {
      // 跳过失败的任务
      if (task.status === 'FAILURE') {
        return;
      }

      // 根据模型类型选择不同的轮询函数
      if (task.manufacturer === 'OPENAI' || task.manufacturer === 'FLUX') {
        // 如果是OpenAI或Flux模型，使用相应的轮询函数
        if (task.status !== 'SUCCESS' || task.image_url === null || task.progress !== '100%') {
          // 为正在进行中的任务添加占位图
          const taskIndex = tasks.value.findIndex(t => t.taskid === task.taskid);
          if (taskIndex !== -1) {
            // 构建当前任务的现有图片数组
            let currentImages: string[] = [];

            // 如果已有图片，添加到数组
            if (task.image_urls && Array.isArray(task.image_urls)) {
              currentImages = [...task.image_urls];
            } else if (task.image_url) {
              currentImages = [task.image_url];
            }

            // 检查第一张图是否已经是占位图
            if (currentImages.length === 0 || currentImages[0] !== PLACEHOLDER_IMAGE_URL) {
              // 创建包含占位图的新图片数组 (占位图放在第一位)
              const updatedImagesWithPlaceholder = [PLACEHOLDER_IMAGE_URL, ...currentImages];

              // 使用不可变更新模式更新任务列表
              const newTasks = [...tasks.value];
              const updatedTask = {
                ...newTasks[taskIndex],
                image_urls: updatedImagesWithPlaceholder
              };
              newTasks[taskIndex] = updatedTask;

              // 使用微任务确保状态更新在当前执行栈完成后进行
              Promise.resolve().then(() => {
                tasks.value = newTasks;
              });

              // 启动轮询，传递图片数组和正确的期望数量
              pollGptTaskState(
                Number(task.taskid),
                updatedImagesWithPlaceholder,
                // 使用任务的expected_count作为期望数量
                (task as any).expected_count || 1
              );
            } else {
              // 已有占位图，直接启动轮询
              pollGptTaskState(
                Number(task.taskid),
                currentImages,
                // 使用任务的expected_count作为期望数量
                (task as any).expected_count || 1
              );
            }
          } else {
            pollGptTaskState(Number(task.taskid), undefined, (task as any).expected_count || 1);
          }
        }
      } else {
        // 对于Midjourney模型
        const needsPolling =
          task.status !== 'SUCCESS' ||
          (task.status === 'SUCCESS' && (task.image_url === null || task.progress !== '100%'));

        if (needsPolling) {
          pollTaskState(Number(task.taskid));
        }
      }
    });
  }
};

/** 监听子组件「initial-channel」事件 */
function handleInitialChannel(channelId: number) {
  // 如果初始频道和当前记录的不同，就更新并加载数据

  currentChannelId.value = channelId;
  resetAndLoadTasks();
}

/** 监听子组件「channel-selected」事件（用户手动点击时） */
function handleChannelSelected(channelId: number) {
  // 如果频道没变，就不处理
  if (channelId === currentChannelId.value) return;
  currentChannelId.value = channelId;
  resetAndLoadTasks();
}

// 处理平台变化
function handlePlatformChange(platform: string) {
  currentPlatform.value = platform;
  console.log('Platform changed to:', platform);
}

// 重置列表并加载当前频道的数据
function resetAndLoadTasks() {
  tasks.value = [];
  searchtasks.value = [];
  page.value = 1;
  hasMore.value = true;
  loadTasks();
}

onMounted(() => {
  loadTasks();
});

// 组件卸载时清理所有轮询定时器
onBeforeUnmount(() => {
  clearAllPollingTimers();
});

const searchpage = ref(1);

// 搜索任务
const handleSearch = async () => {
  if (isLoading.value) return; // 如果请求正在进行中，直接返回

  if (!searchInput.value.trim()) {
    searchtasks.value = [];
    message.info('请输入搜索内容');
    return;
  }

  hasMore.value = true;
  searchtasks.value = [];
  isLoading.value = true;

  const response = await getSearchTasks(searchInput.value, searchpage.value, pageSize.value);
  if (response.data) {
    const formattedTasks = response.data.map(formatTaskData);

    isLoading.value = false;

    if (formattedTasks.length === 0) {
      hasMore.value = false;
    } else {
      searchtasks.value.push(...formattedTasks);

      if (response.data.length < pageSize.value) {
        hasMore.value = false;
      }
    }
  }
};

const handleInfiniteScroll = () => {
  if (hasMore.value) {
    if (searchtasks.value.length !== 0) {
      searchpage.value += 1;
      handleSearch();
    } else {
      page.value += 1;
      loadTasks();
    }
  }
};

function researchpage() {
  searchpage.value = 1;
}

function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleSearch();
  }
}

const handleJobAction = async ({ label, customId, taskId }: { label: string; customId: string; taskId: number }) => {
  try {
    console.log('父组件函数触发');
    // 清空任务列表
    searchtasks.value = [];

    // 显示消息，提示用户任务已开始
    message.success('任务已提交，请耐心等待');

    // 发送请求到后端执行任务
    const response = await jobAction(taskId, customId, currentChannelId.value);

    if (response.data && response.data.taskid) {
      // 构造新的任务数据
      const newTask: ChatMjTask = {
        id: response.data.id,
        pid: response.data.pid,
        username: response.data.username,
        taskid: Number(response.data.taskid),
        action: label,
        status: response.data.status,
        prompt: response.data.prompt,
        prompt_en: response.data.prompt_en,
        description: response.data.description,
        state: response.data.state,
        submit_time: response.data.submit_time,
        start_time: response.data.start_time,
        finish_time: response.data.finish_time,
        image_url: response.data.image_url,
        progress: response.data.progress,
        fail_reason: response.data.fail_reason,
        uptime: response.data.uptime,
        seed: response.data.seed,
        button: response.data.button,
        channels: response.data.channels,
        like_count: response.data.like_count,
        is_like: response.data.is_like,
        model: response.data.model,
        manufacturer: response.data.manufacturer
      };

      // 将新任务插入任务列表
      tasks.value.unshift(newTask);

      // 根据模型类型选择不同的轮询函数
      if (newTask.manufacturer === 'OPENAI' || newTask.manufacturer === 'FLUX') {
        pollGptTaskState(Number(newTask.taskid), undefined, 1);
      } else {
        pollTaskState(Number(newTask.taskid));
      }
    } else if (response.data && response.data.fail_reason) {
      // 如果有失败原因，显示错误消息
      message.error(`任务失败: ${response.data.fail_reason}`);
    }
  } catch (error) {
    // 捕获错误并显示消息
    message.error('提交任务失败');
  }
};

// 辅助函数：从本地UI移除失败图片占位符
const removeFailedImagePlaceholder = (taskId: number, regenerateIndex: number) => {
  const taskIndex = tasks.value.findIndex(task => task.taskid === taskId);
  if (taskIndex === -1) return;

  const currentTask = tasks.value[taskIndex];
  if (!currentTask.image_urls || !Array.isArray(currentTask.image_urls)) return;

  // 创建图片数组的副本
  const updatedImages = [...currentTask.image_urls];

  // 检查指定索引是否有效且为空URL（失败状态）
  if (regenerateIndex < 0 || regenerateIndex >= updatedImages.length || updatedImages[regenerateIndex] !== '') return;

  // 从数组中移除该空URL
  updatedImages.splice(regenerateIndex, 1);

  // 更新任务的图片数组 - 使用不可变更新模式
  const updatedTask = { ...currentTask, image_urls: updatedImages };

  // 创建新的任务列表并更新
  const newTasks = [...tasks.value];
  newTasks[taskIndex] = updatedTask;

  // 使用微任务确保状态更新在当前执行栈完成后进行
  Promise.resolve().then(() => {
    tasks.value = newTasks;
  });

  console.log(`立即从本地UI移除索引 ${regenerateIndex} 处的空URL`);
};

// 准备UI进行重新生成
const prepareUIForRegeneration = async (currentTask: ChatMjTask, taskIndex: number) => {
  // 构建当前任务的现有图片数组
  let currentImages: string[] = [];

  // 如果已有图片，添加到数组
  if (currentTask.image_urls && Array.isArray(currentTask.image_urls)) {
    currentImages = [...currentTask.image_urls];
  } else if (currentTask.image_url) {
    currentImages = [currentTask.image_url];
  }

  // 始终在数组前面添加占位图，无论是否已经有占位图
  const updatedImagesWithPlaceholder = [PLACEHOLDER_IMAGE_URL, ...currentImages];

  // 更新当前任务的图片数组，先在UI上显示占位图
  const updatedTask = {
    ...currentTask,
    progress: '0%', // 重置进度为0%
    image_urls: updatedImagesWithPlaceholder
  };

  // 使用不可变更新模式
  const newTasks = [...tasks.value];
  newTasks[taskIndex] = updatedTask;

  // 使用微任务确保状态更新在当前执行栈完成后进行
  Promise.resolve().then(() => {
    tasks.value = newTasks;
  });

  message.success('开始重新生成图片');
  loading.value = true;

  // 清空搜索结果
  searchtasks.value = [];
};

// 处理重新生成失败的情况
const handleRegenerationFailure = (taskId: number, failReason: string) => {
  // 如果生成失败，移除占位图，恢复原状态
  const currentTaskIndex = tasks.value.findIndex(task => task.taskid === taskId);
  if (currentTaskIndex !== -1) {
    const currentTask = tasks.value[currentTaskIndex];
    let currentImages: string[] = [];

    if (currentTask.image_urls && Array.isArray(currentTask.image_urls)) {
      // 移除可能添加的占位图
      currentImages = [...currentTask.image_urls];
      if (currentImages.length > 0 && currentImages[0] === PLACEHOLDER_IMAGE_URL) {
        currentImages.shift();
      }
    } else if (currentTask.image_url) {
      currentImages = [currentTask.image_url];
    }

    const recoveredTask = {
      ...tasks.value[currentTaskIndex],
      progress: '100%',
      image_urls: currentImages
    };

    // 使用不可变更新模式
    const newTasks = [...tasks.value];
    newTasks[currentTaskIndex] = recoveredTask;

    // 使用微任务确保状态更新在当前执行栈完成后进行
    Promise.resolve().then(() => {
      tasks.value = newTasks;
    });
  }

  // message.error(`创作失败: ${failReason}`);
  console.log('创作失败:', failReason);
};

// 发送重新生成请求
const sendRegenerationRequest = async (
  prompt: string,
  promptImg: string[],
  taskId: number,
  regenerateIndex?: number
) => {
  // 调用OpenAI图片生成API，使用相同taskId
  const response = await gptImage({
    prompt,
    image_urls: promptImg,
    taskid: String(taskId), // 使用原任务ID
    num: 1, // 默认生成一张图片
    preserve_history: true, // 添加参数保留历史图片
    regenerate_index: regenerateIndex // 传递需要重生成的图片索引
  });

  if (response.data && response.data.taskid) {
    // 找到现有任务而不是创建新任务
    const taskIndex = tasks.value.findIndex(task => task.taskid === taskId);

    // 只有当找不到任务时才创建新任务（这种情况不应该发生）
    if (taskIndex === -1) {
      console.warn(`在重新生成时未找到任务ID: ${taskId}，这是一个意外情况`);
      // 仅在未找到原任务的特殊情况下才创建新任务
      const newTask = createTaskFromResponse(response.data);
      tasks.value.unshift(newTask);
      pollGptTaskState(Number(newTask.taskid), undefined, 1);
      return;
    }

    // 使用现有的任务，这样不会重复创建卡片
    const existingTask = tasks.value[taskIndex];

    // 后端返回的image_urls由轮询机制处理，这里不需要额外处理
    // 我们只需确保轮询机制正确启动即可
    if (response.data.image_urls) {
      try {
        // 验证image_urls格式是否有效
        if (typeof response.data.image_urls === 'string') {
          // 尝试解析JSON以验证格式
          JSON.parse(response.data.image_urls);
        }
        // 由轮询机制处理具体的图片更新
      } catch (e) {
        console.error('解析后端返回的image_urls失败:', e);
      }
    }

    // 使用任务现有的图片数组，确保不丢失前端的Loading状态
    let updatedImagesWithPlaceholder: string[] | undefined;

    if (existingTask.image_urls) {
      // 保留现有图片数组
      updatedImagesWithPlaceholder = [...existingTask.image_urls];
    }

    // 开始轮询原始任务状态，而不是新任务
    pollGptTaskState(taskId, updatedImagesWithPlaceholder, 1);
  } else if (response.data && response.data.fail_reason) {
    // 如果生成失败，恢复原状态
    handleRegenerationFailure(taskId, response.data.fail_reason);
  }
};

// 处理重新生成图片事件
const handleRegenerate = async ({
  taskId,
  prompt,
  promptImg,
  regenerateIndex
}: {
  taskId: number;
  prompt: string;
  promptImg: string[];
  regenerateIndex?: number;
}) => {
  if (!prompt) {
    message.error('提示词不能为空');
    return;
  }

  try {
    // 找到当前任务在任务列表中的位置
    const taskIndex = tasks.value.findIndex(task => task.taskid === taskId);
    if (taskIndex === -1) {
      message.error('未找到原始任务');
      return;
    }

    // 获取当前任务
    const currentTask = tasks.value[taskIndex];

    // 如果提供了索引，移除失败占位图 - 这对UI有必要
    if (regenerateIndex !== undefined) {
      removeFailedImagePlaceholder(taskId, regenerateIndex);
    }

    // 准备更新UI
    await prepareUIForRegeneration(currentTask, taskIndex);

    // 发送请求并处理响应 - 不需要创建新任务，而是更新现有任务
    await sendRegenerationRequest(prompt, promptImg, taskId, regenerateIndex);

    // 防止因为任务ID相同导致创建重复卡片的问题
    // 当服务器返回成功结果后，轮询机制会更新任务状态，无需新增任务到列表
  } catch (error) {
    console.error('重新生成图片失败:', error);
    message.error('重新生成图片失败');
  } finally {
    loading.value = false;
  }
};

// 暂时保留此函数以便日后可能的使用
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const parseFluxResponseImageUrls = (imageUrls: any): string[] => {
  let responseImageUrls: string[] = [];

  try {
    if (typeof imageUrls === 'string') {
      const parsed = JSON.parse(imageUrls);
      responseImageUrls = Array.isArray(parsed) ? parsed : [imageUrls];
    } else if (Array.isArray(imageUrls)) {
      responseImageUrls = imageUrls;
    }
  } catch (e) {
    console.error('解析Flux返回的image_urls失败:', e);
  }

  return responseImageUrls;
};

// 处理Flux模型的重新生成
const handleFluxRegenerate = async ({
  taskId,
  prompt,
  promptImg,
  regenerateIndex,
  model
}: {
  taskId: number;
  prompt: string;
  promptImg: string[];
  regenerateIndex?: number;
  model: string;
}) => {
  if (!prompt) {
    message.error('提示词不能为空');
    return;
  }

  try {
    // 找到当前任务在任务列表中的位置
    const taskIndex = tasks.value.findIndex(task => task.taskid === taskId);
    if (taskIndex === -1) {
      message.error('未找到原始任务');
      return;
    }

    // 获取当前任务
    const currentTask = tasks.value[taskIndex];

    // 注意: 不移除失败占位图，因为card-content.vue已经处理了
    // 注意: 不调用prepareUIForRegeneration，因为card-content.vue已经添加了loading组件

    // 设置loading状态和清空搜索结果
    loading.value = true;
    searchtasks.value = [];

    // 构建Flux请求参数
    const payload: any = {
      prompt,
      model_name: model || 'flux_kontext_fp8_api', // 使用传入的模型或默认值
      num: 1,
      preserve_history: true, // 保留历史记录
      regenerate_index: regenerateIndex, // 传递重生成索引
      taskid: String(taskId) // 使用原始任务ID
    };

    // 如果有参考图片，添加到请求中
    if (promptImg && promptImg.length > 0) {
      payload.input_image = promptImg[0]; // Flux只使用第一张参考图片
    }

    // 调用Flux API
    const response = await fluxComfyUIImage(payload);

    if (response.data && response.data.task) {
      // 不创建新任务，使用现有任务
      // 由轮询机制负责更新现有任务的状态

      // 确保获取现有任务的图片数组，以正确启动轮询
      let currentImages: string[] | undefined;
      if (currentTask.image_urls && Array.isArray(currentTask.image_urls)) {
        currentImages = [...currentTask.image_urls];
      }

      // 开始轮询原任务状态
      pollGptTaskState(taskId, currentImages, 1);

      // 注意：我们不再将任务添加到tasks列表中，避免重复卡片
    } else {
      // 如果生成失败，恢复原状态
      handleRegenerationFailure(taskId, '创建Flux任务失败');
    }
  } catch (error) {
    console.error('Flux重新生成图片失败:', error);
    message.error('Flux重新生成图片失败');
  } finally {
    loading.value = false;
  }
};

// 处理 OpenAI 模型的扩图操作
const handlePanAction = async ({
  btnPrompt,
  imageUrls,
  taskId,
  regenerateIndex
}: {
  btnPrompt: string;
  imageUrls: string[];
  taskId: number;
  regenerateIndex?: number;
}) => {
  try {
    // 清空搜索任务列表
    searchtasks.value = [];

    // 如果提供了regenerateIndex，查找原始任务并立即从本地UI移除失败图片占位容器
    if (regenerateIndex !== undefined) {
      removeFailedImagePlaceholder(taskId, regenerateIndex);
    }

    // 显示消息
    message.success('开始扩图，请耐心等待');

    // 调用 panImage API
    const response = await panImage({
      prompt: btnPrompt,
      image_urls: imageUrls,
      num: 1, // 默认生成一张图片
      regenerate_index: regenerateIndex // 已经映射过的索引值
    });

    if (response.data && response.data.taskid) {
      // 创建新任务并添加到任务列表
      const newTask: ChatMjTask = createTaskFromResponse(response.data);
      tasks.value.unshift(newTask);

      // 开始轮询任务状态
      pollGptTaskState(Number(newTask.taskid), undefined, 1);
    } else if (response.data && response.data.fail_reason) {
      message.error('扩图失败');
    }
  } catch (error) {
    message.error('请求过程中发生错误');
  }
};

// 处理 Flux 模型的扩图操作
const handleFluxPanAction = async ({
  btnPrompt,
  imageUrls,
  taskId,
  regenerateIndex,
  model
}: {
  btnPrompt: string;
  imageUrls: string[];
  taskId: number;
  regenerateIndex?: number;
  model?: string;
}) => {
  try {
    // 清空搜索任务列表
    searchtasks.value = [];

    // 如果提供了regenerateIndex，查找原始任务并立即从本地UI移除失败图片占位容器
    if (regenerateIndex !== undefined) {
      removeFailedImagePlaceholder(taskId, regenerateIndex);
    }

    // 调用 fluxPanImage API
    const response = await fluxPanImage({
      prompt: btnPrompt,
      image_urls: imageUrls,
      num: 1, // 默认生成一张图片
      model_name: model || 'flux_kontext_fp8_api', // 使用默认Flux模型
      regenerate_index: regenerateIndex // 已经映射过的索引值
    });

    if (response.data && response.data.task) {
      // 创建新任务并添加到任务列表
      const newTask: ChatMjTask = createTaskFromResponse(response.data.task);
      tasks.value.unshift(newTask); // 添加到任务列表顶部

      // 开始轮询任务状态（Flux使用相同的轮询机制）
      pollGptTaskState(Number(newTask.taskid), undefined, 1);
    }
  } catch (error) {
    console.error('Flux扩图请求过程中发生错误:', error);
    message.error('Flux扩图请求过程中发生错误');
  }
};

// 处理删除任务事件
const handleDeleteTask = ({ taskId }: { taskId: number }) => {
  // 从任务列表中移除已删除的任务
  const taskIndex = tasks.value.findIndex(task => task.id === taskId);
  if (taskIndex !== -1) {
    tasks.value.splice(taskIndex, 1);
  }

  // 同时从搜索结果中移除（如果存在）
  const searchTaskIndex = searchtasks.value.findIndex(task => task.id === taskId);
  if (searchTaskIndex !== -1) {
    searchtasks.value.splice(searchTaskIndex, 1);
  }
};

// 处理移除失败占位符的事件
const handleRemoveFailure = ({ taskId, index }: { taskId: number; index: number }) => {
  // 处理主任务列表
  const processTaskList = (taskList: ChatMjTask[]) => {
    const taskIndex = taskList.findIndex(task => task.taskid === taskId);
    if (taskIndex === -1) return false;

    const currentTask = taskList[taskIndex];

    // 确保任务有image_urls且是数组
    if (!currentTask.image_urls || !Array.isArray(currentTask.image_urls)) return false;

    // 检查索引是否合法且对应位置是否为空字符串(失败状态)
    if (index < 0 || index >= currentTask.image_urls.length) return false;

    // 创建图片数组的副本
    const updatedUrls = [...currentTask.image_urls];

    // 从数组中移除该位置的空字符串(失败状态)
    updatedUrls.splice(index, 1);

    // 更新任务的图片数组
    taskList[taskIndex] = {
      ...currentTask,
      image_urls: updatedUrls
    };

    console.log(`已从任务 ${taskId} 中移除索引 ${index} 处的失败占位符`);
    return true;
  };

  // 尝试在主任务列表中处理
  const processedInMainList = processTaskList(tasks.value);

  // 如果主任务列表中没有找到，尝试在搜索结果中处理
  if (!processedInMainList && searchtasks.value.length > 0) {
    processTaskList(searchtasks.value);
  }
};

// 处理失败后的重新imagine请求
const handleImagineAgain = async ({
  prompt,
  promptImg,
  taskId
}: {
  prompt: string;
  promptImg: string[];
  taskId: number;
}) => {
  // 清空搜索任务列表
  searchtasks.value = [];

  // 显示消息
  message.success('开始重新生成图片，请耐心等待');

  try {
    // 构造imagine请求参数，传入原始任务ID
    const response = await imagine({
      prompt,
      base64Array: promptImg,
      channels: currentChannelId.value,
      taskid: taskId // 传入原始任务ID，后端会自动处理删除
    });

    if (response.data && response.data.taskid) {
      // 创建新任务
      const newTask: ChatMjTask = createTaskFromResponse(response.data);
      tasks.value.unshift(newTask); // 添加到任务列表顶部

      // 启动轮询
      pollTaskState(Number(newTask.taskid));

      // 从前端列表中移除失败的任务（后端会删除数据库记录）
      const taskIndex = tasks.value.findIndex(task => task.id === taskId);
      if (taskIndex !== -1) {
        tasks.value.splice(taskIndex, 1);
      }
    }
  } catch (error) {
    message.error('重新生成图片失败');
    console.error('重新生成图片失败:', error);
  }
};

const handleImageDescribeTask = (task: ChatMjTask, num?: number) => {
  // 将新任务插入任务列表顶部
  tasks.value.unshift(task);

  // 根据模型类型选择不同的轮询函数
  if (task.manufacturer === 'OPENAI' || task.manufacturer === 'FLUX') {
    // 如果提供了num参数且大于1，创建多个占位图
    if (num && num > 1) {
      // 创建图片数组
      const imageUrls = Array(num).fill(PLACEHOLDER_IMAGE_URL);

      // 为任务设置图片数组
      (task as any).image_urls = imageUrls;

      // 传递占位图数组和num参数
      pollGptTaskState(Number(task.taskid), imageUrls, num);
    } else {
      // 单图情况
      pollGptTaskState(Number(task.taskid), undefined, 1);
    }
  } else {
    pollTaskState(Number(task.taskid));
  }
};

const systemMenuRef = ref<InstanceType<typeof SystemMenu> | null>(null);

function onImportParams(payload: {
  action: string;
  prompt: string;
  description: string;
  imageUrl: string | null;
  promptImg?: string[];
}) {
  // 调用 system-menu.vue 中的 importParams 方法
  systemMenuRef.value?.importParams({
    action: payload.action,
    prompt: payload.prompt,
    _description: payload.description,
    imageUrl: payload.imageUrl,
    promptImg: payload.promptImg
  });
}
</script>

<template>
  <NLayout has-sider class="mainbox">
    <NLayoutSider collapse-mode="width" :width="450" bordered :native-scrollbar="false">
      <SystemMenu
        ref="systemMenuRef"
        :loading="loading"
        :channels="currentChannelId"
        @generate="handleGenerate"
        @image-describe-task="handleImageDescribeTask"
        @platform-change="handlePlatformChange"
      />
    </NLayoutSider>
    <NInfiniteScroll class="h-full" :distance="40" @load="handleInfiniteScroll">
      <NLayoutContent class="mx-10">
        <div class="mb-3 w-full flex justify-center gap-2">
          <!-- 作品展示 -->
          <NButton type="success" @click="goToWorks">
            <SvgIcon icon="mdi:creation" class="mr-0.5" />
            作品
          </NButton>

          <!-- 频道 -->
          <MjChannels @channel-selected="handleChannelSelected" @initial-channel="handleInitialChannel" />

          <!-- 搜索历史数据 -->
          <div class="w-full flex justify-center gap-x-2">
            <NInputGroup>
              <NInput
                v-model:value="searchInput"
                placeholder="检索生成的图片（回车确认 / 点击搜索按钮）"
                @update:value="researchpage"
                @keydown="handleKeydown"
              />
              <NButton type="info" @click="handleSearch">
                <SvgIcon icon="weui:search-filled" class="text-5" />
                搜索
              </NButton>
            </NInputGroup>
          </div>
        </div>
        <div v-if="searchtasks.length > 0">
          <CardContent
            v-for="task in searchtasks"
            :id="task.id"
            :key="task.id"
            :date="task.uptime"
            :timestamp="task.timestamp"
            :prompt="task.prompt"
            :image-src="task.image_url"
            :image_urls="task.image_urls"
            :task-id="task.taskid"
            :progress="task.progress"
            :action="task.action"
            :prompt-en="task.prompt_en"
            :fail-reason="task.fail_reason"
            :img-seed="task.seed"
            :button="task.button"
            :like-count="task.like_count"
            :is-like="task.is_like"
            :description="task.description"
            :start-time="task.start_time"
            :status="task.status"
            :expected-count="(task as any).expected_count || 1"
            class="mb-3.5"
            :model="task.model"
            :manufacturer="task.manufacturer"
            :prompt-img="task.prompt_img"
            :prompt-img-oss="task.prompt_img_oss"
            @action="handleAction"
            @job-action="handleJobAction"
            @import-params="onImportParams"
            @regenerate="handleRegenerate"
            @flux-regenerate="handleFluxRegenerate"
            @imagine-again="handleImagineAgain"
            @pan-action="handlePanAction"
            @flux-pan-action="handleFluxPanAction"
            @delete="handleDeleteTask"
            @remove-failure="handleRemoveFailure"
          />
        </div>
        <div v-else>
          <CardContent
            v-for="task in tasks"
            :id="task.id"
            :key="task.id"
            :date="task.uptime"
            :timestamp="task.timestamp"
            :prompt="task.prompt"
            :image-src="task.image_url"
            :image_urls="task.image_urls"
            :task-id="task.taskid"
            :progress="task.progress"
            :action="task.action"
            :prompt-en="task.prompt_en"
            :fail-reason="task.fail_reason"
            :img-seed="task.seed"
            :button="task.button"
            :like-count="task.like_count"
            :is-like="task.is_like"
            :description="task.description"
            :start-time="task.start_time"
            :status="task.status"
            :expected-count="(task as any).expected_count || 1"
            class="mb-3.5"
            :model="task.model"
            :manufacturer="task.manufacturer"
            :prompt-img="task.prompt_img"
            :prompt-img-oss="task.prompt_img_oss"
            @action="handleAction"
            @job-action="handleJobAction"
            @import-params="onImportParams"
            @regenerate="handleRegenerate"
            @flux-regenerate="handleFluxRegenerate"
            @imagine-again="handleImagineAgain"
            @pan-action="handlePanAction"
            @flux-pan-action="handleFluxPanAction"
            @delete="handleDeleteTask"
            @remove-failure="handleRemoveFailure"
          />
        </div>
        <NDivider title-placement="center">
          <NText v-if="!hasMore">没有更多了 🤪</NText>
          <NText v-else>加载中...</NText>
        </NDivider>
      </NLayoutContent>
      <NBackTop :right="60" />
    </NInfiniteScroll>
  </NLayout>
</template>

<style scoped>
:deep(.n-layout-scroll-container) {
  overflow-x: unset !important;
}
</style>
