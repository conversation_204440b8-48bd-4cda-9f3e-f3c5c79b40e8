# Common--------------------------------------------------------------------------------------------
SecretId=XXXXXX
SecretKey=******

region=ap-guangzhou
#host=sts.tencentcloudapi.com

# AssumeRole----------------------------------------------------------------------------------------
RoleArn=qcs::cam::uin/1000xxxx:roleName/test
RoleSessionName=sts_sdk

# HmacSHA1 or HmacSHA256, default:HmacSHA1
# SignatureMethod=HmacSHA256

# priority:1.policy; 2.policy.actions & policy.ressources; if all the policy and policy.* are empty, there is no "policy" in assume role
#policy={}
policy.actions=cos:PutObject,cos:GetObject
policy.resources=qcs::cos:ap-guangzhou:uid/1258xxxxx:examplebucket-1258xxxxx/*,qcs::cos:ap-guangzhou:uid/1258xxxxx:bucketB-1258xxxxx/object2
