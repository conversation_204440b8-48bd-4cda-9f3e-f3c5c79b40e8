<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import PromptsData from '../prompts.json';

interface Prompt {
  category: string;
  name: string;
  param: string;
  image: string;
}

interface PromptData {
  category: string;
  name: string;
  param: string;
  image: string;
  weight: number;
}

const prompts = ref<Prompt[]>([]);
const selectedPrompts = ref<PromptData[]>([]);
// emit 事件
const emit = defineEmits<{
  (e: 'update:selected-prompts', value: PromptData[]): void;
}>();

// 导航栏
const categories = [
  '照明',
  '视觉/构图',
  '环境',
  '风格',
  '材质',
  '画面精度',
  '颜色',
  '国风元素',
  '艺术家',
  '电影/游戏/动漫',
  '时代',
  '时尚服饰',
  '人物',
  '人类造型',
  '设计形式'
];

const loadPrompts = () => {
  prompts.value = PromptsData;
};

onMounted(() => {
  loadPrompts();
});

// 监听selectedPrompts变化并发送给父组件
watch(
  selectedPrompts,
  newValue => {
    emit('update:selected-prompts', newValue);
    console.log('selectedPrompts', newValue);
  },
  { deep: true }
);

// 导航栏滑动
const scrollToTab = (event: MouseEvent) => {
  const scrollBox = document.querySelector('.custom-tabs') as HTMLElement;
  const spanLeft = event.clientX;
  const divBox = (event.target as HTMLElement).clientWidth / 2;
  const totalWidths = document.body.clientWidth;
  const widths = totalWidths / 2;
  const spanRight = totalWidths - spanLeft;
  const scrollL = scrollBox.scrollLeft;
  if (spanRight < 100 || spanLeft < 100) {
    scrollBox.scrollLeft = scrollL + (spanLeft - widths) + divBox;
  }
};

// 处理卡片点击
const handleCardClick = (prompt: Prompt) => {
  const index = selectedPrompts.value.findIndex(p => p.param === prompt.param);
  if (index === -1) {
    selectedPrompts.value.push({ ...prompt, weight: 0 });
  } else {
    selectedPrompts.value.splice(index, 1);
  }
};

// 更新权重
const updateWeight = (param: string, weight: number) => {
  const prompt = selectedPrompts.value.find(p => p.param === param);
  if (prompt) {
    prompt.weight = weight;
  }
};

// 检查是否被选中
const isSelected = (param: string) => {
  return selectedPrompts.value.some(p => p.param === param);
};

const updateSelectedPrompts = (newSelectedPrompts: PromptData[]) => {
  selectedPrompts.value = newSelectedPrompts;
};

// 暴露方法给父组件
defineExpose({ updateSelectedPrompts });
</script>

<template>
  <div class="tabs-container">
    <NTabs type="line" class="custom-tabs">
      <NTabPane v-for="category in categories" :key="category" :name="category" :tab="category" @click="scrollToTab">
        <NGrid cols="1 250:2 400:3 550:4 600:5 1000:7" x-gap="6" y-gap="12">
          <NGridItem v-for="prompt in prompts.filter(p => p.category === category)" :key="prompt.param">
            <NCard
              hoverable
              class="promptBox flex justify-center justify-items-center"
              @click="handleCardClick(prompt)"
            >
              <template #cover>
                <NImage preview-disabled :src="prompt.image"></NImage>
              </template>
              <NText class="flex justify-center justify-items-center text-nowrap">
                <NEllipsis class="prompt_name w-full">{{ prompt.name }}</NEllipsis>
              </NText>
              <div @click.stop>
                <NSlider
                  v-if="isSelected(prompt.param)"
                  v-model:value="selectedPrompts.find(p => p.param === prompt.param)!.weight"
                  :step="1"
                  :max="5"
                  :min="0"
                  @update:value="val => updateWeight(prompt.param, val)"
                >
                  <template #thumb>
                    <SvgIcon icon="noto-v1:grinning-cat-with-smiling-eyes" class="text-2xl"></SvgIcon>
                  </template>
                </NSlider>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped>
.tabs-container {
  overflow-x: scroll;
  -webkit-overflow-scrolling: touch;
}

.custom-tabs {
  display: flex;
  margin-bottom: 20px;
  flex-shrink: 0;
}

::-webkit-scrollbar {
  width: 0.01rem;
  opacity: 0;
  display: none;
}

::-webkit-scrollbar-track {
  background-color: #fff;
  opacity: 0;
}

::-webkit-scrollbar-thumb {
  width: 0.01rem;
  border-radius: 0.01rem;
  opacity: 0;
}

:deep(.promptBox) .n-card__content {
  max-height: 4em;
  min-height: 4em;
}

.promptBox {
  cursor: pointer;
}

:deep(.prompt_name) {
  display: flex;
  justify-content: center !important;
}
</style>
