import base64
import re
import aiohttp
import logging
from PIL import Image
from io import BytesIO

logger = logging.getLogger(__name__)

def is_base64_image(data: str) -> bool:
    """
    检查字符串是否为base64编码的图片数据

    Args:
        data: 要检查的字符串

    Returns:
        bool: 如果是base64编码的图片数据则返回True，否则返回False
    """
    if not data or not isinstance(data, str):
        return False

    # 检查是否包含base64前缀
    if "data:image/" in data and ";base64," in data:
        return True

    # 也可能是没有前缀的纯base64数据
    try:
        # 尝试进行base64解码
        # 添加padding (如果需要)
        padding_needed = len(data) % 4
        if padding_needed:
            data += "=" * (4 - padding_needed)

        # 尝试解码几个字符来检查是否为有效的base64
        base64.b64decode(data[:20])
        return True
    except Exception:
        return False

    # return False


# 检查字符串是否为URL
def is_url(data: str) -> bool:
    """
    检查字符串是否为URL

    Args:
        data: 要检查的字符串

    Returns:
        bool: 如果是URL则返回True，否则返回False
    """
    if not data or not isinstance(data, str):
        return False

    # URL模式匹配
    url_pattern = re.compile(
        r'^https?://'  # http:// 或 https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
        r'(?::\d+)?'  # 可选端口
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)

    return bool(url_pattern.match(data))


def detect_image_type(data: str) -> str:
    """
    检测图片数据的类型

    Args:
        data: 要检测的字符串

    Returns:
        str: 图片类型，"base64", "url", 或 "unknown"
    """
    if not data or not isinstance(data, str):
        return "unknown"
    
    # 优先检查URL，因为URL检测更严格
    if is_url(data):
        return "url"
    
    # 然后检查base64
    if is_base64_image(data):
        return "base64"
    
    return "unknown"


def base64_to_binary(base64_data: str) -> bytes:
    """
    将base64编码的图片数据转换为二进制格式

    Args:
        base64_data: base64编码的图片数据，支持data URL格式或纯base64格式

    Returns:
        bytes: 图片的二进制数据

    Raises:
        Exception: 当base64解码失败时抛出异常
    """
    if not base64_data or not isinstance(base64_data, str):
        raise Exception("base64数据不能为空且必须是字符串")
    
    try:
        # 处理data URL格式，移除前缀（如data:image/png;base64,）
        if ',' in base64_data and ';base64,' in base64_data:
            base64_data = base64_data.split(',', 1)[1]
        
        # 添加padding（如果需要）
        padding_needed = len(base64_data) % 4
        if padding_needed:
            base64_data += "=" * (4 - padding_needed)
        
        # 进行base64解码
        binary_data = base64.b64decode(base64_data)
        return binary_data
        
    except Exception as e:
        logger.error(f"base64转二进制失败: {e}")
        raise Exception(f"base64转二进制失败: {str(e)}")


async def url_to_base64(url: str) -> str:
    """
    将URL的图片转换为base64格式

    Args:
        url: 图片URL

    Returns:
        str: base64编码的图片数据
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()  # 确保请求成功
                content_type = response.headers.get('Content-Type', 'image/png')  # 默认 PNG 类型
                data = await response.read()  # 异步读取内容
                base64_data = base64.b64encode(data).decode('utf-8')
                return f"data:{content_type};base64,{base64_data}"
    except aiohttp.ClientError as e:
        logger.error(f"无法处理 URL: {e}")
        raise Exception(f"URL图片下载失败: {str(e)}")
    except Exception as e:
        logger.error(f"URL转base64失败: {e}")
        raise Exception(f"URL转base64失败: {str(e)}")


async def url_to_binary(url: str) -> bytes:
    """
    将URL的图片转换为二进制格式

    Args:
        url: 图片URL

    Returns:
        bytes: 图片的二进制数据

    Raises:
        Exception: 当URL下载失败时抛出异常
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()  # 确保请求成功
                data = await response.read()  # 异步读取内容
                return data
    except aiohttp.ClientError as e:
        logger.error(f"无法处理 URL: {e}")
        raise Exception(f"URL图片下载失败: {str(e)}")
    except Exception as e:
        logger.error(f"URL转二进制失败: {e}")
        raise Exception(f"URL转二进制失败: {str(e)}")


async def image_to_binary(data: str) -> bytes:
    """
    将图片数据转换为二进制格式（统一接口）

    自动检测输入数据类型（URL或base64），并调用相应的转换函数

    Args:
        data: 图片数据，可以是URL或base64编码的字符串

    Returns:
        bytes: 图片的二进制数据

    Raises:
        Exception: 当数据类型未知或转换失败时抛出异常
    """
    if not data or not isinstance(data, str):
        raise Exception("图片数据不能为空且必须是字符串")
    
    try:
        # 检测数据类型
        image_type = detect_image_type(data)
        
        if image_type == "url":
            # URL转二进制
            return await url_to_binary(data)
        elif image_type == "base64":
            # base64转二进制
            return base64_to_binary(data)
        else:
            # 未知类型
            raise Exception("无法识别的图片数据格式，仅支持URL或base64格式")
            
    except Exception as e:
        logger.error(f"图片转二进制失败: {e}")
        raise Exception(f"图片转二进制失败: {str(e)}")
    

proportion = [
    "1:1",
    "16:9",
    "4:3",
    "3:4",
    "9:16",
]


def find_closest_proportion(width: int, height: int) -> str:
    """
    根据图片的宽高尺寸，找到最相近的预定义比例

    Args:
        width: 图片宽度，必须为正整数
        height: 图片高度，必须为正整数

    Returns:
        str: 最相近的比例字符串，如"1:1"、"16:9"等

    Raises:
        Exception: 当宽度或高度无效时抛出异常
    """
    if not isinstance(width, int) or not isinstance(height, int) or width <= 0 or height <= 0:
        raise Exception("图片宽度和高度必须为正整数")
    
    try:
        # 计算实际比例
        actual_ratio = width / height
        
        # 预定义比例对应的数值
        ratio_values = {
            "1:1": 1.0,
            "16:9": 16/9,
            "4:3": 4/3,
            "3:4": 3/4,
            "9:16": 9/16,
        }
        
        # 找到差值最小的比例
        closest_proportion = min(proportion, key=lambda p: abs(ratio_values[p] - actual_ratio))
        
        return closest_proportion
        
    except Exception as e:
        logger.error(f"比例计算失败: {e}")
        raise Exception(f"比例计算失败: {str(e)}")


async def get_image_proportion(img: str) -> str:
    """
    获取图片的比例，自动检测图片类型（URL或base64）并返回最相近的预定义比例

    Args:
        img: 图片数据，可以是URL或base64编码的字符串

    Returns:
        str: 最相近的比例字符串，如"1:1"、"16:9"等

    Raises:
        Exception: 当图片数据无效、下载失败或处理失败时抛出异常
    """
    if not img or not isinstance(img, str):
        raise Exception("图片数据不能为空且必须是字符串")
    
    try:
        # 使用现有函数将图片转换为二进制数据
        binary_data = await image_to_binary(img)
        
        # 使用PIL从内存中打开图片并获取尺寸
        try:
            with Image.open(BytesIO(binary_data)) as image:
                width, height = image.size
                logger.info(f"图片尺寸: {width}x{height}")
        except Exception as e:
            logger.error(f"PIL图片处理失败: {e}")
            raise Exception(f"无法解析图片数据: {str(e)}")
        
        # 计算并返回最相近的比例
        closest_ratio = find_closest_proportion(width, height)
        logger.info(f"检测到的图片比例: {closest_ratio} (实际比例: {width}/{height})")
        
        return closest_ratio
        
    except Exception as e:
        logger.error(f"图片比例检测失败: {e}")
        raise Exception(f"图片比例检测失败: {str(e)}")

