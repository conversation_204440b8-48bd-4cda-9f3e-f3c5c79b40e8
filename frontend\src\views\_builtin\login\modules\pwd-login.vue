<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { debounce } from 'lodash-es';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';
import { fetchCheckOfficeIp } from '@/service/api/auth';
import { encrypt } from '@/utils/crypto';
import tencentCaptcha from '@/utils/tencentCaptcha';
import { getIsMobile } from '@/utils/mobile';

defineOptions({
  name: 'PwdLogin'
});

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { formRules } = useFormRules();

interface FormModel {
  userName: string;
  password: string;
}

const model: FormModel = reactive({
  // userName: 'Soybean',
  // password: '123456'
  userName: '',
  password: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  return {
    userName: [
      { required: true, message: '请输入用户名或邮箱' },
      {
        validator: (_rule, value) => {
          // 如果值为空，直接返回true，让required规则来处理
          if (!value) return true;

          // 用户名格式: 允许字母数字下划线, 4-32位
          const usernamePattern = /^[a-zA-Z0-9_]{4,32}$/;
          // 邮箱格式
          const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

          if (usernamePattern.test(value) || emailPattern.test(value)) {
            return true;
          }
          return false;
        },
        message: '请输入正确的用户名或邮箱格式',
        trigger: ['input', 'blur']
      }
    ],
    password: formRules.pwd
  };
});

// const loginModuleRecord = {
//   'code-login': 'page.login.tabs.codeLogin',
//   register: 'page.login.tabs.register'
// } as const;
const isLoading = ref(false);

const handleSubmit = async () => {
  await validate();
  try {
    const captchaRes = await tencentCaptcha.show(); // 调用腾讯验证码
    if (captchaRes.ret === 0) {
      console.log('验证通过');
      const encryptedPassword = encrypt(model.password);
      await authStore.login(model.userName, encryptedPassword, captchaRes.ticket, captchaRes.randstr);
    } else {
      console.log('验证码未通过');
    }
  } catch (error) {
    console.error('验证码错误:', error);
  } finally {
    isLoading.value = false;
  }
};

// 登录按钮防抖
const debouncedSubmit = debounce(handleSubmit, 300);

// type AccountKey = 'super' | 'admin' | 'user';

// interface Account {
//   key: AccountKey;
//   label: string;
//   userName: string;
//   password: string;
// }

// const accounts = computed<Account[]>(() => [
//   {
//     key: 'super',
//     label: $t('page.login.pwdLogin.superAdmin'),
//     userName: 'Super',
//     password: '123456'
//   },
//   {
//     key: 'admin',
//     label: $t('page.login.pwdLogin.admin'),
//     userName: 'Admin',
//     password: '123456'
//   },
//   {
//     key: 'user',
//     label: $t('page.login.pwdLogin.user'),
//     userName: 'User',
//     password: '123456'
//   }
// ]);

// async function handleAccountLogin(account: Account) {
//   await authStore.login(account.userName, account.password);
// }

async function handleDingTalkLogin() {
  await authStore.handleDingTalkLogin();
}

const showOtherLogin = ref(false);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const redirectParam = urlParams.get('redirect');
  let newToken = null;

  if (redirectParam) {
    // 处理钉钉回调的情况
    const redirectParams = new URLSearchParams(redirectParam.split('?')[1]);
    newToken = redirectParams.get('token');
  } else {
    // 直接从URL参数获取token (钉钉登录直接返回的情况)
    newToken = urlParams.get('token');
  }

  if (newToken) {
    const loginToken: Api.Auth.LoginToken = { token: newToken, refreshToken: '' };
    // 设置钉钉登录状态
    authStore.isDingding = true;
    const pass = await authStore.loginByToken(loginToken);
    if (pass) {
      await authStore.initUserInfo();
      if (authStore.routeStore.isInitAuthRoute) {
        window.$notification?.success({
          title: $t('page.login.common.loginSuccess'),
          content: $t('page.login.common.welcomeBack', { userName: authStore.userInfo.userName }),
          duration: 4500
        });
      }

      // 清除URL中的参数并跳转到首页
      if (authStore.isLogin) {
        authStore.router.replace('/');
        return;
      }
    }

    // 清除URL中的参数
    urlParams.delete('redirect');
    urlParams.delete('token');
    const newUrl = `${window.location.pathname}${urlParams.toString() ? `?${urlParams.toString()}` : ''}`;
    authStore.router.replace(newUrl);
  } else {
    await authStore.initUserInfo();
  }

  try {
    const { data } = await fetchCheckOfficeIp();
    if (data) {
      showOtherLogin.value = data.allowed === 1;
    }
  } catch (error) {
    console.error('检查IP失败:', error);
    showOtherLogin.value = false;
  }
});
const isMobile = ref<boolean>(getIsMobile());
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="debouncedSubmit">
    <NFormItem path="userName">
      <NInput v-model:value="model.userName" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="isMobile ? 16 : 24">
      <div class="flex-y-center justify-between">
        <NCheckbox>{{ $t('page.login.pwdLogin.rememberMe') }}</NCheckbox>
        <NButton v-if="!isMobile" quaternary @click="toggleLoginModule('reset-pwd')">
          {{ $t('page.login.pwdLogin.forgetPassword') }}
        </NButton>
      </div>
      <!-- 登录按钮 字体颜色白色 -->
      <NButton
        type="primary"
        size="large"
        round
        block
        :loading="authStore.loginLoading"
        class="text-white"
        @click="handleSubmit"
      >
        {{ $t('common.confirm') }}
      </NButton>

      <div v-if="!showOtherLogin" quaternary class="flex-y-center justify-between gap-12px">
        <!--
 <NButton class="flex-1" block @click="toggleLoginModule('code-login')">
          {{ $t(loginModuleRecord['code-login']) }}
        </NButton>
-->

        <!-- v-if="!showOtherLogin" -->
        <NButton class="flex-1" round block @click="toggleLoginModule('register')">注册</NButton>
      </div>

      <div v-if="isMobile" class="text-center">
        <NButton quaternary @click="toggleLoginModule('reset-pwd')">
          {{ $t('page.login.pwdLogin.forgetPassword') }}
        </NButton>
      </div>

      <template v-if="showOtherLogin && !isMobile">
        <NDivider class="text-14px text-#666 !m-0">{{ $t('page.login.pwdLogin.otherAccountLogin') }}</NDivider>
        <NSpace justify="center" class="otherauth">
          <NTooltip trigger="hover">
            <template #trigger>
              <div class="dingding_icon" @click="handleDingTalkLogin">
                <SvgIcon icon="uiw:dingding" />
              </div>
            </template>
            钉钉登录
          </NTooltip>
          <NDivider vertical />
          <NTooltip trigger="hover">
            <template #trigger>
              <div class="osa_icon">
                <SvgIcon icon="carbon:network-enterprise" />
              </div>
            </template>
            OSA登录
          </NTooltip>
        </NSpace>
      </template>
    </NSpace>
  </NForm>
</template>

<style scoped>
.otherauth > * {
  display: flex;
  font-size: 2.2em;
  align-items: center;
}

.dingding_icon {
  /* width: 12% !important;
  height: 100% !important; */
  color: rgb(0, 145, 254) !important;
  cursor: pointer !important;
}

.osa_icon {
  cursor: pointer !important;
}
</style>
