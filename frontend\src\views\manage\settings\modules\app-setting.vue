<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';
import JsonEditorVue from 'json-editor-vue';
import { NSpace } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'AppSetting'
});

interface Props {
  operateType: 'add' | 'edit';
  rowData?: any | null;
}

const props = defineProps<Props>();

type SubmitModel = {
  id?: number;
  key_type: string;
  key_code: string;
  value_type: string;
  key_value: string;
  sync_redis: number;
  pid?: number;
  remark?: string;
  crtime?: string;
  edituser?: string;
  seq: number;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '增加字典' : '修改字典';
});

type Model = {
  id?: number;
  key_type: string;
  key_code: string;
  value_type: string;
  key_value: string;
  sync_redis: number;
  pid?: number;
  remark?: string;
  crtime?: string;
  edituser?: string;
  seq: number;
};

const model: Model = reactive(createDefaultModel());
const data = reactive({
  value: {},
  mode: 'text',
  readOnly: false,
  parser: JSON
});

const is_redis = ref('0');

function createDefaultModel(): Model {
  return {
    key_type: '',
    key_code: '',
    value_type: 'text',
    key_value: '',
    sync_redis: 0,
    pid: 0,
    remark: '',
    seq: 100
  };
}

type RuleKey = 'key_code' | 'key_value';

const rules: Record<RuleKey, any> = {
  key_code: defaultRequiredRule,
  key_value: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'edit' && props.rowData) {
    const rowData = { ...props.rowData };
    // rowData.credit = 0;
    // console.log(rowData);
    Object.assign(model, rowData);
  }
  console.log('model.sync_redis =', model.sync_redis);
  is_redis.value = model.sync_redis === 1 ? '1' : '0';
  if (model.value_type === 'json') {
    try {
      data.value = JSON.parse(model.key_value || '{}');
    } catch (e) {
      console.error('Invalid JSON format:', e);
      data.value = {}; // 重置为默认对象，以免异常状态
    }
  }
  // Object.assign(model, createDefaultModel());
  // if (props.operateType === 'edit' && props.rowData) {
  //   const rowData = { ...props.rowData };
  //   rowData.credit = 0;
  //   Object.assign(model, rowData);
  // }
}

function closeDrawer() {
  visible.value = false;
}

async function handleChange() {
  if (is_redis.value === '0') model.sync_redis = 0;
  else model.sync_redis = 1;
}

async function handleSubmit() {
  await validate();
  const submitModel = {
    ...model
  };
  closeDrawer();
  emit('submitted', submitModel);
}

// const valueTypeOptions = [
//   { label: 'Text', value: 'text' },
//   { label: 'Json', value: 'json' }
// ];

const jsonEditorVueRef = ref();
onMounted(() => {
  // jsonEditorVueRef.value.jsonEditor.focus();
});
const stringified = ref(false);

watch(
  () => model.value_type,
  newType => {
    if (newType === 'json') {
      try {
        data.value = JSON.parse(model.key_value || '{}');
      } catch (e) {
        console.error('Invalid JSON format:', e);
      }
    }
  }
);

watch(
  () => data.value,
  newValue => {
    if (model.value_type === 'json') {
      model.key_value = JSON.stringify(newValue);
    }
  },
  { deep: true }
);

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    if (model.value_type === 'json') {
      try {
        data.value = JSON.parse(model.key_value || '{}');
      } catch (e) {
        console.error('Invalid JSON format:', e);
      }
    }
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-600px">
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="字典分类" path="key_type">
        <NInput v-model:value="model.key_type" placeholder="请输入字典分类,默认system" />
      </NFormItem>
      <!--
      <NFormItem label="Value类型" path="value_type">
        <NSelect v-model:value="model.value_type" :options="valueTypeOptions" placeholder="请选择Value类型" />
      </NFormItem>
      -->
      <NFormItem label="主键" path="key_code">
        <NInput v-model:value="model.key_code" placeholder="请输入主键" />
      </NFormItem>
      <NSpace>
        <NRadioGroup v-model:value="model.value_type">
          <NRadio value="text">Text</NRadio>
          <NRadio value="json">Json</NRadio>
        </NRadioGroup>
      </NSpace>
      <NFormItem label="Value" path="key_value">
        <NInput v-if="model.value_type === 'text'" v-model:value="model.key_value" placeholder="请输入Value" />
        <JsonEditorVue
          v-if="model.value_type === 'json'"
          ref="jsonEditorVueRef"
          v-model="data.value"
          v-model:mode="data.mode"
          :read-only="data.readOnly"
          :parser="data.parser"
          :stringified="stringified"
          placeholder="请输入Value"
        />
      </NFormItem>
      <NFormItem label="父ID" path="pid">
        <NInputNumber v-model:value="model.pid" type="number" placeholder="请输入父ID" clearable />
      </NFormItem>
      <NFormItem label="是否同步Redis" path="sync_redis">
        <NRadioGroup v-model:value="is_redis">
          <NRadio value="0" @change="handleChange">否</NRadio>
          <NRadio value="1" @change="handleChange">是</NRadio>
        </NRadioGroup>
      </NFormItem>
      <NFormItem label="排序" path="seq">
        <NInputNumber v-model:value="model.seq" type="number" placeholder="排序" clearable />
      </NFormItem>
      <NFormItem label="备注" path="remark">
        <NInput v-model:value="model.remark" placeholder="请输入备注" />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace>
        <NButton @click="closeDrawer">取消</NButton>
        <NButton type="primary" @click="handleSubmit">确认</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
