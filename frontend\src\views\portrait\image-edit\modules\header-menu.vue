<script lang="ts" setup></script>

<template>
  <NCard class="h-full" :bordered="false">
    <NFlex justify="end" align="center" class="h-full">
      <NButton>更换图片</NButton>
      <NButtonGroup class="mx-5">
        <NButton type="info">导出图片</NButton>

        <NButton type="info" class="w-5">
          <template #icon>
            <SvgIcon icon="mingcute:more-2-fill" />
          </template>
        </NButton>
      </NButtonGroup>
    </NFlex>
  </NCard>
</template>

<style scoped>
.n-card :deep(.n-card__content) {
  padding: 0;
}

:deep(.n-button-group) {
  gap: 1px !important;
}
</style>
