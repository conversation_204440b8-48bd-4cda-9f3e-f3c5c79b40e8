import CryptoJS from 'crypto-js';

const SECRET_KEY = import.meta.env.VITE_SECRET_KEY;
// 将Base64密钥转换为WordArray
const key = CryptoJS.enc.Base64.parse(SECRET_KEY);

export function encrypt(text: string): string {
  // 生成随机IV
  const iv = CryptoJS.lib.WordArray.random(16);

  // 使用CFB模式加密
  const encrypted = CryptoJS.AES.encrypt(text, key, {
    iv,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.Pkcs7
  });

  // 拼接IV和密文并Base64编码
  const ivCiphertext = iv.concat(encrypted.ciphertext);
  return CryptoJS.enc.Base64.stringify(ivCiphertext);
}

export function decrypt(ciphertext: string): string {
  // Base64解码获取完整密文
  const ivCiphertext = CryptoJS.enc.Base64.parse(ciphertext);

  // 分离IV和密文
  const iv = CryptoJS.lib.WordArray.create(ivCiphertext.words.slice(0, 4));
  const actualCiphertext = CryptoJS.lib.WordArray.create(ivCiphertext.words.slice(4));

  // 解密
  const decrypted = CryptoJS.AES.decrypt(
    CryptoJS.lib.CipherParams.create({
      ciphertext: actualCiphertext
    }),
    key,
    {
      iv,
      mode: CryptoJS.mode.CFB,
      padding: CryptoJS.pad.Pkcs7
    }
  );

  return decrypted.toString(CryptoJS.enc.Utf8);
}
