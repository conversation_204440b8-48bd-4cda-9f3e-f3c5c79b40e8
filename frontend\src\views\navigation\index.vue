<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchToolset, fetchTypeToolsets } from '@/service/api/manage';
import CardContent from './modules/card-content.vue';

// 定义工具类型
const toolTypes = ['全部', '聊天', '绘图', '写作工具', '办公工具', '设计工具', '视频', '编程工具', '音乐', '其他'];

// 数据存储
const toolsets = ref<Record<string, any[]>>(Object.fromEntries(toolTypes.map(type => [type, []])));

// 分页参数
const currentPage = ref(1);
const pageSize = 12;
const isLoading = ref(false);
const noMoreData = ref(false);

// 活动标签
const activeTab = ref('全部');

// 获取数据函数
const fetchToolsets = async (page: number, size: number) => {
  isLoading.value = true;
  const response = await fetchToolset(page, size);
  if (response && response.data && response.data.records) {
    if (response.data.records.length === 0) {
      noMoreData.value = true;
    } else {
      response.data.records.forEach((record: any) => {
        if (toolsets.value[record.type]) {
          toolsets.value[record.type].push(record);
        }
        toolsets.value['全部'].push(record);
      });
    }
  }
  isLoading.value = false;
};

// 获取特定类型的工具集
const fetchTypeSpecificToolsets = async (type: string, page: number, size: number) => {
  isLoading.value = true;
  const response = await fetchTypeToolsets(type, page, size);
  if (response && response.data && response.data.records) {
    toolsets.value[type] = response.data.records;
  }
  isLoading.value = false;
};

// 初始化加载第一页数据
onMounted(() => {
  fetchToolsets(currentPage.value, pageSize);
});

// 处理加载更多数据
const handleLoadMore = async () => {
  if (!isLoading.value && !noMoreData.value && activeTab.value === '全部') {
    currentPage.value += 1;
    await fetchToolsets(currentPage.value, pageSize);
  }
};

// 处理标签切换
const handleTabChange = async (tabName: string) => {
  if (tabName !== activeTab.value) {
    if (toolsets.value[tabName].length === 0 && tabName !== '全部') {
      await fetchTypeSpecificToolsets(tabName, 1, pageSize);
    }
    activeTab.value = tabName;
  }
};
</script>

<template>
  <NTabs
    type="line"
    :default-value="activeTab"
    class="full-width-tabs"
    justify-content="center"
    @update:value="handleTabChange"
  >
    <NTabPane v-for="type in toolTypes" :key="type" :name="type" :tab="type" class="body">
      <NInfiniteScroll :distance="10" @load="handleLoadMore">
        <main>
          <div v-if="type === '全部'">
            <div class="notification-container">
              <SvgIcon icon="f7:speaker-2" />
              <NText class="notification-text ml-1">
                以下工具是我们测评推荐的实用工具，非本后台应用，本后台应用请从左边栏目进入。
              </NText>
            </div>
            <div v-for="subType in toolTypes.slice(1)" :key="subType">
              <template v-if="toolsets[subType].length > 0">
                <NH2 class="title">{{ subType }}</NH2>
                <NGrid cols="1 s:1 m:2 l:3 xl:3 2xl:4" responsive="screen" :x-gap="16" :y-gap="16">
                  <NGridItem v-for="tool in toolsets[subType]" :key="tool.id">
                    <NCard bordered embedded hoverable class="data">
                      <CardContent :tool="tool" />
                    </NCard>
                  </NGridItem>
                </NGrid>
              </template>
            </div>
          </div>
          <div v-else>
            <div class="notification-container mb-3">
              <SvgIcon icon="f7:speaker-2" />
              <NText class="notification-text ml-1">
                以下工具是我们测评推荐的实用工具，非本后台应用，本后台应用请从左边栏目进入。
              </NText>
            </div>
            <NGrid cols="1 s:1 m:2 l:3 xl:3 2xl:4" responsive="screen" :x-gap="16" :y-gap="16">
              <NGridItem v-for="tool in toolsets[type]" :key="tool.id">
                <NCard bordered embedded hoverable class="data">
                  <CardContent :tool="tool" />
                </NCard>
              </NGridItem>
            </NGrid>
          </div>
          <div v-if="noMoreData && type === '全部'" class="text">
            <NDivider title-placement="center">没有更多了</NDivider>
          </div>
        </main>
      </NInfiniteScroll>
      <NBackTop :right="50" />
    </NTabPane>
  </NTabs>
</template>

<style scoped>
.data {
  min-height: 15em;
  height: 100%;
}

.title {
  margin-top: 1em;
}

.body {
  display: flex;
  margin: 0 50px;
  width: 95%;
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
}

.text {
  text-align: center;
  margin-top: 1.5em;
}

.notification-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem auto;
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  width: fit-content;
  max-width: 90%;
}

.notification-text {
  color: #606266;
  font-size: 0.95rem;
  line-height: 1.4;
}
</style>
