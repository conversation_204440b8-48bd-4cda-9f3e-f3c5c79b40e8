import logging
import os
import httpx
import io
import zipfile
import base64
from fastapi import APIRouter, Header, Request
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from tenacity import retry, stop_after_attempt, wait_exponential
from volcengine.visual.VisualService import VisualService
from config import app_settings
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取远程服务的IP地址
upload_service_url = "http://localhost:5002"


class FileItem(BaseModel):
  filename: str  # 原始文件名
  data: str  # base64编码的文件数据


class EnlargeRequest(BaseModel):
  file: List[FileItem]  # 修改为包含文件名和数据的列表
  inputType: str
  model: str
  faceStrong: bool
  isOpenAudio: bool
  enlarge: int


class LogoInfo(BaseModel):
  add_logo: bool
  position: int = 0
  language: int = 0
  opacity: float = 0.3
  logo_text_content: str = ""


class VolcengineEnlargeRequest(BaseModel):
  file: List[FileItem]  # 包含文件名和base64数据的列表
  enlarge: str  # 放大倍率，如"default", "2x", "3x", "4x"
  enable_hdr: bool = False
  enable_wb: bool = False
  result_format: int = 0  # 0:png, 1:jpeg
  jpg_quality: int = 95
  hdr_strength: float = 1.0
  logo_info: Optional[LogoInfo] = None


async def upload_zip(zip_buffer: io.BytesIO, token: str):
  url = f"{upload_service_url}/common/upload/enlarge_images_zip"
  zip_buffer.seek(0)
  files = {'file': ('images.zip', zip_buffer, 'application/zip')}
  headers = {'Authorization': f'Bearer {token}'} if token else {}

  async with httpx.AsyncClient() as client:
    response = await client.post(url, files=files, headers=headers)
    response_data = response.json()

    if response_data.get("code") == "0000":
      return response_data.get("data").get("file_path")
    else:
      raise ClientVisibleException("上传失败，请重试")


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    reraise=True
)
async def download_image_from_url(url: str) -> bytes:
    """从URL下载图像数据，带重试机制"""
    try:
        # 替换域名
        if "https://xxfpfg-gz.gdsre.cn/" in url:
            new_url = url.replace("https://xxfpfg-gz.gdsre.cn/", "https://ai-gz-1259109643.cos.ap-guangzhou.myqcloud.com/")
            logger.info(f"URL已替换：原URL={url}, 新URL={new_url}")
            url = new_url
            
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            if response.status_code == 200:
                return response.content
            else:
                logger.error(f"下载图像失败，状态码: {response.status_code}, URL: {url}")
                raise ClientVisibleException("构建压缩包失败")
    except httpx.RequestError as e:
        logger.error(f"请求错误: {str(e)}, URL: {url}")
        raise
    except Exception as e:
        logger.error(f"下载图像时发生未知错误: {str(e)}, URL: {url}")
        raise


@router.post("/get_enlarge_file", tags=["enlarge"])
async def get_enlarge_file(request: EnlargeRequest, req: Request, authorization: str = Header(None)):
  url = f"{app_settings.ai_server}/enlarge/get_enlarge_file"
  # url = f"http://172.16.0.177:5002/enlarge/get_enlarge_file"

  # 组装请求数据，只传递 base64 数据
  data = {
    "file": [file_item.data for file_item in request.file],
    "inputType": request.inputType,
    "model": request.model,
    "faceStrong": request.faceStrong,
    "isOpenAudio": request.isOpenAudio,
    "enlarge": request.enlarge
  }

  token = None
  if authorization:
    parts = authorization.split()
    if len(parts) == 2 and parts[0] == "Bearer":
      token = parts[1]

  try:
    async with httpx.AsyncClient(timeout=300) as client:
      response = await client.post(url, json=data)

      logger.info(f"Response: {response.content}")

      if response.status_code == 200:
        response_data = response.json()
        if response_data.get("code") == "0000":
          # 获取响应数据
          response_urls = response_data.get("data")
          
          # 根据输入类型处理响应
          if request.inputType == "batch_images":
            # 确保响应数据是列表
            if not isinstance(response_urls, list):
              response_urls = [response_urls]
              
            # 处理URL列表并创建压缩包
            zip_buffer = io.BytesIO()
            failed_downloads = []
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
              for idx, (image_url, file_item) in enumerate(zip(response_urls, request.file)):
                # 获取原始文件名和扩展名
                original_filename, original_ext = os.path.splitext(file_item.filename)
                # 设置新的文件名
                new_filename = f"{original_filename}_copy{original_ext}"
                
                # 下载图像
                try:
                  image_bytes = await download_image_from_url(image_url)
                  # 将图像数据添加到压缩包
                  zip_file.writestr(new_filename, image_bytes)
                except Exception as e:
                  logger.error(f"处理图像失败: {image_url}, 错误: {str(e)}")
                  failed_downloads.append({
                    "filename": file_item.filename,
                    "url": image_url,
                    "error": str(e)
                  })
            
            if failed_downloads:
              logger.warning(f"部分图像处理失败: {failed_downloads}")
              if len(failed_downloads) == len(response_urls):
                raise ClientVisibleException("图像处理失败")
              else:
                logger.info(f"成功处理 {len(response_urls) - len(failed_downloads)}/{len(response_urls)} 个图像")

            # 上传zip文件并获取文件路径
            file_path = await upload_zip(zip_buffer, token)
            # 动态构建下载链接
            download_link = f"{app_settings.ding_talk_redirect_uri}/proxy-default/upload/enlarge/{os.path.basename(file_path)}"
            return {
              "code": "0000",
              "data": {
                "file_path": download_link,
                "filename": "images.zip",
                "inputType": request.inputType,
                "failed_downloads": failed_downloads if failed_downloads else None,
                "success_count": len(response_urls) - len(failed_downloads),
                "total_count": len(response_urls)
              }
            }
          else:
            # 对于单个图像/视频，直接返回URL
            return {
              "code": "0000",
              "data": {
                "response_data": response_urls,
                "inputType": request.inputType
              }
            }
        else:
          logger.error(f"Error from remote service: {response_data}")
          raise ClientVisibleException("处理失败，请重试")
      else:
        logger.error(f"Non-200 status code: {response.status_code}")
        raise ClientVisibleException("请求失败，请重试")

  except httpx.RequestError as e:
    logger.error(f"An error occurred while requesting {e.request.url!r}.")
    raise ClientVisibleException("请求失败，请重试")
  except Exception as e:
    logger.error(f"An error occurred: {str(e)}")
    raise ClientVisibleException("处理失败，请重试") from e


# 计算图片分辨率，根据放大倍率确定resolution_boundary
def calculate_resolution(image_data: str, enlarge: str) -> Dict[str, Any]:
    """
    计算图片分辨率，并根据放大倍率确定resolution_boundary
    
    Args:
        image_data: base64编码的图片数据
        enlarge: 放大倍率，如"2x", "3x", "4x"或"default"
        
    Returns:
        包含resolution_boundary和图片是否超过限制的信息
    """
    import io
    from PIL import Image
    
    # 解码base64数据
    try:
        image_bytes = base64.b64decode(image_data.split(',')[1] if ',' in image_data else image_data)
        img = Image.open(io.BytesIO(image_bytes))
        width, height = img.size
        
        # 如果是default，不设置resolution_boundary
        if enlarge == "default":
            return {
                "resolution_boundary": None,
                "is_oversized": False,
                "original_width": width,
                "original_height": height
            }
        
        # 提取倍率数字
        scale = int(enlarge[0]) if enlarge in ["2x", "3x", "4x"] else 1
        
        # 计算放大后的尺寸
        target_width = width * scale
        target_height = height * scale
        
        # 检查是否超过2K限制 (2048x1152)
        if target_width > 2048 or target_height > 1152:
            return {
                "resolution_boundary": None,
                "is_oversized": True,
                "original_width": width,
                "original_height": height,
                "target_width": target_width,
                "target_height": target_height
            }
        
        # 根据放大倍率设置resolution_boundary
        if scale == 2:
            return {
                "resolution_boundary": "1080p",  # 1080p对应1920x1080
                "is_oversized": False,
                "original_width": width,
                "original_height": height
            }
        elif scale == 3:
            return {
                "resolution_boundary": "720p",  # 720p对应1280x720
                "is_oversized": False,
                "original_width": width,
                "original_height": height
            }
        elif scale == 4:
            return {
                "resolution_boundary": "480p",  # 480p对应640x480
                "is_oversized": False,
                "original_width": width,
                "original_height": height
            }
        
        # 默认返回
        return {
            "resolution_boundary": None,
            "is_oversized": False,
            "original_width": width,
            "original_height": height
        }
    except Exception as e:
        logger.error(f"计算图片分辨率失败: {str(e)}")
        return {
            "resolution_boundary": None,
            "is_oversized": False,
            "error": str(e)
        }


@router.post("/volcengine_enlarge", tags=["enlarge"])
async def volcengine_enlarge(request: VolcengineEnlargeRequest, req: Request, authorization: str = Header(None)):
    """
    调用火山引擎图像增强API
    
    Args:
        request: 包含文件和处理参数的请求
        req: 原始请求对象
        authorization: 认证头信息
        
    Returns:
        增强处理后的图像URL
    """
    access_key_id = app_settings.volcengine_access_key_id
    secret_access_key = app_settings.volcengine_secret_access_key
    
    if not access_key_id or not secret_access_key:
        logger.error("火山引擎认证信息未配置")
        raise ClientVisibleException("生成失败")
    
    # 处理图片数据
    if not request.file or len(request.file) == 0:
        raise ClientVisibleException("请提供图片")
    
    # 仅处理第一个图像，因为API只支持单张图片
    file_item = request.file[0]
    
    # 计算分辨率和检查尺寸限制
    resolution_info = calculate_resolution(file_item.data, request.enlarge)
    
    if resolution_info.get("is_oversized", False):
        raise ClientVisibleException("图片最大放大尺寸为2K(2048 * 1152px),请降低图片尺寸")
    
    try:
        # 初始化火山引擎VisualService
        visual_service = VisualService()
        
        # 设置AK和SK
        visual_service.set_ak(access_key_id)
        visual_service.set_sk(secret_access_key)
        
        # 处理base64数据，确保移除可能存在的前缀
        image_data = file_item.data
        # 如果数据包含base64前缀，只保留实际的base64部分
        if ',' in image_data:
            image_data = image_data.split(',', 1)[1]
        
        # 构建请求数据
        form = {
            "req_key": "lens_lqir",
            "binary_data_base64": [image_data],
            "return_url": True,
            "enable_hdr": request.enable_hdr,
            "enable_wb": request.enable_wb,
            "result_format": request.result_format,
            "jpg_quality": request.jpg_quality,
            "hdr_strength": request.hdr_strength
        }
        
        # 添加日志，记录请求前的数据状态
        logger.info(f"正在调用火山引擎SDK，base64数据长度：{len(image_data)}")
        
        # 添加分辨率边界，如果有
        if resolution_info.get("resolution_boundary"):
            form["resolution_boundary"] = resolution_info["resolution_boundary"]
        
        # 添加水印信息，如果有
        if request.logo_info:
            form["logo_info"] = request.logo_info.dict()
            
        # 调用SDK处理图像
        response = visual_service.cv_process(form)
        logger.info(f"火山引擎SDK响应: {response}")
        
        # 解析SDK响应
        if response and 'code' in response and response['code'] == 10000:
            # 获取处理后的图像URL
            image_urls = response.get('data', {}).get('image_urls', [])
            
            if image_urls and len(image_urls) > 0:
                # 返回第一个URL，因为我们只处理了一张图片
                original_width = resolution_info.get("original_width")
                original_height = resolution_info.get("original_height")
                
                # 计算放大后的尺寸
                enlarge_scale = int(request.enlarge[0]) if request.enlarge in ["2x", "3x", "4x"] else 1
                enlarge_width = original_width * enlarge_scale if original_width else None
                enlarge_height = original_height * enlarge_scale if original_height else None
                
                return {
                    "code": "0000",
                    "data": {
                        "image_url": image_urls[0],
                        "original_size": f"{original_width} * {original_height}" if original_width and original_height else None,
                        "enlarge_size": f"{enlarge_width} * {enlarge_height}" if enlarge_width and enlarge_height else None
                    }
                }
            else:
                logger.error(f"火山引擎SDK未返回图像URL: {response}")
                raise ClientVisibleException("图像处理失败，未返回结果")
        else:
            error_message = response.get('message', '未知错误')
            logger.error(f"火山引擎SDK返回错误: {response}")
            raise ClientVisibleException("图像处理失败")
    
    except Exception as e:
        logger.error(f"调用火山引擎SDK时发生错误: {str(e)}")
        # 尝试解析错误响应
        try:
            if hasattr(e, 'response') and e.response:
                logger.error(f"火山引擎SDK错误详情: {e.response}")
        except:
            pass
        raise ClientVisibleException("处理失败，请重试") from e
