from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship
from utils.database import Base

class SystemSettings(Base):
    __tablename__ = 'system_settings'
    __table_args__ = {'comment': '系统配置项'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(Integer, ForeignKey('system_settings_group.id'), nullable=False, comment='所属分组ID')
    config_key = Column(String(128), nullable=False, comment='配置键名')
    config_value = Column(Text, nullable=True, comment='配置值')
    data_type = Column(String(32), default='string', comment='数据类型: string, number, boolean, json, password')
    form_type = Column(String(32), default='input', comment='表单类型: input, textarea, select, radio, checkbox, switch')
    options = Column(Text, nullable=True, comment='选项数据,用于select/radio/checkbox,JSON格式')
    placeholder = Column(String(255), nullable=True, comment='表单提示文本')
    description = Column(Text, nullable=True, comment='配置描述')
    is_required = Column(Boolean, default=False, comment='是否必填')
    is_sensitive = Column(Boolean, default=False, comment='是否敏感数据')
    seq = Column(Integer, default=0, comment='排序')
    status = Column(Boolean, default=True, comment='状态: 1启用, 0禁用')
    cached = Column(Boolean, default=False, comment='是否缓存到Redis')
    crtime = Column(DateTime, default=func.now(), comment='创建时间')
    edittime = Column(DateTime, default=func.now(), onupdate=func.now(), comment='修改时间')
    edituser = Column(String(32), nullable=True, comment='修改人')

    def __repr__(self):
        return f"<SystemSettings(id={self.id}, config_key={self.config_key}, group_id={self.group_id})>" 