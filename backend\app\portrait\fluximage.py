#coding=utf-8
import logging
from fastapi import APIRouter, Depends, BackgroundTasks, Request
from pydantic import BaseModel, Field
from typing import Optional, List
import time
import json
import asyncio
import nanoid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from service.credit import CreditOperator
from service.flux.draw import create_image, get_result
from models.chat_mj_tasks import chat_models, ChatMjTasks, ChatMjTasksOut, ChatMjTasksModel, ChatMjTasksStatus, ChatMjTasksAction
from models.users import User, get_request_user
from utils.database import get_db
from utils.common import contains_chinese
from service.translate import get_translate
from service.file import save_image_by_url, save_image_b64data
from utils.exceptions import ClientVisibleException
from utils.image import is_base64_image, is_url, url_to_base64
from utils.asset_storage import store_asset_from_instance
from service.comfyui.draw import upload_image_to_comfyui, submit_task_to_comfyui, query_task_status_from_comfyui, get_image_from_comfyui, replace_workflow_data
from utils.redis import redis
import threading

# Redis key前缀，用于存储task的image_urls数据
REDIS_TASK_KEY_PREFIX = "fluximage:task:"
# Redis缓存过期时间（1天）
REDIS_CACHE_EXPIRY = 86400

# 任务锁字典，用于防止并发更新同一任务
task_locks = {}
task_lock = threading.Lock()

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取任务的Redis键名
def get_redis_task_key(taskid):
    return f"{REDIS_TASK_KEY_PREFIX}{taskid}"

# 从Redis获取任务的image_urls数据
async def get_image_urls_from_redis(taskid):
    try:
        data = await redis.exec("get", get_redis_task_key(taskid))
        if data:
            return json.loads(data)
        return None
    except Exception as e:
        logger.error(f"从Redis获取image_urls失败: {str(e)}")
        return None

# 将任务的image_urls数据保存到Redis
async def save_image_urls_to_redis(taskid, image_urls, expiry=REDIS_CACHE_EXPIRY):
    try:
        await redis.exec("set", get_redis_task_key(taskid), json.dumps(image_urls), ex=expiry)
        logger.info(f"已将image_urls保存到Redis: {taskid}")
        return True
    except Exception as e:
        logger.error(f"保存image_urls到Redis失败: {str(e)}")
        return False

# 导入pan映射字典
pan_prompt_mapping = {
    "btn1": "Extend the image to the left", # 向左扩展
    "btn2": "Extend the image to the right", # 向右扩展
    "btn3": "Extend the image upward", # 向上扩展
    "btn4": "Extend the image downward", # 向下扩展
}

class FluxCreateRequest(BaseModel):
    """Flux图像生成请求模型"""
    prompt: str = Field(..., description="提示文本")
    model_name: str = Field("flux-kontext-max", description="模型名称")
    input_image: Optional[str] = Field(None, description="输入图像的base64编码（可选）")
    seed: Optional[int] = Field(None, description="随机种子（可选）")
    aspect_ratio: Optional[str] = Field(None, description="图像宽高比（可选）")
    output_format: str = Field("jpeg", description="输出格式")
    webhook_url: Optional[str] = Field(None, description="webhook回调地址（可选）")
    webhook_secret: Optional[str] = Field(None, description="webhook密钥（可选）")
    prompt_upsampling: bool = Field(False, description="是否进行提示词增强")
    safety_tolerance: int = Field(2, description="安全级别")
    num: int = Field(1, description="生成图片数量", ge=1, le=4)
    taskid: Optional[str] = Field(None, description="任务ID（可选）")
    preserve_history: bool = Field(False, description="是否保留历史图片")
    regenerate_index: Optional[int] = Field(None, description="重新生成特定索引的图片")

class FluxGetResultRequest(BaseModel):
    """获取Flux图像生成结果的请求模型"""
    task_id: str = Field(..., description="任务ID")

class FluxPanRequest(BaseModel):
    """Flux扩图请求模型"""
    prompt: str = Field(..., description="按钮标识（btn1, btn2, btn3, btn4）")
    image_urls: List[str] = Field(default_factory=list, description="图片URL列表")
    taskid: Optional[str] = Field(None, description="任务ID（可选）")
    num: int = Field(1, description="生成图片数量", ge=1, le=4)
    preserve_history: bool = Field(False, description="是否保留历史图片")
    model_name: str = Field("flux-kontext-pro", description="模型名称")
    seed: Optional[int] = Field(None, description="随机种子（可选）")
    aspect_ratio: Optional[str] = Field(None, description="图像宽高比（可选）")
    output_format: str = Field("jpeg", description="输出格式")
    webhook_url: Optional[str] = Field(None, description="webhook回调地址（可选）")
    webhook_secret: Optional[str] = Field(None, description="webhook密钥（可选）")
    prompt_upsampling: bool = Field(False, description="是否进行提示词增强")
    safety_tolerance: int = Field(2, description="安全级别")
    regenerate_index: Optional[int] = Field(None, description="重新生成特定索引的图片")

class ModelInfo(BaseModel):
    """模型信息"""
    model_id: str
    name: str
    description: str

class FluxResponse(BaseModel):
    """API响应模型"""
    code: str = Field("0000", description="响应码")
    msg: str = Field("success", description="响应消息")
    data: dict = Field({}, description="响应数据")

async def process_flux_image_generation(
    prompt_en: str,
    model_name: str,
    task_id: int,
    num: int,
    taskid: str,
    operator: CreditOperator,
    input_image: Optional[str] = None,
    seed: Optional[int] = None,
    aspect_ratio: Optional[str] = None,
    output_format: str = "jpeg",
    webhook_url: Optional[str] = None,
    webhook_secret: Optional[str] = None,
    prompt_upsampling: bool = False,
    safety_tolerance: int = 2,
    preserve_history: bool = False
):
    """
    后台任务：处理Flux图像生成
    """
    from utils.database import AsyncSessionLocal

    # 创建或获取任务锁
    async def get_task_lock():
        global task_locks
        with task_lock:
            if task_id not in task_locks:
                task_locks[task_id] = asyncio.Lock()
            return task_locks[task_id]

    task_specific_lock = await get_task_lock()

    try:
        async with AsyncSessionLocal() as db:
            # 查询任务
            result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.error(f"任务不存在: {task_id}")
                return

            try:
                # 预处理 input_image 参数
                processed_input_image = input_image  # 用于传递给 create_image 的参数
                prompt_img_url = None  # 用于存储到数据库的 OSS URL

                if input_image:
                    logger.info(f"开始处理输入图片: {input_image[:50]}...")

                    try:
                        if is_base64_image(input_image):
                            # 如果是 base64 数据，需要上传到 OSS 并存储 URL，但传递给 API 的仍是原始 base64
                            logger.info("检测到 Base64 格式的输入图片，开始上传到 OSS...")

                            # 处理带有 MIME 前缀的 base64 数据
                            base64_data = input_image
                            if "," in input_image:
                                _, base64_data = input_image.split(",", 1)

                            # 处理 base64 padding 问题
                            padding_needed = len(base64_data) % 4
                            if padding_needed:
                                base64_data += "=" * (4 - padding_needed)

                            # 生成文件名并上传到 OSS
                            filename = f"flux_input_{nanoid.generate(size=10)}.png"
                            prompt_img_url = save_image_b64data(base64_data, filename)

                            if prompt_img_url:
                                logger.info(f"输入图片已上传到 OSS: {prompt_img_url}")
                            else:
                                logger.warning("输入图片上传到 OSS 失败")

                            # 保持原始 base64 数据用于 API 调用
                            processed_input_image = input_image

                        elif is_url(input_image):
                            # 如果是 URL，需要下载并转换为 base64，不需要上传 OSS
                            logger.info("检测到 URL 格式的输入图片，开始下载并转换为 base64...")

                            processed_input_image = await url_to_base64(input_image)
                            logger.info("URL 图片已成功转换为 base64 格式")

                        else:
                            logger.warning(f"未识别的输入图片格式: {input_image[:50]}...")
                            # 保持原始数据不变
                            processed_input_image = input_image

                    except Exception as e:
                        logger.error(f"处理输入图片时发生错误: {str(e)}")
                        # 如果处理失败，可以选择继续使用原始数据或者抛出异常
                        # 这里选择抛出异常，因为输入图片处理失败可能影响生成结果
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = f"输入图片处理失败"
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()
                        logger.error(f"Flux图像生成失败：输入图片处理失败，任务ID: {taskid}，错误: {str(e)}")
                        return
                # 创建协程列表，用于并发调用create_image函数
                coroutines = []

                # 为每个需要生成的图片创建一个协程
                pre_debit_logs = []
                for _ in range(num):
                    coroutine = create_image(
                        prompt=prompt_en,
                        model_name=model_name,
                        input_image=processed_input_image,  # 使用预处理后的图片数据
                        seed=seed,
                        aspect_ratio=aspect_ratio,
                        output_format=output_format,
                        webhook_url=webhook_url,
                        webhook_secret=webhook_secret,
                        prompt_upsampling=prompt_upsampling,
                        safety_tolerance=safety_tolerance
                    )
                    coroutines.append(coroutine)
                    log_id = await operator.pre_debit(1, 'image-generation', model_name)
                    pre_debit_logs.append(log_id)

                # 并发执行所有协程
                results = await asyncio.gather(*coroutines, return_exceptions=True)

                # 处理结果，收集任务ID
                flux_task_ids = []
                error_messages = []
                rollback_ids = []
                for idx, result in enumerate(results):
                    if result is not None and not isinstance(result, Exception):
                        # 从result中提取任务ID
                        if isinstance(result, dict) and 'id' in result:
                            flux_task_ids.append(result['id'])
                        else:
                            logger.warning(f"意外的create_image响应格式: {result}")
                            rollback_ids.append(pre_debit_logs[idx])
                    else:
                        if isinstance(result, Exception):
                            error_messages.append(str(result))
                        rollback_ids.append(pre_debit_logs[idx])

                await operator.rollback(rollback_ids)
                logger.info(f"Flux任务ID列表: {flux_task_ids}")
                await operator.done()

                # 检查是否所有请求都失败了
                if not flux_task_ids and error_messages:
                    # 所有请求都失败，更新任务状态为失败
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = error_messages[0]
                    task.uptime = datetime.now()
                    task.progress = "100%"

                    await db.commit()
                    logger.error(f"Flux图像生成失败：所有图片生成请求失败，任务ID: {taskid}，错误: {error_messages[0]}")
                    return

                # 轮询获取任务结果
                completed_images = []
                max_polling_time = 120  # 最大轮询时间 120s
                polling_interval = 5    # 轮询间隔5秒
                start_time = time.time()

                # 更新任务状态为进行中
                task.status = ChatMjTasksStatus.IN_PROGRESS
                task.progress = "10%"
                await db.commit()

                while len(completed_images) < len(flux_task_ids) and (time.time() - start_time) < max_polling_time:
                    for task_id in flux_task_ids[:]:  # 创建副本以避免在迭代时修改列表
                        try:
                            result = await get_result(task_id)
                            if result and 'status' in result:
                                if result['status'] == 'Ready' and 'result' in result and 'sample' in result['result']:
                                    # 任务完成，获取图片URL
                                    image_url = result['result']['sample']
                                    completed_images.append(image_url)
                                    flux_task_ids.remove(task_id)
                                    logger.info(f"Flux任务 {task_id} 完成，图片URL: {image_url}")
                                elif result['status'] == 'Error':
                                    # 任务失败
                                    error_msg = result.get('result', {}).get('error', 'Unknown error')
                                    logger.error(f"Flux任务 {task_id} 失败: {error_msg}")
                                    flux_task_ids.remove(task_id)
                                # 其他状态（如Pending）继续等待
                        except Exception as e:
                            logger.error(f"轮询Flux任务 {task_id} 状态失败: {str(e)}")

                    # 如果还有未完成的任务，等待一段时间后继续轮询
                    if flux_task_ids:
                        # 更新进度百分比
                        completed_count = len(completed_images)
                        total_count = num
                        progress_percent = min(90, 10 + (completed_count / total_count) * 80)  # 10% - 90%
                        task.progress = f"{int(progress_percent)}%"
                        await db.commit()

                        await asyncio.sleep(polling_interval)

                # 检查是否有完成的图片
                if not completed_images:
                    # 没有成功生成的图片
                    task.status = ChatMjTasksStatus.FAILURE
                    if (time.time() - start_time) >= max_polling_time:
                        task.fail_reason = "图片创作失败，请更换参考图或提示词后重试"
                    else:
                        task.fail_reason = "所有Flux任务都未能成功完成"
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    await db.commit()
                    logger.error(f"Flux图像生成失败：没有任务成功完成，任务ID: {taskid}")
                    return

                # 将生成的图片上传到OSS
                oss_urls = []
                for img_url in completed_images:
                    try:
                        # 使用service.file中的save_image_by_url函数上传图片到OSS
                        oss_url = await save_image_by_url(img_url)
                        if oss_url:
                            oss_urls.append(oss_url)
                    except Exception as e:
                        logger.error(f"上传Flux图片到OSS失败: {str(e)}")

                logger.info(f"Flux oss_urls: {oss_urls}")

                # 加锁，确保同一时间只有一个任务更新同一个数据库记录
                async with task_specific_lock:
                    # 从Redis获取当前image_urls，如果不存在则从数据库获取
                    current_urls = await get_image_urls_from_redis(task.taskid)

                    if current_urls is None:
                        # Redis中没有数据，从数据库获取
                        result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                        task = result.scalars().first()

                        if not task:
                            logger.error(f"任务已不存在: {task_id}")
                            return

                        # 解析数据库中的image_urls
                        if task.image_urls:
                            try:
                                current_urls = json.loads(task.image_urls)
                                if not isinstance(current_urls, list):
                                    current_urls = []
                            except Exception as e:
                                logger.error(f"解析任务的image_urls失败: {str(e)}")
                                current_urls = []
                        else:
                            current_urls = []

                    # 更新任务状态为成功
                    task.status = ChatMjTasksStatus.SUCCESS
                    task.finish_time = datetime.now()

                    # 如果有成功上传的图片
                    if oss_urls:
                        # 设置第一张图片为主图
                        task.image_url = oss_urls[0]

                        # 如果需要保留历史图片，则合并图片URL
                        if preserve_history:
                            # 合并新旧图片URL（新图片在前）
                            merged_urls = oss_urls + current_urls
                            # 保存合并后的图片URL
                            task.image_urls = json.dumps(merged_urls)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, merged_urls)
                        else:
                            # 不需要保留历史，直接使用新图片
                            task.image_urls = json.dumps(oss_urls)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, oss_urls)
                    else:
                        # 如果没有成功上传的图片，使用原始URL
                        if completed_images:
                            task.image_url = completed_images[0]
                            task.image_urls = json.dumps(completed_images)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, completed_images)

                    task.uptime = datetime.now()
                    task.progress = "100%"
                    task.model = model_name

                    # 如果有输入图片的 OSS URL，保存到 prompt_img 字段
                    if prompt_img_url:
                        task.prompt_img = json.dumps([prompt_img_url])
                        logger.info(f"输入图片 OSS URL 已保存到数据库: {prompt_img_url}")

                    await db.commit()
                    await db.refresh(task)

                # 将资产信息存储到资产表
                try:
                    # 根据username查询用户实例
                    user_stmt = select(User).filter(User.username == task.username)
                    user_result = await db.execute(user_stmt)
                    task_user = user_result.scalars().first()

                    if task_user:
                        # 存储资产到资产表
                        asset = await store_asset_from_instance(task, task_user, db)
                        if asset:
                            logger.info(f"成功将Flux任务存储到资产表，资产ID: {asset.id}")
                        else:
                            logger.warning(f"Flux任务存储到资产表失败，任务ID: {task.id}")
                    else:
                        logger.error(f"未找到用户: {task.username}，无法存储资产")
                except Exception as e:
                    logger.error(f"存储Flux资产失败: {str(e)}")

                logger.info(f"Flux图像生成成功，任务ID: {taskid}")

                # 任务完成后释放锁
                if task_id in task_locks:
                    with task_lock:
                        if task_id in task_locks and not task_locks[task_id].locked():
                            del task_locks[task_id]
                            logger.info(f"已释放任务锁: {task_id}")

            except Exception as e:
                # 更新任务状态为失败
                task.status = ChatMjTasksStatus.FAILURE
                task.fail_reason = str(e)
                task.uptime = datetime.now()
                task.progress = "100%"
                await db.commit()

                logger.error(f"Flux图像生成失败: {str(e)}")

    except Exception as e:
        logger.error(f"Flux后台任务处理失败: {str(e)}")

async def process_flux_pan_generation(
    prompt_en: str,
    model_name: str,
    task_id: int,
    num: int,
    taskid: str,
    image_urls: List[str],
    operator: CreditOperator,
    seed: Optional[int] = None,
    aspect_ratio: Optional[str] = None,
    output_format: str = "jpeg",
    webhook_url: Optional[str] = None,
    webhook_secret: Optional[str] = None,
    prompt_upsampling: bool = False,
    safety_tolerance: int = 2,
    preserve_history: bool = False
):
    """
    后台任务：处理Flux扩图生成
    """
    from utils.database import AsyncSessionLocal

    # 创建或获取任务锁
    async def get_task_lock():
        global task_locks
        with task_lock:
            if task_id not in task_locks:
                task_locks[task_id] = asyncio.Lock()
            return task_locks[task_id]

    task_specific_lock = await get_task_lock()

    try:
        async with AsyncSessionLocal() as db:
            # 查询任务
            result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.error(f"Flux扩图任务不存在: {task_id}")
                return

            try:
                # 预处理图片URL（将base64图片数据上传到OSS）
                oss_image_urls = []
                if image_urls:
                    logger.info(f"开始处理Flux扩图输入图片，数量: {len(image_urls)}")
                    for img_data in image_urls:
                        try:
                            # 检查是否为有效数据
                            if not img_data or not isinstance(img_data, str):
                                logger.warning(f"无效的图片数据: {img_data}")
                                continue

                            filename = f"flux_pan_{nanoid.generate(size=10)}.png"
                            oss_url = None

                            # 检查数据类型并相应处理
                            if is_url(img_data):
                                # 如果是URL，使用save_image_by_url上传
                                logger.info(f"检测到URL类型图片，开始处理: {img_data[:30]}...")
                                try:
                                    oss_url = await save_image_by_url(img_data)
                                except Exception as e:
                                    logger.error(f"URL图片上传失败: {str(e)}")
                                    continue
                            elif is_base64_image(img_data):
                                # 如果是base64数据，使用save_image_b64data处理
                                logger.info(f"检测到Base64类型图片，开始处理...")
                                try:
                                    # 处理带有MIME前缀的base64数据
                                    base64_data = img_data
                                    if "," in img_data:
                                        _, base64_data = img_data.split(",", 1)

                                    # 处理base64 padding问题
                                    padding_needed = len(base64_data) % 4
                                    if padding_needed:
                                        base64_data += "=" * (4 - padding_needed)

                                    oss_url = save_image_b64data(base64_data, filename)
                                except Exception as e:
                                    logger.error(f"Base64图片处理失败: {str(e)}")
                                    continue
                            else:
                                logger.warning(f"未知格式的图片数据: {img_data[:30]}...")
                                continue

                            if oss_url:
                                oss_image_urls.append(oss_url)
                                logger.info(f"Flux扩图图片已上传到OSS: {oss_url}")
                            else:
                                logger.warning(f"Flux扩图图片上传到OSS失败")
                        except Exception as e:
                            logger.error(f"处理Flux扩图图片时发生错误: {str(e)}")

                logger.info(f"处理后的Flux扩图OSS图片URL列表: {oss_image_urls}")

                # 检查：如果用户提供了图片但全部上传失败，则直接返回失败
                if image_urls and not oss_image_urls:
                    # 更新任务状态为失败
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = "参考图片上传失败，请重试"
                    task.uptime = datetime.now()
                    task.progress = "100%"

                    await db.commit()

                    logger.error(f"Flux扩图失败：所有图片上传到OSS均失败，任务ID: {taskid}")
                    return

                # 确保有至少一张参考图片用于扩图
                if not oss_image_urls:
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = "扩图功能需要至少一张参考图片"
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    await db.commit()
                    logger.error(f"Flux扩图失败：缺少参考图片，任务ID: {taskid}")
                    return

                # 将第一张图片转换为base64用于API调用
                input_image_base64 = None
                if oss_image_urls:
                    try:
                        input_image_base64 = await url_to_base64(oss_image_urls[0])
                        logger.info("参考图片已转换为base64格式用于API调用")
                    except Exception as e:
                        logger.error(f"转换参考图片为base64失败: {str(e)}")
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = f"参考图片处理失败: {str(e)}"
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()
                        return

                # 创建协程列表，用于并发调用create_image函数
                coroutines = []

                # 为每个需要生成的图片创建一个协程
                for _ in range(num):
                    coroutine = create_image(
                        prompt=prompt_en,
                        model_name=model_name,
                        input_image=input_image_base64,  # 使用转换后的base64图片
                        seed=seed,
                        aspect_ratio=aspect_ratio,
                        output_format=output_format,
                        webhook_url=webhook_url,
                        webhook_secret=webhook_secret,
                        prompt_upsampling=prompt_upsampling,
                        safety_tolerance=safety_tolerance
                    )
                    coroutines.append(coroutine)

                # 并发执行所有协程
                results = await asyncio.gather(*coroutines, return_exceptions=True)

                # 处理结果，收集任务ID
                flux_task_ids = []
                error_messages = []
                for result in results:
                    if result is not None and not isinstance(result, Exception):
                        # 从result中提取任务ID
                        if isinstance(result, dict) and 'id' in result:
                            flux_task_ids.append(result['id'])
                        else:
                            logger.warning(f"意外的Flux扩图create_image响应格式: {result}")
                    elif isinstance(result, Exception):
                        error_messages.append(str(result))

                logger.info(f"Flux扩图任务ID列表: {flux_task_ids}")

                # 检查是否所有请求都失败了
                if not flux_task_ids and error_messages:
                    # 所有请求都失败，更新任务状态为失败
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = error_messages[0]
                    task.uptime = datetime.now()
                    task.progress = "100%"

                    await db.commit()
                    logger.error(f"Flux扩图失败：所有图片生成请求失败，任务ID: {taskid}，错误: {error_messages[0]}")
                    return

                # 轮询获取任务结果
                completed_images = []
                max_polling_time = 300  # 最大轮询时间5分钟
                polling_interval = 5    # 轮询间隔5秒
                start_time = time.time()

                # 更新任务状态为进行中
                task.status = ChatMjTasksStatus.IN_PROGRESS
                task.progress = "10%"
                await db.commit()

                while len(completed_images) < len(flux_task_ids) and (time.time() - start_time) < max_polling_time:
                    for task_id in flux_task_ids[:]:  # 创建副本以避免在迭代时修改列表
                        try:
                            result = await get_result(task_id)
                            if result and 'status' in result:
                                if result['status'] == 'Ready' and 'result' in result and 'sample' in result['result']:
                                    # 任务完成，获取图片URL
                                    image_url = result['result']['sample']
                                    completed_images.append(image_url)
                                    flux_task_ids.remove(task_id)
                                    logger.info(f"Flux扩图任务 {task_id} 完成，图片URL: {image_url}")
                                elif result['status'] == 'Error':
                                    # 任务失败
                                    error_msg = result.get('result', {}).get('error', 'Unknown error')
                                    logger.error(f"Flux扩图任务 {task_id} 失败: {error_msg}")
                                    flux_task_ids.remove(task_id)
                                # 其他状态（如Pending）继续等待
                        except Exception as e:
                            logger.error(f"轮询Flux扩图任务 {task_id} 状态失败: {str(e)}")

                    # 如果还有未完成的任务，等待一段时间后继续轮询
                    if flux_task_ids:
                        # 更新进度百分比
                        completed_count = len(completed_images)
                        total_count = num
                        progress_percent = min(90, 10 + (completed_count / total_count) * 80)  # 10% - 90%
                        task.progress = f"{int(progress_percent)}%"
                        await db.commit()

                        await asyncio.sleep(polling_interval)

                # 检查是否有完成的图片
                if not completed_images:
                    # 没有成功生成的图片
                    task.status = ChatMjTasksStatus.FAILURE
                    if (time.time() - start_time) >= max_polling_time:
                        task.fail_reason = "Flux扩图任务超时：轮询时间超过5分钟"
                    else:
                        task.fail_reason = "所有Flux扩图任务都未能成功完成"
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    await db.commit()
                    logger.error(f"Flux扩图失败：没有任务成功完成，任务ID: {taskid}")
                    return

                # 将生成的图片上传到OSS
                oss_urls = []
                for img_url in completed_images:
                    try:
                        # 使用service.file中的save_image_by_url函数上传图片到OSS
                        oss_url = await save_image_by_url(img_url)
                        if oss_url:
                            oss_urls.append(oss_url)
                    except Exception as e:
                        logger.error(f"上传Flux扩图图片到OSS失败: {str(e)}")

                logger.info(f"Flux扩图 oss_urls: {oss_urls}")

                # 加锁，确保同一时间只有一个任务更新同一个数据库记录
                async with task_specific_lock:
                    # 从Redis获取当前image_urls，如果不存在则从数据库获取
                    current_urls = await get_image_urls_from_redis(task.taskid)

                    if current_urls is None:
                        # Redis中没有数据，从数据库获取
                        result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                        task = result.scalars().first()

                        if not task:
                            logger.error(f"任务已不存在: {task_id}")
                            return

                        # 解析数据库中的image_urls
                        if task.image_urls:
                            try:
                                current_urls = json.loads(task.image_urls)
                                if not isinstance(current_urls, list):
                                    current_urls = []
                            except Exception as e:
                                logger.error(f"解析任务的image_urls失败: {str(e)}")
                                current_urls = []
                        else:
                            current_urls = []

                    # 更新任务状态为成功
                    task.status = ChatMjTasksStatus.SUCCESS
                    task.finish_time = datetime.now()

                    # 如果有成功上传的图片
                    if oss_urls:
                        # 设置第一张图片为主图
                        task.image_url = oss_urls[0]

                        # 如果需要保留历史图片，则合并图片URL
                        if preserve_history:
                            # 合并新旧图片URL（新图片在前）
                            merged_urls = oss_urls + current_urls
                            # 保存合并后的图片URL
                            task.image_urls = json.dumps(merged_urls)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, merged_urls)
                        else:
                            # 不需要保留历史，直接使用新图片
                            task.image_urls = json.dumps(oss_urls)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, oss_urls)
                    else:
                        # 如果没有成功上传的图片，使用原始URL
                        if completed_images:
                            task.image_url = completed_images[0]
                            task.image_urls = json.dumps(completed_images)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, completed_images)

                    task.uptime = datetime.now()
                    task.progress = "100%"
                    task.model = model_name

                    # 保存参考图片的 OSS URL 到 prompt_img 字段
                    if oss_image_urls:
                        task.prompt_img = json.dumps(oss_image_urls)
                        logger.info(f"Flux扩图参考图片 OSS URL 已保存到数据库: {oss_image_urls}")

                    await db.commit()

                    await db.refresh(task)

                # 将资产信息存储到资产表
                try:
                    # 根据username查询用户实例
                    user_stmt = select(User).filter(User.username == task.username)
                    user_result = await db.execute(user_stmt)
                    task_user = user_result.scalars().first()

                    if task_user:
                        # 存储资产到资产表
                        asset = await store_asset_from_instance(task, task_user, db)
                        if asset:
                            logger.info(f"成功将Flux扩图任务存储到资产表，资产ID: {asset.id}")
                        else:
                            logger.warning(f"Flux扩图任务存储到资产表失败，任务ID: {task.id}")
                    else:
                        logger.error(f"未找到用户: {task.username}，无法存储资产")
                except Exception as e:
                    logger.error(f"存储Flux扩图资产失败: {str(e)}")

                logger.info(f"Flux扩图成功，任务ID: {taskid}")

                # 任务完成后释放锁
                if task_id in task_locks:
                    with task_lock:
                        if task_id in task_locks and not task_locks[task_id].locked():
                            del task_locks[task_id]
                            logger.info(f"已释放任务锁: {task_id}")

            except Exception as e:
                # 更新任务状态为失败
                async with task_specific_lock:
                    result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                    task = result.scalars().first()
                    if task:
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = str(e)
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()

                logger.error(f"Flux扩图失败: {str(e)}")

    except Exception as e:
        logger.error(f"Flux扩图后台任务处理失败: {str(e)}")

async def process_comfyui_image_generation(
    prompt_en: str,
    model_name: str,
    task_id: int,
    num: int,
    taskid: str,
    operator: CreditOperator,
    input_image: Optional[str] = None,
    seed: Optional[int] = None,
    aspect_ratio: Optional[str] = None,
    output_format: str = "jpeg",
    webhook_url: Optional[str] = None,
    webhook_secret: Optional[str] = None,
    prompt_upsampling: bool = False,
    safety_tolerance: int = 2,
    preserve_history: bool = False
):
    """
    后台任务：处理ComfyUI图像生成
    """
    from utils.database import AsyncSessionLocal

    # 创建或获取任务锁
    async def get_task_lock():
        global task_locks
        with task_lock:
            if task_id not in task_locks:
                task_locks[task_id] = asyncio.Lock()
            return task_locks[task_id]

    task_specific_lock = await get_task_lock()

    try:
        async with AsyncSessionLocal() as db:
            # 查询任务
            result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.error(f"ComfyUI任务不存在: {task_id}")
                return

            try:
                # 更新任务状态为进行中
                task.status = ChatMjTasksStatus.IN_PROGRESS
                task.progress = "5%"
                await db.commit()

                # 预处理输入图片并上传到ComfyUI
                comfyui_image_name = None
                prompt_img_url = None
                
                # 根据是否有输入图片，选择不同的工作流
                workflow_template = model_name

                if input_image:
                    logger.info(f"开始处理ComfyUI输入图片: {input_image[:50]}...")

                    try:
                        # 处理输入图片数据
                        processed_input_image = input_image
                        
                        if is_base64_image(input_image):
                            # Base64格式，需要上传到OSS保存URL，同时上传到ComfyUI
                            logger.info("检测到Base64格式的输入图片")
                            
                            # 处理带有MIME前缀的base64数据
                            base64_data = input_image
                            if "," in input_image:
                                _, base64_data = input_image.split(",", 1)

                            # 处理base64 padding问题
                            padding_needed = len(base64_data) % 4
                            if padding_needed:
                                base64_data += "=" * (4 - padding_needed)

                            # 上传到OSS保存URL
                            filename = f"comfyui_input_{nanoid.generate(size=10)}.png"
                            prompt_img_url = save_image_b64data(base64_data, filename)
                            
                            # 上传到ComfyUI
                            upload_result = await upload_image_to_comfyui(input_image)
                            if upload_result and 'name' in upload_result:
                                comfyui_image_name = upload_result['name']
                                logger.info(f"图片已上传到ComfyUI: {comfyui_image_name}")
                            else:
                                raise Exception("上传图片到ComfyUI失败")

                        elif is_url(input_image):
                            # URL格式，先转换为base64，再上传到ComfyUI
                            logger.info("检测到URL格式的输入图片")
                            
                            processed_input_image = await url_to_base64(input_image)
                            
                            # 上传到ComfyUI
                            upload_result = await upload_image_to_comfyui(processed_input_image)
                            if upload_result and 'name' in upload_result:
                                comfyui_image_name = upload_result['name']
                                logger.info(f"图片已上传到ComfyUI: {comfyui_image_name}")
                            else:
                                raise Exception("上传图片到ComfyUI失败")

                        else:
                            # 尝试直接上传
                            upload_result = await upload_image_to_comfyui(input_image)
                            if upload_result and 'name' in upload_result:
                                comfyui_image_name = upload_result['name']
                                logger.info(f"图片已上传到ComfyUI: {comfyui_image_name}")
                            else:
                                raise Exception("上传图片到ComfyUI失败")

                    except Exception as e:
                        logger.error(f"处理ComfyUI输入图片时发生错误: {str(e)}")
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = f"输入图片处理失败: {str(e)}"
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()
                        return
                else:
                    # 如果没有输入图片，使用text2img工作流
                    workflow_template = "flux_kontext_text2img"
                    logger.info(f"未提供输入图片，将使用纯文本到图像工作流: {workflow_template}")

                # 更新进度
                task.progress = "15%"
                await db.commit()

                # 创建协程列表，用于并发生成多张图片
                comfyui_prompt_ids = []
                error_messages = []
                pre_debit_logs = []

                for i in range(num):
                    log_id = None
                    try:
                        # 预扣费
                        log_id = await operator.pre_debit(1, 'image-generation', model_name)
                        pre_debit_logs.append(log_id)

                        # 准备工作流替换数据
                        replacements = {}

                        if input_image:
                            # 图片转图片工作流
                            replacements = {
                                "59": {"inputs.trans_text": prompt_en}
                            }

                            # 如果有输入图片，替换图片节点
                            if comfyui_image_name:
                                replacements["41"] = {"inputs.image": comfyui_image_name}
                        else:
                            # 纯文本到图像工作流
                            replacements = {
                                "6": {"inputs.trans_text": prompt_en}
                            }

                        # 如果有种子值，替换相应的种子节点
                        if seed:
                            if input_image:
                                replacements["25"] = {"inputs.noise_seed": seed}
                            else:
                                replacements["14"] = {"inputs.seed": seed}

                        # 替换工作流数据
                        workflow_data = replace_workflow_data(workflow_template, replacements)
                        
                        # 提交任务到ComfyUI
                        submit_result = await submit_task_to_comfyui(workflow_data)
                        
                        if submit_result and 'prompt_id' in submit_result:
                            comfyui_prompt_ids.append(submit_result['prompt_id'])
                            logger.info(f"ComfyUI任务已提交，prompt_id: {submit_result['prompt_id']}")
                        else:
                            error_messages.append("提交任务到ComfyUI失败")
                            if log_id:
                                await operator.rollback([log_id])

                    except Exception as e:
                        error_messages.append(str(e))
                        if log_id:
                            await operator.rollback([log_id])
                        logger.error(f"第{i+1}个ComfyUI任务提交失败: {str(e)}")

                # 完成预扣费操作
                await operator.done()

                # 检查是否所有任务都失败了
                if not comfyui_prompt_ids and error_messages:
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = error_messages[0]
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    await db.commit()
                    logger.error(f"ComfyUI图像生成失败：所有任务提交失败，任务ID: {taskid}")
                    return

                # 更新进度
                task.progress = "25%"
                await db.commit()

                # 轮询获取任务结果
                completed_images = []
                max_polling_time = 300  # 最大轮询时间5分钟
                polling_interval = 5    # 轮询间隔5秒
                start_time = time.time()

                def validate_comfyui_response(status_result, prompt_id):
                    """验证ComfyUI响应数据结构的完整性"""
                    try:
                        if not isinstance(status_result, dict):
                            return False, "响应数据不是字典格式"
                        
                        if prompt_id not in status_result:
                            return False, f"响应中不包含prompt_id: {prompt_id}"
                        
                        task_data = status_result[prompt_id]
                        if not isinstance(task_data, dict):
                            return False, "任务数据不是字典格式"
                        
                        if 'status' not in task_data:
                            return False, "任务数据中缺少status字段"
                        
                        return True, "数据结构验证通过"
                    except Exception as e:
                        return False, f"验证过程中发生异常: {str(e)}"

                while len(completed_images) < len(comfyui_prompt_ids) and (time.time() - start_time) < max_polling_time:
                    for prompt_id in comfyui_prompt_ids[:]:  # 创建副本以避免在迭代时修改列表
                        try:
                            status_result = await query_task_status_from_comfyui(prompt_id)
                            logger.debug(f"ComfyUI状态查询返回数据: {status_result}")
                            
                            # 验证响应数据结构
                            is_valid, validation_msg = validate_comfyui_response(status_result, prompt_id)
                            if not is_valid:
                                logger.warning(f"ComfyUI响应数据验证失败: {validation_msg}")
                                continue
                            
                            # 提取对应prompt_id的任务数据
                            task_data = status_result[prompt_id]
                            task_status = task_data.get('status', {})
                            outputs = task_data.get('outputs', {})
                            
                            logger.debug(f"任务状态: {task_status}")
                            logger.debug(f"输出数据: {outputs}")
                            
                            if task_status.get('status_str') == 'success' and task_status.get('completed', False):
                                logger.info(f"ComfyUI任务 {prompt_id} 成功完成，开始处理输出图片")
                                
                                # 处理输出图片
                                images_processed = False
                                for node_id, node_output in outputs.items():
                                    if 'images' in node_output and node_output['images']:
                                        logger.debug(f"处理节点 {node_id} 的图片输出，数量: {len(node_output['images'])}")
                                        
                                        for image_info in node_output['images']:
                                            filename = image_info.get('filename')
                                            if filename:
                                                try:
                                                    logger.info(f"开始获取ComfyUI图片: {filename}")
                                                    
                                                    # 获取图片数据
                                                    image_bytes = await get_image_from_comfyui(filename)
                                                    
                                                    if not image_bytes:
                                                        logger.error(f"获取图片数据失败: {filename}")
                                                        continue
                                                    
                                                    logger.debug(f"成功获取图片数据，大小: {len(image_bytes)} bytes")
                                                    
                                                    # 将图片数据转换为base64并上传到OSS
                                                    import base64
                                                    base64_data = base64.b64encode(image_bytes).decode('utf-8')
                                                    oss_filename = f"comfyui_{nanoid.generate(size=10)}.png"
                                                    
                                                    logger.info(f"开始上传图片到OSS: {oss_filename}")
                                                    oss_url = save_image_b64data(base64_data, oss_filename)
                                                    
                                                    if oss_url:
                                                        completed_images.append(oss_url)
                                                        images_processed = True
                                                        logger.info(f"ComfyUI图片已成功上传到OSS: {oss_url}")
                                                    else:
                                                        logger.error(f"图片上传到OSS失败: {oss_filename}")
                                                        
                                                except Exception as img_e:
                                                    logger.error(f"处理图片 {filename} 时发生错误: {str(img_e)}")
                                
                                if images_processed:
                                    comfyui_prompt_ids.remove(prompt_id)
                                    logger.info(f"ComfyUI任务 {prompt_id} 处理完成，已从轮询列表中移除")
                                else:
                                    logger.error(f"ComfyUI任务 {prompt_id} 虽然成功但未能处理任何图片")
                                
                            elif task_status.get('status_str') == 'error':
                                # 任务失败
                                error_msg = task_status.get('error', 'Unknown error')
                                logger.error(f"ComfyUI任务 {prompt_id} 失败: {error_msg}")
                                comfyui_prompt_ids.remove(prompt_id)
                            else:
                                # 任务仍在进行中
                                status_str = task_status.get('status_str', 'unknown')
                                logger.debug(f"ComfyUI任务 {prompt_id} 状态: {status_str}")
                                
                        except Exception as e:
                            logger.error(f"轮询ComfyUI任务 {prompt_id} 状态失败: {str(e)}")
                            logger.debug(f"异常详情: ", exc_info=True)

                    # 如果还有未完成的任务，等待一段时间后继续轮询
                    if comfyui_prompt_ids:
                        # 更新进度百分比
                        completed_count = len(completed_images)
                        total_count = num
                        progress_percent = min(90, 25 + (completed_count / total_count) * 65)  # 25% - 90%
                        task.progress = f"{int(progress_percent)}%"
                        await db.commit()

                        await asyncio.sleep(polling_interval)

                # 检查是否有完成的图片
                if not completed_images:
                    task.status = ChatMjTasksStatus.FAILURE
                    if (time.time() - start_time) >= max_polling_time:
                        task.fail_reason = "ComfyUI图像生成超时：轮询时间超过5分钟"
                    else:
                        task.fail_reason = "所有ComfyUI任务都未能成功完成"
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    await db.commit()
                    logger.error(f"ComfyUI图像生成失败：没有任务成功完成，任务ID: {taskid}")
                    return

                # 加锁，确保同一时间只有一个任务更新同一个数据库记录
                async with task_specific_lock:
                    # 从Redis获取当前image_urls，如果不存在则从数据库获取
                    current_urls = await get_image_urls_from_redis(task.taskid)

                    if current_urls is None:
                        # Redis中没有数据，从数据库获取
                        result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                        task = result.scalars().first()

                        if not task:
                            logger.error(f"任务已不存在: {task_id}")
                            return

                        # 解析数据库中的image_urls
                        if task.image_urls:
                            try:
                                current_urls = json.loads(task.image_urls)
                                if not isinstance(current_urls, list):
                                    current_urls = []
                            except Exception as e:
                                logger.error(f"解析任务的image_urls失败: {str(e)}")
                                current_urls = []
                        else:
                            current_urls = []

                    # 更新任务状态为成功
                    task.status = ChatMjTasksStatus.SUCCESS
                    task.finish_time = datetime.now()

                    # 设置图片URL
                    if completed_images:
                        # 设置第一张图片为主图
                        task.image_url = completed_images[0]

                        # 如果需要保留历史图片，则合并图片URL
                        if preserve_history:
                            # 合并新旧图片URL（新图片在前）
                            merged_urls = completed_images + current_urls
                            # 保存合并后的图片URL
                            task.image_urls = json.dumps(merged_urls)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, merged_urls)
                        else:
                            # 不需要保留历史，直接使用新图片
                            task.image_urls = json.dumps(completed_images)
                            # 更新Redis缓存
                            await save_image_urls_to_redis(task.taskid, completed_images)

                    task.uptime = datetime.now()
                    task.progress = "100%"
                    task.model = model_name

                    # 如果有输入图片的OSS URL，保存到prompt_img字段
                    if prompt_img_url:
                        task.prompt_img = json.dumps([prompt_img_url])
                        logger.info(f"ComfyUI输入图片OSS URL已保存到数据库: {prompt_img_url}")

                    await db.commit()
                    await db.refresh(task)

                # 将资产信息存储到资产表
                try:
                    user_stmt = select(User).filter(User.username == task.username)
                    user_result = await db.execute(user_stmt)
                    task_user = user_result.scalars().first()

                    if task_user:
                        asset = await store_asset_from_instance(task, task_user, db)
                        if asset:
                            logger.info(f"成功将ComfyUI任务存储到资产表，资产ID: {asset.id}")
                        else:
                            logger.warning(f"ComfyUI任务存储到资产表失败，任务ID: {task.id}")
                    else:
                        logger.error(f"未找到用户: {task.username}，无法存储资产")
                except Exception as e:
                    logger.error(f"存储ComfyUI资产失败: {str(e)}")

                logger.info(f"ComfyUI图像生成成功，任务ID: {taskid}")

                # 任务完成后释放锁
                if task_id in task_locks:
                    with task_lock:
                        if task_id in task_locks and not task_locks[task_id].locked():
                            del task_locks[task_id]
                            logger.info(f"已释放任务锁: {task_id}")

            except Exception as e:
                # 更新任务状态为失败
                async with task_specific_lock:
                    result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                    task = result.scalars().first()
                    if task:
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = str(e)
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()
                logger.error(f"ComfyUI图像生成失败: {str(e)}")

    except Exception as e:
        logger.error(f"ComfyUI后台任务处理失败: {str(e)}")

@router.post("/comfyui", tags=["flux"])
async def comfyui_create_image(
    request: Request,
    payload: FluxCreateRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用ComfyUI创建Flux图像

    Args:
        payload: 包含提示文本和其他参数的请求

    Returns:
        包含任务ID和轮询URL的响应
    """
    if not payload.prompt:
        raise ClientVisibleException("提示词不能为空")
    
    # if not payload.input_image:
    #     raise ClientVisibleException("输入图片不能为空")

    # 验证 num 参数
    if payload.num > 4:
        raise ClientVisibleException("最大生成上限为4")

    prompt = payload.prompt
    prompt_en = prompt

    # 如果包含中文，进行翻译
    if contains_chinese(prompt):
        try:
            prompt_en = await get_translate(prompt, "zh-CN", "en")
        except Exception as e:
            logger.error(f"翻译失败: {str(e)}")
            raise ClientVisibleException("翻译失败") from e

    try:
        # 检查是否提供了taskid
        task = None
        if payload.taskid:
            # 查询是否存在该任务
            result = await db.execute(
                select(ChatMjTasks).where(
                    ChatMjTasks.taskid == payload.taskid,
                    ChatMjTasks.username == user.username
                )
            )
            task = result.scalars().first()
            
            # 如果找到任务且提供了regenerate_index参数，处理image_urls删除指定的空URL
            if task and payload.regenerate_index is not None and task.image_urls:
                try:
                    # 1.首先从SQL数据库获取最新数据
                    existing_urls = []
                    if task.image_urls:
                        try:
                            existing_urls = json.loads(task.image_urls)
                            if not isinstance(existing_urls, list):
                                logger.warning(f"task.image_urls不是列表类型: {type(existing_urls)}")
                                existing_urls = []
                        except Exception as e:
                            logger.error(f"解析任务的image_urls失败: {str(e)}")

                    # 2.将数据库中的数据设置到Redis
                    await save_image_urls_to_redis(task.taskid, existing_urls)
                    logger.info(f"从数据库获取并保存到Redis: {existing_urls}")

                    # 3.从Redis中获取数据进行后续处理
                    existing_urls = await get_image_urls_from_redis(task.taskid)
                    if existing_urls is None:
                        existing_urls = []

                    # 检查索引是否有效
                    if 0 <= payload.regenerate_index < len(existing_urls):
                        # 检查该索引是否是空URL（即失败的图片）
                        if existing_urls[payload.regenerate_index] == "":
                            logger.info(f"移除索引 {payload.regenerate_index} 处的空URL用于ComfyUI重新生成")
                            logger.info(f"移除前 existing_urls: {existing_urls}")
                            # 删除指定索引的空URL
                            existing_urls.pop(payload.regenerate_index)

                            # 更新Redis中的数据
                            await save_image_urls_to_redis(task.taskid, existing_urls)

                            # 更新任务的image_urls
                            task.image_urls = json.dumps(existing_urls)
                            await db.commit()
                            await db.refresh(task)

                            logger.info(f"移除后 existing_urls: {existing_urls}")
                        else:
                            logger.warning(f"索引 {payload.regenerate_index} 处的URL不为空，跳过删除")
                            logger.info(f"existing_urls: {existing_urls}")
                except Exception as e:
                    logger.error(f"处理ComfyUI regenerate_index删除空URL失败: {str(e)}")

        # 如果任务不存在或未提供taskid，则创建新任务
        if not task:
            # 生成任务ID (时间戳)
            taskid = str(int(time.time() * 1000))

            # 创建任务记录，状态为提交
            task = ChatMjTasks(
                username=user.username,
                taskid=taskid,
                action=ChatMjTasksAction.IMAGINE,
                status=ChatMjTasksStatus.SUBMITTED,
                prompt=prompt,
                prompt_en=prompt_en,
                submit_time=datetime.now(),
                start_time=datetime.now(),
                uptime=datetime.now(),
                manufacturer=ChatMjTasksModel.FLUX,
                model=payload.model_name
            )

            # 保存到数据库
            db.add(task)
            await db.commit()
            await db.refresh(task)

            # 初始化Redis中的image_urls为空列表
            await save_image_urls_to_redis(task.taskid, [])
        else:
            # 更新现有任务
            task.status = ChatMjTasksStatus.SUBMITTED
            task.submit_time = datetime.now()
            task.start_time = datetime.now()
            task.uptime = datetime.now()
            task.fail_reason = ""
            task.model = payload.model_name

            # 如果不保留历史，则清空image_url和image_urls
            if not payload.preserve_history:
                task.image_url = None
                task.image_urls = None
                await save_image_urls_to_redis(task.taskid, [])

            await db.commit()
            await db.refresh(task)

        operator = CreditOperator(
            user_id=user.id,
            ip=request.state.client_ip,
            editor=user.username,
            db=db,
        )

        # 添加后台任务
        background_tasks.add_task(
            process_comfyui_image_generation,
            prompt_en=prompt_en,
            model_name=payload.model_name,
            task_id=task.id,
            num=payload.num,
            taskid=task.taskid,
            input_image=payload.input_image,
            seed=payload.seed,
            aspect_ratio=payload.aspect_ratio,
            output_format=payload.output_format,
            webhook_url=payload.webhook_url,
            webhook_secret=payload.webhook_secret,
            prompt_upsampling=payload.prompt_upsampling,
            safety_tolerance=payload.safety_tolerance,
            preserve_history=payload.preserve_history,
            operator=operator,
        )

        # 返回成功响应
        return FluxResponse(
            code="0000",
            msg="创建ComfyUI图像任务成功",
            data={"task": ChatMjTasksOut(**task.__dict__)}
        )
    except Exception as e:
        # 记录错误并抛出HTTP异常
        logger.error(f"创建ComfyUI图像失败: {str(e)}")
        raise ClientVisibleException("创建ComfyUI图像失败") from e

@router.post("/create", tags=["flux"])
async def flux_create_image(
    request: Request,
    payload: FluxCreateRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建Flux图像

    Args:
        payload: 包含提示文本和其他参数的请求

    Returns:
        包含任务ID和轮询URL的响应
    """
    if not payload.prompt:
        raise ClientVisibleException("提示词不能为空")

    # 验证 num 参数
    if payload.num > 2:
        raise ClientVisibleException("最大生成上限为2")

    prompt = payload.prompt
    prompt_en = prompt

    # 如果包含中文，进行翻译
    if contains_chinese(prompt):
        try:
            prompt_en = await get_translate(prompt, "zh-CN", "en")
        except Exception as e:
            logger.error(f"翻译失败: {str(e)}")
            raise ClientVisibleException("翻译失败") from e

    try:
        # 检查是否提供了taskid
        task = None
        if payload.taskid:
            # 查询是否存在该任务
            result = await db.execute(
                select(ChatMjTasks).where(
                    ChatMjTasks.taskid == payload.taskid,
                    ChatMjTasks.username == user.username
                )
            )
            task = result.scalars().first()
            
            # 如果找到任务且提供了regenerate_index参数，处理image_urls删除指定的空URL
            if task and payload.regenerate_index is not None and task.image_urls:
                try:
                    # 1.首先从SQL数据库获取最新数据
                    existing_urls = []
                    if task.image_urls:
                        try:
                            existing_urls = json.loads(task.image_urls)
                            if not isinstance(existing_urls, list):
                                logger.warning(f"task.image_urls不是列表类型: {type(existing_urls)}")
                                existing_urls = []
                        except Exception as e:
                            logger.error(f"解析任务的image_urls失败: {str(e)}")

                    # 2.将数据库中的数据设置到Redis
                    await save_image_urls_to_redis(task.taskid, existing_urls)
                    logger.info(f"从数据库获取并保存到Redis: {existing_urls}")

                    # 3.从Redis中获取数据进行后续处理
                    existing_urls = await get_image_urls_from_redis(task.taskid)
                    if existing_urls is None:
                        existing_urls = []

                    # 检查索引是否有效
                    if 0 <= payload.regenerate_index < len(existing_urls):
                        # 检查该索引是否是空URL（即失败的图片）
                        if existing_urls[payload.regenerate_index] == "":
                            logger.info(f"移除索引 {payload.regenerate_index} 处的空URL用于重新生成")
                            logger.info(f"移除前 existing_urls: {existing_urls}")
                            # 删除指定索引的空URL
                            existing_urls.pop(payload.regenerate_index)

                            # 更新Redis中的数据
                            await save_image_urls_to_redis(task.taskid, existing_urls)

                            # 更新任务的image_urls
                            task.image_urls = json.dumps(existing_urls)
                            await db.commit()
                            await db.refresh(task)

                            logger.info(f"移除后 existing_urls: {existing_urls}")
                        else:
                            logger.warning(f"索引 {payload.regenerate_index} 处的URL不为空，跳过删除")
                            logger.info(f"existing_urls: {existing_urls}")
                except Exception as e:
                    logger.error(f"处理regenerate_index删除空URL失败: {str(e)}")

        # 如果任务不存在或未提供taskid，则创建新任务
        if not task:
            # 生成任务ID (时间戳)
            taskid = str(int(time.time() * 1000))

            # 创建任务记录，状态为提交
            task = ChatMjTasks(
                username=user.username,
                taskid=taskid,
                action=ChatMjTasksAction.IMAGINE,
                status=ChatMjTasksStatus.SUBMITTED,
                prompt=prompt,
                prompt_en=prompt_en,
                submit_time=datetime.now(),
                start_time=datetime.now(),
                uptime=datetime.now(),
                manufacturer=ChatMjTasksModel.FLUX,
                model=payload.model_name
            )

            # 保存到数据库
            db.add(task)
            await db.commit()
            await db.refresh(task)

            # 初始化Redis中的image_urls为空列表
            await save_image_urls_to_redis(task.taskid, [])
        else:
            # 更新现有任务
            task.status = ChatMjTasksStatus.SUBMITTED
            task.submit_time = datetime.now()
            task.start_time = datetime.now()
            task.uptime = datetime.now()
            task.fail_reason = ""
            task.model = payload.model_name

            # 如果不保留历史，则清空image_url和image_urls
            if not payload.preserve_history:
                task.image_url = None
                task.image_urls = None
                await save_image_urls_to_redis(task.taskid, [])

            await db.commit()
            await db.refresh(task)

        operator = CreditOperator(
            user_id=user.id,
            ip=request.state.client_ip,
            editor=user.username,
            db=db,
        )
        # 添加后台任务
        background_tasks.add_task(
            process_flux_image_generation,
            prompt_en=prompt_en,
            model_name=payload.model_name,
            task_id=task.id,
            num=payload.num,
            taskid=task.taskid,
            input_image=payload.input_image,
            seed=payload.seed,
            aspect_ratio=payload.aspect_ratio,
            output_format=payload.output_format,
            webhook_url=payload.webhook_url,
            webhook_secret=payload.webhook_secret,
            prompt_upsampling=payload.prompt_upsampling,
            safety_tolerance=payload.safety_tolerance,
            preserve_history=payload.preserve_history,
            operator=operator,
        )

        # 返回成功响应
        return FluxResponse(
            code="0000",
            msg="创建图像任务成功",
            data={"task": ChatMjTasksOut(**task.__dict__)}
        )
    except Exception as e:
        # 记录错误并抛出HTTP异常
        logger.error(f"创建Flux图像失败: {str(e)}")
        raise ClientVisibleException("创建Flux图像失败") from e

@router.get("/models", tags=["flux"])
async def get_models():
    """
    获取模型列表

    Returns:
        包含所有厂商和对应模型的列表
    """
    try:
        return FluxResponse(
            code="0000",
            msg="获取模型列表成功",
            data={"models": chat_models}
        )
    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise ClientVisibleException("获取模型列表失败") from e

@router.post("/change/pan", tags=["flux"])
async def flux_pan_image(
    request: Request,
    payload: FluxPanRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用ComfyUI模型扩展图片（向左、向右、向上、向下）

    Args:
        payload: 包含按钮标识和图片URL的请求

    Returns:
        包含任务ID的响应
    """
    if not payload.prompt:
        raise ClientVisibleException("提示词不能为空")

    # 验证 prompt 参数是否为有效的按钮标识
    if payload.prompt not in pan_prompt_mapping:
        raise ClientVisibleException("无效的按钮标识")

    # 验证 num 参数
    if payload.num > 2:
        raise ClientVisibleException("最大生成上限为2")

    # 验证是否提供了图片URL
    if not payload.image_urls:
        raise ClientVisibleException("扩图功能需要至少一张参考图片")

    # 从映射字典中获取对应的英文提示词
    prompt = payload.prompt
    prompt_en = pan_prompt_mapping[prompt]

    try:
        # 检查是否提供了taskid
        task = None
        if payload.taskid:
            # 查询是否存在该任务
            result = await db.execute(
                select(ChatMjTasks).where(
                    ChatMjTasks.taskid == payload.taskid,
                    ChatMjTasks.username == user.username
                )
            )
            task = result.scalars().first()
            
            # 如果找到任务且提供了regenerate_index参数，处理image_urls删除指定的空URL
            if task and payload.regenerate_index is not None and task.image_urls:
                try:
                    # 1.首先从SQL数据库获取最新数据
                    existing_urls = []
                    if task.image_urls:
                        try:
                            existing_urls = json.loads(task.image_urls)
                            if not isinstance(existing_urls, list):
                                logger.warning(f"task.image_urls不是列表类型: {type(existing_urls)}")
                                existing_urls = []
                        except Exception as e:
                            logger.error(f"解析任务的image_urls失败: {str(e)}")

                    # 2.将数据库中的数据设置到Redis
                    await save_image_urls_to_redis(task.taskid, existing_urls)
                    logger.info(f"从数据库获取并保存到Redis: {existing_urls}")

                    # 3.从Redis中获取数据进行后续处理
                    existing_urls = await get_image_urls_from_redis(task.taskid)
                    if existing_urls is None:
                        existing_urls = []

                    # 检查索引是否有效
                    if 0 <= payload.regenerate_index < len(existing_urls):
                        # 检查该索引是否是空URL（即失败的图片）
                        if existing_urls[payload.regenerate_index] == "":
                            logger.info(f"移除索引 {payload.regenerate_index} 处的空URL用于扩图重新生成")
                            logger.info(f"移除前 existing_urls: {existing_urls}")
                            # 删除指定索引的空URL
                            existing_urls.pop(payload.regenerate_index)

                            # 更新Redis中的数据
                            await save_image_urls_to_redis(task.taskid, existing_urls)

                            # 更新任务的image_urls
                            task.image_urls = json.dumps(existing_urls)
                            await db.commit()
                            await db.refresh(task)

                            logger.info(f"移除后 existing_urls: {existing_urls}")
                        else:
                            logger.warning(f"索引 {payload.regenerate_index} 处的URL不为空，跳过删除")
                            logger.info(f"existing_urls: {existing_urls}")
                except Exception as e:
                    logger.error(f"处理扩图 regenerate_index删除空URL失败: {str(e)}")

        # 如果任务不存在或未提供taskid，则创建新任务
        if not task:
            # 生成任务ID (时间戳)
            taskid = str(int(time.time() * 1000))

            # 创建任务记录，状态为提交
            task = ChatMjTasks(
                username=user.username,
                taskid=taskid,
                action=ChatMjTasksAction.PAN,  # 设置action为PAN
                status=ChatMjTasksStatus.SUBMITTED,
                prompt=prompt_en,
                prompt_en=prompt_en,
                submit_time=datetime.now(),
                start_time=datetime.now(),
                uptime=datetime.now(),
                manufacturer=ChatMjTasksModel.FLUX,
                model=payload.model_name
            )

            # 保存到数据库
            db.add(task)
            await db.commit()
            await db.refresh(task)

            # 初始化Redis中的image_urls为空列表
            await save_image_urls_to_redis(task.taskid, [])
        else:
            # 更新现有任务
            task.status = ChatMjTasksStatus.SUBMITTED
            task.action = ChatMjTasksAction.PAN  # 更新action为PAN
            task.prompt = prompt_en
            task.prompt_en = prompt_en
            task.submit_time = datetime.now()
            task.start_time = datetime.now()
            task.uptime = datetime.now()
            task.fail_reason = ""
            task.model = payload.model_name

            # 如果不保留历史，则清空image_url和image_urls
            if not payload.preserve_history:
                task.image_url = None
                task.image_urls = None
                await save_image_urls_to_redis(task.taskid, [])

            await db.commit()
            await db.refresh(task)

        # 取第一张图片URL作为输入图片
        input_image = payload.image_urls[0] if payload.image_urls else None

        operator = CreditOperator(
            user_id=user.id,
            ip=request.state.client_ip,
            editor=user.username,
            db=db,
        )
        # 使用ComfyUI的图像生成函数
        background_tasks.add_task(
            process_comfyui_image_generation,
            prompt_en=prompt_en,
            model_name=payload.model_name,
            task_id=task.id,
            num=payload.num,
            taskid=task.taskid,
            input_image=input_image,
            seed=payload.seed,
            aspect_ratio=payload.aspect_ratio,
            output_format=payload.output_format,
            webhook_url=payload.webhook_url,
            webhook_secret=payload.webhook_secret,
            prompt_upsampling=payload.prompt_upsampling,
            safety_tolerance=payload.safety_tolerance,
            preserve_history=payload.preserve_history,
            operator=operator,
        )

        # 返回成功响应
        return FluxResponse(
            code="0000",
            msg="创建扩图任务成功",
            data={"task": ChatMjTasksOut(**task.__dict__)}
        )
    except Exception as e:
        # 记录错误并抛出HTTP异常
        logger.error(f"创建扩图任务失败: {str(e)}")
        raise ClientVisibleException("创建扩图任务失败") from e
