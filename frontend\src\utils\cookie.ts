// 新增 cookie 配置常量
export const COOKIE_DOMAIN = 'igamebuy.com';
export const COOKIE_PATH = '/';

// 统一的 cookie 配置
export const DEFAULT_COOKIE_OPTIONS = {
  path: COOKIE_PATH,
  domain: COOKIE_DOMAIN,
  sameSite: 'Lax' as const
};

export const setCookie = (name: string, value: string, options: any = {}) => {
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

  const finalOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };

  if (finalOptions.path) {
    cookieString += `; path=${finalOptions.path}`;
  }

  if (finalOptions.domain) {
    cookieString += `; domain=${finalOptions.domain}`;
  }

  if (finalOptions.expires) {
    cookieString += `; expires=${finalOptions.expires.toUTCString()}`;
  }

  // 仅在 options.secure 为 true 时才设置 Secure 属性
  if (finalOptions.secure) {
    cookieString += `; secure`;
  }

  if (finalOptions.sameSite) {
    cookieString += `; samesite=${finalOptions.sameSite}`;
  }

  document.cookie = cookieString;
};

export const getCookie = (name: string): string | null => {
  const nameEQ = `${encodeURIComponent(name)}=`;
  const ca = document.cookie.split(';');
  for (let c of ca) {
    c = c.trim();
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length));
    }
  }
  return null;
};

export const deleteCookie = (name: string, options: any = {}) => {
  setCookie(name, '', {
    ...DEFAULT_COOKIE_OPTIONS,
    ...options,
    expires: new Date(0)
  });
};
