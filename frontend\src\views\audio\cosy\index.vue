<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst, UploadCustomRequestOptions, UploadInst } from 'naive-ui';
import WaveSurfer from 'wavesurfer.js';
import type { Region } from 'wavesurfer.js/dist/plugins/regions.js';
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js';
// import HoverPlugin from 'wavesurfer.js/dist/plugins/Hover.js';
import { blobToBase64, blobUrlToAudioBuffer, bufferToWave, fileToArrayBuffer, sliceAudio } from '@/utils/audio';
// import ZoomPlugin from 'wavesurfer.js/dist/plugins/zoom.js';
import { useThemeStore } from '@/store/modules/theme';
import { getSpeaker, postCosyTast, postExtractSubtitle, postZeroShotTast } from '@/service/api';
import { useFormRules } from '@/hooks/common/form';
import { downloadFile, getRandomValueInRange } from '@/utils/common';

const themeStore = useThemeStore();
let wavesurfer: WaveSurfer | null = null;
// const random = (min: number, max: number) => Math.random() * (max - min) + min;
// const randomColor = () => `rgba(${random(0, 255)}, ${random(0, 255)}, ${random(0, 255)}, 0.5)`;

// 播放暂停方法
// const playPause = () => {
//   wavesurfer?.playPause();
// };
const message = useMessage();
const GPTformRef = ref<FormInst | null>(null);
const ZeroShotformRef = ref<FormInst | null>(null);
const uploadRef = ref<UploadInst>();
const wrapperRef = ref<HTMLElement>();
// const formRef = ref<HTMLElement>();
const textInput = ref<HTMLElement>();
// const promptAudioRef = ref(null);
const loading = ref(false);
const playing = ref(false);
const extract_loading = ref(false);
// const uploadLoading = ref(false);
const gptAudioUrl = ref<string>('');
const zeroShotAudioUrl = ref<string>('');
const promptAudioBlobUrl = ref<string>(''); // 原始的
const tmpPromptAudioBlobUrl = ref<string>(''); // 临时的，编辑后的
// const tmpPromptAudioB64 = ref<string>(''); // 临时的，编辑后的b64
const fileName = ref<string>('');
const defaultSpeakers = ref<CommonType.Option[]>([]);
const customSpeakers = ref<Record<string, { audio: string; model: string }>>({});
const previewAudioUrl = ref('');
const audioStartTime = ref<number>(0); // 音频开始时间
const audioEndTime = ref<number>(0); // 音频结束时间
const previewAudioRef = ref<HTMLAudioElement>();
const isPreviewPlaying = ref(false);
// const modelOption: CommonType.Option[] = [
//   { label: '预训练音色', value: 'cosy' },
//   { label: '3s极速复刻', value: 'zero_shot' }
// ];
// # zero_shot usage, <|zh|><|en|><|jp|><|yue|><|ko|> for Chinese/English/Japanese/Cantonese/Korean
const languageOption: CommonType.Option[] = [
  { label: '默认', value: '' },
  { label: '中文', value: '<|zh|>' },
  { label: '英文', value: '<|en|>' },
  { label: '日文', value: '<|jp|>' },
  { label: '粤语', value: '<|yue|>' },
  { label: '韩语', value: '<|ko|>' }
];
const createDefaultModel = (): Api.Audio.Cosy => {
  return {
    text: '',
    // model: 'cosy',
    model: 'zero_shot',
    speaker: '',
    new: 0,
    speed: 1,
    streaming: 0,
    seed: null,

    prompt_text: '',
    prompt_audio: '',
    prompt_audio_b64: '',
    prompt_audio_start: 0,
    prompt_audio_end: 0,
    save_name: '',
    language: ''
  };
};
const model: Api.Audio.Cosy = reactive(createDefaultModel());

// 是不是复制模式
const isZeroShot = computed(() => {
  return model.model === 'zero_shot';
});
// 是否选择了音频
const hasAudioFile = computed(() => {
  return promptAudioBlobUrl.value !== '';
});
// const clipInfo = computed(() => {
//   if (audioStartTime.value < audioEndTime.value) {
//     return `${audioStartTime.value} - ${audioEndTime.value}`;
//   }
//   return '';
// });
const { defaultRequiredRule } = useFormRules();
type GTPRuleKey = Extract<keyof Api.Audio.Cosy, 'text' | 'speaker'>;
const GPTRules: Record<GTPRuleKey, App.Global.FormRule> = {
  text: defaultRequiredRule,
  speaker: defaultRequiredRule
};

type ZeroShotRuleKey = Extract<keyof Api.Audio.Cosy, 'prompt_text' | 'prompt_audio' | 'text' | ''>;
const ZeroShotRules: Record<ZeroShotRuleKey, App.Global.FormRule> = {
  text: defaultRequiredRule,
  prompt_text: defaultRequiredRule,
  prompt_audio: defaultRequiredRule
};

const handleSpeakerChange = (value: string) => {
  if (customSpeakers.value[value]?.audio) {
    previewAudioUrl.value = `data:audio/wav;base64,${customSpeakers.value[value].audio}`;
  } else {
    previewAudioUrl.value = '';
  }
};

const refreshSpeeker = () => {
  getSpeaker().then(res => {
    if (res.data) {
      const defaultSpeakerData: Record<string, { audio: string; model: string }> = res.data.default_speakers || {};
      const customSpeakerData: Record<string, { audio: string; model: string }> = res.data.custom_speakers || {};

      // 添加日志查看数据结构
      console.log('Speaker Data:', {
        defaultSpeakers: defaultSpeakerData,
        customSpeakers: customSpeakerData
      });

      customSpeakers.value = { ...defaultSpeakerData, ...customSpeakerData };

      // 将所有音色转换为选项格式
      defaultSpeakers.value = [
        ...Object.keys(defaultSpeakerData).map(key => ({
          label: key,
          value: key
        })),
        ...Object.keys(customSpeakerData).map(key => ({
          label: key,
          value: key
        }))
      ];

      // 设置默认选中第一个音色
      if (defaultSpeakers.value.length > 0) {
        model.speaker = defaultSpeakers.value[0].value;
        // 初始化时也要设置预览音频
        handleSpeakerChange(model.speaker);
      }
    }
  });
};

const customRequest = async ({ file, data }: UploadCustomRequestOptions) => {
  fileName.value = '';
  tmpPromptAudioBlobUrl.value = '';
  promptAudioBlobUrl.value = '';
  // console.log(file);
  // uploadLoading.value = true;
  // let duration = 0;
  // file.type
  // mp3=>audio/mpeg
  // mp4=>video/mp4

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }
  console.log(file.type);
  if (file.type === 'video/mp4') {
    // mp3=>audio/mpeg
    // mp4=>video/mp4
    const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // 创建一个AudioContext
    const audioCtx = new AudioContext();
    // arrayBuffer转audioBuffer
    await audioCtx.decodeAudioData(buffer, audioBuffer => {
      console.log('audioCtx.decodeAudioData');
      // audioBuffer就是AudioBuffer
      const blob = bufferToWave(audioBuffer, audioBuffer.sampleRate * audioBuffer.duration);
      // duration = audioBuffer.duration;
      // 音频文件生成file上传
      const uuid = Date.now();
      const newFileName = `${uuid}.wav`;
      const audioFile = new File([blob], newFileName, { type: 'audio/wav', lastModified: Date.now() });
      console.log('提取音频添加到表单');
      formData.append('file', audioFile as File);
      // 使用Blob地址,
      const blobUrl = URL.createObjectURL(blob);
      promptAudioBlobUrl.value = blobUrl;
    });
  } else {
    if (file.file) {
      const blobUrl = URL.createObjectURL(file!.file);
      promptAudioBlobUrl.value = blobUrl;
    }

    // const audioCtx = new AudioContext();
    // const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // await audioCtx.decodeAudioData(buffer, audioBuffer => {
    //   duration = audioBuffer.duration;
    // });

    formData.append('file', file.file as File);
  }

  // const audio = new Audio(promptAudioBlobUrl);
  // console.log('duration', duration);
  // if (duration > 5) {
  //   // message.error('参考音频最好5秒内，不要超过7秒');

  //   // 只提示，不阻止
  //   // promptAudioBlobUrl.value = '';
  //   // uploadLoading.value = false;
  //   // if (uploadRef.value) {
  //   //   uploadRef.value.clear();
  //   // }
  //   // return;
  // }

  tmpPromptAudioBlobUrl.value = promptAudioBlobUrl.value;

  initWaveSurfer();
  fileName.value = file.name;
  uploadRef.value?.clear();
  // 不再上传
  // postUploadFile('cosy', formData, progressEvent => {
  //   if (progressEvent.progress) {
  //     onProgress({ percent: progressEvent!.progress * 100 });
  //   }
  // })
  //   .then(res => {
  //     console.log(res);
  //     if (res.data) model.prompt_audio = res.data.file_path;
  //     fileName.value = file.name;
  //     onFinish();
  //   })
  //   .catch(error => {
  //     console.log(error);
  //     message.error('上传失败');
  //     onError();
  //   })
  //   .finally(() => {
  //     uploadLoading.value = false;
  //   });
};
// 开始裁剪
const clip = async (): Promise<string> => {
  console.log('clip', [audioStartTime.value, audioEndTime.value]);
  const tmpAudioBuffer = await blobUrlToAudioBuffer(promptAudioBlobUrl.value);
  const newAudioBuffer = await sliceAudio(tmpAudioBuffer, audioStartTime.value, audioEndTime.value);

  return new Promise((resolve, reject) => {
    try {
      // 音频文件生成file
      const blob = bufferToWave(newAudioBuffer, newAudioBuffer.sampleRate * newAudioBuffer.duration);

      blobToBase64(blob).then(res => {
        resolve(res);
      });
    } catch (error) {
      reject(error);
      message.error('裁剪出错，请调整裁剪区间');
    }
  });
};
const extract = async () => {
  try {
    const b64 = await clip();
    // console.log(b64);
    extract_loading.value = true;
    // if (!model.prompt_audio) {
    //   message.error('未上传音频');
    //   return;
    // }
    if (!b64) {
      message.error('音频转化失败');
      return;
    }
    const params: Api.Media.Subtitle = {
      file_path: model.prompt_audio,
      prompt_audio_b64: b64,
      model: 'Gpt-4o mini',
      format: 'text',
      gamecode: '默认',
      translate: ''
    };
    model.prompt_text = '';
    postExtractSubtitle(params)
      .then(res => {
        if (res.data) {
          model.prompt_text = res.data.text;
        }
      })
      .finally(() => {
        extract_loading.value = false;
      });
  } catch (error) {
    console.log(error);
    message.error('音频转化失败,请上传音频文件');
  }
};
function initWaveSurfer() {
  // 先销毁原来的
  wavesurfer?.destroy();
  const regions = RegionsPlugin.create();
  // const zooms = ZoomPlugin.create({ scale: 0.2 });

  let activeRegion: Region | null;
  regions.on('region-in', region => {
    activeRegion = region;
  });
  regions.on('region-out', region => {
    console.log('region-out', region);
    console.log('activeRegion', activeRegion);
    // if (activeRegion === region) {
    //   console.log('same region');
    //   region.play();
    // }
    region.play();
  });
  regions.on('region-clicked', (region, e) => {
    console.log('region-clicked');
    e.stopPropagation(); // prevent triggering a click on the waveform
    // if (!playing.value) {
    //   region.play();
    // }
    region.play();
  });
  // regions.on('region-out', region => {
  //   region.play();
  // });
  regions.on('region-updated', region => {
    console.log(region);
    audioStartTime.value = region.start;
    audioEndTime.value = region.end;
    // audioStartTime.value = Number.parseInt(`${region.start}`, 10);
    // audioEndTime.value = Math.ceil(region.end);
    console.log('区间', region.start, region.end);
  });
  // const cursorPlugn = Hover.create({
  //   showTime: true,
  //   opacity: 1,
  //   customShowTimeStyle: {
  //     'background-color': '#000',
  //     color: '#fff',
  //     padding: '2px',
  //     'font-size': '10px'
  //   }
  // });
  // const hoverPlugn = HoverPlugin.create({
  //   // lineColor: '#ff0000',
  //   lineWidth: 1,
  //   labelBackground: '#555',
  //   labelColor: '#fff',
  //   labelSize: '11px'
  // });
  wavesurfer = WaveSurfer.create({
    container: '#waveform', // 绑定容器
    // url: '/proxy-default/upload/separate/80edbfd7c615f3022cda134647363bf50d22966a.wav', // 音频地址，使用的是 vite，将文件放到根目录 public 下才有用!!!
    url: promptAudioBlobUrl.value,
    autoCenter: false, // 自动播放
    waveColor: themeStore.themeColor,
    progressColor: '#383351',
    // Set a bar width
    // barWidth: 5,
    // // Optionally, specify the spacing between bars
    // barGap: 2.5,
    // // And the bar radius
    // barRadius: 2.5,
    barHeight: 0.7,
    barWidth: 3,
    barRadius: 3,
    cursorWidth: 1,
    barGap: 3,
    hideScrollbar: true,
    dragToSeek: true,
    plugins: [
      regions
      // zooms
      // RegionsPlugin.create() // 注册插件的第一种方式,也可以使用 registerPlugin 来注册
    ]
  });

  wavesurfer.on('decode', () => {});
  /** When the audio is both decoded and can play */
  wavesurfer.on('ready', duration => {
    console.log('Ready', `${duration}s`);
    audioEndTime.value = duration;
    playing.value = false;

    // Regions
    regions.addRegion({
      start: audioStartTime.value,
      end: audioEndTime.value,
      content: '',
      // color: randomColor(),
      color: 'rgba(13, 208, 61, 0.5)',
      drag: true,
      resize: true,
      minLength: 1
    });
  });

  /** When visible waveform is drawn */
  wavesurfer.on('redrawcomplete', () => {
    console.log('Redraw began');
  });

  /** When all audio channel chunks of the waveform have drawn */
  wavesurfer.on('redrawcomplete', () => {
    console.log('Redraw complete');
  });

  /** When the audio starts playing */
  wavesurfer.on('play', () => {
    console.log('Play');
    playing.value = true;
  });

  /** When the audio pauses */
  wavesurfer.on('pause', () => {
    console.log('Pause');
    playing.value = false;
  });
  wavesurfer.on('interaction', () => {
    activeRegion = null;
    wavesurfer?.play();
  });
}

const removeAudio = async () => {
  audioStartTime.value = 0;
  audioEndTime.value = 0;
  promptAudioBlobUrl.value = '';
  tmpPromptAudioBlobUrl.value = '';
  wavesurfer?.destroy();
};
const triggerPlayPause = async () => {
  if (playing.value) {
    wavesurfer?.pause();
  } else {
    wavesurfer?.setTime(audioStartTime.value);
    wavesurfer?.play();
  }
};

// 辅助函数：处理Zero Shot任务
const handleZeroShotTask = async (): Promise<boolean> => {
  if (!hasAudioFile.value) {
    message.error('未上传音频样本');
    return false;
  }
  try {
    const b64 = await clip();
    model.prompt_audio_b64 = b64;
  } catch (error) {
    message.error('文件处理失败');
    console.log(error);
    return false;
  }
  await ZeroShotformRef.value!.validate(err => {
    if (err) {
      loading.value = false;
      console.log('验证失败');
    }
  });

  loading.value = true;
  zeroShotAudioUrl.value = '';

  const postData = { ...model };
  postData.text = postData.language + postData.text;
  const res = await postZeroShotTast(postData);
  console.log(res);
  if (res.data) {
    try {
      // 直接使用返回的URL
      if (res.data.type === 'oss_url' && res.data.b64) {
        zeroShotAudioUrl.value = res.data.b64;
        console.log('最终zero_shot音频URL:', zeroShotAudioUrl.value);
      } else {
        message.error('返回数据格式异常');
      }
    } catch (error) {
      console.error('处理zero_shot音频数据时出错:', error);
      message.error('音频数据处理失败');
    }

    if (postData.save_name !== '') {
      refreshSpeeker();
    }
  }
  return true;
};

// 辅助函数：处理Cosy任务
const handleCosyTask = async (): Promise<boolean> => {
  await GPTformRef.value!.validate(err => {
    if (err) {
      console.log('验证失败');
    }
  });
  loading.value = true;
  gptAudioUrl.value = '';
  const postData = { ...model };
  postData.text = postData.language + postData.text;

  // 修改判断自定义音色的逻辑
  const selectedSpeaker = customSpeakers.value[postData.speaker];
  const isDefaultSpeaker =
    selectedSpeaker?.model === '中文女' ||
    selectedSpeaker?.model === '中文男' ||
    selectedSpeaker?.model === '日语男' ||
    selectedSpeaker?.model === '粤语女' ||
    selectedSpeaker?.model === '韩语女';

  postData.new = isDefaultSpeaker ? 0 : 1;

  console.log('Post Data:', postData);
  const res = await postCosyTast(postData);
  if (res.data) {
    try {
      // 直接使用返回的URL
      if (res.data.type === 'oss_url' && res.data.b64) {
        gptAudioUrl.value = res.data.b64;
        console.log('最终音频URL:', gptAudioUrl.value);
      } else {
        message.error('返回数据格式异常');
      }
    } catch (error) {
      console.error('处理音频数据时出错:', error);
      message.error('音频数据处理失败');
    }
  }
  return true;
};

const postTask = async (): Promise<boolean> => {
  console.log(model);
  let result = false;

  if (model.model === 'zero_shot') {
    result = await handleZeroShotTask();
  } else {
    result = await handleCosyTask();
  }

  loading.value = false;
  return result;
};

const randomSeed = () => {
  model.seed = getRandomValueInRange(0, 99999);
};

// 添加音频速率控制和下载相关的状态和函数
const instrumentalAudio = ref<HTMLAudioElement>();
const vocalsAudio = ref<HTMLAudioElement>();

// 下载音频文件
const downloadAudio = (url: string, type: 'instrumental' | 'vocals') => {
  if (!url) {
    message.error('暂无音频文件');
    return;
  }
  const fn = fileName.value.replace(/\.[^/.]+$/, '');
  downloadFile(url, `${fn}_${type}.wav`);
};

// 设置音频播放速率
const setPlaybackRate = (rate: number, type: 'instrumental' | 'vocals') => {
  const audio = type === 'instrumental' ? instrumentalAudio.value : vocalsAudio.value;
  if (audio) {
    audio.playbackRate = rate;
  }
};

const playPreviewAudio = () => {
  if (!previewAudioRef.value) return;

  if (isPreviewPlaying.value) {
    previewAudioRef.value.pause();
    isPreviewPlaying.value = false;
  } else {
    previewAudioRef.value.play();
    isPreviewPlaying.value = true;
  }
};

// 添加响应式屏幕尺寸检测
const windowWidth = ref(window.innerWidth);
const isSmallScreen = computed(() => windowWidth.value < 1500);

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

// 生命周期钩子中添加和移除事件监听器
onMounted(() => {
  refreshSpeeker();
  window.addEventListener('resize', handleResize);

  // 给音频元素添加事件监听器
  setTimeout(() => {
    const audios = document.querySelectorAll('audio');
    audios.forEach(audio => {
      audio.addEventListener('error', e => {
        console.error('音频加载错误:', e);
        console.log('音频src:', audio.src);
      });
      audio.addEventListener('loadeddata', () => {
        console.log('音频加载成功:', audio.src);
      });
    });
  }, 1000);

  // 创建一个观察器实例
  const observer = new MutationObserver(() => {
    const feedbackWrappers = document.querySelectorAll('.n-form-item-feedback-wrapper');
    feedbackWrappers.forEach(wrapper => {
      (wrapper as HTMLElement).style.display = 'none';
    });
  });

  // 配置观察选项
  const config = {
    childList: true,
    subtree: true
  };

  // 开始观察文档中的变化
  observer.observe(document.body, config);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div ref="wrapperRef" class="h-vh flex flex-col gap-16px">
    <NScrollbar class="h-full">
      <NTabs v-model:value="model.model" type="line" animated class="h-full flex">
        <NTabPane name="cosy" tab="预训练音色">
          <div class="h-[calc(100vh-13em)] flex gap-16px">
            <!-- 左侧操作面板 -->
            <div class="panel-container shrink-0">
              <NCard class="h-full">
                <NForm ref="GPTformRef" :model="model" :rules="GPTRules">
                  <NFormItem v-if="!isZeroShot" path="age" label="选择预训练音色">
                    <NInputGroup>
                      <NSelect
                        v-model:value="model.speaker"
                        :options="defaultSpeakers"
                        class="flex-1"
                        @update:value="handleSpeakerChange"
                      />
                      <NButton v-if="previewAudioUrl" secondary class="w-1/13 flex-shrink-0" @click="playPreviewAudio">
                        <SvgIcon
                          :icon="isPreviewPlaying ? 'zondicons:pause-outline' : 'zondicons:play-outline'"
                          class="h-20px w-20px text-blue-600"
                        />
                      </NButton>
                    </NInputGroup>
                    <audio
                      ref="previewAudioRef"
                      :src="previewAudioUrl"
                      class="hidden"
                      @ended="isPreviewPlaying = false"
                    />
                  </NFormItem>
                  <NGrid cols="24" :x-gap="10" item-responsive responsive="screen">
                    <NFormItemGi span="24" path="text" label="输入待录制文本" class="mt-3">
                      <NInput
                        ref="textInput"
                        v-model:value="model.text"
                        type="textarea"
                        placeholder="输入要转换的文字"
                        class="min-h-200px"
                      />
                    </NFormItemGi>
                    <NFormItemGi span="8" label="语种" class="mt-3">
                      <NSelect v-model:value="model.language" :options="languageOption" />
                    </NFormItemGi>
                    <NFormItemGi span="8" label="语速" class="mt-3">
                      <NSlider v-model:value="model.speed" :step="0.1" :min="0.5" :max="2" />
                    </NFormItemGi>
                    <NFormItemGi span="8" label="种子" class="mt-3">
                      <NInputGroup>
                        <NInputNumber v-model:value="model.seed" class="w-3/4" />
                        <NButton type="primary" class="w-1/4" @click.stop="randomSeed">随机</NButton>
                      </NInputGroup>
                    </NFormItemGi>
                  </NGrid>
                </NForm>
                <div class="mt-5 flex justify-center">
                  <NButton type="primary" class="h-55px w-70" size="large" :loading="loading" @click="postTask">
                    生成
                  </NButton>
                </div>
              </NCard>
            </div>
            <!-- 右侧自适应宽度面板 -->
            <div class="panel-container">
              <NCard class="h-full">
                <div class="mt-[0.5em] flex flex-col gap-2">
                  <audio ref="instrumentalAudio" controls :src="gptAudioUrl" class="w-full"></audio>
                  <div class="flex justify-center">
                    <NButtonGroup>
                      <NButton size="small" @click="downloadAudio(gptAudioUrl, 'instrumental')">
                        <template #icon>
                          <SvgIcon icon="mdi:download" />
                        </template>
                        下载
                      </NButton>
                      <NButton size="small" @click="setPlaybackRate(0.5, 'instrumental')">0.5x</NButton>
                      <NButton size="small" @click="setPlaybackRate(1.0, 'instrumental')">1.0x</NButton>
                      <NButton size="small" @click="setPlaybackRate(1.5, 'instrumental')">1.5x</NButton>
                    </NButtonGroup>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>

        <NTabPane name="zero_shot" tab="3s极速复刻">
          <div class="h-[calc(100vh-13em)] flex gap-16px">
            <!-- 左侧面板 -->
            <NCard class="panel-container h-full flex flex-col overflow-hidden overflow-y-auto">
              <NForm ref="ZeroShotformRef" :model="model" :rules="ZeroShotRules">
                <NGrid cols="24" :x-gap="10" item-responsive responsive="screen">
                  <NGridItem span="24" class="relative">
                    <div class="my-2 flex justify-center">
                      <NUpload
                        v-show="!hasAudioFile"
                        ref="uploadRef"
                        class="relative"
                        action=""
                        :custom-request="customRequest"
                        accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
                        :show-file-list="false"
                      >
                        <NUploadDragger class="min-h-128px">
                          <NText class="text-lg">点击或者拖动文件到该区域来上传</NText>
                          <NOl class="list-decimal">
                            音频为纯净人声（无背景音、特效音等）、不否包含多人音色
                            <br />
                            请控制音频在5秒左右
                          </NOl>
                        </NUploadDragger>
                      </NUpload>
                    </div>
                    <div class="n-upload-trigger h-full flex flex-col">
                      <div v-show="hasAudioFile" id="waveform" class="min-h-128px"></div>

                      <!-- 使用响应式条件控制显示NText或NMarquee -->

                      <div class="mb-3 flex items-center justify-between">
                        <template v-if="isSmallScreen">
                          <NMarquee class="mr-5 w-50">从音频两端可以拉到选框前裁剪音频</NMarquee>
                        </template>
                        <template v-else>
                          <NText class="flex-1">从音频两端可以拉到选框前裁剪音频</NText>
                        </template>
                        <!-- <div class="flex-1"></div> -->
                        <NButton strong secondary circle size="large" class="h-35px w-35px" @click="triggerPlayPause">
                          <SvgIcon
                            :icon="playing ? 'zondicons:pause-outline' : 'zondicons:play-outline'"
                            class="h-35px w-35px text-blue-600"
                          ></SvgIcon>
                        </NButton>
                        <div class="flex-1">
                          <NTag
                            :closable="hasAudioFile"
                            round
                            :bordered="false"
                            size="large"
                            class="mx-2 h-35px"
                            :on-close="removeAudio"
                          >
                            <template v-if="hasAudioFile">{{ fileName }}</template>
                            <template v-else>未选择音频</template>
                          </NTag>
                        </div>
                      </div>
                    </div>
                  </NGridItem>

                  <NGridItem span="24" class="relative">
                    <NFormItem path="prompt_text" label="示例文本">
                      <NInput
                        v-model:value="model.prompt_text"
                        type="textarea"
                        clearable
                        :rows="3"
                        placeholder="示例文本"
                      />
                      <div class="absolute right-0 top--8">
                        <NButton :loading="extract_loading" size="small" @click="extract">从音频提取样本</NButton>
                      </div>
                    </NFormItem>
                  </NGridItem>

                  <NGridItem span="24" class="mt-3">
                    <NFormItem label="保存为新音色" class="h-full">
                      <NInput
                        v-model:value="model.save_name"
                        clearable
                        placeholder="不为空则保存该次训练的音色模型"
                        class="h-full"
                        maxlength="8"
                      />
                    </NFormItem>
                  </NGridItem>

                  <NGridItem span="24" class="mt-3">
                    <NFormItem label="输入待录制文本" path="text">
                      <NInput
                        ref="textInput"
                        v-model:value="model.text"
                        type="textarea"
                        clearable
                        placeholder="输入要转换的文字"
                      />
                    </NFormItem>
                  </NGridItem>

                  <NFormItemGi span="8" class="mt-3" label="语种">
                    <NSelect v-model:value="model.language" :options="languageOption" />
                  </NFormItemGi>

                  <NFormItemGi span="8" class="mt-2" label="语速">
                    <NSlider v-model:value="model.speed" :step="0.1" :min="0.5" :max="2" />
                  </NFormItemGi>

                  <NFormItemGi span="8" class="mt-2" label="种子">
                    <NInputGroup>
                      <NInputNumber v-model:value="model.seed" class="w-3/4" />
                      <NButton type="primary" class="w-1/4" @click.stop="randomSeed">随机</NButton>
                    </NInputGroup>
                  </NFormItemGi>
                </NGrid>
              </NForm>
              <div class="mt-5 flex justify-center">
                <NButton type="primary" class="h-55px w-70" size="large" :loading="loading" @click="postTask">
                  生成
                </NButton>
              </div>
            </NCard>

            <!-- 右侧自适应宽度面板 -->
            <div class="panel-container">
              <NCard class="h-full">
                <div class="mt-[0.5em] flex flex-col gap-2">
                  <audio ref="vocalsAudio" controls :src="zeroShotAudioUrl" class="w-full"></audio>
                  <div class="flex justify-center">
                    <NButtonGroup>
                      <NButton size="small" @click="downloadAudio(zeroShotAudioUrl, 'vocals')">
                        <template #icon>
                          <SvgIcon icon="mdi:download" />
                        </template>
                        下载
                      </NButton>
                      <NButton size="small" @click="setPlaybackRate(0.5, 'vocals')">0.5x</NButton>
                      <NButton size="small" @click="setPlaybackRate(1.0, 'vocals')">1.0x</NButton>
                      <NButton size="small" @click="setPlaybackRate(1.5, 'vocals')">1.5x</NButton>
                    </NButtonGroup>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>
      </NTabs>
    </NScrollbar>
  </div>
</template>

<style lang="scss">
.media-warp {
  padding: 0 5px;
  grid-column: span 24 / span 24;
  background-color: aliceblue;
}

:deep(.n-scrollbar-rail) {
  right: 2px !important;
}

:deep(.n-scrollbar-content) {
  padding-right: 14px;
  height: 100% !important;
}

:deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;

  .n-card-header {
    flex-shrink: 0;
  }

  .n-card__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
}

// 确保表单内容可滚动
:deep(.n-form) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

// 新增均等布局样式
.panel-container {
  width: 50%;
  max-width: 820px;
  padding: 0 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  @media screen and (max-width: 1200px) {
    max-width: 600px;
  }
}
</style>
