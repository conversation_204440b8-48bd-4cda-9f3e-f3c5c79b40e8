from datetime import datetime
from enum import StrEnum
from typing import Annotated, Any, TypedDict, TypeAlias, Literal

from pydantic import BaseModel, Field


class Operation(StrEnum):
    REMOVE_WATERMARK = "remove_watermark"
    """去水印"""

    PARTIAL_REMOVE = "partial_remove"
    """部分消除"""

    HIGH_DEFINITION = "high_definition"
    """高清化"""

    LOSSLESS_ENLARGEMENT = "lossless_enlargement"
    """无损放大"""

    SMART_CUTOUT = "smart_cutout"
    """智能抠图"""

    PARTIAL_REPAINT = "partial_repaint"
    """部分重绘"""

    AI_EXTEND = "ai_extend"
    """AI 括图"""

    STYLE_TRANSFORM = "style_transform"
    """风格转换"""


class HistoryRecord(BaseModel):
    id: Annotated[int, Field(description="记录 ID")]
    origin_url: Annotated[str, Field(description="原始图片 URL")]
    operation: Annotated[Operation, Field(description="对应的操作")]
    input_url: Annotated[str, Field(description="待处理的图片 URL")]
    params: Annotated[dict[str, Any], Field(description="输入参数")]
    result_url: Annotated[str, Field(description="生成的图片 URL")]
    create_time: Annotated[datetime, Field(description="创建时间")]


class History(BaseModel):
    id: Annotated[int, Field(description="历史记录标识")]
    records: Annotated[list[HistoryRecord], Field(description="对应的每条历史记录")]


class PartialRemoveParams(TypedDict):
    """局部消除参数"""
    mask_url: str


class LosslessEnlargementParams(TypedDict):
    """无损放大参数"""
    times: Literal[2, 3, 4]


class SmartCutoutParams(TypedDict):
    """智能抠图参数"""
    background_style: Literal["transparent", "white", "black"]


class PartialRepaintParams(TypedDict):
    """局部重绘参数"""
    mask_url: str
    value: str


class AiExtendParams(TypedDict):
    """AI 扩图参数"""
    type: Literal["direction", "scale"]
    text: Literal["top", "bottom", "left", "right", "1:1", "2:3", "3:4", "9:16", "3:2", "4:3", "16:9"]


class StyleTransformParams(TypedDict):
    """风格转换参数"""
    style_id: str


Params: TypeAlias = (
        PartialRemoveParams
        | LosslessEnlargementParams
        | SmartCutoutParams
        | PartialRepaintParams
        | AiExtendParams
        | StyleTransformParams
        | dict
)


class OperationReq(BaseModel):
    pid: Annotated[int | None, Field(default=None, description="父记录 ID，如果为空，则会创建新的历史记录集")]
    operation: Annotated[Operation, Field(description="对应的操作")]
    params: Annotated[Params, Field(default_factory=dict, description="额外参数，不同的操作需要不同的额外参数")]


class TransformStyle(BaseModel):
    id: Annotated[str, Field(description="风格 ID")]
    name: Annotated[str, Field(description="风格名称")]
    preview_url: Annotated[str, Field(description="预览图")]
