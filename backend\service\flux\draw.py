import logging
import aiohttp
from config import app_settings

logger = logging.getLogger(__name__)


async def create_image(
    prompt: str,
    model_name: str = "flux-kontext-pro",
    input_image: str = None,
    seed: int = None,
    aspect_ratio: str = None,
    output_format: str = "jpeg",
    webhook_url: str = None,
    webhook_secret: str = None,
    prompt_upsampling: bool = False,
    safety_tolerance: int = 2
):
    """
    使用Flux API创建或编辑图像

    Args:
        prompt: 提示文本（必填）
        model_name: 模型名称，默认为flux-kontext-max
        input_image: 输入图像的base64编码字符串（可选）
        seed: 随机种子（可选）
        aspect_ratio: 图像宽高比（可选）
        output_format: 输出格式，默认为jpeg
        webhook_url: webhook回调地址（可选）
        webhook_secret: webhook密钥（可选）
        prompt_upsampling: 是否进行提示词增强，默认为False
        safety_tolerance: 安全级别，默认为2

    Returns:
        dict: 包含任务ID和轮询URL的响应

    Raises:
        Exception: 当API请求失败时
    """
    # API地址
    url = f"https://api.bfl.ai/v1/{model_name}"

    # 准备请求头
    headers = {
        "x-key": app_settings.flux_api_key,
        "Content-Type": "application/json"
    }

    # 准备请求负载
    payload = {
        "prompt": prompt,
        "prompt_upsampling": prompt_upsampling,
        "safety_tolerance": safety_tolerance
    }

    # 添加可选参数
    if input_image:
        payload["input_image"] = input_image
    if seed is not None:
        payload["seed"] = seed
    if aspect_ratio:
        payload["aspect_ratio"] = aspect_ratio
    if output_format:
        payload["output_format"] = output_format
    if webhook_url:
        payload["webhook_url"] = webhook_url
    if webhook_secret:
        payload["webhook_secret"] = webhook_secret

    # 发起API请求
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                response.raise_for_status()  # 如果响应状态码不是2xx，则抛出异常

                # 返回响应数据
                return await response.json()
    except Exception as e:
        error_message = f"图像创建失败"
        logger.error(f"Flux图像创建失败: {str(e)}")
        raise Exception(error_message)


async def get_result(result_id: str):
    """
    获取Flux API的任务结果

    Args:
        result_id: 任务ID

    Returns:
        dict: 任务结果

    Raises:
        Exception: 当API请求失败时
    """
    # API地址
    url = f"https://api.bfl.ai/v1/get_result"

    # 准备请求头
    headers = {
        "x-key": app_settings.flux_api_key,
        "Content-Type": "application/json"
    }

    # 准备请求参数
    params = {"id": result_id}

    # 发起API请求
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, headers=headers) as response:
                response.raise_for_status()  # 如果响应状态码不是2xx，则抛出异常

                # 返回响应数据
                return await response.json()
    except Exception as e:
        error_message = f"获取Flux任务结果失败: {str(e)}"
        logger.error(error_message)
        raise Exception(error_message)
