import { onMounted, onUnmounted, ref } from 'vue';

/**
 * 检测屏幕是否为大屏幕的复用函数
 *
 * @param breakpoint 屏幕宽度断点，默认为1500px
 * @returns 返回响应式布尔值，true表示大屏幕，false表示小屏幕
 */
export function useScreenDetection(breakpoint: number = 1500) {
  // 响应式屏幕宽度
  const screenWidth = ref(window.innerWidth);

  // 计算是否为大屏幕
  const isLargeScreen = ref(screenWidth.value > breakpoint);

  // 监听窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth;
    isLargeScreen.value = screenWidth.value > breakpoint;
  };

  // 生命周期钩子：添加事件监听器
  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  // 生命周期钩子：移除事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  return {
    screenWidth,
    isLargeScreen
  };
}
