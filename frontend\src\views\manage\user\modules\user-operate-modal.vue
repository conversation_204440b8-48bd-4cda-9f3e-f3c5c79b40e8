<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchAllUserCompany, fetchGetAllRoles, postSaveUser } from '@/service/api';
import { $t } from '@/locales';
import { enableStatusOptions, userGenderOptions } from '@/constants/business';
import { encrypt } from '@/utils/crypto';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.manage.user.addUser'),
    edit: $t('page.manage.user.editUser')
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.SystemManage.User,
  | 'id'
  | 'username'
  | 'gender'
  | 'nickname'
  | 'email'
  | 'status'
  | 'password'
  | 'role_id'
  | 'role'
  | 'company'
  | 'credit'
>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    username: '',
    gender: '0',
    nickname: '',
    email: '',
    password: '',
    role_id: 0,
    status: '1',
    company: '',
    credit: 1,
    role: []
  };
}

type RuleKey = Extract<keyof Model, 'username' | 'nickname' | 'status' | 'email' | 'password'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  username: defaultRequiredRule,
  nickname: defaultRequiredRule,
  status: defaultRequiredRule,
  email: {
    required: true,
    trigger: 'blur',
    validator(__rule, value) {
      // 验证是否为空
      if (!value) {
        return new Error('请填写邮箱');
      }

      // 邮箱格式验证
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(value)) {
        return new Error('请输入正确的邮箱地址');
      }

      return true;
    }
  },
  password: {
    validator: (_rule, value) => {
      if (!value) return true; // 允许为空，因为编辑时可能不修改密码

      const { patternRules } = useFormRules();
      const pwdRule = patternRules.pwd;

      if (value && !pwdRule.pattern.test(value)) {
        return new Error(pwdRule.message);
      }
      return true;
    },
    trigger: ['input', 'blur']
  }
};

/** the enabled role options */
const roleOptions = ref<CommonType.Option<number>[]>([]);
const companyOption = ref<CommonType.Option[]>([]);

async function getRoleOptions() {
  if (roleOptions.value.length > 0) return;
  const { error, data } = await fetchGetAllRoles();
  if (!error) {
    const options = data!.records.map((item: any) => ({
      value: item.id,
      label: item.roleName
    }));
    roleOptions.value = [...options];
  }
}

async function getAllCompany() {
  if (companyOption.value.length > 0) return;
  const { error, data } = await fetchAllUserCompany();
  if (!error) {
    const options = data!.records.map((item: any) => ({
      value: item,
      label: item
    }));
    companyOption.value = [...options];
  }
}

async function handleInitModel() {
  Object.assign(model, createDefaultModel(), { gender: 1 });

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData, { status: `${props.rowData.status}` });
  }
  // console.log(model.credit);
}

function closeDrawer() {
  visible.value = false;
}

const loading = ref(false);

async function handleSubmit() {
  loading.value = true; // 开始加载

  try {
    await validate(); // 验证表单

    // 创建一个新对象来存储要发送的数据
    const submitData = { ...model };

    // 如果有设置密码，则加密密码
    if (submitData.password) {
      submitData.password = encrypt(submitData.password);
    }

    // 处理角色数据 - 将role数组转换为role_id
    if (Array.isArray(submitData.role) && submitData.role.length > 0) {
      submitData.role_id = Number(submitData.role[0]);
    }

    // 发送请求
    await postSaveUser(submitData);
    window.$message?.success($t('common.updateSuccess'));

    // 如果请求成功，结束加载并关闭模态框
    loading.value = false;
    closeDrawer();
    emit('submitted');

    return true; // 返回true表示操作成功
  } catch (error) {
    // 请求失败，结束加载但不关闭模态框
    loading.value = false;
    window.$message?.error('添加失败，请重试');
    console.error('提交失败:', error);

    return false; // 返回false表示操作失败，防止模态框关闭
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    getRoleOptions();
    getAllCompany();
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    display-directive="show"
    :width="360"
    :title="title"
    preset="dialog"
    :native-scrollbar="false"
    closable
    :positive-text="$t('common.confirm')"
    :negative-text="$t('common.cancel')"
    class="min-w-1000px"
    :loading="loading"
    :on-positive-click="handleSubmit"
    @negative-click="closeDrawer"
  >
    <NScrollbar class="max-h-500px p-10" :size="5" :x-scrollable="false">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-align="right" label-width="80">
        <NFormItem :label="$t('page.manage.user.userName')" path="username">
          <NInput v-model:value="model.username" :placeholder="$t('page.manage.user.form.userName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userGender')" path="gender">
          <NRadioGroup v-model:value="model.gender">
            <NRadio
              v-for="item in userGenderOptions"
              :key="item.value"
              :value="parseInt(item.value)"
              :label="$t(item.label)"
            />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.nickName')" path="nickname">
          <NInput v-model:value="model.nickname" :placeholder="$t('page.manage.user.form.nickName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.company')" path="company">
          <NSelect
            v-if="companyOption.length > 1"
            v-model:value="model.company"
            :options="companyOption"
            class="w-30"
            placeholder="筛选公司"
          />
          <NInput v-else v-model:value="model.company" :placeholder="$t('page.manage.user.company')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userEmail')" path="email">
          <NInput v-model:value="model.email" :placeholder="$t('page.manage.user.form.userEmail')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userStatus')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio v-for="item in enableStatusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userRole')" path="role">
          <NSelect
            v-model:value="model.role"
            multiple
            :options="roleOptions"
            :placeholder="$t('page.manage.user.form.userRole')"
          />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.password')" path="password">
          <NInput v-model:value="model.password" :placeholder="$t('page.login.common.passwordPlaceholder')" />
        </NFormItem>
      </NForm>
    </NScrollbar>
  </NModal>
</template>

<style scoped></style>
