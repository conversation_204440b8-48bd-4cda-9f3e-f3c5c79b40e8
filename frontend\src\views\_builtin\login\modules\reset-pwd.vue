<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { debounce } from 'lodash';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { encrypt } from '@/utils/crypto';
import { fetchResetPassword } from '@/service/api';
import tencentCaptcha from '@/utils/tencentCaptcha';

defineOptions({
  name: 'ResetPwd'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

// 添加防抖处理
const debouncedSubmit = debounce(handleSubmit, 300);

interface FormModel {
  email: string;
  code: string;
  password: string;
  confirmPassword: string;
}

const model: FormModel = reactive({
  email: '',
  code: '',
  password: '',
  confirmPassword: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    email: formRules.email,
    code: formRules.code,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password)
  };
});

async function handleSubmit() {
  await validate();

  try {
    const encryptedPassword = encrypt(model.password);
    const encryptedConfirmPassword = encrypt(model.confirmPassword);

    const { data } = await fetchResetPassword({
      user_email: model.email,
      user_pwd: encryptedPassword,
      repeat_pwd: encryptedConfirmPassword,
      email_code: model.code
    });

    if (data) {
      window.$message?.success('密码重置成功，请重新登录');
      toggleLoginModule('pwd-login');
    }
  } catch (error: any) {
    window.$message?.error(error.message || '重置密码失败');
  }
}
const isLoading = ref(false);

async function handleGetCaptcha() {
  if (!model.email) {
    window.$message?.error($t('form.email.required'));
    return;
  }

  try {
    const captchaRes = await tencentCaptcha.show(); // 调用腾讯验证码
    if (captchaRes.ret === 0) {
      await getCaptcha(model.email, captchaRes.randstr, captchaRes.ticket, 'reset');
    } else {
      console.log('验证码未通过');
    }
  } catch (error: any) {
    window.$message?.error(error.message || '发送验证码失败');
  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="debouncedSubmit">
    <NFormItem path="email">
      <NInput v-model:value="model.email" :placeholder="$t('page.login.common.emailPlaceholder')" />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center">
        <NInput
          v-model:value="model.code"
          class="mr-5 !w-14em"
          :placeholder="$t('page.login.common.codePlaceholder')"
        />
        <NButton size="large" class="!w-11em" :disabled="isCounting" :loading="loading" @click="handleGetCaptcha">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block class="text-white" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
