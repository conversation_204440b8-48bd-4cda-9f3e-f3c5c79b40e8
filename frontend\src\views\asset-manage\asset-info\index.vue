<script setup lang="ts">
import { computed, defineProps, onMounted, onUnmounted, ref, toRaw, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import useClipboard from 'vue-clipboard3';
import type { ShareInfo } from '@/service/api/share';
import { ShareType, fetchShare } from '@/service/api/share';
import { ParamsFillBack, encodeParams } from '@/utils/paramsFillBack';
import { $t } from '@/locales';
import { downloadFile } from '@/utils/common';
import { getAssetInfoList, toggleCollect } from '@/service/api/assets';
import { deleteTask } from '@/service/api/midjourney'; // 导入midjourney删除任务API
import { deleteVideoTask } from '@/service/api/video'; // 导入视频删除任务API
import { useThemeStore } from '@/store/modules/theme';

// 定义组件接收的props
const props = defineProps({
  // 模态框模式下的查询参数
  modalQueryParams: {
    type: Object,
    default: () => null
  }
});

// 资产接口定义
interface Asset {
  taskid: string;
  type: string;
  url: string | string[];
  create_time: string;
  iscollect: number;
  parameter: {
    prompt?: string;
    prompt_img?: string | string[];
    [key: string]: any;
  };
}

// 规范化URL函数，去除查询参数等
function normalizeUrl(url: string): string {
  try {
    // 提取URL的主要部分，去除查询参数
    const baseUrl = url.split('?')[0];
    return baseUrl;
  } catch (error) {
    console.warn('URL规范化失败:', url, error);
    return url;
  }
}

const route = useRoute();
const themeStore = useThemeStore();

const message = useMessage();
const { toClipboard } = useClipboard();

// 添加变量来跟踪是否从user-report页面跳转而来
const isFromUserReport = ref(false);

// 分享信息，null 表示分享信息不存在
const shareInfo = ref<ShareInfo | null>(null);
// 确定也是是否在加载中
const loading = ref(false);

// 用于处理临时分享数据
const tempShareData = ref<any>(null);

// 资产列表相关
const assetList = ref<Asset[]>([]);
const assetLoading = ref(false);
const currentPage = ref(1);
// 动态设置pageSize，初始值根据当前窗口宽度设置
const pageSize = ref(11);
const isLargeScreen = ref(window.innerWidth > 1500);
const hasMore = ref(true);
const currentAssetType = ref('IMAGE');
const currentTaskId = ref<string | null>(null);
const currentAssetUrl = ref<string | null>(null);

// 选择资产
const selectAsset = (asset: Asset, url: string) => {
  // 防止重复选择当前资产
  if (url === currentAssetUrl.value) return;

  console.log('选择资产:', asset);

  currentTaskId.value = asset.taskid;
  currentAssetUrl.value = url;

  // 创建临时分享数据结构
  const params: Record<string, any> = {
    prompt: asset.parameter?.prompt || ''
  };

  // 处理参考图片
  if (asset.parameter?.prompt_img) {
    const promptImg = asset.parameter.prompt_img;
    params.prompt_img = Array.isArray(promptImg) ? promptImg : [promptImg];
  }

  // 构建结果数据
  const result = { url: asset.url };

  // 清除原有分享信息
  shareInfo.value = null;

  // 保留原有的userid信息
  const originalUserId = tempShareData.value?.userid;

  // 设置临时分享数据
  tempShareData.value = {
    share_key: `temp-${Date.now()}`,
    share_type: asset.type,
    params,
    result,
    // 保留原有的userid信息，确保后续请求带上userid
    userid: originalUserId
  };

  console.log('更新显示内容:', tempShareData.value);
};

// 添加处理记录的辅助函数
const processRecords = (records: Asset[], append: boolean = false): Asset[] => {
  // 过滤掉AUDIO类型的资产
  const processedRecords = records.filter(asset => asset.type !== 'AUDIO');
  console.log(`原始资产: ${records.length}条，过滤AUDIO后: ${processedRecords.length}条`);

  if (append) {
    // 创建现有URL规范化集合用于去重
    const existingNormalizedUrls = new Set(
      assetList.value.map(asset => {
        if (Array.isArray(asset.url)) {
          return asset.url.map(url => normalizeUrl(url));
        }
        return normalizeUrl(asset.url);
      })
    );
    // 过滤掉已存在URL的资产
    const uniqueNewRecords = processedRecords.filter(
      asset =>
        !existingNormalizedUrls.has(
          Array.isArray(asset.url) ? asset.url.map(url => normalizeUrl(url)) : normalizeUrl(asset.url)
        )
    );
    console.debug(`追加数据：${processedRecords.length}条，去重后：${uniqueNewRecords.length}条`);
    return uniqueNewRecords;
  }
  // 初始加载时进行URL去重
  const uniqueUrls = new Set<string | string[]>();
  const uniqueRecords = processedRecords.filter(asset => {
    const normalizedUrl = Array.isArray(asset.url) ? asset.url.map(url => normalizeUrl(url)) : normalizeUrl(asset.url);
    if (uniqueUrls.has(normalizedUrl)) return false;
    uniqueUrls.add(normalizedUrl);
    return true;
  });
  console.debug(`初始加载：${processedRecords.length}条，去重后：${uniqueRecords.length}条`);
  return uniqueRecords;
};

// 处理默认资产选择
const handleDefaultSelection = (uniqueRecords: Asset[]) => {
  const needsDefaultSelection =
    !currentTaskId.value || !uniqueRecords.some((asset: Asset) => asset.taskid === currentTaskId.value);

  if (needsDefaultSelection && uniqueRecords[0]) {
    console.log('设置默认选中资产:', uniqueRecords[0]);
    selectAsset(uniqueRecords[0], Array.isArray(uniqueRecords[0].url) ? uniqueRecords[0].url[0] : uniqueRecords[0].url);
  }
};

// 加载资产列表
const loadAssetList = async (
  page: number,
  append: boolean = false,
  resetPage: boolean = true,
  useTaskId: boolean = true
) => {
  if (assetLoading.value || (page > currentPage.value && !hasMore.value)) return;

  assetLoading.value = true;
  try {
    // 准备请求参数
    const requestParams: any = {
      page,
      size: pageSize.value,
      type: currentAssetType.value,
      resetPage
    };

    // 有条件添加taskid
    if (useTaskId && currentTaskId.value) {
      requestParams.taskid = currentTaskId.value;
    }

    // 无条件添加userid(如果有)，确保所有请求(包括翻页)都带上userid参数
    if (isFromUserReport.value && tempShareData.value?.userid) {
      requestParams.userid = Number.parseInt(tempShareData.value.userid, 10);
    }

    const response = await getAssetInfoList(requestParams);

    console.log('资产列表返回数据:', response.data);

    // 根据提供的返回数据结构正确解析
    const data = response.data || {};
    let records = (data.records || []) as Asset[];
    records = records.map(r => {
      try {
        return { ...r, url: JSON.parse(r.url as string) };
      } catch (e) {
        return r;
      }
    });

    if (records.length > 0) {
      const uniqueRecords = processRecords(records, append);

      if (append) {
        // 只添加唯一的新记录
        assetList.value = [...assetList.value, ...uniqueRecords];
      } else {
        assetList.value = uniqueRecords;
        // 处理默认选择
        handleDefaultSelection(uniqueRecords);
      }

      // 更新是否有更多数据的标志
      hasMore.value = records.length === pageSize.value && data.total > page * pageSize.value;
      // 使用API返回的current更新当前页码，而不是使用传入的page参数
      currentPage.value = data.current || page;
    } else {
      hasMore.value = false;

      // 如果返回空数据且当前不是第一页，回到第一页
      if (page > 1) {
        loadAssetList(1, false, true, useTaskId);
      }
    }
  } catch (error) {
    console.error('加载资产列表失败:', error);
    hasMore.value = false;
  } finally {
    assetLoading.value = false;
  }
};

// 监听窗口大小变化，动态调整pageSize
const handleResize = () => {
  isLargeScreen.value = window.innerWidth > 1500;

  // 不再根据屏幕大小变化重新加载数据，只更新布局标志
};

// 翻页浏览资产
const prevPage = () => {
  if (currentPage.value <= 1 || assetLoading.value) {
    return;
  }
  // 翻页时不使用taskid，但userid会自动添加
  loadAssetList(currentPage.value - 1, false, false, false);
};

const nextPage = () => {
  if (!hasMore.value || assetLoading.value) {
    return;
  }
  // 翻页时不使用taskid，但userid会自动添加
  loadAssetList(currentPage.value + 1, false, false, false);
};

// 处理临时分享数据的辅助函数
const processTempShareData = (tempData: string, fromSource?: string): boolean => {
  try {
    // 解析临时分享数据
    const decodedData = JSON.parse(decodeURIComponent(tempData));

    // 检查数据中是否包含来源信息
    if (decodedData.from === 'user-report' || fromSource === 'user-report') {
      isFromUserReport.value = true;
    }

    // 智能检测媒体类型
    let shareType = ShareType.IMAGE;
    // 特殊处理ALL类型标志
    let isAllType = false;

    // 处理媒体类型
    if (decodedData.type) {
      shareType = decodedData.type === 'VIDEO' ? ShareType.VIDEO : ShareType.IMAGE;
      isAllType = decodedData.type === 'ALL';
    } else if (decodedData.result?.url) {
      // 如果没有类型信息，通过URL检测
      const url = decodedData.result.url.toLowerCase();
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mkv'];
      const isVideo = videoExtensions.some(ext => url.includes(ext));
      shareType = isVideo ? ShareType.VIDEO : ShareType.IMAGE;
    }

    tempShareData.value = {
      share_key: `temp-${Date.now()}`,
      share_type: shareType,
      params: {
        prompt: decodedData.prompt || '',
        prompt_img: decodedData.prompt_img || []
      },
      result: decodedData.result || { url: '' },
      // 添加用户ID，如果存在
      userid: decodedData.userid
    };

    // 设置当前资产类型，特殊处理ALL类型
    currentAssetType.value = isAllType ? 'ALL' : shareType;

    // 如果有taskid, 设置当前taskid
    if (decodedData.taskid) {
      currentTaskId.value = decodedData.taskid;
      console.log('设置当前taskId:', decodedData.taskid);
    }

    return true;
  } catch (e) {
    console.error('解析临时分享数据失败:', e);
    return false;
  }
};

// 加载初始资产数据
const loadInitialData = async () => {
  // 加载分享信息
  loading.value = true;

  // 重置状态
  tempShareData.value = null;
  shareInfo.value = null;
  currentTaskId.value = null;
  currentAssetUrl.value = null;
  assetList.value = [];
  currentPage.value = 1;
  hasMore.value = true;

  // 处理数据流程:
  // 1. 优先检查模态框参数
  // 2. 如果没有模态框参数或处理失败，检查路由参数
  // 3. 如果都没有，则尝试普通分享方式

  // 1. 优先处理模态框参数
  if (props.modalQueryParams) {
    const { temp, data, from } = props.modalQueryParams;

    if (temp === 'true' && data) {
      const success = processTempShareData(data, from);
      if (success) {
        loading.value = false;
        loadAssetList(1);
        return;
      }
    }
  }

  // 2. 处理路由参数
  const isTemp = route.query.temp === 'true';
  const tempData = route.query.data as string | undefined;
  const from = route.query.from as string | undefined;

  // 设置来源标记
  isFromUserReport.value = from === 'user-report';

  // 处理临时分享链接
  if (isTemp && tempData) {
    const success = processTempShareData(tempData, from);
    if (success) {
      loading.value = false;
      loadAssetList(1);
      return;
    }
  }

  // 3. 处理正常分享信息
  let share_key = route.query.key;
  if (!share_key) {
    loading.value = false;
    return;
  }

  if (Array.isArray(share_key)) {
    share_key = share_key[0];
  }

  try {
    shareInfo.value = await fetchShare(share_key as string);

    // 设置当前资产类型
    if (shareInfo.value) {
      currentAssetType.value = shareInfo.value.share_type;
    }

    // 请求资产列表
    loadAssetList(1);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 初始化时设置pageSize
  pageSize.value = 11;

  // 添加窗口调整大小事件监听
  window.addEventListener('resize', handleResize);

  loadInitialData();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 监听路由变化，当route.query.data变化时重新加载数据
watch(
  () => route.query.data,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      console.log('路由参数变化，重新加载数据');
      loadInitialData();
    }
  }
);

// 监听props变化，当模态框传入的参数变化时重新加载数据
watch(
  () => props.modalQueryParams,
  (newVal, oldVal) => {
    if (newVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      console.log('模态框参数变化，重新加载数据');
      loadInitialData();
    }
  },
  { deep: true }
);

// 结果URL计算逻辑
const resultUrl = computed<string | null>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value) {
    return tempShareData.value.result.url || null;
  }

  // 使用正常分享数据
  if (shareInfo.value === null) {
    return null;
  }
  const url = shareInfo.value.result.url;
  if (!url) {
    return null;
  }
  return url.toString() || null;
});

// 添加计算属性：明确判断当前是否为视频资产
const isVideoAsset = computed<boolean>(() => {
  if (tempShareData.value) {
    // 优先使用明确设置的类型
    if (tempShareData.value.share_type === ShareType.VIDEO) {
      return true;
    }

    // 如果类型不明确，则通过URL检测
    if (resultUrl.value) {
      const url = Array.isArray(resultUrl.value) ? resultUrl.value[0] : resultUrl.value;
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mkv'];
      return videoExtensions.some(ext => url.includes(ext));
    }
  }

  // 使用正常分享数据
  return shareInfo.value?.share_type === ShareType.VIDEO || false;
});

const prompt = computed<string>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value && tempShareData.value.params.prompt) {
    return tempShareData.value.params.prompt;
  }

  // 使用正常分享数据
  if (shareInfo.value === null) {
    return '';
  }
  const p = shareInfo.value.params.prompt;
  if (!p) {
    return '';
  }
  return p.toString();
});

const parsePromptImg = (img: any): string[] => {
  if (!img) return [];
  if (Array.isArray(img)) {
    const result: string[] = [];
    img.forEach((item: any) => {
      if (typeof item === 'string') {
        let parsed;
        try {
          parsed = JSON.parse(item);
        } catch (e) {
          parsed = null;
        }
        if (parsed) {
          if (Array.isArray(parsed)) {
            result.push(...parsed);
          } else {
            result.push(parsed.toString());
          }
        } else {
          result.push(item);
        }
      } else {
        result.push(item.toString());
      }
    });
    return result;
  }
  if (typeof img === 'string') {
    let parsed;
    try {
      parsed = JSON.parse(img);
    } catch (e) {
      parsed = null;
    }
    if (parsed) {
      if (Array.isArray(parsed)) return parsed;
      return [parsed.toString()];
    }
    return [img];
  }
  return [img.toString()];
};

const promptImg = computed<string[]>(() => {
  // 优先使用临时分享数据
  if (tempShareData.value) {
    if (tempShareData.value.params.prompt_img) {
      return parsePromptImg(tempShareData.value.params.prompt_img);
    }
  }
  // 使用正常分享数据
  if (shareInfo.value === null) {
    return [];
  }
  const img = shareInfo.value.params.prompt_img;
  if (!img) {
    return [];
  }
  if (!Array.isArray(img)) {
    return [img.toString()];
  }
  return img;
});

const downloading = ref(false);

// 收藏功能相关
const collectLoading = ref(false);

// 获取当前资产的收藏状态
const currentAssetCollectStatus = computed(() => {
  if (currentTaskId.value && assetList.value.length > 0) {
    const currentAsset = assetList.value.find(asset => asset.taskid === currentTaskId.value);
    return currentAsset?.iscollect || 0;
  }
  return 0;
});

// 收藏切换函数
const handleToggleCollect = async () => {
  if (!currentTaskId.value) {
    message.warning('当前资产没有任务ID，无法切换收藏状态');
    return;
  }

  if (collectLoading.value) return;

  collectLoading.value = true;
  try {
    // 调用收藏切换API
    const response = await toggleCollect(currentTaskId.value);

    // 如果请求成功
    if (response.data) {
      // 更新本地收藏状态
      const currentAsset = assetList.value.find(asset => asset.taskid === currentTaskId.value);
      if (currentAsset) {
        currentAsset.iscollect = response.data.iscollect;
      }

      // 显示成功消息
      message.success(response.data.iscollect === 1 ? '收藏成功' : '已取消收藏');
    } else {
      // 显示错误消息
      message.error('收藏失败');
      console.error('切换收藏状态失败:', response.data?.msg || '未知错误');
    }
  } catch (error) {
    console.error('切换收藏请求失败:', error);
    message.error('收藏失败');
  } finally {
    collectLoading.value = false;
  }
};

const handleDownload = async () => {
  if (!resultUrl.value) {
    return;
  }
  downloading.value = true;
  try {
    // 使用通用下载函数
    // 确定文件类型，使用isVideoAsset计算属性判断
    const fileExt = isVideoAsset.value ? 'mp4' : 'png';
    const ext = resultUrl.value.split('.').pop()?.split('?')[0] || fileExt;
    const customFileName = `result.${ext}`;

    await downloadFile(resultUrl.value, customFileName);
  } finally {
    downloading.value = false;
  }
};

const handleCopy = async (field: 'prompt' | 'shareUrl') => {
  let text = '';
  switch (field) {
    case 'prompt':
      text = prompt.value;
      break;
    case 'shareUrl':
      {
        // 构建指向share-page的链接，而不是当前页面
        const baseUrl = `${window.location.origin}/share-page`;
        if (tempShareData.value) {
          // 临时分享，构建temp=true&data=...格式
          const shareData: Record<string, any> = {
            type: tempShareData.value.share_type,
            prompt: tempShareData.value.params.prompt,
            prompt_img: tempShareData.value.params.prompt_img,
            result: tempShareData.value.result
          };

          // 如果是从user-report页面来，添加来源标识
          if (isFromUserReport.value) {
            shareData.from = 'user-report';
          }

          const encodedData = encodeURIComponent(JSON.stringify(shareData));
          text = `${baseUrl}?temp=true&data=${encodedData}`;

          // 如果是从user-report页面来，添加from参数
          if (isFromUserReport.value) {
            text += '&from=user-report';
          }
        } else if (shareInfo.value) {
          // 正常分享，使用key参数
          text = `${baseUrl}?key=${shareInfo.value.share_key}`;

          // 如果是从user-report页面来，添加from参数
          if (isFromUserReport.value) {
            text += '&from=user-report';
          }
        } else {
          text = location.href || '';
        }
      }
      break;
    default:
      break;
  }
  try {
    await toClipboard(text);
    message.success($t('page.share-page.copySuccess'));
  } catch (e) {
    console.error(e);
    message.error($t('page.share-page.copyError'));
  }
};

const router = useRouter();

const handleMakeSameImage = () => {
  // 首先检查是否有临时分享数据
  if (tempShareData.value) {
    router.push({
      path: '/portrait/midjourney',
      query: {
        [ParamsFillBack]: encodeParams(tempShareData.value.params)
      }
    });
    return;
  }

  // 使用正常分享数据
  if (shareInfo.value?.share_type === ShareType.IMAGE) {
    router.push({
      path: '/portrait/midjourney',
      query: {
        [ParamsFillBack]: encodeParams(toRaw(shareInfo.value.params))
      }
    });
  }
};

const handleCreateVideo = () => {
  // 检查临时分享数据
  if (tempShareData.value) {
    const params = { ...tempShareData.value.params };
    params.prompt_img = resultUrl.value as string;
    router.push({
      path: '/video/framepack',
      query: {
        [ParamsFillBack]: encodeParams(params)
      }
    });
    return;
  }
  // 使用正常分享数据
  if (!shareInfo.value) {
    return;
  }
  const params = toRaw(shareInfo.value.params);
  params.prompt_img = resultUrl.value as string;
  router.push({
    path: '/video/framepack',
    query: {
      [ParamsFillBack]: encodeParams(params)
    }
  });
};

// 删除资产相关
const deleteLoading = ref(false);

// 处理删除资产
const handleDeleteAsset = async () => {
  if (!currentTaskId.value) {
    message.warning('当前资产没有任务ID，无法删除');
    return;
  }

  if (deleteLoading.value) return;

  deleteLoading.value = true;
  try {
    let response: any;
    // 根据资产类型调用不同的删除API
    if (isVideoAsset.value) {
      // 视频资产使用framepack的删除API
      response = await deleteVideoTask(currentTaskId.value);
    } else {
      // 图片资产使用midjourney的删除API
      response = await deleteTask(Number(currentTaskId.value));
    }

    // 如果请求成功
    if (response.data) {
      message.success('删除成功');

      // 从资产列表中移除已删除的资产
      assetList.value = assetList.value.filter(asset => asset.taskid !== currentTaskId.value);

      // 如果删除后列表为空，刷新资产列表
      if (assetList.value.length === 0) {
        loadAssetList(1);
      } else if (currentTaskId.value === assetList.value[0].taskid) {
        // 如果删除的是当前选中的资产，选择下一个资产
        selectAsset(
          assetList.value[0],
          Array.isArray(assetList.value[0].url) ? assetList.value[0].url[0] : assetList.value[0].url
        );
      }
    } else {
      message.error('删除失败');
      console.error('删除资产失败:', response.msg || '未知错误');
    }
  } catch (error) {
    console.error('删除资产请求失败:', error);
    message.error('删除失败');
  } finally {
    deleteLoading.value = false;
  }
};
</script>

<template>
  <div class="h-screen max-w-screen flex flex-col p-8">
    <!--
 <header>
      <img :src="logo" alt="Logo" class="h-8" />
      <div v-if="tempShareData" class="mt-2 rounded-md bg-amber-50 p-2 text-amber-800">
        <small>这是一个临时分享链接，仅在当前会话有效</small>
      </div>
    </header>
-->
    <main class="mt-8 flex flex-col grow gap-7 lg:flex-row">
      <div class="side-panel flex flex-col flex-1 items-center lg:grow">
        <div
          class="aspect-16/9 max-w-[84em] w-full flex justify-center overflow-hidden rounded-lg"
          :class="themeStore.darkMode ? 'bg-black' : 'bg-gray-100'"
        >
          <NSkeleton v-if="loading" :sharp="false" height="100%" width="100%" />
          <NImage v-else-if="resultUrl && !isVideoAsset" class="h-full" :src="resultUrl" alt="Result" />
          <video v-else-if="resultUrl && isVideoAsset" controls class="h-full w-full" :src="resultUrl" />
        </div>

        <!-- 资产列表  -->
        <div class="mt-6 w-full" :class="isLargeScreen ? 'max-w-[84em]' : 'max-w-[66em]'">
          <div class="mb-2 flex items-center justify-between">
            <NH5 class="mb-0 font-semibold">相关资产</NH5>
          </div>

          <div class="asset-container">
            <!-- 左侧箭头按钮 -->
            <div
              class="arrow-button left-arrow"
              :class="{ disabled: currentPage <= 1 || assetLoading }"
              @click="prevPage"
            >
              <SvgIcon icon="material-symbols:keyboard-arrow-left" />
            </div>

            <!-- 资产列表 -->
            <NScrollbar class="asset-list-wrapper" x-scrollable>
              <div class="asset-list">
                <div v-if="assetLoading && assetList.length === 0" class="asset-loading">
                  <NSpin size="small" />
                </div>

                <template v-else>
                  <div
                    v-for="asset in assetList"
                    :key="Array.isArray(asset.url) ? asset.url.join(',') : asset.url"
                    class="asset-item"
                    :class="{ active: asset.url === currentAssetUrl }"
                  >
                    <!-- 图片资产 -->
                    <div v-if="asset.type === 'IMAGE'">
                      <div v-if="Array.isArray(asset.url)">
                        <img
                          v-for="u of asset.url"
                          :key="u"
                          :src="`${u}?imageView2/1/w/120/h/120`"
                          alt="Asset Thumbnail"
                          class="asset-thumbnail"
                          @click="selectAsset(asset, u)"
                        />
                      </div>
                      <div v-else>
                        <img
                          :src="`${asset.url}?imageView2/1/w/120/h/120`"
                          alt="Asset Thumbnail"
                          class="asset-thumbnail"
                          @click="selectAsset(asset, asset.url)"
                        />
                      </div>
                    </div>

                    <!-- 视频资产 -->
                    <div v-else-if="asset.type === 'VIDEO'">
                      <div v-if="Array.isArray(asset.url)" class="video-thumbnail-container">
                        <div v-for="u of asset.url" :key="u">
                          <video :src="u" preload="metadata" muted class="asset-thumbnail" />
                          <!-- 添加播放图标 -->
                          <div class="video-play-icon">
                            <SvgIcon icon="solar:play-bold" />
                          </div>
                        </div>
                      </div>
                      <div v-else>
                        <video :src="asset.url" preload="metadata" muted class="asset-thumbnail" />
                        <!-- 添加播放图标 -->
                        <div class="video-play-icon">
                          <SvgIcon icon="solar:play-bold" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 加载中状态 -->
                  <div v-if="assetLoading" class="loading-indicator">
                    <NSpin size="small" />
                  </div>
                </template>
              </div>
            </NScrollbar>

            <!-- 右侧箭头按钮 -->
            <div class="arrow-button right-arrow" :class="{ disabled: !hasMore || assetLoading }" @click="nextPage">
              <SvgIcon icon="material-symbols:keyboard-arrow-right" />
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-col grow lg:w-110 lg:grow-0">
        <div class="flex items-center justify-between">
          <NH5 class="mb-0 font-semibold">{{ $t('page.share-page.prompt') }}</NH5>
          <NButton size="tiny" @click="handleCopy('prompt')">
            <template #icon>
              <SvgIcon icon="solar:copy-bold" />
            </template>
            {{ $t('page.share-page.copy') }}
          </NButton>
        </div>
        <div class="mb-10 mt-4 grow">
          <NScrollbar class="h-full max-h-55">
            <NSkeleton v-if="loading" :text="true" :sharp="false" :repeat="8" />
            <NSkeleton v-if="loading" :text="true" :sharp="false" class="w-[60%]" />
            <NText v-else class="leading-6">
              {{ prompt }}
            </NText>
          </NScrollbar>
          <div class="mt-4">
            <NSpace v-if="loading">
              <NSkeleton :sharp="false" class="h-16 w-10" />
            </NSpace>
            <NImageGroup v-else>
              <NSpace>
                <NImage v-for="img of promptImg" :key="img" :src="img" class="h-16 rounded" alt="Prompt Image" />
              </NSpace>
            </NImageGroup>
          </div>
        </div>
        <div :class="isLargeScreen ? '' : 'mb-[25px]'">
          <!-- <div> -->
          <!-- <NButton type="primary" class="w-full" @click="handleMakeSame"> -->
          <!-- <template #icon> -->
          <!-- <SvgIcon v-if="loading" icon="gg:spinner" class="animate-spin" /> -->
          <!-- <SvgIcon v-else icon="mingcute:ai-fill" /> -->
          <!-- </template> -->
          <!-- {{ $t('page.share-page.createSame') }} -->
          <!-- </NButton> -->
          <!-- </div> -->
          <div class="mt-2 flex gap-2">
            <!--
 <NButton
              v-if="(shareInfo && shareInfo.share_type === ShareType.IMAGE) || tempShareData"
              class="grow"
              type="primary"
              @click="handleCreateVideo"
            >
              <template #icon>
                <SvgIcon v-if="loading" icon="gg:spinner" class="animate-spin" />
                <SvgIcon v-else icon="ri:film-ai-line" />
              </template>
              生成视频
              {{ $t('page.share-page.makeVideo') }}
            </NButton>
-->

            <NPopover trigger="hover" :show-arrow="false" placement="top" class="w-69">
              <template #trigger>
                <NButton type="primary" class="flex-1">
                  <template #icon>
                    <SvgIcon v-if="loading" icon="gg:spinner" class="animate-spin" />
                    <SvgIcon v-else icon="mingcute:ai-fill" />
                  </template>
                  {{ $t('page.share-page.createSame') }}
                </NButton>
              </template>
              <div class="popover-buttons">
                <!-- 如果是图片类型，显示两个选项 -->
                <template v-if="!isVideoAsset">
                  <NButton block @click="handleMakeSameImage">
                    <template #icon>
                      <SvgIcon icon="humbleicons:image" />
                    </template>
                    生成图片
                  </NButton>
                  <NButton block class="mt-2" @click="handleCreateVideo">
                    <template #icon>
                      <SvgIcon icon="ri:film-ai-line" />
                    </template>
                    生成视频
                  </NButton>
                </template>
                <!-- 如果是视频类型，只显示一个选项 -->
                <template v-else>
                  <NButton block @click="handleCreateVideo">
                    <template #icon>
                      <SvgIcon icon="ri:film-ai-line" />
                    </template>
                    生成视频
                  </NButton>
                </template>
              </div>
            </NPopover>

            <NButton
              v-if="!isFromUserReport"
              secondary
              :disabled="collectLoading"
              :type="currentAssetCollectStatus === 1 ? 'warning' : 'default'"
              @click="handleToggleCollect"
            >
              <template #icon>
                <SvgIcon v-if="collectLoading" icon="gg:spinner" class="animate-spin" />
                <SvgIcon
                  v-else
                  :icon="currentAssetCollectStatus === 1 ? 'fluent:star-32-filled' : 'fluent:star-32-regular'"
                  :class="{ 'text-yellow-500': currentAssetCollectStatus === 1 }"
                />
              </template>
            </NButton>

            <NButton secondary :disabled="downloading" @click="handleDownload">
              <template #icon>
                <SvgIcon v-if="downloading || loading" icon="gg:spinner" class="animate-spin" />
                <SvgIcon v-else icon="material-symbols:download-rounded" />
              </template>
            </NButton>

            <!-- 分享按钮 - 从user-report来时直接显示分享按钮 -->
            <NButton v-if="isFromUserReport" secondary @click="handleCopy('shareUrl')">
              <template #icon>
                <SvgIcon icon="material-symbols:share" />
              </template>
            </NButton>

            <!-- 更多操作按钮 - 从user-report来时不显示 -->
            <NPopover v-if="!isFromUserReport" trigger="click" :show-arrow="false" placement="top-end" class="w-auto">
              <template #trigger>
                <NButton secondary>
                  <template #icon>
                    <SvgIcon v-if="loading" icon="gg:spinner" class="animate-spin" />
                    <SvgIcon v-else icon="nrk:more" />
                  </template>
                </NButton>
              </template>
              <div class="popover-actions">
                <NButton block text @click="handleCopy('shareUrl')">
                  <template #icon>
                    <SvgIcon icon="material-symbols:share" />
                  </template>
                  分享
                </NButton>
                <NPopconfirm placement="left" class="w-55" @positive-click="handleDeleteAsset">
                  <template #trigger>
                    <NButton block text>
                      <template #icon>
                        <SvgIcon v-if="deleteLoading" icon="gg:spinner" class="animate-spin" />
                        <SvgIcon v-else icon="material-symbols:delete" />
                      </template>
                      删除
                    </NButton>
                  </template>
                  <span>删除后该内容将无法恢复</span>
                </NPopconfirm>
              </div>
            </NPopover>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
/* 资产列表样式 */
.asset-container {
  position: relative;
  width: 100%;
  height: 9em;
  display: flex;
  align-items: center;
}

.asset-list-wrapper {
  flex: 1;
  height: 100%;
  position: relative;
}

.asset-list {
  display: flex;
  padding: 8px 0;
  gap: 10px;
  height: 100%;
  min-width: max-content; /* 确保内容超出时可以滚动 */

  @media (min-width: 1500px) {
    gap: 14px;
  }
}

.arrow-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: absolute;
  z-index: 10;
  transition: all 0.2s ease;
  opacity: 0;
  pointer-events: none;

  &:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
  }

  &.disabled {
    cursor: not-allowed;
    &:hover {
      transform: none;
    }
  }

  &.left-arrow {
    left: -22px;
  }

  &.right-arrow {
    right: -18px;
  }

  :deep(svg) {
    font-size: 24px;
    color: #333;
  }
}

.asset-container:hover .arrow-button {
  opacity: 1;
  pointer-events: auto;

  &.disabled {
    opacity: 0.5;
  }
}

.asset-item {
  flex: 0 0 auto;
  width: 94px;
  height: 94px;
  overflow: hidden;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.active {
    border: 3px solid v-bind('themeStore.darkMode ? "#ffffff" : "rgb(200, 197, 197)"');
  }
}

.asset-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
}

.video-play-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;

  :deep(svg) {
    font-size: 14px;
    color: white;
  }
}

.asset-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 100px;
}

/* 弹出菜单按钮样式 */
.popover-buttons {
  width: 100%;
}

/* 弹出的操作菜单样式 */
.popover-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 100px;

  :deep(.n-button) {
    text-align: left;
    padding: 6px 12px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

.divider {
  height: 100vh !important;
}

.side-panel {
  border-right: 1.5px solid v-bind('themeStore.darkMode ? "rgba(255,255,255,0.09)" : "rgb(239,239,245)"');
}
</style>
