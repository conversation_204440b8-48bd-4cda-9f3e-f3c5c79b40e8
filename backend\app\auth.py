import logging

from fastapi import APIRouter, Body, Depends, HTTPException, Request, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.hash.md5 import md5
from utils.redis import redis
from service.user import getEncryptPassword
from models.users import create_access_token, User, clear_user_tokens, get_request_user, destory_token
from models.user_role import UserRole
from models.roles import Role
from pydantic import BaseModel
import time
from sqlalchemy.future import select
import hmac
from Crypto.Hash import RIPEMD160
from config import endpoint_config
import traceback
from urllib.parse import quote, urlencode
import os
from fastapi.responses import JSONResponse
from starlette.responses import RedirectResponse
from datetime import datetime
import httpx
from utils.seed_email import generate_verification_code, send_verification_email
from models.work_teams import WorkTeams
import uuid
from utils.hash.aes import decrypt
from models.user_credit import UserCredit, UserCreditLog
from models.app_seting import AppSetting
import json
from config import app_settings
from utils.tencent_captcha import verify_tencent_captcha

router = APIRouter()

logger = logging.getLogger(__name__)


class LoginRequest(BaseModel):
  userName: str
  password: str
  ticket: str # 腾讯验证码
  randstr: str # 腾讯验证码随机串


class UserInfo(BaseModel):
  id: int
  username: str
  email: str

  # 根据 admin 表中的字段添加更多信息


class Config:
  from_attributes = True


class LoginResponse(BaseModel):
  data: dict
  code: str
  msg: str


class UserInfoData(BaseModel):
  userId: int
  userName: str
  roles: list
  buttons: list


class UserInfoResponse(BaseModel):
  data: UserInfoData | None
  code: str
  msg: str


def get_token_key(token: str) -> str:
  # 使用 HMAC 和 RIPEMD160 哈希算法生成一个哈希值
  h = hmac.new(endpoint_config.get("token").get("key").encode("utf-8"), digestmod=RIPEMD160)
  h.update(token.encode("utf-8"))
  encode_token = h.hexdigest()
  return "tp:%s" % encode_token

@router.post(
  "/login",
  tags=["auth"],
  response_model=LoginResponse
)
async def login(
  login_data: LoginRequest,
  request: Request,
  user_agent: str = Header(None),
  db: AsyncSession = Depends(get_db),
):
  """
    ## 用户登陆并返回token
    ### token 有效时间为15天
    """
  if not user_agent:
    raise ClientVisibleException("请求失败")

  userName = login_data.userName
  password = login_data.password
  ticket = login_data.ticket # 腾讯验证码
  randstr = login_data.randstr # 腾讯验证码随机串
  logger.info("user login: %s" % userName)

  # ip = request.client.host
  ip = request.state.client_ip

  ipkey = f"loginFailure:{ip}"
  userkey = f"loginFailure:{userName}"

  ip_fail_count = await redis.get(ipkey)
  user_fail_count = await redis.get(userkey)

  try:
    if not (userName and password):
      raise ClientVisibleException("用户名和密码不能为空")

    verify_res = await verify_tencent_captcha(ip, randstr, ticket)
    if not verify_res:
       raise ClientVisibleException("验证码验证失败，请稍后再试")

    if (ip_fail_count and int(ip_fail_count) >= 20) or (user_fail_count and int(user_fail_count) >= 10):
      raise ClientVisibleException("登录失败次数过多，请稍后再试")

    # 查询用户 - 支持用户名或邮箱登录
    result = await db.execute(
      select(User).where(
        (User.username == userName) | (User.email == userName)
      )
    )
    user = result.scalars().first()

    if not user:
      raise ClientVisibleException("用户不存在")

    if user.status != 1:
      raise ClientVisibleException("用户已锁定")

    try:
      password = decrypt(password)  # 先解密前端传来的密码
      if getEncryptPassword(password, user.salt) != user.password:
        raise ClientVisibleException("用户名/密码错误")
    except Exception as e:
      raise ClientVisibleException("用户名/密码错误")

    # async with db.begin():
    # 更新用户信息
    user.loginip = ip
    user.prevtime = user.logintime
    user.loginfailure = 0
    user.logintime = int(time.time())
    #         await db.commit()

    if ip_fail_count:
      await redis.delete(ipkey)
    if user_fail_count:
      await redis.delete(userkey)

    # 生成token
    token = await create_access_token(user=user, user_agent=user_agent)

    # 更新用户的token字段
    user.token = token
    await db.commit()

    return LoginResponse(data={"token": token, "refreshToken": ""}, code="0000", msg="请求成功")
  except HTTPException as e:
    await redis.exec("incr", ipkey)
    await redis.exec("incr", userkey)
    await redis.exec("expire", ipkey, 3600)
    await redis.exec("expire", userkey, 600)
    logger.error(e.status_code, e.detail)
    raise ClientVisibleException(e.detail) from e


@router.get("/dingding_login",
            tags=["auth"],
            # response_model=LoginResponse
            )
async def dingding_login(request: Request, user_agent: str = Header(None)):
  redirect_uri = quote(
    f"{app_settings.ding_talk_redirect_uri}/proxy-default/auth/dingding_callback",
    safe=""
  )
  login_url = f"https://osa.originmood.com/open/auth/dinglogin?appid={app_settings.ding_talk_app_id}&redirect={redirect_uri}"
  return JSONResponse(content={"data": {"login_url": login_url}, "code": "0000"})


@router.get("/dingding_callback",
            tags=["auth"],
            # response_model=LoginResponse
            )
async def dingtalk_callback(
  request: Request,
  code: str = Query(...),
  user_agent: str = Header(None),
  db: AsyncSession = Depends(get_db)):
  try:
    async with httpx.AsyncClient() as client:
      response = await client.post(
        app_settings.ding_talk_osa_auth_url,
        json={
          "code": code,
          "appid": app_settings.ding_talk_app_id,
          "secret": app_settings.ding_talk_app_secret
        }
      )
    if response.status_code != 200 or response.json().get("code") != 1:
      raise ClientVisibleException("Failed to authenticate with DingTalk")

    data = response.json()["data"]
    user_info = data["user"]

    email = user_info.get("email")
    username = user_info.get("username")
    if not email:
      raise ClientVisibleException("Email not provided by DingTalk")

    result = await db.execute(select(User).filter(User.email == email))
    user = result.scalars().first()

    userip = request.state.client_ip

    if user:
      # 更新用户登录信息
      user.loginip = userip
      user.prevtime = user.logintime if user.logintime else int(time.time())
      user.logintime = int(time.time())

      token = await create_access_token(user=user, user_agent=user_agent)
      user.token = token
      await db.commit()

      response_data = {
        'token': token,
        'refreshToken': '',
      }
    else:
      # 获取邮箱域名
      email_domain = email.split("@")[1]

      # 从app_setting表中查询公司配置
      from models.app_seting import AppSetting
      import json

      result = await db.execute(
          select(AppSetting).where(AppSetting.key_type == "company")
      )
      company_settings = result.scalars().all()

      # 设置默认值
      company_name = "未知公司"
      role_code = "tourist"  # 默认为游客角色

      # 遍历所有公司配置，查找匹配的邮箱后缀
      for setting in company_settings:
          try:
              if setting.value_type == 'json':
                  company_config = json.loads(setting.key_value)
                  if 'email_suffixs' in company_config:
                      email_suffixes = company_config['email_suffixs'].split(',')
                      # 检查用户邮箱后缀是否匹配
                      if any(email_domain.strip() == suffix.strip() for suffix in email_suffixes):
                          company_name = company_config.get('company_name', setting.key_code)
                          role_code = "staff"  # 如果邮箱匹配，则为员工角色
                          break
          except Exception as e:
              logger.error(f"解析公司配置失败: {str(e)}")
              continue

      # 兼容原有逻辑，如果在新配置中未找到匹配，尝试使用原有的work_teams表
      if role_code == "tourist":
          company_result = await db.execute(
              select(WorkTeams).where(WorkTeams.email.like(f"%{email_domain}"))
          )
          company = company_result.scalars().first()
          if company:
              company_name = company.team_name
              role_code = "staff"

      email_prefix = email.split("@")[0]
      timestamp = int(time.time())
      createtime = datetime.fromtimestamp(timestamp)  # 转换为datetime对象

      new_user = User(
        username=email_prefix,
        nickname=username,
        email=email,
        status=1,
        joinip=userip,
        loginip=userip,
        logintime=int(time.time()),
        createtime=createtime,
        avatar=user_info["avatar"],
        role_id=3,  # 新用户默认为普通用户
        company=company_name,
      )
      db.add(new_user)
      await db.flush()
      # 添加默认角色
      role_res = await db.execute(select(Role).where(Role.roleCode == role_code))
      role     = role_res.scalars().first()

      if role:
        user_role = UserRole(
          user_id = new_user.id,
          role_id = role.id
        )
        db.add(user_role)

      await db.commit()
      await db.refresh(new_user)

      token = await create_access_token(user=new_user, user_agent=user_agent)

      # 更新用户的 token 字段
      new_user.token = token
      await db.commit()
      # await db.refresh(new_user)

      response_data = {
        'token': token,
        'refreshToken': '',
      }

    redirect_url = f"{app_settings.ding_talk_redirect_uri}?{urlencode(response_data)}"
    return RedirectResponse(url=redirect_url)
  except Exception as e:
    logger.error(f"Failed to handle DingTalk callback: {e}")
    return RedirectResponse(url=f"{app_settings.ding_talk_redirect_uri}/login?error_tips=登陆失败")


@router.get("/getUserInfo", tags=["auth"], response_model=UserInfoResponse)
async def getUserInfo(
    current_user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前登录用户的信息
    """
    # 查询用户角色
    result = await db.execute(
        select(Role.roleCode)
        .select_from(UserRole)
        .join(Role, UserRole.role_id == Role.id)
        .where(UserRole.user_id == current_user.id)
    )
    roles = result.scalars().all()

    # 如果没有角色,赋予默认角色
    if not roles:
        roles = ["staff"]

    return UserInfoResponse(
        data=UserInfoData(
            userId=current_user.id,
            userName=current_user.username,
            roles=roles,  # 使用查询到的实际角色列表
            buttons=[]  # 需要根据用户角色动态获取按钮权限
        ),
        code="0000",
        msg="请求成功"
    )


@router.get("/token_login", tags=["auth"], response_model=UserInfoResponse)
async def token_login(
    request: Request,
    current_user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Token 登录
    """
    try:
        # 查询用户角色
        result = await db.execute(
            select(Role.roleCode)
            .select_from(UserRole)
            .join(Role, UserRole.role_id == Role.id)
            .where(UserRole.user_id == current_user.id)
        )
        roles = result.scalars().all()

        # 如果没有角色,赋予默认角色
        if not roles:
            roles = ["staff"]

        return UserInfoResponse(
            data=UserInfoData(
                userId=current_user.id,
                userName=current_user.nickname,
                roles=roles,  # 使用查询到的实际角色列表
                buttons=[]  # 需要根据用户角色动态获取按钮权限
            ),
            code="0000",
            msg="请求成功"
        )
    except Exception as e:
        logger.error(f"Failed to validate token: {e}")
        raise ClientVisibleException("登录失败") from e


@router.get("/logout", tags=["auth"])
async def logout(
  current_user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
  authorization: str | None = Header(None),
):
  """
    用户退出登录
    """
  try:
    # 清空数据库中的token字段
    if current_user:
      current_user.token = ''
      await db.commit()
      logger.info(f"Cleared database token for user {current_user.username}")

    # 清除Redis中的token数据
    if authorization:
      # 解析Authorization header获取token
      parts = authorization.split()
      if len(parts) == 2 and parts[0] == "Bearer":
        token = parts[1]
        try:
          await destory_token(token)
          logger.info(f"Successfully cleared Redis token for user {current_user.username}")
        except Exception as redis_error:
          logger.error(f"Failed to clear Redis token for user {current_user.username}: {redis_error}")
          # Redis清除失败不影响整体登出流程
      else:
        logger.warning(f"Invalid authorization header format for user {current_user.username}")
    else:
      logger.warning(f"No authorization header found for user {current_user.username}")

    return JSONResponse(content={"code": "0000", "msg": "用户已成功退出登录"})
  except Exception as e:
    logger.error(f"Failed to logout user {current_user.username}: {e}")
    raise ClientVisibleException("退出登录失败") from e


@router.get("/test", tags=["auth"])
async def test(request: Request):
  user = request.state.user
  return {"userid": user.id, "username": user.username, "nickname": user.nickname}


@router.post("/send_email_code", tags=["auth"])
async def send_email_code(
    request: Request,
    email: str = Body(..., embed=True),
    randstr: str =  Body(..., embed=True),
    ticket: str =  Body(..., embed=True),
    type: str = Body(..., embed=True),  # 区分register/reset
    db: AsyncSession = Depends(get_db)
):
    """
    发送邮箱验证码
    type: register - 注册场景
          reset - 重置密码场景
    """
    try:
        # 验证腾讯验证码
        ip = request.state.client_ip

        verify_res = await verify_tencent_captcha(ip, randstr, ticket)
        if not verify_res:
        # if not verify_tencentCaptcha(ip,randstr,ticket):
            raise ClientVisibleException('验证失败，请稍后再试')

        # 检查邮箱是否已注册
        result = await db.execute(
            select(User).where(User.email == email)
        )
        user = result.scalars().first()

        # 注册场景:邮箱已注册则返回错误
        if type == "register" and user:
            raise ClientVisibleException("该邮箱已被注册")

        # 重置密码场景:邮箱未注册则返回错误
        if type == "reset" and not user:
            raise ClientVisibleException("该邮箱未注册")

        # 获取邮箱域名
        email_domain = email.split('@')[1]

        # 使用 Redis 锁来防止并发发送
        lock_key = f"email_lock:{email}"
        code_key = f"email_code:{email}"

        # 尝试获取锁
        if await redis.get(lock_key):
            raise ClientVisibleException("请求太频繁，请稍后再试")

        # 检查是否存在未过期的验证码
        existing_code = await redis.get(code_key)
        if existing_code:
            return JSONResponse(
                content={"code": "0000", "msg": "验证码已发送", "data": {"email": email}},
                status_code=200
            )

        try:
            # 设置发送锁，防止并发，有效期 60 秒
            await redis.set(lock_key, "1", 60)

            # 生成验证码
            verification_code = generate_verification_code()

            # 发送邮件
            await send_verification_email(email, verification_code)

            # 存储验证码，有效期 120 秒
            await redis.set(code_key, verification_code, 120)

            logger.info(f"Successfully sent verification code to {email}")
            return JSONResponse(
                content={"code": "0000", "msg": "验证码已发送", "data": {"email": email}},
                status_code=200
            )
        except Exception as mail_error:
            # 发送失败时清理 Redis 数据
            await redis.delete(lock_key)
            await redis.delete(code_key)
            logger.error(f"Failed to send verification email: {str(mail_error)}")
            raise
        finally:
            # 确保解锁
            await redis.delete(lock_key)

    except Exception as e:
        logger.error(f"发送验证码失败: {str(e)}")
        raise ClientVisibleException("发送验证码失败，请稍后重试") from e


class RegisterRequest(BaseModel):
    user_email: str
    user_pwd: str
    repeat_pwd: str
    email_code: str
    ticket: str
    randstr: str

@router.post("/register", tags=["auth"])
async def register(
    register_data: RegisterRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    用户注册接口
    """
    try:
        ip = request.state.client_ip
        # ticket = register_data.ticket
        # randstr = register_data.randstr
        email = register_data.user_email
        # 解密前端传来的加密密码
        try:
            password = decrypt(register_data.user_pwd)
            repeat_pwd = decrypt(register_data.repeat_pwd)
            logger.debug(password, repeat_pwd)
        except Exception as e:
            logger.error(f"密码解密失败: {str(e)}")
            raise ClientVisibleException("用户名/密码错误") from e

        email_code = register_data.email_code

        # 验证密码是否一致
        if password != repeat_pwd:
            raise ClientVisibleException("两次输入的密码不一致")

        # 验证邮箱验证码
        redis_code = await redis.get(f"email_code:{email}")
        if not redis_code or redis_code.decode() != email_code:
            raise ClientVisibleException("验证码错误或已过期")

        # 获取邮箱域名
        email_domain = email.split('@')[1]

        # 验证邮箱是否已存在
        result = await db.execute(
            select(User).where(User.email == email)
        )
        if result.scalars().first():
            raise ClientVisibleException("该邮箱已被注册")

        # 创建新用户
        username = email.split('@')[0]
        salt = str(uuid.uuid4())[:8]
        encrypted_password = getEncryptPassword(password, salt)

        # 从app_setting表中查询公司配置
        # 查询所有key_type为company的配置
        result = await db.execute(
            select(AppSetting).where(AppSetting.key_type == "company")
        )
        company_settings = result.scalars().all()

        # 设置默认值
        company_name = "未知公司"
        role_code = "tourist"  # 默认为游客角色

        # 遍历所有公司配置，查找匹配的邮箱后缀
        for setting in company_settings:
            try:
                if setting.value_type == 'json':
                    company_config = json.loads(setting.key_value)
                    if 'email_suffixs' in company_config:
                        email_suffixes = company_config['email_suffixs'].split(',')
                        # 检查用户邮箱后缀是否匹配
                        if any(email_domain.strip() == suffix.strip() for suffix in email_suffixes):
                            company_name = company_config.get('company_name', setting.key_code)
                            role_code = "staff"  # 如果邮箱匹配，则为员工角色
                            break
            except Exception as e:
                logger.error(f"解析公司配置失败: {str(e)}")
                continue

        # 兼容原有逻辑，如果在新配置中未找到匹配，尝试使用原有的work_teams表
        if role_code == "tourist":
            company_result = await db.execute(
                select(WorkTeams).where(WorkTeams.email.like(f"%{email_domain}"))
            )
            company = company_result.scalars().first()
            if company:
                company_name = company.team_name
                role_code = "staff"

        new_user = User(
            username=username,
            nickname=username,
            email=email,
            password=encrypted_password,
            salt=salt,
            joinip=ip,
            loginip=ip,
            logintime=int(time.time()),
            status=1,
            role_id=3,  # 默认普通用户角色
            company=company_name
        )

        db.add(new_user)
        await db.flush()

        # 添加角色
        role_res = await db.execute(select(Role).where(Role.roleCode == role_code))
        role = role_res.scalars().first()

        if role:
            user_role = UserRole(
                user_id=new_user.id,
                role_id=role.id
            )
            db.add(user_role)

        # 添加默认积分记录
        new_user_credit = UserCredit(
            user_id=new_user.id,
            credit=200,  # 默认 200 积分
            begin_date=datetime.now(),
        )
        db.add(new_user_credit)

        # 添加积分变更日志
        credit_log = UserCreditLog(
            user_id=new_user.id,
            credit=200,
            after_credit=200,
            ip=request.state.client_ip,
            matter='注册赠送',
            editor="system",
        )
        db.add(credit_log)

        await db.commit()

        # 注册成功后删除验证码
        await redis.delete(f"email_code:{email}")

        return JSONResponse(
            content={"code": "0000", "msg": "注册成功", "data": {"username": new_user.username}},
            status_code=200
        )

    except Exception as e:
        logger.error(f"注册失败: {str(e)}")
        raise ClientVisibleException("注册失败，请稍后重试") from e


@router.get("/check_office_ip", tags=["auth"])
async def check_office_ip(request: Request):
    """
    检查是否是办公室IP
    """
    try:
        client_ip = request.client.host
        # 尝试从 X-Forwarded-For 头部获取真实 IP
        x_forwarded_for = request.headers.get('X-Forwarded-For')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0].strip()
        allowed_ips = app_settings.allow_ips.split(",")

        is_allowed = any(
            client_ip.strip() == allowed_ip.strip()
            for allowed_ip in allowed_ips
        )

        return JSONResponse(
            content={
                "code": "0000",
                "msg": "请求成功",
                "data": {"allowed": 1 if is_allowed else 0}
            }
        )
    except Exception as e:
        logger.error(f"IP检查失败: {str(e)}")
        raise ClientVisibleException("IP检查失败") from e


class ResetPasswordRequest(BaseModel):
    user_email: str
    user_pwd: str
    repeat_pwd: str
    email_code: str

@router.post("/reset_password", tags=["auth"])
async def reset_password(
    reset_data: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    重置密码接口
    """
    try:
        email = reset_data.user_email

        # 解密前端传来的加密密码
        try:
            password = decrypt(reset_data.user_pwd)
            repeat_pwd = decrypt(reset_data.repeat_pwd)
        except Exception as e:
            logger.error(f"密码解密失败: {str(e)}")
            raise ClientVisibleException("用户名/密码错误") from e

        # 验证密码是否一致
        if password != repeat_pwd:
            raise ClientVisibleException("两次输入的密码不一致")

        # 验证邮箱验证码
        redis_code = await redis.get(f"email_code:{email}")
        if not redis_code or redis_code.decode() != reset_data.email_code:
            raise ClientVisibleException("验证码错误或已过期")

        # 查询用户是否存在
        result = await db.execute(
            select(User).where(User.email == email)
        )
        user = result.scalars().first()
        if not user:
            raise ClientVisibleException("用户不存在")

        # 更新用户密码
        salt = str(uuid.uuid4())[:8]
        encrypted_password = getEncryptPassword(password, salt)
        user.password = encrypted_password
        user.salt = salt
        user.updatetime = datetime.now()

        await db.commit()

        # 重置密码成功后删除验证码
        await redis.delete(f"email_code:{email}")

        return JSONResponse(
            content={"code": "0000", "msg": "密码重置成功", "data": {"email": email}},
            status_code=200
        )

    except Exception as e:
        logger.error(f"重置密码失败: {str(e)}")
        raise ClientVisibleException("重置密码失败，请稍后重试") from e

def validate_password_strength(password: str) -> bool:
    """验证密码强度，要求同时包含字母和数字，长度6-18位"""
    import re
    # 检查密码是否同时包含字母和数字
    if not re.match(r'^(?=.*[A-Za-z])(?=.*\d)\S{6,18}$', password):
        return False
    return True
