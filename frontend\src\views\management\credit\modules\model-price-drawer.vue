<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ModelPriceDrawer'
});

type CapacityOptions = { label: string; value: string }[];

interface Props {
  /** the type of operation */
  operateType: 'add' | 'edit';
  /** the edit row data */
  rowData?: any | null;
  capacityOptions: CapacityOptions;
}

const props = defineProps<Props>();

type SubmitModel = {
  capacity: string;
  model: string;
  api_type: number;
  unit: number;
  credit: number;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '新增模型积分' : '编辑信息';
});

type Model = {
  capacity: string | null;
  model: string;
  api_type: number;
  unit: number;
  credit: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    capacity: null,
    model: '',
    api_type: 1,
    unit: 1,
    credit: 1
  };
}

const rules: Record<string, any> = {
  capacity: defaultRequiredRule,
  model: defaultRequiredRule,
  api_type: defaultRequiredRule,
  credit: defaultRequiredRule,
  unit: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  // console.log('Model after assigning default values:', props.rowData);

  if (props.operateType === 'edit' && props.rowData) {
    const rowData = { ...props.rowData };
    Object.assign(model, rowData);
    // console.log('Model after assigning rowData:', model);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // Mock request
  // console.log(model);
  const submitModel = {
    ...model,
    capacity: model.capacity || ''
    // lang: model.lang.filter(Boolean).join(',') // 将 lang 数组转换为逗号分隔的字符串
  };
  closeDrawer();
  emit('submitted', submitModel);
}

const APITypeOptions = [
  { label: '次数', value: 1 },
  { label: 'Token', value: 2 }
];

watch(visible, newVal => {
  // console.log('visible changed:', newVal);
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="AI 能力" path="capacity">
          <NSelect v-model:value="model.capacity" :options="capacityOptions" placeholder="请选择能力" />
        </NFormItem>
        <NFormItem label="模型名称" path="model">
          <NInput v-model:value="model.model" placeholder="请输入模型名称" />
        </NFormItem>
        <NFormItem label="计费类型" path="api_type">
          <NSelect v-model:value="model.api_type" :options="APITypeOptions" placeholder="请选择计费类型" />
        </NFormItem>
        <NFormItem label="积分" path="credit">
          <NInputNumber
            v-model:value="model.credit"
            type="number"
            :min="0"
            :step="1"
            :precision="0"
            placeholder="请输入积分"
          />
        </NFormItem>
        <NFormItem label="单位" path="unit">
          <NInput v-model:value="model.unit" placeholder="请输入单位" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
