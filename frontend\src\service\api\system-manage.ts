import { request } from '../request';

export interface SettingGroup {
  id: number;
  group_code: string;
  group_name: string;
  description?: string;
  seq: number;
  status: boolean;
}

export interface SettingGroupCreate {
  group_code: string;
  group_name: string;
  description?: string;
  seq?: number;
  status?: boolean;
}

// 配置项相关接口
export interface Setting {
  id: number;
  group_id: number;
  config_key: string;
  config_value: string;
  data_type: string;
  form_type: string;
  options?: string;
  placeholder?: string;
  description?: string;
  is_required: boolean;
  is_sensitive: boolean;
  seq: number;
  status: boolean;
}

export interface SettingCreate {
  group_id: number;
  config_key: string;
  config_value: string;
  data_type?: string;
  form_type?: string;
  options?: string;
  placeholder?: string;
  description?: string;
  is_required?: boolean;
  is_sensitive?: boolean;
  seq?: number;
  status?: boolean;
}

export interface RoleRes {
  current: number;
  size: number;
  total: number;
  records: Api.SystemManage.AllRole[];
}

// 定义响应结构
export interface ApiResponse<T> {
  code: string;
  data: T;
  msg: string;
}

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/system/get_role',
    method: 'get',
    params
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.RoleList>({
    url: '/system/get_role',
    method: 'get'
  });
}
// get_role_menu
export function fetchGetRoleMenu(id: number | string) {
  return request<Api.SystemManage.MenuList>({
    url: '/system/get_role_menu',
    method: 'get',
    params: { id }
  });
}
// get_role_user
export function fetchGetRoleUser(role_id: number | string) {
  return request<Api.SystemManage.UserList>({
    url: '/system/get_role_user',
    method: 'get',
    params: { role_id }
  });
}
// set_role_user
export function postSetRoleUser(role_id: number, user: number[]) {
  return request<Api.Common.ResultResponse>({
    url: '/system/set_role_user',
    method: 'post',
    data: { role_id, user }
  });
}
// get_menu_role
export function fetchGetMenuRole(menu_id: number | string) {
  return request<Api.SystemManage.UserList>({
    url: '/system/get_menu_role',
    method: 'get',
    params: { menu_id }
  });
}
// set_menu_role
export function postSetMenuRole(menu_id: number, role: number[]) {
  return request<Api.Common.ResultResponse>({
    url: '/system/set_menu_role',
    method: 'post',
    data: { menu_id, role }
  });
}

// set_role_menu
export function postSetRoleMenu(role_id: number, menu: string[]) {
  return request<Api.Common.ResultResponse>({
    url: '/system/set_role_menu',
    method: 'post',
    data: { role_id, menu }
  });
}
/** 新增或者修改角色 */
export function postSaveRole(data: Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'roleDesc'>) {
  return request<Api.Common.ResultResponse>({
    url: '/system/save_role',
    method: 'post',
    data
  });
}
export function postDelRole(id: number) {
  const data = { id };
  return request<Api.Common.ResultResponse>({
    url: '/system/del_role',
    method: 'post',
    data
  });
}

/** 新增修改查找团队 */
export function fetchGetTeamList(params?: Api.SystemManage.TeamSearchParams) {
  return request<Api.SystemManage.TeamList>({
    url: '/system/get_team',
    method: 'get',
    params
  });
}

export function postSaveTeam(data: Pick<Api.SystemManage.Teams, 'team_name' | 'team_code' | 'email'>) {
  return request<Api.Common.ResultResponse>({
    url: '/system/save_team',
    method: 'post',
    data
  });
}
export function postDelTeam(id: number) {
  const data = { id };
  return request<Api.Common.ResultResponse>({
    url: '/system/del_team',
    method: 'post',
    data
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/system/getUserList',
    method: 'get',
    params
  });
}
export function fetchAllUserCompany() {
  return request<{ records: string[] }>({
    url: '/system/get_all_user_company',
    method: 'get'
  });
}

export function postSaveUser(
  data: Pick<Api.SystemManage.User, 'username' | 'gender' | 'nickname' | 'email' | 'status' | 'role_id' | 'company'>
) {
  return request<Api.Common.ResultResponse>({
    url: '/system/updateUser',
    method: 'post',
    data
  });
}

export function postSaveUserCredit(data: { id: number; credit: number; detail: string }) {
  return request<Api.Common.ResultResponse>({
    url: '/system/updateUserCredit',
    method: 'post',
    data
  });
}
export function postDelUser(id: number) {
  const data = { id };
  return request<Api.Common.ResultResponse>({
    url: '/system/deleteUser',
    method: 'post',
    data
  });
}

/** 读取所有菜单 */
export function fetchGetMenuList() {
  return request<Api.SystemManage.MenuList>({
    // url: '/systemManage/getMenuList/v2',
    url: '/system/get_menu',
    method: 'get'
  });
}
export function delMenu(id: number) {
  return request<Api.SystemManage.MenuList>({
    url: '/system/del_menu',
    method: 'post',
    data: { id }
  });
}

/** get all pages */
export function fetchGetAllPages() {
  return request<string[]>({
    url: '/system/getAllPages',
    method: 'get'
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    url: '/system/get_menu_tree',
    method: 'get'
  });
}

/**
 * 保存菜单
 *
 * @param data
 */
export function postSaveMenu(data: any) {
  return request<Api.Common.ResultResponse>({
    url: '/system/save_menu',
    method: 'post',
    data
  });
}

// 查询积分日志 fetchCreditLogSearch
export function fetchCreditLogSearch(params: Api.SystemManage.CreditLogSearchParams) {
  return request({
    url: '/creditLog/search',
    method: 'get',
    params
  });
}

/** 查询积分日志 */
export function fetchCreditLog(page: number, size: number) {
  return request({
    url: '/creditLog/all',
    method: 'get',
    params: { current: page, size }
  });
}

// 查询用户积分
export function fetchUserCreditSearch(params: Api.SystemManage.UserCreditSearch) {
  return request({
    url: '/usercredit/search',
    method: 'get',
    params
  });
}

/** 查询用户积分 */
export function fetchUserCredit(page: number, size: number) {
  return request({
    url: '/usercredit/all',
    method: 'get',
    params: { current: page, size }
  });
}

// 增加用户积分
export function fetchAddUserCredit(data: any) {
  return request<Api.Common.ResultResponse>({
    url: '/user/add_user_credit',
    method: 'post',
    data
  });
}

// 修改用户积分
export function fetchAddNewUserCredit(data: any) {
  return request<Api.Common.ResultResponse>({
    url: '/user/add_new_user_credit',
    method: 'post',
    data
  });
}

// 查新用户
export function fetchNewCreditUserList() {
  return request({
    url: '/usercredit/newcreditlist',
    method: 'get'
  });
}

// fetchAddManagerSetting, fetchUpdataManagerSetting, fetchManagerSetting, fetchManagerSettingSearch
/** 查询所有系统配置 */
export function fetchManagerSetting(page: number, size: number) {
  return request({
    url: '/system/setting/all',
    method: 'get',
    params: { current: page, size }
  });
}
/** 根据条件查询系统配置 */
export function fetchManagerSettingSearch(params: Api.SystemManage.SettingSearchParams) {
  return request({
    url: '/system/setting/search',
    method: 'get',
    params
  });
}

// 删除系统配置
export function fetchDelManagerSetting(id: number) {
  return request<Api.SystemManage.MenuList>({
    url: '/system/setting/delete',
    method: 'post',
    data: { id }
  });
}

export function fetchAddManagerSetting(data: any) {
  return request<Api.Common.ResultResponse>({
    url: '/system/setting/add',
    method: 'post',
    data
  });
}

// 修改字典配置
export function fetchUpdataManagerSetting(data: any) {
  return request<Api.Common.ResultResponse>({
    url: '/system/setting/update',
    method: 'post',
    data
  });
}

/** 系统配置 */
// 获取配置分组列表
export function fetchGetSettingGroups(status: boolean = true) {
  return request<ApiResponse<SettingGroup[]>>({
    url: `/config/groups?status=${status}`,
    method: 'get'
  });
}

// 获取分组下的配置项
export function fetchGetGroupSettings(groupId: number) {
  return request<ApiResponse<Setting[]>>({
    url: `/config/group/${groupId}`,
    method: 'get'
  });
}

// 更新配置项
export function fetchUpdateSetting(settingId: number, data: SettingCreate) {
  return request<ApiResponse<Setting>>({
    url: `/config/setting/${settingId}`,
    method: 'put',
    data
  });
}

// 创建配置项
export function fetchCreateSetting(data: SettingCreate) {
  return request<ApiResponse<Setting>>({
    url: '/config/setting',
    method: 'post',
    data
  });
}

// 创建配置组
export function fetchCreateSettingGroup(data: SettingGroupCreate) {
  return request<ApiResponse<SettingGroup>>({
    url: '/config/groups',
    method: 'post',
    data
  });
}

/** 模型管理接口 * */

/** 获取模型列表 */
export function fetchAllModels(params: Omit<Api.Common.PaginatingCommonParams, 'total'>) {
  const sp = new URLSearchParams(params);
  return request<Api.SystemManage.ModelsPagination>({
    url: `/models?${sp.toString()}`,
    method: 'get'
  });
}

/** 获取单个模型的信息 */
export function fetchModel(model_id: number) {
  return request<Api.SystemManage.Model>({
    url: `/models/${model_id}`,
    method: 'get'
  });
}

/** 新增模型 */
export function fetchCreateModel(data: Omit<Api.SystemManage.Model, 'id'>) {
  return request<Api.SystemManage.Model>({
    url: '/models',
    method: 'post',
    data
  });
}

/** 更新模型 */
export function fetchUpdateModel(model_id: number, data: Omit<Api.SystemManage.Model, 'id'>) {
  return request<Api.SystemManage.Model>({
    url: `/models/${model_id}`,
    method: 'put',
    data
  });
}

/** 删除模型 */
export function fetchDeleteModel(model_id: number) {
  return request<null>({
    url: `/models/${model_id}`,
    method: 'delete'
  });
}
