<script setup lang="tsx">
import { NButton, NPopconfirm } from 'naive-ui';
import { fetchGetTeamList, postDelTeam } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import TeamOperateModal from './modules/team-operate-modal.vue';
import TeamSearch from './modules/team-search.vue';
const appStore = useAppStore();
const { columns, columnChecks, data, loading, getData, mobilePagination, searchParams, resetSearchParams } = useTable({
  apiFn: fetchGetTeamList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    team_name: '',
    team_code: ''
  },
  columns: () => [
    {
      key: 'id',
      title: 'id',
      width: 64,
      align: 'center'
    },
    {
      key: 'team_code',
      title: '团队编码',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'team_name',
      title: '团队名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'email',
      title: '邮箱',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'update_time',
      title: '更新日期',
      minWidth: 120,
      render: row => row.update_time.replace('T', ' ')
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 290,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onDeleted } = useTableOperate(
  data,
  getData
);

function handleDelete(id: number) {
  // request
  postDelTeam(id).then(() => {
    onDeleted();
  });
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <TeamSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="团队配置" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="(row :any) => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <TeamOperateModal
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
