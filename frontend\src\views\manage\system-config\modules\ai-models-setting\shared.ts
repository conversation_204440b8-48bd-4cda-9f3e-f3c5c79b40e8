import { fetchAllModels } from '@/service/api';

let promise: Promise<Api.SystemManage.Model[]> | null = null;

/** 获取AI模型列表，同时做节流操作，避免重复请求 */
export const getAiModels = async (): Promise<Api.SystemManage.Model[]> => {
  if (promise) {
    return promise;
  }
  const res = await fetchAllModels({ size: 9999 });
  promise = new Promise<Api.SystemManage.Model[]>(resolve => {
    if (res.error) {
      resolve([]);
    } else {
      resolve(res.data.records);
    }
  });
  return promise;
};
