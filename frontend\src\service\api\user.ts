import { request } from '../request';
export function fetchGetCurrUserInfo() {
  return request<Api.SystemManage.User>({
    url: '/user/get_user_info',
    method: 'get'
  });
}
export function postSetUserInfo(data: Partial<Api.SystemManage.User>) {
  return request<Api.SystemManage.User>({
    url: '/user/set_user_info',
    method: 'post',
    data
  });
}

// 获取用户积分信息
export function fetchGetUserCreditInfo() {
  return request({
    url: '/usercredit/info',
    method: 'get'
  });
}

// 获取用户积分记录
export function fetchGetUserCreditLog(current: number = 1, size: number = 10) {
  return request({
    url: '/usercredit/personal_log',
    method: 'get',
    params: { current, size }
  });
}
