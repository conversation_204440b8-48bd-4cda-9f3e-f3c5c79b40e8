<script setup lang="tsx">
import { defineProps } from 'vue';
import type { Ref, VNodeChild } from 'vue';
import { NButton, NEllipsis, NPopconfirm, useMessage } from 'naive-ui';
import { useTable, useTableOperate } from '@/hooks/common/table';
import {
  deletePresets,
  fetchPresets,
  savePresets,
  searchPresets,
  uploadPresets,
  uploadTranslationPresets
} from '@/service/api/text';
import type { AddPresetsPayload, SavePresetsPayload } from '@/service/api/text';
import { $t } from '@/locales';
import type { TableDataWithIndex } from '../../../../packages/hooks/src/use-table';
import GameOperateDrawer from './modules/preset-edit-drawer.vue';
import GameSearch from './modules/preset-search.vue';

const props = defineProps<{ selectedGameCode: string }>();
const gamecode = props.selectedGameCode;
const message = useMessage();

interface TableData {
  id: number;
  gamecode: string;
  zh: string;
  en: string;
  updatetime: string;
  editor: string;
}

function convertToNaiveTableData(data: TableData[]): NaiveUI.TableData[] {
  return data as unknown as NaiveUI.TableData[];
}

const { columns, columnChecks, data, getData, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPresets,
  apiParams: {
    gamecode,
    current: 1,
    size: 10
  },
  showTotal: true,
  columns: () => [
    // {
    //   type: 'selection',
    //   align: 'center',
    //   width: 48
    // },
    // {
    //   key: 'id',
    //   title: 'ID',
    //   align: 'center',
    //   width: 64
    // },
    // {
    //   key: 'gamecode',
    //   title: '游戏编号',
    //   align: 'center',
    //   minWidth: 100
    // },
    {
      key: 'zh',
      title: '中文',
      align: 'center',
      minWidth: 180,
      maxWidth: 180,
      render: (rowData: TableDataWithIndex<any>): VNodeChild => (
        <NEllipsis expand-trigger="click" line-clamp="3" tooltip={false}>
          {rowData.zh}
        </NEllipsis>
      )
    },
    {
      key: 'en',
      title: '英文',
      align: 'center',
      minWidth: 150,
      maxWidth: 180,
      render: (rowData: TableDataWithIndex<any>): VNodeChild => (
        <NEllipsis expand-trigger="click" line-clamp="3" tooltip={false}>
          {rowData.en}
        </NEllipsis>
      )
    },
    {
      key: 'update_time',
      title: '更新时间',
      align: 'center',
      minWidth: 180
    },
    // {
    //   key: 'editor',
    //   title: '修改者',
    //   align: 'center',
    //   minWidth: 100
    // },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: (row: TableDataWithIndex<any>): VNodeChild => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确认删除？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys } = useTableOperate(
  data as Ref<NaiveUI.TableData[]>,
  getData
);

async function handleDelete(id: number) {
  try {
    await deletePresets(id);
    getData();
  } catch (error) {
    // console.error('请求删除预设时发生错误');
  }
}

async function edit(id: number) {
  handleEdit(id);
  // editingData.value = { ...editingData.value, ...response.data };
  // console.log('id:', id);
  // console.log('Editing Data:', editingData.value);
}

function handleSearch(searchResults: TableData[]) {
  data.value = convertToNaiveTableData(searchResults);
}

function handleSubmit() {
  getData();
}

async function handleUploadPresets(parms: AddPresetsPayload) {
  try {
    if (operateType.value === 'add') {
      await uploadPresets(parms);
    } else if (operateType.value === 'edit') {
      const saveParms: SavePresetsPayload = {
        id: parms.id,
        zh: parms.zh,
        en: parms.en
      };
      await savePresets(saveParms);
    }
    getData();
  } catch (error) {
    // 处理请求错误
  }
}

async function searchPresetsWithParams(params: {
  gamecode?: string;
  zh?: string;
  en?: string;
  current?: number;
  size?: number;
}) {
  if (!params.gamecode) {
    params.gamecode = gamecode;
  }

  try {
    const response = await searchPresets(params);
    if (response.data) {
      handleSearch(response.data.records);
    }
  } catch (error) {}
}

async function handleUploadFile({ file, gameCode }: { file: File; gameCode: string }) {
  // console.log('handleUploadFile called', file, gameCode);
  const formData = new FormData();
  formData.append('file', file);
  formData.append('gamecode', gameCode); // 传递 gamecode
  try {
    const response = await uploadTranslationPresets(formData);
    if (response.data) {
      message.success($t('page.text.uploadSuccess'));
      getData();
    } else {
      message.error(response.data.msg);
    }
  } catch (error) {
    // message.error('上传表格时发生错误');
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <GameSearch
      v-model:model="searchParams"
      :game-code="props.selectedGameCode"
      @search="searchPresetsWithParams"
      @upload-file="handleUploadFile"
    />
    <NCard title="预设管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="convertToNaiveTableData(data as TableData[])"
        size="small"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <GameOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :game-code="editingData?.gamecode ?? props.selectedGameCode"
        class="w-[40vw]"
        @submitted="handleSubmit"
        @upload-presets="handleUploadPresets"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
