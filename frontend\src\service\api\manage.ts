import type { AiCapacity } from '@/utils/AiCapacity';
import { request } from '../request';

export interface AddGameRequest {
  gamecode: string;
  gamename: string;
  info?: string;
  lang?: string;
  editor?: string;
}

export interface AddToolRequest {
  name: string;
  type: string;
  description?: string;
  main_function?: string;
  url?: string;
  recommendation_rating?: number;
  is_paid?: boolean;
  is_available_in_china?: boolean;
  origin_country?: string;
}

export interface AddModelPriceRequest {
  id?: number;
  api_name?: string;
  api_path: string;
  model?: string;
  credit_type: number;
  api_type: number;
  unit?: string;
  credit: number;
  editor?: string;
}

/** 获取游戏管理数据 */
export function fetchGameManagement(page: number, size: number) {
  return request({
    url: '/gameManage/all_game_management',
    method: 'get',
    params: { current: page, size }
  });
}

/** 查询游戏管理数据 */
export function fetchGameSearch(params: Api.SystemManage.CommonSearchParams) {
  return request({
    url: '/gameManage/search',
    method: 'get',
    params
  });
}

/** 删除游戏管理数据 */
export function fetchDelGame(gamecodes: string[]) {
  return request({
    url: '/gameManage/delete',
    method: 'post',
    data: { gamecodes }
  });
}

/** 添加游戏管理数据 */
export function fetchAddGame(data: AddGameRequest) {
  return request({
    url: '/gameManage/addGame',
    method: 'post',
    data
  });
}

/** 更新游戏管理数据 */
export function fetchUpdataGame(data: AddGameRequest) {
  return request({
    url: '/gameManage/update',
    method: 'post',
    data
  });
}

/** 获取工具集列表 */
export function fetchToolset(page: number, size: number) {
  return request({
    url: '/toolset/all_toolsets',
    method: 'get',
    params: { current: page, size }
  });
}

/** 查询工具 */
export function fetchToolSearch(params: Api.SystemManage.CommonSearchParams) {
  return request({
    url: '/toolset/search',
    method: 'get',
    params
  });
}

/** 删除游戏管理数据 */
export function fetchDelTool(name: string) {
  return request({
    url: '/toolset/delete',
    method: 'post',
    data: { name }
  });
}

/** 添加游戏管理数据 */
export function fetchAddTool(data: AddToolRequest) {
  return request({
    url: '/toolset/addTool',
    method: 'post',
    data
  });
}

/** 更新游戏管理数据 */
export function fetchUpdataTool(data: AddToolRequest) {
  return request({
    url: '/toolset/update',
    method: 'post',
    data
  });
}

/** 获取指定分类的工具集列表 */
export function fetchTypeToolsets(type: string, page: number, size: number) {
  return request({
    url: '/toolset/type_toolsets',
    method: 'get',
    params: { type, current: page, size }
  });
}

// 用于频道管理 管理员获取mj频道列表
export function fetchChannelList(page?: number, size?: number) {
  return request({
    url: '/midjourney/channel_list',
    method: 'get',
    params: { page, size }
  });
}

// 删除mj频道
export function fetchDelChannel(channel_id: number) {
  return request({
    url: '/midjourney/del_channel',
    method: 'post',
    data: { channel_id }
  });
}

/** 获取积分管理数据 */
export function fetchModelPrice(page: number, size: number) {
  return request({
    url: '/modelPrice/all',
    method: 'get',
    params: { current: page, size }
  });
}

/** 查询积分管理数据 */
export function fetchModelPriceSearch(params: Api.SystemManage.CommonSearchParams) {
  return request({
    url: '/modelPrice/search',
    method: 'get',
    params
  });
}
/** 获取积分管理数据 */
export function fetchModelPriceItem(params: { capacity?: AiCapacity; model?: string }) {
  return request({
    url: '/modelPrice/item',
    method: 'get',
    params
  });
}

/** 删除积分管理数据 */
export function fetchDelModelPrice(id: number) {
  return request({
    url: '/modelPrice/delete',
    method: 'post',
    data: { id }
  });
}

/** 添加积分管理数据 */
export function fetchAddModelPrice(data: AddModelPriceRequest) {
  return request({
    url: '/modelPrice/add',
    method: 'post',
    data
  });
}

/** 更新积分管理数据 */
export function fetchUpdataModelPrice(data: AddModelPriceRequest) {
  return request({
    url: '/modelPrice/update',
    method: 'post',
    data
  });
}
