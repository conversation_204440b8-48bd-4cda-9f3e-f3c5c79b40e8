import { localStg } from '@/utils/storage';
import { DEFAULT_COOKIE_OPTIONS, deleteCookie, getCookie } from '@/utils/cookie';

/** 获取 token */
export function getToken() {
  // 先从 localStorage 获取 token
  let token = localStg.get('token');
  if (!token) {
    // 如果 localStorage 中没有，则尝试从 cookie 中获取
    token = getCookie('token') || '';
  }
  return token;
}

/** 清除认证相关的缓存 */
export function clearAuthStorage() {
  localStg.remove('token');
  localStg.remove('refreshToken');
  // 使用统一的 cookie 配置
  deleteCookie('token', DEFAULT_COOKIE_OPTIONS);
}
