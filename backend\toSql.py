import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = (
  "mysql+pymysql://{username}:{password}@{host}:{port}/{database}".format(
    username=os.getenv("DB_USER"),
    password=os.getenv("DB_PASSWORD"),
    host=os.getenv("DB_HOST"),
    port=os.getenv("DB_PORT") or 3306,
    database=os.getenv("DB_NAME"),
  )
)

# 创建数据库引擎
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

import pandas as pd

# 完整的原始数据
source_data = [
    {"EN": "hi", "ZH": "你好"}, {"EN": "today", "ZH": "今天"}, {"EN": "Candidate", "ZH": "秀女"}, {"EN": "Repliant", "ZH": "答应"}
]

# 分离en_data和zh_data
en_data = [entry["EN"] for entry in source_data]
zh_data = [entry["ZH"] for entry in source_data]

# 检查每个英文条目是否都有对应的中文条目
for en, zh in zip(en_data, zh_data):
    if zh is None or zh == "":
        print(f"Missing Chinese translation for: {en}")
    else:
        print(f"EN: {en} - ZH: {zh}")

# 创建并展示数据框
df = pd.DataFrame({'EN': en_data, 'ZH': zh_data})
print(df)


# 插入数据的逻辑
def insert_data(game_name, company, editor, zh_data, en_data):
  with engine.connect() as connection:
    for zh, en in zip(zh_data, en_data):
      query = text("""
                INSERT INTO gametranslations (gamecode, game_name, translations, zh, en, updatetime, editor, company)
                VALUES (:gamecode, :game_name, :translations, :zh, :en, NOW(), :editor, :company)
            """)
      params = {
        'gamecode': '8',
        'game_name': game_name,
        'translations': '{}',
        'zh': zh,
        'en': en,
        'editor': editor,
        'company': company
      }
      connection.execute(query, params)
      connection.commit()  # 确保事务被提交
      print(f"Inserted: {en} - {zh}")


# 调用插入函数
insert_data(
  game_name="测试用2",
  company="igamebuy",
  editor=None,  #
  zh_data=zh_data,
  en_data=en_data
)
