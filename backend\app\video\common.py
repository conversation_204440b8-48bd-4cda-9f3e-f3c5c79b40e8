import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.users import User
from models.tasks import Task, TaskStatus, TaskType
from task_queue.task_manager import TaskManager
from service.translate import get_translate
from utils.common import contains_chinese
from service.tencent.cos import move_object
from utils.exceptions import ClientVisibleException

TENCENT_COS_HOST = app_settings.tencent_cos_host
logger = logging.getLogger(__name__)

# 请求模型
class VideoGenerationRequest(BaseModel):
    image_base64: str = ""
    prompt: str
    negative_prompt: str = ""
    seed: int = 31337
    total_second_length: float = 5.0
    latent_window_size: int = 9
    steps: int = 25
    cfg: float = 1.0
    gs: float = 10.0
    rs: float = 0.0
    gpu_memory_preservation: float = 6.0
    use_teacache: bool = True
    mp4_crf: int = 16
    model: str = "framepack"  # 添加model字段，默认为framepack

    model_config = {"protected_namespaces": ()}

# 响应模型
class VideoTaskResponse(BaseModel):
    code: str = "0000"
    data: Optional[Dict[str, Any]] = None
    msg: Optional[str] = None

# 历史记录响应模型
class VideoHistoryResponse(BaseModel):
    code: str = "0000"
    data: Optional[List[Dict[str, Any]]] = None
    msg: Optional[str] = None

# 取消任务请求模型
class CancelTaskRequest(BaseModel):
    taskid: str

# 任务取消响应模型
class CancelTaskResponse(BaseModel):
    code: str = "0000"
    msg: Optional[str] = None

# 删除任务请求模型
class DeleteTaskRequest(BaseModel):
    taskid: str

# 删除任务响应模型
class DeleteTaskResponse(BaseModel):
    code: str = "0000"
    msg: Optional[str] = None

async def process_video_generation(
    req: VideoGenerationRequest,
    user: User,
    db: AsyncSession,
    api_tag: str = "framepack",  # 默认为framepack，可以被重写为volcengine
    credit_operator_id: str | None = None
) -> VideoTaskResponse:
    """
    处理视频生成请求的共享函数
    """
    try:
        # 任务参数
        params = req.model_dump()
        # 添加积分处理的 ID
        params["credit_operator_id"] = credit_operator_id

        # 检查并转换临时存储URL为长期存储URL
        image_base64 = params.get("image_base64", "")
        if isinstance(image_base64, str) and TENCENT_COS_HOST and image_base64.startswith(TENCENT_COS_HOST):
            try:
                # 解析URL获取key
                cos_key = image_base64.replace(TENCENT_COS_HOST, "")
                logger.info(f"解析到COS对象key: {cos_key}")

                # 检查并替换路径中的24_hours为1_years
                if '/24_hours/' in cos_key:
                    new_cos_key = cos_key.replace('/24_hours/', '/1_years/')
                    logger.info(f"将对象移动到新路径: {new_cos_key}")

                    # 移动对象
                    try:
                        new_url = await move_object(cos_key, new_cos_key)
                        logger.info(f"对象移动成功或已存在, 新URL: {new_url}")

                        # 更新image_base64为新的URL
                        params["image_base64"] = new_url
                    except Exception as move_err:
                        # 如果报错信息包含"NoSuchResource"，可能是对象已经被其他任务移动
                        if "NoSuchResource" in str(move_err):
                            # 直接构造可能的新URL
                            new_result_url = TENCENT_COS_HOST + new_cos_key
                            logger.warning(f"源对象不存在，可能已被其他任务移动，使用构造的新URL: {new_result_url}")

                            # 更新image_base64为构造的新URL
                            params["image_base64"] = new_result_url
                        else:
                            logger.error(f"移动对象失败: {move_err}", exc_info=True)
                else:
                    logger.info(f"对象路径 {cos_key} 不包含 '/24_hours/'，无需移动")
            except Exception as e:
                logger.error(f"处理图像URL时出错: {e}", exc_info=True)

        # 检测prompt是否包含中文，如果是，则翻译为英文
        original_prompt = params.get("prompt", "")
        if contains_chinese(original_prompt):
            # 翻译中文prompt为英文
            try:
                translated_prompt = await get_translate(original_prompt, from_lang="zh-CN", to_lang="en")
                logger.info(f"翻译prompt: '{original_prompt}' -> '{translated_prompt}'")
                # 添加翻译后的prompt到参数中，保留原始prompt
                params["translated_prompt"] = translated_prompt
            except Exception as e:
                logger.error(f"翻译prompt失败: {e}", exc_info=True)
                # 如果翻译失败，使用原始prompt
                params["translated_prompt"] = original_prompt
        else:
            # 如果不包含中文，translated_prompt与prompt相同
            params["translated_prompt"] = original_prompt

        # 创建任务
        task, error_msg = await TaskManager.create_task(
            db=db,
            username=user.username,
            task_type=TaskType.VIDEO,
            action="GENERATE",
            task_params=params,
            prompt_media_url=params.get("image_base64"),  # 保存更新后的图像URL到prompt_media_url字段
            model=params.get("model")  # 直接传入前端提供的model参数
        )

        # 检查任务是否因超限而未创建
        if task is None:
            raise ClientVisibleException(error_msg or "创建任务失败，请重试")

        # 转换为通用输出格式
        task_out = {
            "id": task.id,
            "taskid": task.taskid,
            "username": task.username,
            "status": task.status,
            "action": task.action,
            "submit_time": task.submit_time.isoformat() if task.submit_time else None,
            "queue_position": task.queue_position,
            # 将特定于视频的参数从task_params提取出来
            "prompt": task.task_params.get("prompt", "")
        }

        # 返回任务信息
        return VideoTaskResponse(
            code="0000",
            data=task_out
        )

    except Exception as e:
        logger.error(f"创建生成任务失败 for user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("创建任务失败，请重试") from e
