import asyncio
import json
import logging
import os
import sys
from typing import Dict, Any, Callable, Awaitable
from sqlalchemy.future import select

from config import app_settings
from utils.database import AsyncSessionLocal
from utils.redis import redis
from models.tasks import Task, TaskStatus, TaskType

from task_queue.task_manager import TaskManager, TASK_QUEUE_PREFIX

logger = logging.getLogger(__name__)


class TaskProcessor:
    """通用任务处理器"""
    
    def __init__(self):
        # 任务处理器映射，每种任务类型对应一个处理函数
        self.task_handlers: Dict[str, Callable] = {}
        # 处理器激活状态
        self.active_processors: Dict[str, bool] = {}
        # 导入环境变量
        self.load_env_vars()

    @property
    def ai_server(self):
        # 将常用API添加为实例变量，便于处理器访问
        ai_server = app_settings.ai_server

        # 添加更多环境变量检查
        if not ai_server:
            logger.warning("警告: 未设置AI-SERVER环境变量或格式不正确")

        return ai_server
        
    @staticmethod
    def load_env_vars():
        """加载环境变量"""
        # 检查数据库连接环境变量
        db_host = app_settings.db_host
        db_port = app_settings.db_port
        db_user = app_settings.db_user
        db_name = app_settings.db_name
        if not all([db_host, db_port, db_user, db_name]):
            logger.warning(f"警告: 数据库连接环境变量不完整 - Host:{db_host}, Port:{db_port}, User:{db_user}, DB:{db_name}")
        else:
            logger.info(f"数据库连接配置: {db_host}:{db_port}/{db_name}")
            
        # 检查Redis连接环境变量
        redis_host = app_settings.redis_host
        redis_port = app_settings.redis_port
        if not all([redis_host, redis_port]):
            logger.warning(f"警告: Redis连接环境变量不完整 - Host:{redis_host}, Port:{redis_port}")
        else:
            logger.info(f"Redis连接配置: {redis_host}:{redis_port}")
    
    def register_handler(self, task_type: str, handler: Callable[[Dict[str, Any]], Awaitable[Dict[str, Any]]]):
        """
        注册任务处理器
        
        参数:
        - task_type: 任务类型
        - handler: 处理函数，接收任务数据，返回处理结果
        """
        self.task_handlers[task_type] = handler
        logger.info(f"已注册 {task_type} 类型任务的处理器")
    
    def start_processor(self, task_type: str):
        """
        启动指定类型的任务处理器
        
        参数:
        - task_type: 任务类型
        """
        if task_type not in self.task_handlers:
            logger.error(f"无法启动处理器: 未找到 {task_type} 类型任务的处理器")
            return
        
        self.active_processors[task_type] = True
        asyncio.create_task(self._process_task_queue(task_type))
        logger.info(f"{task_type} 任务处理器已启动")
    
    def stop_processor(self, task_type: str):
        """
        停止指定类型的任务处理器
        
        参数:
        - task_type: 任务类型
        """
        self.active_processors[task_type] = False
        logger.info(f"{task_type} 任务处理器将在当前任务完成后停止")
    
    async def _process_task_queue(self, task_type: str):
        """
        处理特定类型任务的队列
        
        参数:
        - task_type: 任务类型
        """
        queue_key = f"{TASK_QUEUE_PREFIX}{task_type}"
        logger.info(f"开始处理 {queue_key} 队列")
        
        while self.active_processors.get(task_type, False):
            try:
                # 从队列阻塞式获取任务
                task_info = await redis.brpop(queue_key, timeout=0)
                if not task_info:
                    continue
                
                _, task_data_json = task_info
                task_data = json.loads(task_data_json.decode('utf-8'))
                
                db_task_id = task_data.get("db_task_id")
                taskid = task_data.get("taskid")
                username = task_data.get("username")
                action = task_data.get("action")
                task_type_from_data = task_data.get("task_type")
                params = task_data.get("params")
                
                if not all([db_task_id, taskid, username, action, params, task_type_from_data]):
                    logger.error(f"从队列接收到无效的任务数据: {task_data}")
                    continue
                
                # 确认任务类型匹配
                if task_type_from_data != task_type:
                    logger.error(f"任务类型不匹配: 预期 {task_type}, 实际 {task_type_from_data}")
                    continue
                
                logger.info(f"开始处理任务: DB ID={db_task_id}, TaskID={taskid}, User={username}, Action={action}")
                
                # 处理任务
                await self._handle_task(db_task_id, taskid, username, task_type, action, params)
                
            except asyncio.CancelledError:
                logger.info(f"{task_type} 任务处理器被取消")
                break
            except json.JSONDecodeError as json_err:
                logger.error(f"无法解析来自队列的任务数据: {task_data_json}. 错误: {json_err}")
            except Exception as e:
                logger.critical(f"{task_type} 任务处理器主循环遇到错误: {e}", exc_info=True)
                logger.info(f"{task_type} 处理器将在 10 秒后尝试重启...")
                await asyncio.sleep(10)
    
    async def _handle_task(self, db_task_id: int, taskid: str, username: str, task_type: str, action: str, params: Dict[str, Any]):
        """
        处理单个任务
        
        参数:
        - db_task_id: 数据库任务ID
        - taskid: 任务ID
        - username: 用户名
        - task_type: 任务类型
        - action: 任务动作
        - params: 任务参数
        """
        handler = self.task_handlers.get(task_type)
        if not handler:
            logger.error(f"未找到 {task_type} 类型任务的处理器")
            return
        
        async with AsyncSessionLocal() as db:
            try:
                # 先检查任务是否已被取消
                stmt = select(Task).filter(Task.id == db_task_id)
                result = await db.execute(stmt)
                task = result.scalars().first()
                
                if not task:
                    logger.error(f"未找到任务 DB ID: {db_task_id}")
                    return
                
                # 如果任务已被取消，跳过处理
                if task.status == TaskStatus.CANCELED:
                    logger.info(f"任务 {taskid} (DB ID: {db_task_id}) 已被取消，跳过处理")
                    return
                
                # 更新任务状态为处理中
                await TaskManager.update_task_status(
                    db=db,
                    task_id=db_task_id,
                    status=TaskStatus.IN_PROGRESS
                )
                
                # 调用任务处理器
                try:
                    result = await handler({
                        "db_task_id": db_task_id,
                        "taskid": taskid,
                        "username": username,
                        "action": action,
                        "params": params,
                        "db": db  # 传递数据库会话，以便处理器可以更新任务
                    })
                    
                    # 处理结果
                    if result.get("success", False):
                        # 检查任务当前状态，如果已被取消则不更新状态
                        task_current = await TaskManager.get_task_status(
                            db=db,
                            taskid=taskid,
                            username=username
                        )
                        
                        if task_current and task_current.status == TaskStatus.CANCELED:
                            logger.info(f"任务 {taskid} (DB ID: {db_task_id}) 已被取消，不更新状态")
                            # 只更新结果，不更新状态
                            await TaskManager.update_task_result(
                                db=db,
                                task_id=db_task_id,
                                result=result.get("result"),
                                resource_url=result.get("resource_url"),
                                update_status=False
                            )
                        else:
                            # 任务成功且未被取消
                            await TaskManager.update_task_status(
                                db=db,
                                task_id=db_task_id,
                                status=TaskStatus.SUCCESS,
                                result=result.get("result"),
                                resource_url=result.get("resource_url")
                            )
                            logger.info(f"任务 {taskid} (DB ID: {db_task_id}) 成功完成")
                    else:
                        # 任务失败 - 再次检查是否已取消，避免覆盖取消状态
                        # 检查是否有特殊标记表明任务已在处理器中被标记为已取消
                        if result.get("task_already_canceled", False):
                            logger.info(f"任务 {taskid} (DB ID: {db_task_id}) 已在处理器中被标记为已取消，无需进一步处理")
                            # 已经在处理器中处理完毕，不需要进一步处理
                            return
                        
                        task_current = await TaskManager.get_task_status(
                            db=db,
                            taskid=taskid,
                            username=username
                        )
                        
                        if task_current and task_current.status == TaskStatus.CANCELED:
                            logger.info(f"任务 {taskid} (DB ID: {db_task_id}) 返回失败但已被取消，保留取消状态")
                            # 只更新结果，不更新状态
                            await TaskManager.update_task_result(
                                db=db,
                                task_id=db_task_id,
                                result=result.get("result"),
                                update_status=False
                            )
                        else:
                            # 任务确实失败且未被取消
                            await TaskManager.update_task_status(
                                db=db,
                                task_id=db_task_id,
                                status=TaskStatus.FAILURE,
                                fail_reason=result.get("error")
                            )
                            logger.error(f"任务 {taskid} (DB ID: {db_task_id}) 处理失败: {result.get('error')}")
                
                except Exception as handler_e:
                    logger.error(f"任务 {taskid} (DB ID: {db_task_id}) 处理器异常: {handler_e}", exc_info=True)
                    # 更新任务状态为失败
                    await TaskManager.update_task_status(
                        db=db,
                        task_id=db_task_id,
                        status=TaskStatus.FAILURE,
                        fail_reason=f"处理任务时发生内部错误: {str(handler_e)}"
                    )
            
            except Exception as db_e:
                logger.error(f"更新任务 {taskid} (DB ID: {db_task_id}) 状态时数据库错误: {db_e}", exc_info=True)
                # 尝试再次更新状态为失败
                try:
                    async with AsyncSessionLocal() as new_db:
                        await TaskManager.update_task_status(
                            db=new_db,
                            task_id=db_task_id,
                            status=TaskStatus.FAILURE,
                            fail_reason=f"数据库错误: {str(db_e)}"
                        )
                except Exception as e:
                    logger.critical(f"二次尝试更新任务 {taskid} (DB ID: {db_task_id}) 状态时再次失败: {e}", exc_info=True)
                    
    def check_status(self):
        """
        检查任务处理器状态
        
        返回:
        - 状态信息字典
        """
        status = {
            "registered_handlers": list(self.task_handlers.keys()),
            "active_handlers": {
                task_type: is_active 
                for task_type, is_active in self.active_processors.items()
            },
            "environment": {
                "AI_SERVER": self.ai_server,
                "REDIS_HOST": app_settings.redis_host,
                "REDIS_PORT": app_settings.redis_port,
                "DB_HOST": app_settings.db_host,
                "DB_PORT": app_settings.db_port,
                "PYTHON_PATH": sys.path,
            }
        }
        return status


# 全局任务处理器实例
task_processor = TaskProcessor()

# 动态导入和注册任务处理器函数
def init_task_processors():
    """初始化所有任务处理器"""
    logger.info(f"开始初始化任务处理器，当前工作目录: {os.getcwd()}")
    logger.info(f"Python路径: {sys.path}")
    
    # 尝试导入所有处理器模块
    try:
        # 音乐处理器
        logger.info("尝试导入音乐任务处理器...")
        try:
            from task_handlers.music_handler import handle_music_task
            task_processor.register_handler(TaskType.MUSIC, handle_music_task)
            logger.info("音乐任务处理器导入并注册成功")
        except ImportError as e:
            logger.error(f"导入音乐任务处理器失败: {e}", exc_info=True)
            # 尝试打印模块搜索路径
            music_module_paths = [
                os.path.join(path, "task_handlers", "music_handler.py") 
                for path in sys.path if os.path.exists(os.path.join(path, "task_handlers", "music_handler.py"))
            ]
            if music_module_paths:
                logger.info(f"找到可能的音乐处理器模块路径: {music_module_paths}")
            else:
                logger.error("未找到音乐处理器模块文件")
        
        # 视频处理器
        logger.info("尝试导入视频任务处理器...")
        try:
            from task_handlers.video_handler import handle_video_task
            task_processor.register_handler(TaskType.VIDEO, handle_video_task)
            logger.info("视频任务处理器导入并注册成功")
        except ImportError as e:
            logger.error(f"导入视频任务处理器失败: {e}", exc_info=True)
            # 尝试打印模块搜索路径
            video_module_paths = [
                os.path.join(path, "task_handlers", "video_handler.py") 
                for path in sys.path if os.path.exists(os.path.join(path, "task_handlers", "video_handler.py"))
            ]
            if video_module_paths:
                logger.info(f"找到可能的视频处理器模块路径: {video_module_paths}")
            else:
                logger.error("未找到视频处理器模块文件")
        
        # TODO: 导入其他处理器
        
        # 启动所有已注册的处理器
        registered_handlers = list(task_processor.task_handlers.keys())
        if registered_handlers:
            logger.info(f"成功注册的处理器: {registered_handlers}")
            for task_type in registered_handlers:
                task_processor.start_processor(task_type)
            logger.info("所有任务处理器初始化并启动完成")
        else:
            logger.error("没有任何处理器被成功注册，无法启动任务处理")
        
    except ImportError as e:
        logger.error(f"导入任务处理器模块失败: {e}", exc_info=True)
        logger.error(f"当前Python路径: {sys.path}")
        # 尝试检查task_handlers目录是否存在
        for path in sys.path:
            handlers_dir = os.path.join(path, "task_handlers")
            if os.path.exists(handlers_dir):
                logger.info(f"找到task_handlers目录: {handlers_dir}")
                # 列出目录内容
                try:
                    files = os.listdir(handlers_dir)
                    logger.info(f"task_handlers目录内容: {files}")
                except Exception as list_e:
                    logger.error(f"无法列出目录内容: {list_e}")
    except Exception as e:
        logger.error(f"初始化任务处理器时遇到错误: {e}", exc_info=True) 