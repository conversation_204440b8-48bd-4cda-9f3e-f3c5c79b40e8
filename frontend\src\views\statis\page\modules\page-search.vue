<script setup lang="ts">
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'StaticSearch'
});

interface Emits {
  (e: 'search'): void;
}
const emit = defineEmits<Emits>();

const { formRef } = useNaiveForm();

const filterType: CommonType.Option[] = [
  { label: '按日', value: 'date' },
  { label: '按月', value: 'month' }
];

const model = defineModel<Api.Statis.TaskStatisSearchParams>('model', {
  required: true
});
function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper pt-5">
    <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
      <NSpace>
        <NFormItem>
          <NSelect v-model:value="model.filter_type" :options="filterType" class="w-30" />
        </NFormItem>
        <NFormItem>
          <NDatePicker
            v-if="model.filter_type === 'month'"
            v-model:formatted-value="model.month"
            type="month"
            format="y-M"
            year-format="y"
            month-format="M"
            value-format="yyyyMM"
          />
          <NDatePicker
            v-if="model.filter_type === 'date'"
            v-model:formatted-value="model.date"
            type="date"
            format="y-M-dd"
            year-format="y"
            month-format="M"
            value-format="yyyyMMdd"
          />
        </NFormItem>
        <NFormItem>
          <NButton type="primary" ghost @click="search">
            <template #icon>
              <icon-ic-round-search class="text-icon" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </NFormItem>
      </NSpace>
    </NForm>
  </NCard>
</template>

<style scoped></style>
