import asyncio
import logging

from fastapi import Fast<PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware


logger = logging.getLogger(__name__)

class SemaphoreLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: FastAPI, path_limits: dict[str, int]):
        super().__init__(app)
        self.semaphores = {path: asyncio.Semaphore(limit) for path, limit in path_limits.items()}

    async def dispatch(self, request: Request, call_next):
        path = request.url.path
        if path in self.semaphores:
            logger.debug(f"Applying concurrency limit to {request.url.path}")
            async with self.semaphores[path]:
                response = await call_next(request)
        else:
            logger.debug(f"No concurrency limit for {request.url.path}")
            response = await call_next(request)
        return response
