import { request } from '../request';

export interface VoiceExampleItem {
  name: string;
  file_name: string;
  content: string;
  file_path: string;
}
export interface RoleVoiceExampleItem {
  [key: string]: VoiceExampleItem[];
}
export interface VoiceExampleResult {
  role: string[];
  voice_example: RoleVoiceExampleItem;
}
export interface GPTSoVITSTTSREsult {
  b64: string;
}
export interface CHATTTSREsult {
  url: string;
}
// ** 提取声音样例 */
export function getRoleVoiceExample() {
  return request<VoiceExampleResult>({
    url: '/media/get_role_voice_example',
    method: 'get'
  });
}

export function postTTS(data: any) {
  return request<GPTSoVITSTTSREsult>({
    url: '/media/tts',
    method: 'post',
    data
  });
}
export function postChatTTS(data: any) {
  return request<CHATTTSREsult>({
    url: '/media/chat_tts',
    method: 'post',
    data
  });
}

// ***************************************** COSY *****************************************

export interface SpeakersResult {
  default_speakers: Record<string, { audio: string; model: string }>;
  custom_speakers: Record<string, { audio: string; model: string }>;
}
export interface CosyResult {
  b64: string;
  type: string;
}
export interface SoVITSZeroShotResult {
  b64: string;
}
export interface SeparateResult {
  instrumental: string;
  vocals: string;
  type: string;
}

export function getSpeaker() {
  return request<SpeakersResult>({
    url: '/cosy/speaker',
    method: 'get'
  });
}
export function postSeparate(data: Api.Audio.Separate) {
  return request<SeparateResult>({
    url: '/media/voice_separate',
    method: 'post',
    data
  });
}
export function postCosyTast(data: Api.Audio.Cosy) {
  return request<CosyResult>({
    url: '/cosy/task',
    method: 'post',
    data
  });
}
export function postZeroShotTast(data: Api.Audio.Cosy) {
  return request<CosyResult>({
    url: '/cosy/zero_shot',
    method: 'post',
    data
  });
}

// SoVITS ZeroShot
export function postSoVITSZeroShotTast(data: Api.Audio.SoVITSZeroShot) {
  return request<SoVITSZeroShotResult>({
    url: '/media/sovits_zero_shot',
    method: 'post',
    data
  });
}

export function getUserModels() {
  return request({
    url: '/media/user_model',
    method: 'get'
  });
}

// ***************************************** 火山 *****************************************

export function postVolcanoTTS(data: Api.Audio.VolcanoTTS) {
  return request<Api.Audio.VolcanoTTSResult>({
    url: '/volcano/tts',
    method: 'post',
    data
  });
}

// ***************************************** 音频降噪 *****************************************
// 提交音频降噪任务
export function postReduction(data: Api.Audio.Reduction) {
  return request({
    url: '/reduction/noise_reduction',
    method: 'post',
    data
  });
}

// 查询音频降噪任务状态
export function getReductionStatus(job_id: string) {
  return request({
    url: `/reduction/reduction_status/${job_id}`,
    method: 'get'
  });
}

// 获取音频降噪任务历史
export function getHistory(page: number, size: number) {
  return request({
    url: '/reduction/history',
    method: 'get',
    params: {
      page,
      size
    }
  });
}

// ***************************************** clearvoice *****************************************
export function postEnhancement(data: Api.Audio.Separate) {
  return request({
    url: '/clearvoice/enhancement',
    method: 'post',
    data
  });
}

export function postSeparation(data: Api.Audio.Separate) {
  return request({
    url: '/clearvoice/separation',
    method: 'post',
    data
  });
}

// ***************************************** inspiremusic *****************************************
// Define a more specific type for the task data if not available globally
interface MusicTask {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_data?: { url: string; type: string; duration?: number; sample_rate?: number } | null; // Assuming audio_url is parsed into audio_data
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

// interface MusicTaskResponse {
//   code: string;
//   data?: MusicTask | null;
//   msg?: string;
// }

// interface MusicHistoryResponse {
//   code: string;
//   data?: MusicTask[] | null; // Expecting an array of tasks
//   msg?: string;
// }

interface InspireMusicGenerateData {
  text: string;
  music_title?: string;
  model_name?: string;
  chorus?: string;
  output_sample_rate?: number;
  max_generate_audio_seconds?: number;
}

interface InspireMusicContinueData extends InspireMusicGenerateData {
  audio_base64: string;
}

export function postMusicGenerate(data: InspireMusicGenerateData) {
  return request<MusicTask | null>({
    url: '/inspiremusic/generate',
    method: 'post',
    data
  });
}

export function postMusicContinue(data: InspireMusicContinueData) {
  return request<MusicTask | null>({
    url: '/inspiremusic/continue',
    method: 'post',
    data
  });
}

export function getMusicPrompts() {
  return request({
    url: '/inspiremusic/prompts',
    method: 'get'
  });
}

export function getMusicTaskStatus(taskid: string) {
  return request<MusicTask | null>({
    url: `/inspiremusic/task_status/${taskid}`,
    method: 'get'
  });
}

// 获取历史记录
export function getMusicHistory(page: number, size: number, title?: string) {
  return request<MusicTask[] | null>({
    url: '/inspiremusic/history',
    method: 'get',
    params: { page, size, title }
  });
}

// *** 音色库 ***

// 语言枚举
export enum Lang {
  CMN = '中文-普通话',
  YUE = '中文-粤语',
  ENG = '英语',
  JPN = '日语',
  KOR = '韩语'
}

// 音色信息
export interface SaveTone {
  id: number;
  name: string;
  gender: 0 | 1; // 0: 女性, 1: 男性
  lang: Lang;
  description: string;
  create_time: string;
  update_time: string;
  is_favorite: boolean;
  audio_url: string;
  prompt_text: string;
  is_reduction: boolean;
}

// 创建/修改音色请求
export interface ModifyToneReq {
  name: string;
  gender: 0 | 1;
  lang: Lang | null;
  description: string;
  audio_url: string;
  is_favorite: boolean;
  need_reduction?: boolean;
}

// 音色列表查询参数
export interface TonesListParams {
  size?: number;
  current?: number;
  gender?: 0 | 1;
  lang?: Lang;
  is_favorite?: number;
}

// 获取音色列表
export function getTonesList(params?: TonesListParams) {
  return request<Api.Common.PaginatingQueryRecord<SaveTone>>({
    url: '/tones/',
    method: 'get',
    params
  });
}

// 获取单个音色
export function getTone(toneId: number) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'get'
  });
}

// 创建音色
export function createTone(data: ModifyToneReq) {
  return request<SaveTone>({
    url: '/tones/',
    method: 'post',
    data
  });
}

// 更新音色
export function updateTone(toneId: number, data: ModifyToneReq) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'put',
    data
  });
}

// 删除音色
export function deleteTone(toneId: number) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'delete'
  });
}

// ***************************************** 音频合成 *****************************************

// 音色类型枚举
export enum ToneType {
  CUSTOM = 'custom',
  PRETRAINED = 'pretrained'
}

// 模型功能枚举
export enum ModelFunction {
  FAST_CLONE = 'fast_clone', // GPT-SoVITS 极速复刻 + CosyVoice 3S 极速复刻
  COSY_PRETRAINED = 'cosy_pretrained', // CosyVoice 预训练音色
  CHAT_TTS = 'chat_tts', // Chat TTS 语音合成
  GPT_SOVITS_TTS = 'gpt_sovits_tts', // GPT-SoVITS 语音合成
  VOLCENGINE = 'volcengine' // 火山语音
}

// 任务状态枚举
export enum SynthesisTaskStatus {
  SUBMITTED = 'SUBMITTED',
  IN_PROGRESS = 'IN_PROGRESS',
  CANCELED = 'CANCELED',
  FAILURE = 'FAILURE',
  SUCCESS = 'SUCCESS'
}

// 预训练音色分类
export interface PretrainedToneCategory {
  id: string;
  name: string;
  children?: PretrainedToneCategory[] | null;
}

// 音色信息
export interface SynthesisTone {
  tone_type: ToneType;
  tone_id: number | string;
  tone_name: string;
  description: string;
  model_function: ModelFunction;
  is_favorite: boolean;
  url: string;
  gender: 0 | 1 | null;
  lang: string | null;
  prompt_text: string;
}

// 修改收藏状态请求
export interface ModifyFavoriteStatusReq {
  tone_type: ToneType;
  is_favorite: boolean;
}

// 合成参数段
export interface ParamSeg {
  tone_type: ToneType;
  tone_id: number | string;
  model_function: ModelFunction;
  text: string;
  speed?: number; // 语速 0.5-2.0，默认1
  pitch?: number; // 声调 -12到12，默认0
}

// 创建任务请求
export interface CreateTaskReq {
  segs: ParamSeg[];
}

// 创建任务响应
export interface CreateTaskRes {
  task_ids: number[];
}

// 输出参数段
export interface ParamSegOut extends ParamSeg {
  tone_name: string;
  model_name: string;
  function_name: string;
}

// 输出参数
export interface ParamsOut {
  segs: ParamSegOut[];
}

// 任务项
export interface TaskItem {
  id: number;
  params: ParamsOut;
  url: string;
  duration: number;
  create_time: string;
  status: SynthesisTaskStatus;
}

// 音色列表查询参数
export interface TonesQueryParams {
  tone_type?: ToneType;
  category?: string;
  is_recent?: boolean;
  is_favorite?: boolean;
  size?: number;
  current?: number;
  search_text?: string;
}

// 任务列表查询参数
export interface TasksQueryParams {
  size?: number;
  current?: number;
}

// 获取预训练音色分类列表
export function getPretrainedToneCategories() {
  return request<PretrainedToneCategory[]>({
    url: '/synthesis/pretrained_tone_categories',
    method: 'get'
  });
}

// 获取音色列表（包含音色库音色和预训练音色）
export function getSynthesisTones(params?: TonesQueryParams) {
  return request<Api.Common.PaginatingQueryRecord<SynthesisTone>>({
    url: '/synthesis/tones',
    method: 'get',
    params
  });
}

// 更改音色收藏状态
export function changeToneFavoriteStatus(toneId: number | string, data: ModifyFavoriteStatusReq) {
  return request<SynthesisTone>({
    url: `/synthesis/tones/${toneId}/is_favorite`,
    method: 'post',
    data
  });
}

// 创建合成任务
export function createSynthesisTask(data: CreateTaskReq) {
  return request<CreateTaskRes>({
    url: '/synthesis/tasks',
    method: 'post',
    data
  });
}

// 获取合成任务信息
export function getSynthesisTask(taskId: number) {
  return request<TaskItem>({
    url: `/synthesis/tasks/${taskId}`,
    method: 'get'
  });
}

// 获取合成任务列表
export function getSynthesisTaskList(params?: TasksQueryParams) {
  return request<Api.Common.PaginatingQueryRecord<TaskItem>>({
    url: '/synthesis/tasks',
    method: 'get',
    params
  });
}

// 删除合成任务
export function deleteSynthesisTask(taskId: number) {
  return request<TaskItem>({
    url: `/synthesis/tasks/${taskId}`,
    method: 'delete'
  });
}
