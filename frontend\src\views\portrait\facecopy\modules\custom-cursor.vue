<script lang="ts">
import { computed, defineComponent, reactive, ref } from 'vue';

export default defineComponent({
  name: 'CustomCursor',
  setup() {
    const containerRef = ref<HTMLElement | null>(null);
    const cursorState = reactive({
      visible: false,
      size: 20,
      x: 0,
      y: 0
    });

    const onMouseMove = (event: MouseEvent) => {
      const rect = containerRef.value?.getBoundingClientRect();
      if (rect) {
        cursorState.x = event.clientX - rect.left - cursorState.size / 2;
        cursorState.y = event.clientY - rect.top - cursorState.size / 2;
      }
    };

    const onMouseEnter = () => {
      cursorState.visible = true;
    };

    const onMouseLeave = () => {
      cursorState.visible = false;
    };

    const onMouseWheel = (event: WheelEvent) => {
      cursorState.size += event.deltaY > 0 ? -5 : 5;
      cursorState.size = Math.max(5, Math.min(cursorState.size, 100));
    };

    const cursorStyle = computed(() => ({
      position: 'absolute',
      left: `${cursorState.x}px`,
      top: `${cursorState.y}px`,
      width: `${cursorState.size}px`,
      height: `${cursorState.size}px`,
      borderRadius: '50%',
      background: 'rgba(0, 0, 0, 0.5)',
      pointerEvents: 'none' as const
    }));

    return {
      containerRef,
      onMouseMove,
      onMouseEnter,
      onMouseLeave,
      onMouseWheel,
      cursorStyle,
      cursorState
    };
  }
});
</script>

<template>
  <div
    id="container"
    ref="containerRef"
    @mousemove="onMouseMove"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
    @wheel.prevent="onMouseWheel"
  >
    <slot></slot>
    <NDiv v-if="cursorState.visible" :style="cursorStyle" class="cursor-circle"></NDiv>
  </div>
</template>

<style scoped>
#container {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  left: 0;
  cursor: none;
  /* background-color: #f0f0f0; */
}
.cursor-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  pointer-events: none;
}
</style>
