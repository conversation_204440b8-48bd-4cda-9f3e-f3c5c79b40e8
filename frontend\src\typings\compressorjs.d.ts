// declare module 'compressorjs' {
//   interface CompressorOptions {
//     strict?: boolean;
//     checkOrientation?: boolean;
//     retainExif?: boolean;
//     maxWidth?: number;
//     maxHeight?: number;
//     minWidth?: number;
//     minHeight?: number;
//     width?: number;
//     height?: number;
//     resize?: 'none' | 'contain' | 'cover';
//     quality?: number;
//     mimeType?: string;
//     convertTypes?: string[] | string;
//     convertSize?: number;
//     beforeDraw?: (context: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => void;
//     drew?: (context: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => void;
//     success?: (result: Blob | File) => void;
//     error?: (err: Error) => void;
//   }

//   class Compressor {
//     constructor(file: File | Blob, options?: CompressorOptions);

//     abort(): void;
//     static noConflict(): void;
//   }

//   export default Compressor;
// }
