<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { type UploadFileInfo, useMessage } from 'naive-ui';
import { useRoute } from 'vue-router';
import {
  cancelVideoTask,
  deleteVideoTask,
  generateFramepackVideo,
  generateVolcengineVideo,
  getVideoHistory,
  getVideoTaskStatus
} from '@/service/api/video';
import { generateCosKey, uploadToCOS } from '@/utils/tencentCos';
import { ParamsFillBack, decodeParams } from '@/utils/paramsFillBack';
import {
  extractFramepackFormData,
  restoreFramepackFormCache,
  saveFramepackFormCache,
  validateFramepackCacheData
} from '@/utils/formCache';
import { AiCapacity } from '@/utils/AiCapacity';
import VideoInfoCard from './modules/video-infoCard.vue';
import ModelSelect from './modules/model-select.vue';

const message = useMessage();
const route = useRoute();
const mediaPreview = ref<string | null>(null);
const mediaType = ref<'image' | null>(null);
const uploading = ref<boolean>(false);
const uploadedUrl = ref<string | null>(null);
const videoDescription = ref<string>('');
const videoSeconds = ref<number>(4);
const videoSeed = ref<string>('31337');

// 模型选择相关
const selectedModel = ref<string>('volcengine'); // 默认选择即梦AI
const selectedSubModel = ref<string>('img'); // 默认选择图生视频模型

// 计算属性：判断是否为文生视频模式
const isTextToVideoMode = computed(() => {
  return selectedSubModel.value === 'text';
});

// 计算属性：判断是否禁用生成秒数输入框（即梦AI模式下禁用）
const isSecondsDisabled = computed(() => {
  return selectedModel.value === 'volcengine';
});

// 历史记录相关
const historyItems = ref<any[]>([]);
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const loading = ref<boolean>(false);
const hasMore = ref<boolean>(true);
const taskGenerating = ref<boolean>(false);

// 处理平台和模型变化
const handlePlatformChange = (platform: string, model: string) => {
  selectedModel.value = platform;
  selectedSubModel.value = model;
};

// 监听子模型变化，文生视频模式下清空图片
watch(selectedSubModel, newValue => {
  if (newValue === 'text') {
    // 如果切换到文生视频模式，清空图片相关数据
    mediaPreview.value = null;
    uploadedUrl.value = null;
    mediaType.value = null;
  }
});

// 生成任务相关
interface GeneratingTask {
  taskId: string;
  status: string;
  imageUrl: string;
  prompt: string;
  secondLength: number;
  progress: number;
  startTime: number;
  isGenerating: boolean;
  videoUrl: string;
  fail_reason?: string;
  queue_position?: number;
  task_params?: any;
  submit_time?: string;
  prompt_media_url?: string;
  model?: string; // 添加模型字段
  model_name?: string; // 添加模型显示名称
}

const generatingTasks = reactive(new Map<string, GeneratingTask>());
const pollingInterval = ref<number | null>(null);

interface CustomRequestOptions {
  file: UploadFileInfo;
  onFinish: () => void;
}

// 根据开始时间计算模拟进度
/**
 * 根据任务开始时间计算模拟进度
 *
 * @param startTime 任务开始时间的时间戳（毫秒）
 * @param previousProgress 之前计算的进度值，用于确保进度不会后退
 * @returns 计算后的进度值（0-100）
 */
const calculateProgressByTime = (startTime: number, previousProgress = 0): number => {
  // 如果开始时间无效，返回0（不再保留旧进度）
  if (!startTime || startTime <= 0) {
    return 0;
  }

  const now = new Date().getTime();
  // 如果开始时间在未来，返回0
  if (startTime > now) {
    return 0;
  }

  const elapsedMinutes = (now - startTime) / (1000 * 60); // 经过的分钟数
  const totalMinutes = 13; // 总共13分钟

  // 计算进度，最大为99（除非任务完成）
  const calculatedProgress = Math.min(Math.floor((elapsedMinutes / totalMinutes) * 100), 99);

  // 确保返回的进度不低于之前的进度值
  return Math.max(calculatedProgress, previousProgress);
};

// 停止轮询
const stopPolling = (): void => {
  if (pollingInterval.value !== null) {
    window.clearInterval(pollingInterval.value);
    pollingInterval.value = null;
  }
};

// 读取回填数据
const loadFillBackParams = () => {
  const s = route.query[ParamsFillBack];
  if (!s) {
    return;
  }
  if (Array.isArray(s)) {
    return;
  }
  try {
    const params = decodeParams<{ prompt: string; prompt_img: string }>(s);
    if (params.prompt) {
      videoDescription.value = params.prompt;
    }
    if (params.prompt_img) {
      uploadedUrl.value = params.prompt_img;
      mediaType.value = 'image';
      mediaPreview.value = params.prompt_img;

      // 有图片时，自动设置为图生视频模式
      selectedSubModel.value = 'img';
    }
  } catch (e) {
    console.error('无法解析回填参数', e);
  }
};

// 处理表单缓存恢复
const handleFormCacheRestore = () => {
  // 检查路由守卫设置的恢复标记
  if ((window as any).FRAMEPACK_CACHE_RESTORE) {
    try {
      const cachedData = restoreFramepackFormCache();
      if (cachedData && validateFramepackCacheData(cachedData)) {
        console.log('恢复表单缓存数据:', cachedData);

        // 恢复表单数据
        mediaPreview.value = cachedData.mediaPreview;
        uploadedUrl.value = cachedData.uploadedUrl;
        mediaType.value = cachedData.mediaType;
        videoDescription.value = cachedData.videoDescription;
        videoSeconds.value = cachedData.videoSeconds;
        videoSeed.value = cachedData.videoSeed;
        selectedModel.value = cachedData.selectedModel;
        selectedSubModel.value = cachedData.selectedSubModel;

        // message.success('已恢复表单数据');
      }
    } catch (error) {
      console.error('恢复表单缓存失败:', error);
    }

    // 清除恢复标记
    delete (window as any).FRAMEPACK_CACHE_RESTORE;
  }
};

// 处理表单缓存保存
const handleFormCacheSave = () => {
  // 检查路由守卫设置的保存标记
  if ((window as any).FRAMEPACK_CACHE_SAVE) {
    try {
      // 提取当前表单数据
      const formData = extractFramepackFormData({
        mediaPreview: mediaPreview.value,
        uploadedUrl: uploadedUrl.value,
        mediaType: mediaType.value,
        videoDescription: videoDescription.value,
        videoSeconds: videoSeconds.value,
        videoSeed: videoSeed.value,
        selectedModel: selectedModel.value,
        selectedSubModel: selectedSubModel.value
      });

      // 保存到缓存
      saveFramepackFormCache(formData);
      console.log('已保存表单缓存数据:', formData);
    } catch (error) {
      console.error('保存表单缓存失败:', error);
    }

    // 清除保存标记
    delete (window as any).FRAMEPACK_CACHE_SAVE;
  }
};

// 加载历史记录函数声明（定义在后面）
let loadHistory: (reset?: boolean) => Promise<void>;

// 检查是否所有任务都不在生成状态
const allTasksCompleted = (): boolean => {
  return Array.from(generatingTasks.values()).every(t => !t.isGenerating);
};

// 更新任务状态的辅助函数
interface TaskUpdateOptions {
  status: string;
  videoUrl?: string;
  isGenerating?: boolean;
  progress?: number;
  failReason?: string;
  queuePosition?: number;
  taskParams?: any;
  promptMediaUrl?: string;
  submitTime?: string;
  startTime?: number;
}

const updateTaskStatus = (taskId: string, task: GeneratingTask, options: TaskUpdateOptions): void => {
  generatingTasks.set(taskId, {
    ...task,
    status: options.status,
    isGenerating: options.isGenerating !== undefined ? options.isGenerating : task.isGenerating,
    progress: options.progress !== undefined ? options.progress : task.progress,
    videoUrl: options.videoUrl || task.videoUrl,
    fail_reason: options.failReason || task.fail_reason,
    queue_position: options.queuePosition !== undefined ? options.queuePosition : task.queue_position,
    task_params: options.taskParams || task.task_params,
    imageUrl: task.imageUrl,
    prompt_media_url: options.promptMediaUrl || task.prompt_media_url,
    submit_time: options.submitTime || task.submit_time,
    startTime: options.startTime || task.startTime,
    model: task.model
  });
};

// 处理进行中状态的任务
const handleInProgressStatus = (taskId: string, task: GeneratingTask, responseData: any, progress: number): void => {
  const failReason = responseData.fail_reason;
  const queuePosition = responseData.queue_position;
  const taskParams = responseData.task_params || task.task_params;
  const promptMediaUrl = responseData.prompt_media_url || task.prompt_media_url;
  const submitTime = responseData.submit_time || task.submit_time;

  if (task.status === 'submitted') {
    // 任务从排队状态转为生成中状态，更新startTime为后端返回的start_time或当前时间
    const newStartTime = responseData.start_time ? new Date(responseData.start_time).getTime() : new Date().getTime();

    updateTaskStatus(taskId, task, {
      status: 'in_progress',
      isGenerating: true,
      progress,
      failReason,
      queuePosition,
      taskParams,
      promptMediaUrl,
      submitTime,
      startTime: newStartTime // 更新开始时间
    });
  } else {
    // 保持进行中状态，更新其他信息
    updateTaskStatus(taskId, task, {
      status: 'in_progress',
      isGenerating: true,
      progress,
      failReason,
      queuePosition,
      taskParams,
      promptMediaUrl,
      submitTime
    });
  }
};

// 处理任务状态更新的辅助函数
const handleTaskStatusUpdate = (
  taskId: string,
  task: GeneratingTask,
  responseData: any,
  progress: number
): { completed: boolean; status?: string } => {
  const status = responseData.status;
  const videoUrl = responseData.video_url || '';
  const taskParams = responseData.task_params || task.task_params;
  const promptMediaUrl = responseData.prompt_media_url || task.prompt_media_url;
  const queuePosition = responseData.queue_position;
  const failReason = responseData.fail_reason;
  const submitTime = responseData.submit_time || task.submit_time;

  // 根据状态更新任务
  switch (status) {
    case 'success':
      // 任务完成
      updateTaskStatus(taskId, task, {
        status,
        videoUrl,
        isGenerating: false,
        progress: 100,
        failReason,
        queuePosition,
        taskParams,
        promptMediaUrl,
        submitTime
      });
      return { completed: true, status };

    case 'failure':
      // 任务失败
      updateTaskStatus(taskId, task, {
        status,
        isGenerating: false,
        progress: 0,
        failReason: failReason || '未知错误',
        queuePosition,
        taskParams,
        promptMediaUrl,
        submitTime
      });
      return { completed: true, status };

    case 'canceled':
      // 任务已取消
      updateTaskStatus(taskId, task, {
        status,
        isGenerating: false,
        failReason: failReason || '用户取消任务',
        queuePosition,
        taskParams,
        promptMediaUrl,
        submitTime
      });
      return { completed: true, status };

    case 'in_progress':
      handleInProgressStatus(taskId, task, responseData, progress);
      break;

    case 'submitted':
      // 任务排队中，进度为0
      updateTaskStatus(taskId, task, {
        status,
        isGenerating: true,
        progress: 0,
        failReason,
        queuePosition,
        taskParams,
        promptMediaUrl,
        submitTime
      });
      break;

    default:
      // 未知状态，保持当前任务状态，只更新从服务器获取的信息
      console.warn(`任务 ${taskId} 返回未知状态: ${status}`);
      updateTaskStatus(taskId, task, {
        status: status || task.status, // 如果状态为空，保持原状态
        failReason,
        queuePosition,
        taskParams,
        promptMediaUrl,
        submitTime
      });
  }

  return { completed: false };
};

// 计算任务进度
const calculateTaskProgress = (task: GeneratingTask, responseData: any): number => {
  if (responseData.status !== 'in_progress') {
    return 0;
  }

  const startTime = responseData.start_time || task.startTime;

  // 如果任务状态从submitted变为in_progress，使用后端返回的start_time或当前时间作为开始时间
  // 否则使用已有的startTime
  let effectiveStartTime;

  if (task.status === 'submitted') {
    // 从排队转为进行中状态
    effectiveStartTime = responseData.start_time ? new Date(responseData.start_time).getTime() : new Date().getTime();
  } else {
    // 保持进行中状态
    effectiveStartTime = startTime ? new Date(startTime).getTime() : task.startTime;
  }

  return calculateProgressByTime(effectiveStartTime, task.progress);
};

// 轮询单个任务状态
const pollSingleTask = async (
  taskId: string,
  task: GeneratingTask
): Promise<{ completed: boolean; status?: string; error?: unknown }> => {
  try {
    // 实际查询任务状态
    const response: any = await getVideoTaskStatus(taskId);

    if (!response || !response.data) {
      // API请求返回空数据，保持当前状态不变
      console.warn(`任务 ${taskId} 状态查询返回空数据`);
      return { completed: false };
    }

    // 计算任务进度
    const progress = calculateTaskProgress(task, response.data);

    // 处理任务状态更新
    return handleTaskStatusUpdate(taskId, task, response.data, progress);
  } catch (error) {
    console.error(`轮询任务 ${taskId} 状态失败:`, error);
    // 发生错误时，不更新任务状态，保持当前状态
    // 这样可以防止因网络问题导致任务状态错误变化
    return { completed: false, error };
  }
};

// 轮询任务状态
const pollTaskStatus = async (): Promise<void> => {
  if (generatingTasks.size === 0) {
    stopPolling();
    return;
  }

  // 创建任务数组以便并行处理
  const tasks = Array.from(generatingTasks.entries());
  const completedTasks: { taskId: string; status: string }[] = [];

  // 并行处理所有任务轮询
  const promises = tasks.map(async ([taskId, task]) => {
    const result = await pollSingleTask(taskId, task);
    // 只添加成功、失败或取消的任务
    if (
      result.completed &&
      result.status &&
      (result.status === 'success' || result.status === 'failure' || result.status === 'canceled')
    ) {
      completedTasks.push({ taskId, status: result.status });
    }
  });

  await Promise.all(promises);

  // 处理完成的任务
  if (completedTasks.length > 0) {
    // 延迟移除已完成、失败或取消的任务
    completedTasks.forEach(({ taskId }) => {
      setTimeout(() => {
        generatingTasks.delete(taskId);
        // 如果所有任务都已完成，停止轮询
        if (allTasksCompleted()) {
          stopPolling();
        }
      }, 3000);
    });

    // 重新加载历史记录
    await loadHistory(true);
  }
};

// 启动轮询
const startPolling = (): void => {
  if (pollingInterval.value !== null) return;

  pollingInterval.value = window.setInterval(() => {
    pollTaskStatus();
  }, 5000); // 每5秒轮询一次
};

// 加载历史记录
loadHistory = async (reset = false): Promise<void> => {
  if (loading.value || (!hasMore.value && !reset)) return;

  try {
    loading.value = true;
    // console.log(`加载历史记录: 页码=${currentPage.value}, 每页数量=${pageSize.value}`);

    // 如果是重置，将页码重置为1
    if (reset) {
      currentPage.value = 1;
      historyItems.value = [];
      hasMore.value = true;
    }

    const response: any = await getVideoHistory(currentPage.value, pageSize.value);

    if (response && response.data) {
      // 添加历史项目
      if (currentPage.value === 1) {
        historyItems.value = Array.isArray(response.data) ? [...response.data] : [];
      } else {
        historyItems.value = [...historyItems.value, ...(Array.isArray(response.data) ? response.data : [])];
      }

      // 检查是否还有更多数据
      hasMore.value = Array.isArray(response.data) && response.data.length >= pageSize.value;
      // console.log(`历史记录加载完成: 加载了${response.data.length}条数据, 是否有更多: ${hasMore.value}`);

      // 为所有进行中的任务添加到轮询队列
      if (Array.isArray(response.data)) {
        response.data.forEach((item: any) => {
          if (item.status === 'submitted' || item.status === 'in_progress') {
            if (!generatingTasks.has(item.taskid)) {
              // 根据任务状态设置isGenerating和progress
              const isGenerating = item.status === 'in_progress' || item.status === 'submitted';
              const progress =
                item.status === 'in_progress'
                  ? calculateProgressByTime(new Date(item.start_time || item.submit_time || '').getTime())
                  : 0; // 使用start_time计算进度，如果没有则回退到submit_time

              generatingTasks.set(item.taskid, {
                taskId: item.taskid,
                status: item.status,
                imageUrl: '',
                prompt: item.prompt || '',
                secondLength: item.task_params?.total_second_length || 5,
                progress,
                startTime: new Date(item.start_time || item.submit_time || '').getTime(),
                isGenerating,
                videoUrl: '',
                fail_reason: item.fail_reason,
                queue_position: item.queue_position,
                task_params: item.task_params,
                submit_time: item.submit_time,
                prompt_media_url: item.prompt_media_url || item.task_params?.image_base64 || '',
                model: item.model || 'framepack'
              });
            }
          }
        });
      }

      // 启动轮询（如果有需要轮询的任务）
      if (generatingTasks.size > 0) {
        startPolling();
      }

      // 增加页码
      if (hasMore.value) {
        currentPage.value++;
      }
    } else {
      // message.error(response?.msg || '获取历史记录失败');
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    // message.error('获取历史记录发生错误');
  } finally {
    loading.value = false;
  }
};

// 构建视频生成请求参数
const buildVideoParams = (): any => {
  const params: any = {
    prompt: videoDescription.value,
    total_second_length: videoSeconds.value,
    seed: Number(videoSeed.value),
    model: selectedModel.value,
    task_params: {
      model: selectedModel.value,
      // 如果是即梦AI，添加子模型类型
      ...(selectedModel.value === 'volcengine' && { subModel: selectedSubModel.value }),
      prompt: videoDescription.value,
      total_second_length: videoSeconds.value,
      seed: Number(videoSeed.value)
    }
  };

  // 图生视频模式下才添加图片参数
  if (!isTextToVideoMode.value && uploadedUrl.value) {
    params.image_base64 = uploadedUrl.value;
    params.task_params.image_base64 = uploadedUrl.value;
  }

  return params;
};

// 创建生成任务对象
const createGeneratingTask = (response: any, params: any): void => {
  const taskId = response.data.taskid;
  // 确保使用服务器返回的状态，默认为submitted
  const taskStatus = response.data.status || 'submitted';
  // 获取模型信息
  const model = response.data.model || selectedModel.value;
  const modelName = response.data.model_name || (selectedModel.value === 'volcengine' ? '即梦AI' : 'FramePack');

  // 占位图
  const imageUrl = '';
  // 服务器返回的图片URL，初始为空，等待轮询更新
  const promptMediaUrl =
    response.data.prompt_media_url ||
    'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202505/64c7ca42cb11a33215346f73eea52c16_20250528205813.png';

  // 创建任务参数对象，包含image_base64字段用于备选显示
  const taskParams = params.task_params || {
    prompt: videoDescription.value,
    total_second_length: videoSeconds.value,
    seed: Number(videoSeed.value),
    image_base64: uploadedUrl.value, // 保留用户上传的图片URL作为备选
    model // 添加模型参数到任务参数中
  };

  generatingTasks.set(taskId, {
    taskId,
    status: taskStatus,
    imageUrl,
    prompt_media_url: promptMediaUrl,
    prompt: videoDescription.value,
    secondLength: videoSeconds.value,
    progress: 0,
    startTime: response.data.start_time ? new Date(response.data.start_time).getTime() : new Date().getTime(),
    isGenerating: taskStatus === 'in_progress' || taskStatus === 'submitted' || taskStatus === 'not_start',
    videoUrl: '',
    task_params: taskParams,
    queue_position: response.data.queue_position || 0,
    submit_time: response.data.submit_time || new Date().toISOString(),
    fail_reason: response.data.fail_reason || '',
    model, // 添加模型信息
    model_name: modelName // 添加模型显示名称
  });
};

// 提交生成视频请求
const submitVideoGeneration = async (): Promise<void> => {
  // 图生视频模式下才检查图片上传
  if (!isTextToVideoMode.value && !uploadedUrl.value) {
    message.error('请先上传图片');
    return;
  }

  if (!videoDescription.value.trim()) {
    message.error('请输入视频描述');
    return;
  }

  try {
    taskGenerating.value = true;

    // 构建请求参数
    const params = buildVideoParams();

    // 根据选择的模型调用不同的API
    let response: any;
    if (selectedModel.value === 'volcengine') {
      // 调用即梦AI接口
      response = await generateVolcengineVideo(params);
    } else {
      // 调用FramePack接口
      response = await generateFramepackVideo(params);
    }

    if (response && response.data) {
      message.success('已提交视频生成请求');

      // 创建并添加生成任务
      createGeneratingTask(response, params);

      // 延迟1秒后设置loading状态为false
      setTimeout(() => {
        taskGenerating.value = false;
      }, 1000);

      // 确保轮询已启动
      startPolling();

      // 重新加载历史记录
      await loadHistory(true);
    } else {
      // message.error(response?.msg || '提交请求失败');
      taskGenerating.value = false;
    }
  } catch (error) {
    console.error('生成请求失败:', error);
    // message.error('提交请求发生错误，请重试');
    taskGenerating.value = false;
  }
};

// 下载视频
const downloadVideo = (url: string): void => {
  window.open(url, '_blank');
};

// 使用同款参数
const useVideoParams = (params: {
  imageUrl: string;
  prompt: string;
  secondLength: number;
  hasImage: boolean;
  model: string;
}): void => {
  videoDescription.value = params.prompt;
  videoSeconds.value = params.secondLength;

  // 根据是否有图片选择不同的模型
  if (params.hasImage) {
    // 图生视频模式
    mediaPreview.value = params.imageUrl;
    uploadedUrl.value = params.imageUrl;
    mediaType.value = 'image';

    // 如果是即梦AI，设置为img子模型
    if (params.model === 'volcengine') {
      selectedSubModel.value = 'img';
    }
    selectedModel.value = params.model;
  } else {
    // 文生视频模式 - 清空图片相关信息
    mediaPreview.value = null;
    uploadedUrl.value = null;
    mediaType.value = null;

    // 强制切换到文生视频模式
    selectedModel.value = 'volcengine';
    selectedSubModel.value = 'text';
  }
};

const beforeMediaUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): boolean => {
  const file = data.file.file;
  if (!file) {
    message.error('无法获取文件');
    return false;
  }
  const fileType = file.type || '';
  if (!fileType.startsWith('image/')) {
    message.error('仅支持上传图片格式的文件');
    return false;
  }
  return true;
};

const customMediaRequest = ({ file, onFinish }: CustomRequestOptions): void => {
  if (!file.file) {
    message.error('上传文件无效');
    onFinish();
    return;
  }

  const currentFile = file.file;
  const reader = new FileReader();
  reader.onload = async e => {
    if (e.target && e.target.result) {
      const previewUrl = URL.createObjectURL(new Blob([e.target.result as ArrayBuffer], { type: currentFile.type }));
      mediaPreview.value = previewUrl;
      mediaType.value = 'image';

      uploading.value = true;
      uploadedUrl.value = null;
      const key = generateCosKey(currentFile);

      try {
        const url = await uploadToCOS(currentFile, key);
        uploadedUrl.value = url;
        message.success('上传成功');
        // console.log('Uploaded URL:', url);
      } catch (error) {
        message.error('上传失败');
        console.error('Upload error:', error);
        // 清空预览和URL
        mediaPreview.value = null;
        uploadedUrl.value = null;
        mediaType.value = null;
      } finally {
        uploading.value = false;
        onFinish();
      }
    } else {
      message.error('文件读取失败');
      onFinish();
    }
  };
  reader.onerror = () => {
    message.error('文件读取出错');
    onFinish();
  };
  reader.readAsArrayBuffer(currentFile);
};

// 页面加载时获取历史记录和恢复表单缓存
onMounted(async () => {
  loadFillBackParams();

  // 检测并恢复表单缓存
  handleFormCacheRestore();

  await loadHistory();
});

// 页面卸载前清除轮询器和处理表单缓存
onBeforeUnmount(() => {
  // 检测并保存表单缓存
  handleFormCacheSave();

  stopPolling();
});

// 处理无限滚动加载事件
const handleInfiniteScroll = (): void => {
  if (hasMore.value && !loading.value) {
    loadHistory();
  }
};

// 合并生成中的任务和历史记录，按提交时间降序排序
const allTasks = computed(() => {
  // 将generatingTasks转换为数组
  const generatingTasksArray = Array.from(generatingTasks.entries()).map(([_taskId, task]) => {
    // 不再处理task_params.image_base64
    const taskParams = task.task_params || {};

    return {
      taskid: task.taskId,
      video_url: task.videoUrl,
      prompt_media_url: task.prompt_media_url || '',
      imageUrl: task.imageUrl || '',
      prompt: task.prompt,
      status: task.status,
      fail_reason: task.fail_reason || '',
      queue_position: task.queue_position || 0,
      task_params: taskParams,
      submit_time: task.submit_time || '',
      isGenerating: task.isGenerating,
      progress: task.progress,
      model: task.model || 'framepack', // 添加模型信息默认值
      model_name: task.model_name || 'FramePack' // 添加模型显示名称默认值
    };
  });

  // 创建一个生成中任务ID的集合，用于后续去重
  const generatingTaskIds = new Set(generatingTasksArray.map(task => task.taskid));

  // 复制历史记录数组，并为每个项目添加isGenerating和progress属性
  // 过滤掉已经在generatingTasks中的任务，避免重复
  const historyItemsArray = historyItems.value
    .filter(item => !generatingTaskIds.has(item.taskid))
    .map(item => {
      // 不再处理task_params.image_base64
      const taskParams = item.task_params || {};

      return {
        ...item,
        imageUrl: item.imageUrl || '',
        task_params: taskParams,
        isGenerating: false,
        progress: 100,
        model: taskParams.model || item.model || 'framepack', // 优先使用task_params.model
        model_name: item.model_name || (taskParams.model === 'volcengine' ? '即梦AI' : 'FramePack') // 添加模型显示名称默认值
      };
    });

  // 合并两个数组
  const combined = [...generatingTasksArray, ...historyItemsArray];

  // 按提交时间降序排序
  return combined.sort((a, b) => {
    const timeA = new Date(a.submit_time || 0).getTime();
    const timeB = new Date(b.submit_time || 0).getTime();
    return timeB - timeA;
  });
});

// 取消任务
const cancelVideoGeneration = async (taskId: string): Promise<void> => {
  try {
    const response: any = await cancelVideoTask(taskId);
    if (!response) {
      // message.error(response?.msg || '取消任务失败');
      return;
    }

    message.success('任务已取消');

    // 如果任务不在生成中列表，直接返回
    if (!generatingTasks.has(taskId)) {
      await loadHistory(true);
      return;
    }

    const task = generatingTasks.get(taskId);
    if (!task) {
      await loadHistory(true);
      return;
    }

    // 更新任务状态
    generatingTasks.set(taskId, {
      ...task,
      status: 'canceled',
      isGenerating: false,
      fail_reason: '用户取消任务'
    });

    // 检查是否需要停止轮询
    if (allTasksCompleted()) {
      stopPolling();
    }

    // 重新加载历史记录
    await loadHistory(true);
  } catch (error) {
    console.error('取消任务失败:', error);
    // message.error('取消任务时发生错误');
  }
};

// 删除任务
const deleteVideoGeneration = async (taskId: string): Promise<void> => {
  try {
    const response: any = await deleteVideoTask(taskId);
    if (!response) {
      // message.error(response?.msg || '删除任务失败');
      return;
    }

    message.success('记录已删除');

    // 如果任务在生成中列表，移除它
    if (generatingTasks.has(taskId)) {
      generatingTasks.delete(taskId);

      // 检查是否需要停止轮询
      if (allTasksCompleted()) {
        stopPolling();
      }
    }

    // 重新加载历史记录
    await loadHistory(true);
  } catch (error) {
    console.error('删除记录失败:', error);
    // message.error('删除任务时发生错误');
  }
};
</script>

<template>
  <NFlex class="h-full" :wrap="false">
    <NCard class="h-full max-w-[550px] w-1/3 flex flex-col">
      <ModelSelect
        v-model="selectedModel"
        :sub-model-value="selectedSubModel"
        @platform-change="handlePlatformChange"
        @sub-model-change="(value: string) => (selectedSubModel = value)"
      />

      <NUpload
        v-if="!isTextToVideoMode"
        :custom-request="customMediaRequest"
        :default-upload="true"
        accept="image/*,video/*"
        :multiple="false"
        :show-file-list="false"
        class="uploadbox mt-3"
        @before-upload="beforeMediaUpload"
      >
        <NUploadDragger class="upload-dragger-container">
          <div class="media-preview-container">
            <template v-if="mediaPreview">
              <template v-if="mediaType === 'image'">
                <NImage :src="mediaPreview" class="preview-image" object-fit="contain" preview-disabled />
              </template>

              <!--
                <template v-else>
                  <video :src="mediaPreview" controls class="preview-video"></video>
                </template>
                -->

              <NSpin
                v-if="uploading"
                class="absolute inset-0 flex items-center justify-center bg-black/30"
                size="large"
              />
            </template>
            <div v-else>
              <div class="flex justify-center">
                <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
              </div>
              <NText>点击或者拖动预处理图片到该区域进行上传</NText>
              <NP depth="3">请不要上传不支持格式的文件。</NP>
            </div>
          </div>
        </NUploadDragger>
      </NUpload>

      <div class="h-full flex flex-col">
        <NInput
          v-model:value="videoDescription"
          type="textarea"
          placeholder="请输入视频描述"
          class="message-input my-3 flex-grow-2"
          maxlength="2000"
          show-count
        >
          <template #suffix>
            <!-- <NButton type="primary" size="small" text disabled> -->
            <!-- <template #icon> -->
            <!-- <SvgIcon icon="game-icons:moebius-star" /> -->
            <!-- </template> -->
            <!-- </NButton> -->
          </template>
        </NInput>

        <div class="mt-3 flex-shrink-0">
          <NFlex align="center" justify="right">
            <NInputNumber
              v-model:value="videoSeconds"
              class="flex-1"
              :min="1"
              :max="10"
              :step="1"
              :precision="0"
              :disabled="isSecondsDisabled"
            >
              <template #prefix>生成秒数：</template>
            </NInputNumber>

            <NInput v-model:value="videoSeed" class="flex-1">
              <template #prefix>Seed：</template>
            </NInput>
          </NFlex>
          <ModelPrice v-slot="priceProps" :capacity="AiCapacity.VIDEO_GENERATION" :model="selectedModel">
            <NButton
              type="info"
              class="mt-3 w-full"
              :loading="taskGenerating || priceProps.loading"
              :disabled="(isTextToVideoMode ? false : !uploadedUrl) || !priceProps.creditEnough"
              @click="submitVideoGeneration"
            >
              <PriceIndicator :credit="priceProps.credit" />
              生成视频
            </NButton>
          </ModelPrice>
        </div>
      </div>
    </NCard>

    <NInfiniteScroll
      class="h-full max-w-[1200px] w-2/3 overflow-auto"
      :distance="50"
      :has-more="hasMore"
      @load="handleInfiniteScroll"
    >
      <div class="p-4">
        <!-- 按提交时间降序显示所有任务 -->
        <template v-for="task in allTasks" :key="`task-${task.taskid}`">
          <VideoInfoCard
            class="mb-3"
            :is-generating="task.isGenerating"
            :progress="task.progress"
            :video-url="task.video_url"
            :image-url="task.imageUrl || ''"
            :prompt="task.prompt"
            :task-id="task.taskid"
            :second-length="task.task_params?.total_second_length || 5"
            :status="task.status"
            :fail-reason="task.fail_reason || ''"
            :queue-position="task.queue_position || 0"
            :task-params="task.task_params || {}"
            :submit-time="task.submit_time || ''"
            :prompt-media-url="task.prompt_media_url || ''"
            :model="task.model || 'framepack'"
            :model-name="task.model_name || 'FramePack'"
            @generate-similar="useVideoParams"
            @download="downloadVideo"
            @cancel="cancelVideoGeneration"
            @delete="deleteVideoGeneration"
          />
        </template>

        <!-- 加载状态 -->
        <NSpin v-if="loading" class="my-4 flex justify-center" />
        <NEmpty v-if="historyItems.length === 0 && !loading && generatingTasks.size === 0" description="暂无视频记录" />
      </div>
    </NInfiniteScroll>
  </NFlex>
</template>

<style scoped lang="scss">
.message-input :deep(.n-input-wrapper) {
  flex-direction: column;
}

.message-input :deep(.n-input__suffix),
.message-input :deep(.n-input__prefix) {
  display: block;
  margin: 0;
}

/* 修改上传框样式 */
:deep(.uploadbox) {
  height: 200px;
  min-height: 200px;
  max-height: 200px;
  display: flex;
  overflow: hidden; /* 确保内容不溢出 */
  flex: 0 0 auto; /* 禁止flex扩展 */
}

:deep(.uploadbox) .n-upload-trigger {
  width: 100%;
  height: 100%;
}

:deep(.upload-dragger-container) {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px; /* 添加内边距 */
  box-sizing: border-box; /* 确保padding不增加整体尺寸 */
}

.media-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column; /* 恢复垂直排列 */
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative; /* 为绝对定位做准备 */
}

/* 确保预览图片不会撑开容器 */
.preview-image,
.preview-video {
  max-width: 100%;
  max-height: 250px; /* 限制图片最大高度 */
  object-fit: contain;
  display: block; /* 防止底部间隙 */
}

/* 处理图片容器 */
:deep(.n-image) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-height: 250px;
  line-height: 0; /* 消除行高影响 */
}

:deep(.n-image img) {
  max-width: 100%;
  max-height: 250px;
  object-fit: contain;
  vertical-align: middle; /* 防止基线对齐导致的额外空间 */
}

/* 对NUploadDragger内部元素精确控制 */
:deep(.n-upload-dragger-inside) {
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 自定义flex增长因子 */
.flex-grow-3 {
  flex-grow: 3;
}

.flex-grow-2 {
  flex-grow: 2;
}

/* 确保n-card内部的div占满高度 */
.n-card-content {
  height: 100%;
}

/* 确保n-card内容占满高度 */
:deep(.n-card__content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 确保滚动容器可以正确滚动 */
:deep(.n-scrollbar-container) {
  height: 100%;
}

:deep(.n-infinite-scroll-container) {
  min-height: 100%;
}
</style>
