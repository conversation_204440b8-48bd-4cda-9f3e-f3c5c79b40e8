def is_non_validate_route(path: str):
    skip_paths = [
        "/auth/login",
        # "/auth/token_login",
        "/auth/dingding_callback",
        "/auth/dingding_login",
        "/auth/register",
        "/auth/reset_password",
        "/auth/send_email_code",
        "/auth/check_office_ip",
        # "/auth/getUserInfo",
        "/system/get_const_menu",
        # "/system/get_user_menu",
        "/midjourney/callback",
        "/volcano/ttstest",
        "/volcano/ttype2",
        "/volcano/ttype3",
        "/captcha/captcha_init",
        "/captcha/get_captcha_type",
        "/task/status_update",
        "/share/records",
        "/gptimage/check",
    ]
    if (path.startswith("/data/")
            or path.startswith("/upload/")
            or path.startswith("/media/get_voice")
            or "/sd/" in path
            or path in skip_paths):
        return True
    return False
