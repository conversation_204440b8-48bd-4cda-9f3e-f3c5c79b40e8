import logging

from sqlalchemy import Column, Integer
from utils.database import Base


logger = logging.getLogger(__name__)

class UserRole(Base):
    __tablename__ = "user_role"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, default=0, nullable=False, comment='用户ID users.id')
    role_id = Column(Integer, default=0, nullable=False, comment='角色ID role.id')

    @classmethod
    def bulk_delete_user_roles(self, session, user_id):
        """
        批量删除用户的所有角色绑定。
        """
        # session.query(self).filter_by(user_id=user_id).delete()
        # session.commit()
        logger.debug(f"用户 {user_id} 的所有角色绑定已删除。")

    @classmethod
    def bulk_add_user_roles(self, session, user_id, role_ids):
        """
        批量添加用户的角色绑定。
        """
        user_roles = [self(user_id=user_id, role_id=role_id) for role_id in role_ids]
        session.bulk_save_objects(user_roles)
        session.commit()
        logger.debug(f"用户 {user_id} 的所有新角色已添加：角色ID {role_ids}")
