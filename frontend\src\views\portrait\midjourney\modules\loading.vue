<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useThemeStore } from '@/store/modules/theme';

/**
 * 加载组件，提供可自定义的加载动画
 *
 * 使用示例: <loading /> - 使用默认尺寸和文本 <loading width="300" height="300" text="请稍候..." /> - 自定义尺寸和文本 <loading
 * :show="isLoading" /> - 控制加载动画的显示状态 <loading :custom-colors="{ primaryPath: '#ff0000' }" /> - 自定义颜色 <loading
 * :start-time="Date.now()" /> - 添加计时器功能，显示从开始时间到现在的持续时间
 */

// 获取主题信息
const themeStore = useThemeStore();

// 颜色接口定义
interface ColorScheme {
  background?: string;
  text?: string;
  primaryPath?: string;
  secondaryPath?: string;
  basePath?: string;
}

// 定义组件属性
interface Props {
  // 容器宽度，可以是数字(单位px)或字符串(如'100%')
  width?: string | number;
  // 容器高度，可以是数字(单位px)或字符串(如'100%')
  height?: string | number;
  // 加载时显示的文本
  text?: string;
  // 主路径动画持续时间（秒）
  primaryDuration?: number;
  // 次要路径动画持续时间（秒）
  secondaryDuration?: number;
  // 文本动画持续时间（秒）
  textDuration?: number;
  // 是否显示加载动画
  show?: boolean;
  // 自定义颜色，会覆盖主题颜色
  customColors?: ColorScheme;
  // 计时器开始时间（时间戳，毫秒）
  startTime?: number;
}

// 组件默认值
const props = withDefaults(defineProps<Props>(), {
  width: 240,
  height: 240,
  text: 'LOADING',
  primaryDuration: 2,
  secondaryDuration: 1.8,
  textDuration: 1.8,
  show: true,
  customColors: () => ({}),
  startTime: undefined
});

// 计时器相关的响应式数据
const currentTime = ref(Date.now());
const timer = ref<number | null>(null);

// 格式化经过的时间为 "Xs" 格式
const formatElapsedTime = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  return `${seconds}s`;
};

// 计算经过的时间
const elapsedTime = computed(() => {
  if (!props.startTime) return '';
  const elapsed = currentTime.value - props.startTime;
  return formatElapsedTime(Math.max(0, elapsed));
});

// 启动计时器
const startTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
  }
  if (props.startTime) {
    timer.value = window.setInterval(() => {
      currentTime.value = Date.now();
    }, 1000);
  }
};

// 清理计时器
const clearTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

// 生命周期钩子
onMounted(() => {
  startTimer();
});

onUnmounted(() => {
  clearTimer();
});

// 监听 startTime 变化
watch(
  () => props.startTime,
  () => {
    currentTime.value = Date.now();
    startTimer();
  }
);

// 根据主题计算颜色
const themeColors = computed((): ColorScheme => {
  if (themeStore.darkMode) {
    // 暗色主题颜色
    return {
      background: '#222428',
      text: 'grey',
      primaryPath: '#2AA198',
      secondaryPath: '#859900',
      basePath: '#373a42'
    };
  }
  // 亮色主题颜色
  return {
    background: '#f5f5f5',
    text: '#555555',
    primaryPath: '#2AA198',
    secondaryPath: '#859900',
    basePath: '#c8c8c8'
  };
});

// 合并主题颜色和自定义颜色
const colors = computed((): ColorScheme => {
  return {
    ...themeColors.value,
    ...props.customColors
  };
});

// 计算容器样式
const containerStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    height: typeof props.height === 'number' ? `${props.height}px` : props.height,
    backgroundColor: colors.value.background,
    display: props.show ? 'flex' : 'none'
  };
});

// 计算SVG尺寸，确保它能适应容器
const svgSize = computed(() => {
  const minSize = Math.min(
    typeof props.width === 'number' ? props.width : 240,
    typeof props.height === 'number' ? props.height : 240
  );
  return Math.max(38, minSize * 0.8); // 确保最小尺寸为38px，最大为容器的80%
});
</script>

<template>
  <div class="loading-container" :style="containerStyle">
    <div class="scene">
      <svg
        id="dc-spinner"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        :width="svgSize"
        :height="svgSize"
        viewBox="0 0 38 38"
        preserveAspectRatio="xMinYMin meet"
      >
        <text x="14" y="18" font-family="Monaco" font-size="2px" class="tracking-[0.6px]" :fill="colors.text">
          {{ text }}
          <animate attributeName="opacity" values="0;1;0" :dur="`${textDuration}s`" repeatCount="indefinite" />
        </text>

        <!-- 计时器文本 -->
        <text
          v-if="startTime && elapsedTime"
          x="20"
          y="25"
          font-family="Monaco"
          font-size="3.4px"
          class="tracking-[0.6px]"
          :fill="colors.text"
          text-anchor="middle"
          dominant-baseline="middle"
        >
          {{ elapsedTime }}
          <animate
            attributeName="opacity"
            values="0.7;1;0.7"
            :dur="`${textDuration * 1.5}s`"
            repeatCount="indefinite"
          />
        </text>
        <path
          :fill="colors.basePath"
          d="M20,35c-8.271,0-15-6.729-15-15S11.729,5,20,5s15,6.729,15,15S28.271,35,20,35z M20,5.203
          C11.841,5.203,5.203,11.841,5.203,20c0,8.159,6.638,14.797,14.797,14.797S34.797,28.159,34.797,20
          C34.797,11.841,28.159,5.203,20,5.203z"
        ></path>

        <path
          :fill="colors.basePath"
          d="M20,33.125c-7.237,0-13.125-5.888-13.125-13.125S12.763,6.875,20,6.875S33.125,12.763,33.125,20
          S27.237,33.125,20,33.125z M20,7.078C12.875,7.078,7.078,12.875,7.078,20c0,7.125,5.797,12.922,12.922,12.922
          S32.922,27.125,32.922,20C32.922,12.875,27.125,7.078,20,7.078z"
        ></path>

        <path
          :fill="colors.primaryPath"
          :stroke="colors.primaryPath"
          stroke-width="0.6027"
          stroke-miterlimit="10"
          d="M5.203,20
          c0-8.159,6.638-14.797,14.797-14.797V5C11.729,5,5,11.729,5,20s6.729,15,15,15v-0.203C11.841,34.797,5.203,28.159,5.203,20z"
        >
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 20 20"
            to="360 20 20"
            calcMode="spline"
            keySplines="0.4, 0, 0.2, 1"
            keyTimes="0;1"
            :dur="`${primaryDuration}s`"
            repeatCount="indefinite"
          />
        </path>

        <path
          :fill="colors.secondaryPath"
          :stroke="colors.secondaryPath"
          stroke-width="0.2027"
          stroke-miterlimit="10"
          d="M7.078,20
          c0-7.125,5.797-12.922,12.922-12.922V6.875C12.763,6.875,6.875,12.763,6.875,20S12.763,33.125,20,33.125v-0.203
          C12.875,32.922,7.078,27.125,7.078,20z"
        >
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 20 20"
            to="360 20 20"
            :dur="`${secondaryDuration}s`"
            repeatCount="indefinite"
          />
        </path>
      </svg>
    </div>
  </div>
</template>

<style scoped>
.loading-container {
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.scene {
  width: 100%;
  height: 100%;
  perspective: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes arrow-spin {
  50% {
    transform: rotateY(360deg);
  }
}
</style>
