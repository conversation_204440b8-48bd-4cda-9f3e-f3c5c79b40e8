import asyncio
import logging
import uuid
from base64 import b64encode, b64decode
from tempfile import TemporaryDirectory
from typing import TypedDict

import httpx
from datetime import timedelta

import nanoid
from pydub import AudioSegment
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.assets import AssetType
from models.audio_synthesis import AudioSynthesisHistory, Status
from models.users import User
from service.tencent.cos import upload_file
from utils.asset_storage import store_asset_directly

from .http_model import ModelFunction, ToneType
from .pretrained_tones import get_pretrained_tone_by_id

logger = logging.getLogger(__name__)
biz_id = 'audio_synthesis'
_sem = asyncio.Semaphore(8)
"""资源机合成功能的并发量"""
HTTP_TIMEOUT = timedelta(minutes=10).total_seconds()


class SynthesisParams(TypedDict):
    tone_type: ToneType
    tone_id: str | int
    tone_name: str

    model_function: ModelFunction
    model_name: str
    function_name: str

    text: str
    speed: float
    pitch: int

    prompt_audio_url: str | None
    prompt_text: str | None
    prompt_lang: str | None


async def do_synthesis(params: list[SynthesisParams], task_id: int, user: User, db: AsyncSession):
    with TemporaryDirectory() as tmpdir:
        coros = []
        for param in params:
            coros.append(_do_synthesis(param, task_id, user, db, tmpdir))
        try:
            files = await asyncio.gather(*coros)
            files = [file for file in files if file]  # 过滤掉空的内容
            assert len(files) > 0, '未合成任何音频'
        except Exception as e:
            logger.error(f"合成失败：{e}")
            await update_task_status(task_id, Status.FAILURE, db)
            return
        except asyncio.CancelledError:
            logger.error("合成任务被取消")
            await update_task_status(task_id, Status.CANCELED, db)
            return
        # 合成成功，将文件组合起来
        combined_audio = AudioSegment.empty()
        for file in files:
            combined_audio += AudioSegment.from_file(file)
        final_file = f'{tmpdir}/combined.wav'
        combined_audio.export(final_file, format='wav')
        # 将音频上传到 OSS
        url = await asyncio.to_thread(
            upload_file,
            file=final_file,
            cos_path=f'audio_synthesis/1_years/{nanoid.generate()}.wav'
        )
        duration = combined_audio.duration_seconds
        # 更新任务状态
        await update_task_status(task_id, Status.SUCCESS, db, url=url, duration=duration)
        # 加入资产库
        await store_asset_directly(
            db,
            AssetType.AUDIO,
            url,
            user.id,
            taskid=str(task_id),
            biz_id='audio_synthesis',
            parameter={ 'segs': params },
        )


_status_lock = asyncio.Lock()


async def update_task_status(
        task_id: int, status: Status, db: AsyncSession, url: str | None = None, duration: int| None = None):
    async with _status_lock:
        async with db as session:
            stmt = select(AudioSynthesisHistory).where(AudioSynthesisHistory.id == task_id)
            res = await session.execute(stmt)
            history: AudioSynthesisHistory | None = res.scalars().first()
            if history:
                if history.status != status:
                    history.status = status
                    if url:
                        history.url = url
                    if duration:
                        history.duration = duration
                    session.add(history)
                    await session.commit()
            else:
                logger.warning(f"未找到任务：{task_id}，无法将其更新为：{status}")


async def _do_synthesis(params: SynthesisParams, task_id: int, user: User, db: AsyncSession, tmpdir: str) -> str | None:
    """合成单个音频，同时返回文件名"""
    try:
        match params['tone_type']:
            case ToneType.CUSTOM:
                match params['model_name']:
                    case 'GPT-SoVITS':
                        return await _do_custom_sovits(params, user, tmpdir, task_id, db)
                    case 'CosyVoice':
                        return await _do_custom_cosyvoice(params, user, tmpdir, task_id, db)
                    case _:
                        logger.error(f"不支持的模型名称：{params['model_name']}")
                        return None
            case ToneType.PRETRAINED:
                match params['model_name']:
                    case 'CosyVoice':
                        return await _do_pretrained_cosyvoice(params, user, tmpdir, task_id, db)
                    case 'GPT-SoVITS':
                        return await _do_pretrained_sovits(params, user, tmpdir, task_id, db)
                    case 'Volcengine':
                        return await _do_pretrained_volcengine(params, user, tmpdir, task_id, db)
                    case _:
                        logger.error(f"不支持的模型名称：{params['model_name']}")
                        return None
            case _:
                logger.error(f"不支持的音色类型：{params['tone_type']}")
                return None
    except Exception as e:
        logger.error(f"合成失败：{e}")
        return None


def _guess_language(text: str, lang_type: str = 'code') -> str | None:
    """通过 UTF-8 编码范围猜测语言"""
    count = {
        'zh': 0,
        'en': 0,
        'ja': 0,
        'ko': 0,
    }
    for ch in text:
        if '\u4e00' <= ch <= '\u9fff':
            count['zh'] += 1
        elif '\u0041' <= ch <= '\u005a':
            count['en'] += 1
        elif '\u3040' <= ch <= '\u309f':
            count['ja'] += 1
        elif '\u30a0' <= ch <= '\u30ff':
            count['ko'] += 1
    l = max(count, key=count.get) or None
    lang_chinese_map = {
        'zh': '中文',
        'en': '英文',
        'ja': '日文',
        'ko': '韩文',
    }
    match lang_type.lower():
        case 'code':
            return l
        case 'chinese':
            return lang_chinese_map[l]
        case _:
            return None

async def _save_url_to_file(client: httpx.AsyncClient, url: str, dir_path: str) -> str:
    if not url:
        raise ValueError("URL 为空")
    res = await client.get(url)
    res.raise_for_status()
    file_name = f'{dir_path}/{uuid.uuid4()}.wav'
    with open(file_name, 'wb') as f:
        f.write(res.content)
    return file_name

_sovits_lock = asyncio.Lock()
"""GPT SoVITS 无法支持并发"""

async def _do_custom_sovits(params: SynthesisParams, user: User, tmpdir: str, task_id: int, db: AsyncSession) -> str:
    file_url = params['prompt_audio_url']
    if file_url is None:
        raise ValueError("音频 URL 为空，无法生成")
    data = {
        "text": params['text'],
        "text_language": _guess_language(params['text']),
        "prompt_text": params['prompt_text'],
        "prompt_language": params['prompt_lang'] or _guess_language(params['prompt_text']),
        "temperature": 0.6,
        "speed": params['speed'],
        "top_p": 0.6,
        "top_k": 20,
        "user": user.username,
        "reference_id": "",
        "save_model": False,
        "file_path": '',
    }
    upload_url = f"{app_settings.ai_server}/gpt_sovits/upload_file"
    zero_shot_url = f"{app_settings.ai_server}/gpt_sovits/gpt_sovits_zeroshort"
    async with _sovits_lock:
        await update_task_status(task_id, Status.IN_PROGRESS, db)
        async with httpx.AsyncClient(timeout=HTTP_TIMEOUT) as client:
            res = await client.get(file_url)
            res.raise_for_status()
            files = {'file': (f'{uuid.uuid4()}.wav', res.content)}
            res = await client.post(upload_url, files=files)
            res.raise_for_status()
            data["file_path"] = res.json().get("file_path", "")
            res = await client.post(url=zero_shot_url, json=data)
            res.raise_for_status()
            body = res.json()
            if 'b64' in body and 'type' in body and body.get('type') == 'oss_url':
                url = body.get('b64')
                return await _save_url_to_file(client, url, tmpdir)
            raise ValueError("为返回合成的 URL")

async def _do_custom_cosyvoice(params: SynthesisParams, user: User, tmpdir: str, task_id: int, db: AsyncSession) -> str:
    file_url = params['prompt_audio_url']
    if file_url is None:
        raise ValueError("音频 URL 为空，无法生成")
    data = {
        "text": params['text'],
        "prompt_wav_base64": '',
        "prompt_text": params['prompt_text'],
        "save_name": '',
        "speed": params['speed'],
        "streaming": 0,
        "instruct_text": '',
        "username": user.username
    }
    async with _sem:
        await update_task_status(task_id, Status.IN_PROGRESS, db)
        async with httpx.AsyncClient(timeout=HTTP_TIMEOUT) as client:
            res = await client.get(file_url)
            audio_base64 = b64encode(res.content).decode('utf-8')
            data['prompt_wav_base64'] = audio_base64
            res = await client.post(
                url=f'{app_settings.ai_server}/cosyvoice/zero_shot',
                headers={
                    "Content-Type": "application/json"
                },
                json=data
            )
            res.raise_for_status()
            body = res.json()
            url = body.get("b64", "")
            return await _save_url_to_file(client, url, tmpdir)

async def _do_pretrained_cosyvoice(params: SynthesisParams, user: User, tmpdir: str, task_id: int, db: AsyncSession) -> str:
    data = {
        "text": params['text'],
        "speaker": params['tone_name'],
        "new": 0,
        "speed": params['speed'],
        "streaming": 0,
        "username": user.username
    }
    async with _sem:
        await update_task_status(task_id, Status.IN_PROGRESS, db)
        async with httpx.AsyncClient(timeout=HTTP_TIMEOUT) as client:
            res = await client.post(
                url=f'{app_settings.ai_server}/cosyvoice/task',
                headers={
                    "Content-Type": "application/json"
                },
                json=data
            )
            res.raise_for_status()
            body = res.json()
            url = body.get("b64", "")
            return await _save_url_to_file(client, url, tmpdir)


_volcengine_sem = asyncio.Semaphore(1)
"""即梦的免费 API 并发量就是 1"""

async def _do_pretrained_volcengine(params: SynthesisParams, user: User, tmpdir: str, task_id: int, db: AsyncSession) -> str:
    uid = str(uuid.uuid4())
    reqid = str(uuid.uuid4())
    data = {
        "app": {
            "appid": app_settings.volcengine_appid,
            "token": app_settings.volcengine_access_token,
            "cluster": app_settings.volcengine_cluster
        },
        "user": {
            "uid": uid
        },
        "audio": {
            "voice_type": params['tone_id'],
            "encoding": "mp3",
            "speed_ratio": params['speed'],
            "volume_ratio": 1,
            "pitch_ratio": params['pitch'],
            "emotion": '',
            "language": '',
        },
        "request": {
            "reqid": reqid,
            "text": params['text'],
            "text_type": "plain",
            "operation": "query",
        }
    }
    async with _volcengine_sem:
        await update_task_status(task_id, Status.IN_PROGRESS, db)
        api_url = f"{app_settings.volcengine_api_host}/api/v1/tts"
        header = {"Authorization": f"Bearer;{app_settings.volcengine_access_token}"}
        async with httpx.AsyncClient(timeout=HTTP_TIMEOUT) as client:
            res = await client.post(url=f'{api_url}', json=data, headers=header)
            res.raise_for_status()
            body = res.json()
            if body["code"] != 3000:
                raise ValueError(f'火山引擎错误响应：{body["code"]}, {body["message"]}')
            if data := body.get("data", ""):
                filename = f'{tmpdir}/{uuid.uuid4()}.mp3'
                with open(filename, 'wb') as f:
                    f.write(b64decode(data.encode()))
                return filename
            raise ValueError(f'火山引擎未返回音频数据')

async def _do_pretrained_sovits(params: SynthesisParams, user: User, tmpdir: str, task_id: int, db: AsyncSession) -> str:
    tone = get_pretrained_tone_by_id(params['tone_id'])
    if tone is None:
        raise ValueError(f'未找到音色：{params['tone_id']}')
    refer_wav_path = tone.get('params', {}).get('refer_wav_path', '')
    if not refer_wav_path:
        raise ValueError(f'未找到模型的 refer_wav_path：{params['tone_id']}')
    data = {
        'prompt_language': _guess_language(params['prompt_text'], 'chinese'),
        'prompt_text': params['prompt_text'],
        'refer_wav_path': refer_wav_path,
        'text': params['text'],
        'text_language': _guess_language(params['text'], 'chinese'),
    }
    async with _sem:
        await update_task_status(task_id, Status.IN_PROGRESS, db)
        async with httpx.AsyncClient(timeout=HTTP_TIMEOUT) as client:
            res = await client.get(
                url=f'{app_settings.ai_server}/gpt_sovits/tts',
                params=data
            )
            res.raise_for_status()
            body = res.json()
            if 'b64' in body and 'type' in body and body.get('type') == 'oss_url':
                url = body.get('b64')
                return await _save_url_to_file(client, url, tmpdir)
            raise ValueError("为返回合成的 URL")
