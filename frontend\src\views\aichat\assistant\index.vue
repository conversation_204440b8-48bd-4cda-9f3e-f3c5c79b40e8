<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import type { ScrollbarInst } from 'naive-ui';
import { debounce } from 'lodash';
// import { useThemeStore } from '@/store/modules/theme';
import { fetchDeleteChat, getChat, getChatState, getModels, processStream, stopChat } from '@/service/api/chat';
import { getIsMobile } from '@/utils/mobile';
import MobileDrawer from '@/views/aichat/assistant/modules/MobileDrawer.vue';
import { Capacity, capacityOp } from '@/utils/capacity';
import ChatSidebar from './modules/assistant-sidebar.vue';
import ChatHeader from './modules/assistant-header.vue';
import ChatContent from './modules/assistant-content.vue';
import ChatInput from './modules/assistant-input.vue';

const isLoading = ref<boolean>(false);

const promptData = ref<string>('');
const imageData = ref<string[]>([]);

const isMobile = ref<boolean>(getIsMobile());

const useWebStorageKey = 'aichat:assistant:useWeb';
const getUseWeb = () => {
  const value = localStorage.getItem(useWebStorageKey);
  return value === 'true';
};
const useWeb = ref<boolean>(getUseWeb());
watch(useWeb, () => {
  localStorage.setItem(useWebStorageKey, useWeb.value.toString());
});

const selectedChatTitle = ref<string>('');

interface ChatMessage {
  uuid: number;
  inversion: boolean;
  content: string;
  timestamp: string;
  files?: Array<{ ext: string; url: string; type: string }>;
  qid?: number;
  model?: string;
  modelLabel?: string;
  modelDesc?: string;
}

// 聊天记录
const chatState = reactive({
  active: null as number | null,
  history: [] as Array<{
    id: number;
    uuid: number;
    title: string;
    create_time: string;
    isEdit: boolean;
    memory: string;
    role: string;
  }>,
  isProcessing: false,
  currentModel: null as string | null,
  error: null as string | null
});

const chatMessages = ref<Array<any>>([]); // 用于存储获取到的聊天内容

type Model = {
  label: string;
  value: string;
  desc: string;
  icon: string;
  capacity: number;
};
const models = ref<Array<Model>>([]);

//
const model = ref(models.value[1]?.value);

const modelObj = computed<Model>(() => {
  return models.value.find(m => m.value === model.value) || models.value[0];
});

// 滚动相关控制
const scrollbar = ref<ScrollbarInst>();
const canScroll = ref(true);
const handleScroll = (e: Event) => {
  const target = e.target as HTMLDivElement;
  const currentScrollTop = target.scrollTop;
  const currentScrollHeight = target.scrollHeight;
  const height = target.clientHeight;
  canScroll.value = currentScrollTop + height >= currentScrollHeight - 5; // 某些高 DPI 视口下可能出现问题，所以 -5 适配一下
};
const checkAndScrollToBottom = async () => {
  if (canScroll.value) {
    await nextTick(() => scrollbar.value?.scrollTo({ top: 1000000 }));
  }
};

// 获取服务端支持的模型
async function fetchModels() {
  const response = await getModels();
  if (response.data) {
    models.value = response.data;
  }
}

const loading = ref<boolean>(false);

// 格式化时间的函数
function formatTimestamp(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以需要 +1
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 添加一个辅助函数来获取模型的完整信息
function getModelInfo(modelValue: string) {
  const modelInfo = models.value.find(m => m.value === modelValue);
  return {
    value: modelValue,
    label: modelInfo?.label || modelValue,
    desc: modelInfo?.desc
  };
}

async function fetchChatContent(chatid: number | null) {
  if (chatid === null) return;

  loading.value = true;
  const response = await getChat(chatid);
  if (response.data && response.data.data) {
    // 创建一个 Map 来按 qid 分组存储消息
    const messagesByQid = new Map();

    response.data.data.forEach((item: any) => {
      const modelInfo = getModelInfo(item.model);
      const message = {
        uuid: item.id,
        inversion: item.inversion,
        content: item.text,
        timestamp: item.dateTime,
        files: item.files,
        qid: item.qid,
        model: modelInfo.value,
        modelLabel: modelInfo.label,
        modelDesc: modelInfo.desc
      };

      if (!messagesByQid.has(item.qid)) {
        messagesByQid.set(item.qid, {
          userMessage: null,
          assistantMessages: new Map()
        });
      }

      const qidGroup = messagesByQid.get(item.qid);
      if (item.inversion) {
        qidGroup.userMessage = message;
      } else {
        // 对于助手消息，只保留最新的回复
        const existingMessage = qidGroup.assistantMessages.get(item.model);
        if (!existingMessage || new Date(item.dateTime) > new Date(existingMessage.timestamp)) {
          qidGroup.assistantMessages.set(item.model, message);
        }
      }
    });

    // 将分组的消息转换为有序数组
    const messages: ChatMessage[] = [];
    messagesByQid.forEach(group => {
      if (group.userMessage) {
        messages.push(group.userMessage);
        // 添加该问题的所有助手回复
        group.assistantMessages.forEach((assistantMessage: ChatMessage) => {
          messages.push(assistantMessage);
        });
      }
    });

    // 按 qid 和时间戳排序
    chatMessages.value = messages.sort((a, b) => {
      if (a.qid !== b.qid) {
        return a.qid! - b.qid!;
      }
      // 同一 qid 内，用户消息在前，助手消息在后
      if (a.inversion !== b.inversion) {
        return a.inversion ? -1 : 1;
      }
      return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    });
  }
  loading.value = false;
}

// 获取聊天状态
async function fetchChatState() {
  const response = await getChatState();
  if (response.data) {
    // console.log('Chat state response:', response.data);
    const { active, history } = response.data;

    // 如果 history 为空，添加一个默认聊天记录
    if (!history || history.length === 0) {
      const newUuid = Date.now();
      const defaultChat = {
        uuid: newUuid,
        title: '新建聊天',
        isEdit: false,
        create_time: formatTimestamp(new Date()),
        id: newUuid,
        memory: '',
        role: 'assistant'
      };

      chatState.active = newUuid;
      chatState.history = [defaultChat];
      selectedChatTitle.value = '新建聊天';
      return;
    }

    // 检查active是否变化，避免重复调用fetchChatContent
    const previousActive = chatState.active;

    chatState.active = active;
    chatState.history = history;

    // 查找当前活动的聊天，优先使用 uuid 匹配
    const activeChat = history.find((chat: (typeof chatState.history)[0]) => chat.uuid === active);
    // console.log('Active chat found:', activeChat);

    if (activeChat) {
      selectedChatTitle.value = activeChat.title;
    } else {
      // 如果找不到匹配的活动聊天，使用第一个聊天的标题
      selectedChatTitle.value = history[0]?.title || '新建聊天';
    }

    // 只有当active变化时才获取聊天内容，避免重复调用
    if (previousActive !== active) {
      await fetchChatContent(active);
    }
  }
}

const handleModelUpdate = (newValue: string) => {
  model.value = newValue;
  // 确保模型更新后重置相关状态
  isLoading.value = false;
};

// function get_msg_process() {
//   console.log('Processing message:', promptData.value, imageData.value);
// }

// 在顶层声明 streamController
let streamController: { cancel: () => void } | null = null;

// 使用防抖包装模型切换函数
const debouncedModelChange = debounce(async (message: ChatMessage, selectedModel: string) => {
  if (!message.qid) return;

  // 如果正在加载中，不允许切换模型
  if (isLoading.value) return;

  // 如果当前模型和选择的模型相同，则不做任何处理
  if (message.model === selectedModel) return;

  // 保存原始内容，以便在出错时恢复
  const originalContent = message.content;
  const originalFiles = message.files || [];

  // 找到对应的用户问题（使用 qid 匹配）
  const userMessage = chatMessages.value.find(msg => msg.inversion && msg.qid === message.qid);
  if (!userMessage) {
    console.error('Cannot find original question for qid:', message.qid);
    return;
  }

  // 立即更新UI状态
  const modelInfo = models.value.find(m => m.value === selectedModel);
  message.model = selectedModel;
  message.modelLabel = modelInfo?.label;
  message.modelDesc = modelInfo?.desc;
  message.content = '思考中...';
  message.files = [];

  // 取消当前客户端连接 - 立即停止前端接收旧模型响应
  if (streamController) {
    streamController.cancel();
    streamController = null;
  }

  // 设置加载状态
  isLoading.value = true;

  // 后台处理 - 用户已看到模型切换和"思考中..."状态
  try {
    // 调用后端停止生成
    await stopChat();

    // 请求新模型生成回复
    streamController = await processStream(
      {
        prompt: userMessage.content,
        chatid: chatState.active || Date.now(),
        gpt_model: selectedModel,
        files: userMessage.files || [],
        qid: message.qid,
        switchModel: true, // 添加标识，表明这是切换模型的请求
        useWeb: useWeb.value
      },
      async (responseMessage: string) => {
        try {
          // 确保在接收流式响应过程中isLoading保持为true
          isLoading.value = true;

          const cleanedLine = responseMessage.replace(/^data: /, '').trim();
          if (cleanedLine === '[DONE]') return;

          const parsedMessage = JSON.parse(cleanedLine);
          if (parsedMessage.data?.text !== undefined) {
            // 使用 uuid 确保更新正确的消息
            const messageIndex = chatMessages.value.findIndex(msg => msg.uuid === message.uuid);

            if (messageIndex !== -1) {
              updateMessageContent(messageIndex, parsedMessage);
            }
          }
        } catch (error) {
          console.error('Error parsing message:', error);
        }
      },
      error => {
        isLoading.value = false;
        console.error('Stream error:', error);
        message.content = '请求出错，请重试';
      },
      () => {
        isLoading.value = false;
      }
    );
  } catch (error) {
    console.error('Error during model change:', error);
    message.content = '模型切换失败，请稍后再试';
    // 短暂延时后恢复原始内容
    setTimeout(() => {
      message.content = originalContent;
      message.files = originalFiles;
      isLoading.value = false;
    }, 2000);
    isLoading.value = false;
  }
}, 300); // 300ms防抖延迟

// 模型切换处理函数 - 调用防抖包装后的函数
const handleModelChange = async (message: ChatMessage, selectedModel: string) => {
  // 如果正在加载中，不允许切换模型
  if (isLoading.value) return;

  // 更新全局模型值，使header下拉框同步更新
  model.value = selectedModel;

  // 使用防抖包装后的函数处理
  debouncedModelChange(message, selectedModel);
};

// 添加停止生成的处理函数
async function handleStopGeneration() {
  try {
    // 立即重置加载状态 - 先更新UI
    isLoading.value = false;

    // 取消当前的 SSE 连接 - 立即停止前端接收响应
    if (streamController) {
      streamController.cancel();
      streamController = null;
    }

    // 后台处理停止请求
    (async () => {
      try {
        // 调用停止生成接口
        await stopChat();
      } catch (error) {
        console.error('Error stopping generation:', error);
      }
    })();
  } catch (error) {
    console.error('Error in handleStopGeneration:', error);
  }
}

// 修改updateMessageContent函数
function updateMessageContent(messageIndex: number, parsedMessage: any) {
  // 确保消息存在
  if (!chatMessages.value[messageIndex]) return;

  // 更新消息内容
  chatMessages.value[messageIndex].content = parsedMessage.data.text;
  if (parsedMessage.data.files) {
    chatMessages.value[messageIndex].files = parsedMessage.data.files;
  }
  checkAndScrollToBottom();
}

// 在组件卸载时清理
onBeforeUnmount(() => {
  if (streamController) {
    streamController.cancel();
  }
});

// 添加获取主题变量的逻辑
// const themeVars = useThemeVars();

async function handleSubmit(data: { prompt: string; imageList: string[] }) {
  promptData.value = data.prompt;
  imageData.value = data.imageList;

  const uuid = chatState.active || Date.now();
  const initialFiles = imageData.value.length > 0 ? [{ type: 'image', url: imageData.value[0] }] : [];

  // 生成一个更具唯一性的 qid，使用时间戳+随机数
  const tempQid = Date.now() * 1000 + Math.floor(Math.random() * 1000);

  // 创建用户消息
  const userMessage = {
    uuid: Date.now(), // 为每条消息生成唯一的 uuid
    inversion: true,
    content: promptData.value,
    timestamp: formatTimestamp(new Date()),
    files: initialFiles,
    qid: tempQid
  };

  // 创建助手消息
  const assistantMessage = {
    uuid: Date.now() + 1, // 确保与用户消息的 uuid 不同
    inversion: false,
    content: '思考中...',
    timestamp: formatTimestamp(new Date()),
    files: [],
    model: model.value,
    qid: tempQid,
    modelLabel: models.value.find(m => m.value === model.value)?.label,
    modelDesc: models.value.find(m => m.value === model.value)?.desc
  };

  // 添加消息到聊天记录 - 立即更新UI
  chatMessages.value = [...chatMessages.value, userMessage, assistantMessage];
  await checkAndScrollToBottom();

  try {
    const currentModel = model.value || models.value[0]?.value;

    // 立即取消当前连接 - 立即停止前端接收响应
    if (streamController) {
      streamController.cancel();
      streamController = null;
    }

    // 设置加载状态
    isLoading.value = true;

    // 后台处理停止和新消息处理
    (async () => {
      try {
        // 调用后端停止生成接口
        await stopChat();

        // 请求新消息处理
        streamController = await processStream(
          {
            prompt: promptData.value,
            chatid: uuid,
            gpt_model: currentModel,
            files: imageData.value.map(url => ({ type: 'image', url })),
            qid: tempQid, // 确保传递正确的 qid
            useWeb: useWeb.value
          },
          async line => {
            try {
              // 确保在接收流式响应过程中isLoading保持为true
              isLoading.value = true;

              const cleanedLine = line.replace(/^data: /, '').trim();
              if (cleanedLine === '[DONE]') return;

              const parsedMessage = JSON.parse(cleanedLine);
              if (parsedMessage.data) {
                if (parsedMessage.data.qid) {
                  // 更新消息的 qid，使用 uuid 来确保更新正确的消息
                  const userIndex = chatMessages.value.findIndex(msg => msg.uuid === userMessage.uuid);
                  const assistantIndex = chatMessages.value.findIndex(msg => msg.uuid === assistantMessage.uuid);

                  if (userIndex !== -1) {
                    chatMessages.value[userIndex].qid = parsedMessage.data.qid;
                  }
                  if (assistantIndex !== -1) {
                    chatMessages.value[assistantIndex].qid = parsedMessage.data.qid;
                  }
                }

                if (parsedMessage.data.text !== undefined) {
                  // 使用 uuid 来确保更新正确的助手消息
                  const messageIndex = chatMessages.value.findIndex(msg => msg.uuid === assistantMessage.uuid);

                  if (messageIndex !== -1) {
                    updateMessageContent(messageIndex, parsedMessage);
                  }
                }
              }
            } catch (e) {
              console.warn('JSON parse error:', {
                line,
                error: e,
                messageLength: line?.length
              });
            }
          },
          error => {
            isLoading.value = false;
            console.error('Stream error:', error);
            const messageIndex = chatMessages.value.findIndex(msg => msg.uuid === assistantMessage.uuid);
            if (messageIndex !== -1) {
              chatMessages.value[messageIndex].content = '请求出错，请重试';
              chatMessages.value = [...chatMessages.value];
            }
          },
          async () => {
            loading.value = false;
            isLoading.value = false;

            // 更新聊天标题
            const response = await getChatState();
            if (response.data?.history) {
              const updatedChat = response.data.history.find(
                (chat: (typeof chatState.history)[0]) => chat.uuid === uuid
              );
              if (updatedChat) {
                selectedChatTitle.value = updatedChat.title;
                const currentChat = chatState.history.find(chat => chat.uuid === uuid);
                if (currentChat) {
                  currentChat.title = updatedChat.title;
                }
              }
            }
          }
        );
      } catch (error) {
        console.error('Error during stream processing:', error);
        const messageIndex = chatMessages.value.findIndex(msg => msg.uuid === assistantMessage.uuid);
        if (messageIndex !== -1) {
          chatMessages.value[messageIndex].content = '请求出错，请重试';
          chatMessages.value = [...chatMessages.value];
        }
        isLoading.value = false;
      }
    })();
  } catch (error) {
    console.error('Error in handleSubmit:', error);
    const messageIndex = chatMessages.value.findIndex(msg => msg.uuid === assistantMessage.uuid);
    if (messageIndex !== -1) {
      chatMessages.value[messageIndex].content = '请求出错，请重试';
      chatMessages.value = [...chatMessages.value];
    }
    isLoading.value = false;
  }
}

function handleChatAdded() {
  canScroll.value = true;
  if (!(chatMessages.value.length > 0)) {
    return;
  }

  chatMessages.value = []; // 清空 chatMessages

  addChat();
}

async function handleSubmitAgain(messageToRegenerate: ChatMessage) {
  if (!messageToRegenerate.qid) return;

  // 如果已经在加载中，则不执行
  if (isLoading.value) return;

  // 设置思考中状态 - 立即更新UI
  messageToRegenerate.content = '思考中...';
  messageToRegenerate.files = [];
  isLoading.value = true;

  // 找到对应的用户问题
  const userMessage = chatMessages.value.find(msg => msg.inversion && msg.qid === messageToRegenerate.qid);
  if (!userMessage) return;

  try {
    const currentModel = messageToRegenerate.model || model.value;

    // 立即取消当前连接 - 立即停止前端接收响应
    if (streamController) {
      streamController.cancel();
      streamController = null;
    }

    // 后台处理停止和重新生成
    await (async () => {
      // 调用后端停止生成接口并等待它完成
      try {
        await stopChat();

        // 添加短暂延迟，确保后端数据库操作完成
        await new Promise<void>(resolve => {
          setTimeout(() => {
            resolve();
          }, 500);
        });
      } catch (stopError) {
        console.error('Error stopping chat:', stopError);
        // 即使停止出错，我们也继续尝试启动新生成
      }

      try {
        streamController = await processStream(
          {
            prompt: userMessage.content,
            chatid: chatState.active || Date.now(),
            gpt_model: currentModel,
            files: userMessage.files || [],
            qid: messageToRegenerate.qid,
            regenerate: true, // 添加标识，表明这是重新生成的请求
            useWeb: useWeb.value
          },
          async (message: string) => {
            try {
              // 确保在接收流式响应过程中isLoading保持为true
              isLoading.value = true;

              const cleanedLine = message.replace(/^data: /, '').trim();
              if (cleanedLine === '[DONE]') return;

              const parsedMessage = JSON.parse(cleanedLine);
              if (parsedMessage?.data?.text) {
                // 更新消息内容
                messageToRegenerate.content = parsedMessage.data.text;
                if (parsedMessage.data.files) {
                  messageToRegenerate.files = parsedMessage.data.files;
                }
                messageToRegenerate.timestamp = formatTimestamp(new Date()); // 更新时间戳
                await checkAndScrollToBottom();
              }
            } catch (e) {
              console.warn('JSON parse error:', e);
            }
          },
          error => {
            isLoading.value = false;
            console.error('Stream error:', error);
            messageToRegenerate.content = '请求出错，请重试';
          },
          () => {
            isLoading.value = false;
          }
        );
      } catch (error) {
        console.error('Error during stream processing:', error);
        messageToRegenerate.content = '请求出错，请重试';
        isLoading.value = false;
      }
    })();
  } catch (error) {
    console.error('Error in handleSubmitAgain:', error);
    messageToRegenerate.content = '请求出错，请重试';
    isLoading.value = false;
  }
}

watch(
  () => chatState.active,
  async (newActive, oldActive) => {
    // 只有当active真正变化时才获取聊天内容
    if (newActive !== oldActive && newActive !== null) {
      await fetchChatContent(newActive);
      await checkAndScrollToBottom();
    }
  }
);

onMounted(async () => {
  await fetchModels();
  if (models.value.length > 0) {
    model.value = models.value[0]?.value;
  }
  await fetchChatState();
  await checkAndScrollToBottom();
});

const handleChangeWeb = (data: { useWeb: boolean }) => {
  useWeb.value = data.useWeb;
};

const handleChangeTab = (event: string) => {
  selectedChatTitle.value = event;
  canScroll.value = true;
};

const showDrawer = ref(false);
const handleOpenDrawer = () => {
  showDrawer.value = true;
};
watch(
  () => chatState.active,
  () => {
    showDrawer.value = false;
  }
);

function addChat() {
  const newChat = {
    id: Date.now(),
    uuid: Date.now(),
    title: '新建聊天',
    create_time: new Date().toISOString(), // 将 Date 转换为字符串
    isEdit: false,
    memory: '',
    role: ''
  };
  chatState.history.unshift(newChat);
  chatState.active = newChat.uuid;
  selectedChatTitle.value = newChat.title;
}

const handleDeleteChat = async (uuid: number) => {
  const currentIndex = chatState.history.findIndex(chat => chat.uuid === uuid);
  const nextChat = chatState.history[currentIndex + 1] || chatState.history[currentIndex - 1];

  // 先调用删除 API
  await fetchDeleteChat(uuid);

  // 从列表中移除
  chatState.history = chatState.history.filter(chat => chat.uuid !== uuid);

  // 如果删除后没有任何聊天记录，创建一个新的
  if (chatState.history.length === 0) {
    addChat();
  } else if (nextChat) {
    // 如果存在下一个聊天，自动选择它
    chatState.active = nextChat.uuid;
    selectedChatTitle.value = nextChat.title;
  }
};

const handleUseSkill = (payload: { skill: string | null }) => {
  if (payload.skill === null) {
    return;
  }
  // 目前的技能基本都要模型有 Tool Call 能力
  // 所以现在想要使用技能，先检查当前模型是否支持 Tool Call，否则就把模型换成支持 Tool Call 的
  let currentModel: Model | undefined;
  for (const m of models.value) {
    if (m.value === model.value) {
      currentModel = m;
      break;
    }
  }
  if (currentModel !== undefined && capacityOp.has(currentModel.capacity, Capacity.TOOL_CALL)) {
    return;
  }
  for (const m of models.value) {
    if (capacityOp.has(m.capacity, Capacity.TOOL_CALL)) {
      model.value = m.value;
      return;
    }
  }
};
</script>

<template>
  <NFlex vertical class="h-full">
    <NFlex
      class="h-full border border-[#f2f2f2] rounded-lg bg-[#fffffe] dark:border-[#2d2d2d] dark:bg-[#1c1c1c]"
      style="flex-wrap: nowrap; gap: 0"
    >
      <NFlex
        vertical
        class="h-full overflow-hidden border-r border-[#f2f2f2] dark:border-[#2d2d2d]"
        :class="{ 'border-0': isMobile }"
      >
        <ChatSidebar
          v-if="!isMobile"
          v-model:selected-chat-id="chatState.active"
          v-model:chat-list="chatState.history"
          :selected-chat-title="selectedChatTitle"
          class="w-60"
          :is-mobile="isMobile"
          @chat-added="handleChatAdded"
          @update:selected-chat-title="handleChangeTab"
          @chat-delete="handleDeleteChat"
        />
        <MobileDrawer
          v-else
          v-model:show-drawer="showDrawer"
          v-model:selected-chat-id="chatState.active"
          v-model:chat-list="chatState.history"
          :selected-chat-title="selectedChatTitle"
          :is-mobile="isMobile"
          @chat-add="handleChatAdded"
          @tab-change="handleChangeTab"
          @chat-delete="handleDeleteChat"
        />
      </NFlex>
      <NFlex v-if="loading" class="flex-1" justify="center">
        <NSpin></NSpin>
      </NFlex>
      <NFlex v-else vertical class="chat-container">
        <!-- 头部 -->
        <NFlex class="header border-b border-[#f2f2f2] dark:border-[#2d2d2d]" align="center">
          <ChatHeader
            v-model:model="model"
            :models="models"
            :title="selectedChatTitle"
            :is-mobile="isMobile"
            @update:model="handleModelUpdate"
            @chat-added="handleChatAdded"
            @open-sidebar="handleOpenDrawer"
          />
        </NFlex>

        <!-- 聊天主容器 - 包含内容区域和输入区域 -->
        <NFlex vertical class="chat-main-container flex-1">
          <div class="chat-content-input-container">
            <!-- 聊天内容区域 -->
            <NFlex vertical class="chat-content-wrapper flex-1">
              <NScrollbar ref="scrollbar" :size="1" :on-scroll="handleScroll" trigger="none" class="w-full">
                <div class="chat-content">
                  <ChatContent
                    v-if="chatMessages.length > 0"
                    :messages="chatMessages"
                    :models="models"
                    :model="model"
                    :stream-rendering="true"
                    :is-loading="isLoading"
                    :is-mobile="isMobile"
                    @regenerate-message="handleSubmitAgain"
                    @model-change="handleModelChange"
                  />
                  <div
                    v-else
                    class="m-auto mt-4 flex items-center justify-center px-4 text-neutral-300"
                    :class="{ 'text-center': isMobile }"
                  >
                    <span class="mt-6 max-w-200 text-zinc-400">
                      <SvgIcon icon="material-symbols:brand-awareness-rounded" class="inline text-xl" />
                      {{ model ? models.find(item => item.value === model)?.desc : 'No model selected' }}
                    </span>
                  </div>
                </div>
              </NScrollbar>
            </NFlex>

            <!-- 底部输入区域 -->
            <NFlex vertical class="footer-area">
              <ChatInput
                :loading="isLoading"
                :model-name="model"
                :use-web="useWeb"
                :is-mobile="isMobile"
                :model="modelObj"
                @submit="handleSubmit"
                @stop="handleStopGeneration"
                @change-web="handleChangeWeb"
                @use-skill="handleUseSkill"
              />
            </NFlex>
          </div>
        </NFlex>
      </NFlex>
    </NFlex>
  </NFlex>
</template>

<style scoped>
.chat-container {
  width: 100%;
  height: 100%;
  flex: 1;
}

.header {
  height: 60px;
  z-index: 10;
}

.chat-main-container {
  display: flex;
  justify-content: center;
  width: 100%;
  overflow: hidden;
}

.chat-content-input-container {
  display: flex;
  flex-direction: column;
  max-width: 90em;
  width: 100%;
  margin: 0 auto;
  height: 100%;
}

.chat-content-wrapper {
  overflow: hidden;
  flex-grow: 1;
}

/* .chat-content {
  padding: 0 20px;
} */

.footer-area {
  background-color: var(--n-color);
  z-index: 10;
  margin: 1em 0;
  padding: 0 1em;
  width: 100%;
}

.footer-area .mb-2 {
  margin-bottom: 0.5rem;
  min-height: 40px; /* 确保停止按钮区域有固定高度 */
}

:deep(.chat-content-wrapper) .n-scrollbar-rail__scrollbar {
  display: none !important;
}
</style>
