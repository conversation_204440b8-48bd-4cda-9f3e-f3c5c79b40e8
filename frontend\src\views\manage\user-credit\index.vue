<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NButton } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchAddNewUserCredit, fetchAddUserCredit, fetchUserCredit, fetchUserCreditSearch } from '@/service/api';
import { $t } from '@/locales';
import AddUserCredit from './modules/add-user-credit.vue';
import NewUserCredit from './modules/new-user-credit.vue';
import UserCreditSearch from './modules/user-credit-search.vue';

const appStore = useAppStore();

interface UserCreditData extends NaiveUI.TableData {
  id: number;
  user_id: number;
  user_name: string;
  credit: number;
  updatetime: string;
}

// 获取用户积分
const fetchTyped: NaiveUI.TableApiFn<UserCreditData, Api.SystemManage.UserCreditSearch> = async params => {
  const response = await fetchUserCredit(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<UserCreditData>>;
};

const searchParams = ref<Api.SystemManage.UserCreditSearch>({
  current: 1,
  size: 10
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'user_id',
      title: '用户 ID',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'user_name',
      title: '用户',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'credit',
      title: '积分',
      align: 'left',
      minWidth: 80
    },
    {
      key: 'updatetime',
      title: '更新日期',
      align: 'center',
      width: 200,
      render: row => row.updatetime.replace('T', ' ')
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleAddUserCredit(row)}>
            加积分
          </NButton>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, checkedRowKeys } = useTableOperate(data, getData);
const editData = ref<any>(null);
const newVisible = ref(false);

// 加列表用户积分
async function handleAddUserCredit(row: any) {
  editData.value = row;
  drawerVisible.value = true;
}

// 加新用户积分
async function handleNew() {
  newVisible.value = true;
}

async function handleSubmit(creditData: any) {
  try {
    // if (operateType.value === 'add') {
    await fetchAddUserCredit(creditData);
    // } else if (operateType.value === 'edit') {
    // await fetchUpdataUserCredit(creditData);
    // }
    window.$message?.success($t('common.updateSuccess'));
    // 判断是否有搜索条件，如果有则使用搜索条件刷新，否则使用普通刷新
    if (searchParams.value.user_id) {
      handleSearch(searchParams.value);
    } else {
      getData();
    }
  } finally {
    drawerVisible.value = false;
  }
}

async function handleSubmitNew(creditData: any) {
  try {
    await fetchAddNewUserCredit(creditData);
    window.$message?.success('添加成功');
    if (searchParams.value.user_id) {
      handleSearch(searchParams.value);
    } else {
      getData();
    }
  } finally {
    drawerVisible.value = false;
  }
}

// 查询
async function handleSearch(params: Api.SystemManage.UserCreditSearch) {
  searchParams.value = params;
  console.log('==========', searchParams.value);
  const response = await fetchUserCreditSearch(searchParams.value);
  data.value = response.data.records;
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

type Row = {
  id: string;
};
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserCreditSearch v-model:model="searchParams" @add="handleNew" @search="handleSearch" />
    <AddUserCredit
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editData"
      @submitted="handleSubmit"
    />
    <NCard title="用户积分管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleNew" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1024"
        :loading="loading"
        remote
        :row-key="(row: Row) => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>
    <NewUserCredit v-model:visible="newVisible" @submitted="handleSubmitNew" />
  </div>
</template>

<style scoped>
/**
:deep(.n-scrollbar-container) .n-scrollbar-content,
:deep(.n-scrollbar-container) .n-data-table-table {
  height: 100% !important;
}
  */
</style>
