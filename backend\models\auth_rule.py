from sqlalchemy import Column, Integer, String, Enum, BigInteger, Text
from utils.database import Base


class AuthRule(Base):
    __tablename__ = "auth_rule"

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    type = Column(Enum('menu', 'file'), nullable=False, default='file', comment='menu为菜单,file为权限节点')
    pid = Column(Integer, nullable=False, default=0, comment='父ID')
    name = Column(String(100), unique=True, nullable=True, default='', comment='规则名称')
    title = Column(String(50), nullable=True, default='', comment='规则名称')
    icon = Column(String(50), nullable=True, default='', comment='图标')
    url = Column(String(255), nullable=True, default='', comment='规则URL')
    condition = Column(String(255), nullable=True, default='', comment='条件')
    remark = Column(String(255), nullable=True, default='', comment='备注')
    ismenu = Column(Integer, nullable=False, default=0, comment='是否为菜单')
    menutype = Column(Enum('addtabs', 'blank', 'dialog', 'ajax'), nullable=True, comment='菜单类型')
    extend = Column(String(255), nullable=True, default='', comment='扩展属性')
    py = Column(String(30), nullable=True, default='', comment='拼音首字母')
    pinyin = Column(String(100), nullable=True, default='', comment='拼音')
    createtime = Column(BigInteger, nullable=True, comment='创建时间')
    updatetime = Column(BigInteger, nullable=True, comment='更新时间')
    weigh = Column(Integer, nullable=False, default=0, comment='权重')
    status = Column(String(30), nullable=True, default='', comment='状态')

    def __repr__(self):
        return f"<AuthRule(id={self.id}, name='{self.name}', title='{self.title}', url='{self.url}')>"
