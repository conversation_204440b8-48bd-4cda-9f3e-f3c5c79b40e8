<script setup lang="ts">
import { ref } from 'vue';
import { getAssetStatistics } from '@/service/api/assets';

// 统计数据
const totalAssets = ref(0);
const imageAssets = ref(0);
const videoAssets = ref(0);
const audioAssets = ref(0);

// 更新统计数据的方法
async function updateStatistics(params = {}) {
  try {
    const response = await getAssetStatistics(params);
    console.log('获取统计数据响应:', response);

    // 检查响应格式
    if (response && response.data) {
      // 确保响应中存在数据
      const statsData = response.data;
      totalAssets.value = Number(statsData.total || 0);
      imageAssets.value = Number(statsData.image_count || 0);
      videoAssets.value = Number(statsData.video_count || 0);
      audioAssets.value = Number(statsData.audio_count || 0);

      console.log('统计数据更新为:', {
        total: totalAssets.value,
        image: imageAssets.value,
        video: videoAssets.value,
        audio: audioAssets.value
      });
    } else {
      console.error('统计数据响应格式错误:', response);
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
  }
}

// 首次加载时获取数据
updateStatistics();

// 定义更新方法，供父组件调用
defineExpose({ updateStatistics });
</script>

<template>
  <NCard class="flex px-3">
    <NFlex justify="center" vertical align="center">
      <NText class="text-4">全部资产数量</NText>
      <div class="text-7">
        <NNumberAnimation ref="numberAnimationInstRef" :from="0" :to="totalAssets" />
      </div>
    </NFlex>

    <NDivider vertical />

    <NFlex justify="center" vertical align="center">
      <NText class="text-4">图片资产数量</NText>
      <div class="text-7">
        <NNumberAnimation ref="numberAnimationInstRef" :from="0" :to="imageAssets" />
      </div>
    </NFlex>

    <NDivider vertical />

    <NFlex justify="center" vertical align="center">
      <NText class="text-4">视频资产数量</NText>
      <div class="text-7">
        <NNumberAnimation ref="numberAnimationInstRef" :from="0" :to="videoAssets" />
      </div>
    </NFlex>

    <NDivider vertical />

    <NFlex justify="center" vertical align="center">
      <NText class="text-4">音频资产数量</NText>
      <div class="text-7">
        <NNumberAnimation ref="numberAnimationInstRef" :from="0" :to="audioAssets" />
      </div>
    </NFlex>
  </NCard>
</template>

<style scoped lang="scss">
:deep(.n-card__content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.n-divider) {
  height: 4em !important;
}
</style>
