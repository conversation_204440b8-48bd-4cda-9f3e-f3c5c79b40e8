import logging

from fastapi import APIRouter, Depends, Query, Body, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, delete

from models.users import get_roles_checker
from utils.database import get_db
from pydantic import BaseModel
from typing import List
import datetime
from models.work_teams import WorkTeams
from utils.exceptions import ClientVisibleException

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
logger = logging.getLogger(__name__)

class TeamRes(BaseModel):
  id: int
  team_code: str
  team_name: str
  email: str  
  update_time: datetime.datetime

  class Config:
    from_attributes = True

class SaveTeamRequest(BaseModel):
  id: int = 0
  team_code: str =''
  team_name: str =''
  email: str =''  

class PaginatedData(BaseModel):
  records: List[TeamRes]
  current: int
  size: int
  total: int


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()


@router.get("/get_team", response_model=PaginatedResponse, tags=["system"])
async def get_team(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  team_code: str = Query(''),
  team_name: str = Query(''),  
  db: AsyncSession = Depends(get_db)
):
  """
  获取团队管理数据
  """
  where=[]
  if team_code:
    where.append(WorkTeams.team_code.like(f'%{team_code}%'))
  if team_name:
    where.append(WorkTeams.team_name.like(f'%{team_name}%'))  
  async with db as session:
    # 获取分页数据
    result = await session.execute(
      select(WorkTeams).filter(*where).offset((page - 1) * size).limit(size)
    )
    records = result.scalars().all()

    # 获取总数
    count_res = await session.execute(select(func.count(WorkTeams.id)).filter(*where))
    total = count_res.scalar()

    records_out = [TeamRes.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )

@router.post("/save_team", tags=["system"])
async def save_team(
  save_team_data: SaveTeamRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
):
  """
    更新团队数据
  """  

  try:
    async with db as session:

      if save_team_data.id > 0:
        logger.debug("update user")
        # 更新团队信息
        result = await session.execute( select(WorkTeams).where(WorkTeams.id == save_team_data.id) )
        team_in_db = result.scalars().first()
        if not team_in_db:
          raise ClientVisibleException("团队信息不存在")

        team_in_db.team_code   = save_team_data.team_code
        team_in_db.team_name   = save_team_data.team_name
        team_in_db.email   = save_team_data.email        
        team_in_db.update_time = datetime.datetime.now()

      else:
        # 创建新角色
        new_team = WorkTeams (
          team_code=save_team_data.team_code,
          team_name=save_team_data.team_name,
          email=save_team_data.email,         
          update_time=datetime.datetime.now(),
        )
        session.add(new_team)

      await session.commit()

      return {"code": "0000", "msg": "提交成功"}

  except Exception as e:
    logger.error(f"Failed to update teams: {e}")
    raise ClientVisibleException("提交失败") from e

@router.post("/del_team", tags=["system"])
async def del_team(
  id: int = Body(0,alias='id',embed=True),
  db: AsyncSession = Depends(get_db)
):
  """
  删除数据
  """
  async with db as session:
    await session.execute( delete(WorkTeams).where(WorkTeams.id == id))    
    await session.commit()

    return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})