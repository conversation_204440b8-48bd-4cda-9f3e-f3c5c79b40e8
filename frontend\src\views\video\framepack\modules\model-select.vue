<script setup lang="ts">
import { computed, ref, watch } from 'vue';

// 定义localStorage的key
const PLATFORM_STORAGE_KEY = 'framepack_selected_platform';
const MODEL_STORAGE_KEY = 'framepack_selected_model';

// 定义props和emits
const props = defineProps<{
  modelValue?: string;
  subModelValue?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'platformChange', platform: string, model: string): void;
  (e: 'sub-model-change', value: string): void;
}>();

// 平台选择 - 优先从localStorage读取，如果没有则使用默认值'volcengine'(即梦AI)
const platform = ref(localStorage.getItem(PLATFORM_STORAGE_KEY) || 'volcengine');

// 平台选项
const platformOptions = [
  // { label: 'FramePack', value: 'framepack' },
  { label: '即梦 s2.0 pro', value: 'volcengine' }
];

// 即梦AI的模型选项
const volcengineModels = [
  { value: 'text', label: '文生视频' },
  { value: 'img', label: '图生视频' }
];

// FramePack没有子模型选项，保留一个默认选项
const framepackModels = [{ value: 'framepack', label: 'FramePack默认模型' }];

// 根据平台获取对应的模型列表
const getModelsByPlatform = () => {
  return platform.value === 'framepack' ? framepackModels : volcengineModels;
};

// 当前显示的模型列表
const currentModels = ref(getModelsByPlatform());

// 获取存储的模型或默认模型
const getSavedOrDefaultModel = () => {
  // 如果有props.modelValue，优先使用
  if (props.modelValue) {
    return props.modelValue;
  }

  // 尝试从localStorage获取模型
  const savedModel = localStorage.getItem(MODEL_STORAGE_KEY);
  if (savedModel) {
    // 验证保存的模型是否在当前平台的模型列表中
    const isValidModel = currentModels.value.some(model => model.value === savedModel);
    if (isValidModel) {
      return savedModel;
    }
  }

  // 如果没有有效的保存模型，使用当前平台的第一个模型
  return currentModels.value[0].value;
};

// 当前选择的模型
const selectedModel = ref(props.subModelValue || getSavedOrDefaultModel());

// 监听平台变化，更新模型列表和选中的模型，并保存到localStorage
watch(
  platform,
  newPlatform => {
    // 保存平台选择到localStorage
    localStorage.setItem(PLATFORM_STORAGE_KEY, newPlatform);

    currentModels.value = getModelsByPlatform();

    // 如果切换到framepack平台，强制选择img（图生视频）
    if (newPlatform === 'framepack') {
      selectedModel.value = 'img';
    } else {
      // 如果新平台支持当前模型则保持，否则使用第一个模型
      const currentModelExists = currentModels.value.some(model => model.value === selectedModel.value);
      selectedModel.value = currentModelExists ? selectedModel.value : currentModels.value[0].value;
    }

    // 保存模型选择到localStorage
    localStorage.setItem(MODEL_STORAGE_KEY, selectedModel.value);

    emit('update:modelValue', newPlatform === 'framepack' ? 'framepack' : selectedModel.value);
    // 通知父组件平台和模型变化
    emit('platformChange', newPlatform, selectedModel.value);
  },
  { immediate: true }
);

// 监听subModelValue的变化
watch(
  () => props.subModelValue,
  newValue => {
    if (newValue && newValue !== selectedModel.value) {
      selectedModel.value = newValue;
    }
  }
);

// 监听选中模型变化，向父组件发送事件并保存到localStorage
watch(selectedModel, newValue => {
  localStorage.setItem(MODEL_STORAGE_KEY, newValue);

  // 如果选择文生视频，强制设置平台为即梦AI
  if (newValue === 'text' && platform.value !== 'volcengine') {
    platform.value = 'volcengine';
    // 保存平台选择到localStorage
    localStorage.setItem(PLATFORM_STORAGE_KEY, 'volcengine');
  }

  const finalValue = platform.value === 'framepack' ? 'framepack' : newValue;
  emit('update:modelValue', finalValue);
  emit('platformChange', platform.value, newValue);

  // 向父组件发送子模型变更事件
  emit('sub-model-change', newValue);
});

// 计算属性：当平台为framepack时禁用文生视频选项
// const isTextOptionDisabled = computed(() => {
//   return platform.value === 'framepack';
// });

// 计算属性：根据选择的模型动态显示平台选项
const dynamicPlatformOptions = computed(() => {
  // 如果选择的是文生视频(text)，只显示即梦AI选项
  if (selectedModel.value === 'text') {
    return [{ label: '即梦 s2.0 pro', value: 'volcengine' }];
  }
  // 如果选择的是图生视频(img)，显示全部选项
  return platformOptions;
});

// We no longer need computed properties or helper functions since we're using direct components
</script>

<template>
  <NFlex class="w-full" vertical :size="12">
    <NRadioGroup v-model:value="selectedModel" name="model-select" class="w-full">
      <NRadioButton value="img" class="w-1/2 text-center">图生视频</NRadioButton>
      <NRadioButton value="text" class="w-1/2 text-center">文生视频</NRadioButton>
    </NRadioGroup>

    <NFlex class="w-full">
      <NSelect v-model:value="platform" class="w-full" :options="dynamicPlatformOptions" />
    </NFlex>
  </NFlex>
</template>

<style scoped>
.n-radio-group {
  margin-bottom: 12px;
}
</style>
