services:
  # nginx:
  #   container_name: aiadmin_nginx
  #   image: nginx
  #   ports:
  #     - "81:80"
  #     - "443:443"
  #   volumes:
  #     - /data/web:/data/web
  #     - /data/logs/nginx:/data/logs/nginx
  #     - ${CONFIG_PATH}/nginx/conf.d:/etc/nginx/conf.d
  #   environment:
  #     TZ: Asia/Shanghai
  #   networks:
  #     - lnmp
  #   restart: unless-stopped

  search-mcp:
    container_name: search-mcp
    image: supercorp/supergateway:latest
    ports:
      - "12025:12025"
    command: pnpm dlx supergateway --port 12025 --stdio "npx -y bing-cn-mcp"
    volumes:
      - /data/web:/data/web
    networks:
      - lnmp
    environment:
      TZ: Asia/Shanghai
    restart: unless-stopped

  nodejs:
    container_name: nodejs
    image: node:18.18.1-bullseye
    command: sh -c "apt update && apt -y install xdg-utils && npm install -g pnpm && cd /data/web/ai-admin/frontend && pnpm i && pnpm run dev:prod"
    ports:
      - 3001:3001
      - 9527:9527
    volumes:
      - /data/web:/data/web
    networks:
      - lnmp
    environment:
      TZ: Asia/Shanghai
    restart: unless-stopped

  aiadmin:
    container_name: aiadmin
    image: python:3.12-bullseye
    command: sh -c "cd /data/web/ai-admin/backend && pip install -r requirements.txt && uvicorn main:app --reload --host 0.0.0.0 --port 5002"
    volumes:
      - /data/web:/data/web
    networks:
      - lnmp
    environment:
      TZ: Asia/Shanghai
    #depends_on:
    #  - redis
    #  - mysql
    restart: unless-stopped

  #redis:
  #  container_name: redis
  #  image: redis:latest
  #  ports:
  #    - "6379:6379"
  #  networks:
  #    - lnmp
  #  restart: unless-stopped
#
  #mysql:
  #  container_name: mysql
  #  image: mysql:8.2.0
  #  ports:
  #    - "3306:3306"
  #  volumes:
  #    - ${DATA_PATH}/mysql:/var/lib/mysql
  #    - ${CONFIG_PATH}/mysql:/etc/mysql/conf.d
  #  environment:
  #    MYSQL_ROOT_PASSWORD: root
  #    TZ: Asia/Shanghai
  #  networks:
  #    - lnmp
  #  restart: unless-stopped
#
  #mjp-plus:
  #  container_name: mjp-plus
  #  image: dockerpull.com/novicezk/plus-jdk17:latest
  #  ports:
  #    - 9092:8080
  #  volumes:
  #    - /data/configs/mjplus:/home/<USER>/config
  #  environment:
  #    TZ: Asia/Shanghai
  #  networks:
  #    - lnmp
  #  restart: unless-stopped
#
  #mj-captcha:
  #  container_name: mj-captcha
  #  image: python:3.12-bullseye
  #  working_dir: /home/<USER>/config
  #  command: sh -c "pip install -r requirements.txt && python server.py"
  #  ports:
  #    - 8000:8000
  #  volumes:
  #    - /data/configs/midjourney-captcha-bot:/home/<USER>/config
  #  environment:
  #    TZ: Asia/Shanghai
  #  networks:
  #    - lnmp
  #  restart: unless-stopped

  aiadmin-task-queue:
    container_name: aiadmin-task-queue
    image: python:3.12-bullseye
    command: sh -c "cd /data/web/ai-admin/backend && pip install -r requirements.txt && cd /data/web/ai-admin/backend/task_queue && python run.py"
    volumes:
      - /data/web:/data/web
    networks:
      - lnmp
    environment:
      TZ: Asia/Shanghai
    #depends_on:
    #  - redis
    #  - mysql
    restart: unless-stopped

networks:
  lnmp:
    driver: bridge
