<script setup lang="ts">
import { reactive, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { postSaveUserCredit } from '@/service/api';

interface Props {
  user: {
    id: number;
    credit: number;
  } | null;
}

const props = defineProps<Props>();

type SubmitModel = {
  id: number;
  credit: number;
  detail: string;
};

interface Emits {
  (e: 'submitted', id: number): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  id: number;
  currentCredit: number;
  option: '+' | '-';
  delta: number;
  detail: string;
};

const model: Model = reactive({
  id: 0,
  currentCredit: 0,
  option: '+',
  delta: 0,
  detail: ''
});

type RuleKey = 'id' | 'currentCredit' | 'option' | 'delta' | 'detail';

const rules: Record<RuleKey, any> = {
  id: defaultRequiredRule,
  currentCredit: defaultRequiredRule,
  option: defaultRequiredRule,
  delta: defaultRequiredRule,
  detail: defaultRequiredRule
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  let afterCredit: number;
  if (model.option === '-') {
    afterCredit = model.currentCredit - model.delta;
  } else {
    afterCredit = model.currentCredit + model.delta;
  }
  const submitModel: SubmitModel = {
    id: model.id,
    credit: afterCredit,
    detail: model.detail
  };
  try {
    await postSaveUserCredit(submitModel);
    closeDrawer();
    emit('submitted', model.id);
    window.$notification?.success({
      title: '积分已调整',
      duration: 3000
    });
  } catch (error) {
    console.error('更新用户积分失败', error);
  }
}

watch(visible, newVal => {
  if (newVal) {
    if (props.user) {
      model.id = props.user.id;
      model.currentCredit = props.user.credit;
      model.delta = 0;
    }
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="$t('page.manage.user.creditManagement')" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="用户 ID" path="id">
          <NInputNumber v-model:value="model.id" placeholder="用户 ID" class="w-full" :show-button="false" disabled />
        </NFormItem>
        <NFormItem label="当前积分" path="currentCredit">
          <NInputNumber
            v-model:value="model.currentCredit"
            type="number"
            class="w-full"
            :show-button="false"
            disabled
          />
        </NFormItem>
        <NFormItem label="积分调整" path="delta">
          <NFlex>
            <NRadioGroup v-model:value="model.option" class="w-full" size="small">
              <NRadioButton value="+">增加积分</NRadioButton>
              <NRadioButton :disabled="model.currentCredit <= 0" value="-">扣减积分</NRadioButton>
            </NRadioGroup>
            <NInputNumber
              v-model:value="model.delta"
              type="number"
              placeholder="请输入调整的积分"
              class="w-full"
              :min="0"
              :precision="0"
              :step="10"
            />
          </NFlex>
        </NFormItem>
        <NFormItem label="积分调整原因" path="detail">
          <NInput v-model:value="model.detail" placeholder="请输入积分调整原因" type="textarea" class="w-full" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
