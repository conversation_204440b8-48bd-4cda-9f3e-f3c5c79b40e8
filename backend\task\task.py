import asyncio
import importlib
import json
import logging

from utils.redis import redis
from config import endpoint_config
from redis.exceptions import ResponseError

logger = logging.getLogger(__name__)

aiadmin_app_queue_name = endpoint_config["aiadmin_app_queue"]["queue_name"]  # 队列名称
aiadmin_app_queue_group_name = endpoint_config["aiadmin_app_queue"]["group_name"]  # 消费组名称
aiadmin_app_queue_consumer_name = endpoint_config["aiadmin_app_queue"]["consumer_name"]  # 消费者名称

RETRY_LIMIT = 3  # 重试次数限制


async def handle_task(task_id, task_data):
  # 将任务数据从二进制解码为字符串
  task_data = {k.decode('utf-8'): v.decode('utf-8') for k, v in task_data.items()}

  # 获取重试次数，如果不存在则设为0
  retries = int(task_data.get("retries", 0))

  try:
    # 获取任务模块和参数
    task_module = task_data["task_module"]
    task_argv = json.loads(task_data["task_argv"])

    # 动态导入并执行任务模块
    module = importlib.import_module(f"task.{task_module}")
    await module.process_task(task_argv)

    # 任务成功，确认消息
    await redis.xack(aiadmin_app_queue_name, aiadmin_app_queue_group_name, task_id)
    logger.info(f"任务 {task_id} 执行成功")

  except Exception as e:
    logger.error(f"handle_task 异常 : {e}")

    logger.info(f"task_id:{task_id}")
    logger.info(f"当前重试次数: {retries}")

    # 如果重试次数未达到限制，增加重试次数并添加新任务
    if retries < RETRY_LIMIT:
      retries += 1
      task_data['retries'] = str(retries)

      # 添加新任务
      new_task_id = await redis.xadd(aiadmin_app_queue_name, task_data)
      logger.info(f"任务 {task_id} 重试，新任务ID: {new_task_id}，当前重试次数: {retries}")

      # 确认并删除旧任务
      await redis.xack(aiadmin_app_queue_name, aiadmin_app_queue_group_name, task_id)
      await redis.exec('xdel', aiadmin_app_queue_name, task_id)
    else:
      # 重试次数达到限制，确认任务
      await redis.xack(aiadmin_app_queue_name, aiadmin_app_queue_group_name, task_id)
      logger.info(f"任务 {task_id} 重试次数达到上限，已确认")


async def create_group_if_not_exists():
  try:
    # 创建消费者组，如果消费者组已存在，hulv BUSYGROUP错误
    await redis.xgroup_create(
      aiadmin_app_queue_name,
      aiadmin_app_queue_group_name,
      id="0",
      mkstream=True
    )
  except ResponseError as e:
    if "BUSYGROUP" not in str(e):
      raise e


async def process_pending_tasks():
  await create_group_if_not_exists()  # 确保消费者组存在
  while True:
    try:
      # 从队列中读取任务
      tasks = await redis.xread_group(
        aiadmin_app_queue_group_name,
        aiadmin_app_queue_consumer_name,
        {aiadmin_app_queue_name: ">"},
        count=10,
        block=0
      )
      for task in tasks:
        stream, messages = task
        for message in messages:
          task_id, task_data = message
          await handle_task(task_id, task_data)  # 处理每个任务
    except ResponseError as e:
      logger.error(f"tasks异常: {e}")
      if "NOGROUP" in str(e):
        await create_group_if_not_exists()
        await asyncio.sleep(1)  # 等待一段时间再重试
      else:
        raise e


async def add_task(task_data):
  """
    向 Redis Stream 队列发送任务消息
    """
  logger.info(f"task_data: {task_data}")
  try:
    await redis.xadd(aiadmin_app_queue_name, task_data)
    logger.info("Task successfully added to Redis stream")
  except Exception as e:
    logger.error(f"Failed to add task to Redis stream: {e}")


# def run_process_pending_tasks():
#     asyncio.run(process_pending_tasks)


def init_task():
  # block = asyncio.Lock()  # 进程锁，确保任务处理的同步
  loop = asyncio.get_event_loop()
  loop.create_task(process_pending_tasks())  # 在事件循环中创建任务处理协程
