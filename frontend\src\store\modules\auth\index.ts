import { computed, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { SetupStoreId } from '@/enum';
import { useRouterPush } from '@/hooks/common/router';
import { fetchDingTalkLogin, fetchGetUserInfo, fetchLogin, fetchLogout, fetchtokenLogin } from '@/service/api';
import { localStg } from '@/utils/storage';
import { $t } from '@/locales';
import { DEFAULT_COOKIE_OPTIONS, setCookie } from '@/utils/cookie';
import { useRouteStore } from '../route';
import { useCreditStore } from '../credit';
import { clearAuthStorage, getToken } from './shared';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const router = useRouter();
  const routeStore = useRouteStore();
  const creditStore = useCreditStore();
  const {
    // toLogin,
    redirectFromLogin
  } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    userId: '',
    userName: '',
    roles: [],
    buttons: []
  });

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  // 添加初始化Promise缓存
  let initPromise: Promise<boolean> | null = null;

  // 标记是否是钉钉登录
  const isDingding = ref<boolean>(false);

  /** Reset auth store */
  async function resetStore() {
    initPromise = null;
    const authStore = useAuthStore();
    clearAuthStorage();
    isDingding.value = false; // 重置钉钉登录状态
    token.value = '';
    authStore.$reset();
    routeStore.resetStore();
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param ticket 腾讯验证码成功后返回的tick
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, ticket: string, randstr: string, redirect = true) {
    startLoading();

    // password 已经是加密后的密文,直接传给后端
    const { data: loginToken, error } = await fetchLogin(userName, password, ticket, randstr);

    if (!error) {
      const pass = await loginByToken(loginToken);

      if (pass) {
        await routeStore.initAuthRoute();

        if (redirect) {
          await redirectFromLogin();
        }

        if (routeStore.isInitAuthRoute) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', { userName: userInfo.userName }),
            duration: 4500
          });
        }
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    localStg.set('refreshToken', loginToken.refreshToken);

    // 将 token 设置到 cookie 中,使用统一配置
    setCookie('token', loginToken.token, {
      ...DEFAULT_COOKIE_OPTIONS,
      // 设置过期时间 7 天后过期
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    });

    // 2. get user info
    const pass = await getUserInfo();
    if (pass) {
      token.value = loginToken.token;
      return true;
    }
    return false;
  }

  async function getUserInfo() {
    let error;
    let info;

    if (isDingding.value) {
      const { data, error: tokenError } = await fetchtokenLogin(token.value);
      info = data;
      error = tokenError;
    } else {
      const { data, error: getUserInfoError } = await fetchGetUserInfo();
      info = data;
      error = getUserInfoError;
    }

    if (!error) {
      Object.assign(userInfo, info);
      return true;
    }

    if (error) {
      clearAuthStorage();
      return false;
    }

    return false;
  }

  async function initUserInfo() {
    if (initPromise) {
      return initPromise;
    }

    initPromise = new Promise(resolve => {
      const currentToken = getToken();
      if (currentToken) {
        fetchtokenLogin(currentToken)
          .then(({ error }) => {
            if (!error) {
              return Promise.all([routeStore.initAuthRoute(), creditStore.updateCreditInfo()]).then(() =>
                resolve(true)
              );
            }
            throw new Error('Token validation failed');
          })
          .catch(error => {
            console.error('Token validation error:', error);
            clearAuthStorage();
            // 使用统一的cookie配置
            setCookie('token', '', {
              ...DEFAULT_COOKIE_OPTIONS,
              expires: new Date(0)
            });

            // 如果当前不在登录页，则跳转到登录页
            if (route.name !== 'login') {
              window.location.href = '/login';
            }
            return resolve(false);
          });
      } else {
        resolve(false);
      }
    });

    return initPromise;
  }

  /** Handle DingTalk Login */
  async function handleDingTalkLogin() {
    // 清除之前的token和状态
    clearAuthStorage();
    token.value = '';
    isDingding.value = true;

    const { data, error } = await fetchDingTalkLogin();
    if (!error && data.login_url) {
      window.location.href = data.login_url;
    }
  }

  /** 退出登录 */
  async function logout() {
    try {
      await fetchLogout();
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      try {
        // 无论后端请求成功与否，都清除本地状态
        await resetStore();
      } finally {
        // 使用硬重定向替代路由跳转，强制刷新页面
        window.location.href = '/login';
      }
    }
  }

  return {
    token,
    userInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    routeStore,
    router,
    redirectFromLogin,
    resetStore,
    login,
    initUserInfo,
    handleDingTalkLogin,
    loginByToken,
    getUserInfo,
    logout,
    isDingding
  };
});
