<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import type { Component } from 'vue';
import {
  // getPaletteColorByNumber,
  mixColor
} from '@sa/color';
import { useRouter } from 'vue-router';
import { $t } from '@/locales';
// import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { loginModuleRecord } from '@/constants/app';
import Logo_long from '@/assets/svg-icon/logo_long.png';
import { useAuthStore } from '@/store/modules/auth';
import { getIsMobile } from '@/utils/mobile';
import PwdLogin from './modules/pwd-login.vue';
import CodeLogin from './modules/code-login.vue';
import Register from './modules/register.vue';
import ResetPwd from './modules/reset-pwd.vue';
import BindWechat from './modules/bind-wechat.vue';

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}

const props = defineProps<Props>();

// const appStore = useAppStore();
const themeStore = useThemeStore();

// 如果已经登录，那就直接跳去首页
const authStore = useAuthStore();
const router = useRouter();
onMounted(() => {
  if (authStore.isLogin) {
    router.push('/');
  }
});

// 添加 logoStyle 计算属性，在亮色主题下应用过滤器
const logoStyle = computed(() => {
  if (!themeStore.darkMode) {
    return {
      filter:
        'brightness(0) saturate(100%) invert(35%) sepia(51%) saturate(2878%) hue-rotate(230deg) brightness(91%) contrast(98%)'
    };
  }
  return {};
});

interface LoginModule {
  label: string;
  component: Component;
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: loginModuleRecord['pwd-login'], component: PwdLogin },
  'code-login': { label: loginModuleRecord['code-login'], component: CodeLogin },
  register: { label: loginModuleRecord.register, component: Register },
  'reset-pwd': { label: loginModuleRecord['reset-pwd'], component: ResetPwd },
  'bind-wechat': { label: loginModuleRecord['bind-wechat'], component: BindWechat }
};

const activeModule = computed(() => moduleMap[props.module || 'pwd-login']);

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff';

  const ratio = themeStore.darkMode ? 0.5 : 0.2;

  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio);
});

// const bgThemeColor = computed(() =>
//   themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor
// );

const isMobile = ref<boolean>(getIsMobile());
</script>

<template>
  <div
    class="login-container relative size-full flex flex-center overflow-hidden"
    :style="{ backgroundColor: bgColor }"
  >
    <!-- 背景图片层 -->
    <div v-if="!isMobile" class="login-bg-image"></div>
    <div v-else class="absolute left-0 top-0 h-full w-full bg-[#fffffe] dark:bg-[#191919]"></div>

    <!-- 波浪背景，调整透明度 -->
    <!-- <WaveBg :theme-color="bgThemeColor" class="wave-bg" /> -->

    <NCard
      :bordered="false"
      class="login-card relative z-4 w-auto rd-12px"
      :class="{
        'bg-transparent': isMobile
      }"
    >
      <div class="w-400px lt-sm:w-300px">
        <header class="flex-y-center justify-center">
          <!-- <SystemLogo class="text-64px text-primary lt-sm:text-48px" /> -->
          <!-- <h3 class="text-28px text-primary font-500 lt-sm:text-22px">{{ $t('system.title') }}</h3> -->
          <!--
 <div class="i-flex-col">
            <ThemeSchemaSwitch
              :theme-schema="themeStore.themeScheme"
              :show-tooltip="false"
              class="text-20px lt-sm:text-18px"
              @switch="themeStore.toggleThemeScheme"
            />
            <LangSwitch
              :lang="appStore.locale"
              :lang-options="appStore.localeOptions"
              :show-tooltip="false"
              @change-lang="appStore.changeLocale"
            />
          </div>
-->
          <img
            :src="Logo_long"
            alt="Logo"
            class="h-40px lt-sm:h-32px"
            loading="eager"
            fetchpriority="high"
            :style="logoStyle"
          />
        </header>
        <main class="pt-24px">
          <h3 v-if="!isMobile" class="text-18px text-primary font-medium">{{ $t(activeModule.label) }}</h3>
          <div class="pt-24px">
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.login-container {
  position: relative;
}

.login-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 直接使用相对路径，确保图片文件存在于此路径 */
  background-image: url('@/assets/imgs/login-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  /* opacity: 0.8;  */
  z-index: 1;
}

.wave-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6; /* 调整波浪透明度 */
  z-index: 2;
}

.login-card {
  /* backdrop-filter: blur(8px); */
  /* background-color: rgba(255, 255, 255, 0.8);  */
  /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); */
  z-index: 3;
}

/* 暗黑模式下的调整 */
:deep(.dark .login-card) {
  background-color: rgba(30, 30, 30, 0.8);
}
</style>
