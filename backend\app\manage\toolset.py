import logging

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from models.toolset import Toolset
from utils.database import get_db
from pydantic import BaseModel
from typing import List, Optional
from sqlalchemy import func
from fastapi.responses import JSONResponse
from sqlalchemy.sql import case
from service.file import save_image_b64data, save_image_by_url
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


class ToolsetOut(BaseModel):
  id: int
  name: str
  type: Optional[str]
  description: Optional[str]
  main_function: Optional[str]
  url: Optional[str]
  recommendation_rating: Optional[int]
  is_paid: Optional[bool]
  is_available_in_china: Optional[bool]
  origin_country: Optional[str]
  image_url: Optional[str]
  document: Optional[str]

  class Config:
    from_attributes = True


class PaginatedData(BaseModel):
  records: List[ToolsetOut]
  current: int
  size: int
  total: int


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str


class AddToolRequest(BaseModel):
  name: str
  type: str
  description: Optional[str] = None
  main_function: Optional[str] = None
  url: Optional[str] = None
  recommendation_rating: Optional[int] = None
  is_paid: Optional[bool] = None
  is_available_in_china: Optional[bool] = None
  origin_country: Optional[str] = None
  image_url: Optional[str] = None
  document: Optional[str] = None


class DeleteToolRequest(BaseModel):
  name: str


async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()


@router.get("/all_toolsets", response_model=PaginatedResponse, tags=["toolset"])
async def get_all_toolsets(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
  获取工具集数据
  """
  try:
    async with db as session:
      # 预定义分类顺序  ADD COLUMN sort number 0 COMMENT '分类排序'
      predefined_order = ['聊天', '绘图', '写作工具', '办公工具', '设计工具', '视频', '编程工具', '音乐', '其他']

      # 使用 case 语句定义自定义排序规则
      order_case = case(
        {category: index for index, category in enumerate(predefined_order)},
        value=Toolset.type
      )

      # 获取分页数据并排序
      result = await session.execute(
        select(Toolset)
        .order_by(order_case, Toolset.recommendation_rating.desc(), Toolset.id.asc())
        .offset((page - 1) * size)
        .limit(size)
      )
      records = result.scalars().all()

      # 获取总数
      total = await get_total_count(session, Toolset)

      records_out = [ToolsetOut.from_orm(record) for record in records]

      return PaginatedResponse(
        data=PaginatedData(
          records=records_out,
          current=page,
          size=size,
          total=total
        ),
        code="0000"
      )
  except Exception as e:
    logger.error(f"Error fetching toolsets: {e}")
    raise ClientVisibleException("Server Error") from e


@router.get("/type_toolsets", response_model=PaginatedResponse, tags=["toolset"])
async def get_type_toolsets(
        type: str = Query(..., alias="type"),
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
    获取指定分类的工具集数据
    """
    try:
        async with db as session:
            # 获取指定分类的分页数据
            result = await session.execute(
                select(Toolset)
                .where(Toolset.type == type)
                .order_by(Toolset.id.asc())
                .offset((page - 1) * size)
                .limit(size)
            )
            records = result.scalars().all()

            # 获取总数
            total_result = await session.execute(
                select(func.count(Toolset.id)).where(Toolset.type == type)
            )
            total = total_result.scalar()

            records_out = [ToolsetOut.from_orm(record) for record in records]

            return PaginatedResponse(
                data=PaginatedData(
                    records=records_out,
                    current=page,
                    size=size,
                    total=total
                ),
                code="0000"
            )
    except Exception as e:
        logger.error(f"Error fetching toolsets by type: {e}")
        raise ClientVisibleException("Server Error") from e


@router.get("/search", response_model=PaginatedResponse, tags=["toolset"])
async def search_toolset(
  tool_type: Optional[str] = Query(None),
  name: Optional[str] = Query(None),
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
  查询工具集数据
  """
  # 构建查询语句
  query = select(Toolset)
  if tool_type:
    query = query.where(Toolset.type == tool_type)
  if name:
    query = query.where(Toolset.name.like(f"%{name}%"))

  async with db as session:
    # 获取符合条件的总数
    total_query = select(func.count()).select_from(query.subquery())
    total_result = await session.execute(total_query)
    total = total_result.scalar()

    # 获取分页数据
    result = await session.execute(query.offset((page - 1) * size).limit(size))
    records = result.scalars().all()

    records_out = [ToolsetOut.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


@router.post("/addTool", response_model=PaginatedResponse, tags=["toolset"])
async def add_tool(
    add_tool_data: AddToolRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    添加新的工具集数据
    """
    try:
        async with db as session:
            # 验证工具名称是否在数据库中存在
            result = await session.execute(
                select(Toolset).filter(
                    Toolset.name == add_tool_data.name
                )
            )
            existing_tool = result.scalars().first()
            if existing_tool:
                raise ClientVisibleException("工具已存在")

            # 处理 image_url，上传到 OSS 并获取返回的 URL
            uploaded_image_url = None
            if add_tool_data.image_url:
                # print(f"image_url: {add_tool_data.image_url}")
                try:
                    if add_tool_data.image_url.startswith(("http://", "https://")):
                        # print("image_url is a URL")
                        uploaded_image_url = await save_image_by_url(
                            add_tool_data.image_url
                        )
                    else:
                        # print("image_url is a base64 data")
                        uploaded_image_url = save_image_b64data(
                            add_tool_data.image_url.split(",")[1],
                            f"{add_tool_data.name}.png"
                        )
                except Exception as e:
                    logger.error(f"Failed to upload image: {e}")
                    raise ClientVisibleException("上传图片失败") from e

            # 添加新的工具集数据
            new_tool = Toolset(
                name=add_tool_data.name,
                type=add_tool_data.type,
                description=add_tool_data.description,
                main_function=add_tool_data.main_function,
                url=add_tool_data.url,
                recommendation_rating=add_tool_data.recommendation_rating,
                is_paid=add_tool_data.is_paid,
                is_available_in_china=add_tool_data.is_available_in_china,
                origin_country=add_tool_data.origin_country,
                image_url=uploaded_image_url,  # 保存图片URL
                document=add_tool_data.document
            )
            session.add(new_tool)
            await session.commit()

            # 获取分页数据
            page = 1
            size = 10
            result = await session.execute(
                select(Toolset)
                .offset((page - 1) * size)
                .limit(size)
            )
            records = result.scalars().all()

            # 获取总数
            total = await get_total_count(session, Toolset)

            records_out = [ToolsetOut.from_orm(record) for record in records]

            return PaginatedResponse(
                data=PaginatedData(
                    records=records_out,
                    current=page,
                    size=size,
                    total=total
                ),
                code="0000"
            )

    except Exception as e:
        logger.error(f"Failed to add tool: {e}")
        raise ClientVisibleException("添加工具失败") from e


@router.post("/update", response_model=PaginatedResponse, tags=["toolset"])
async def update_tool(
    update_tool_data: AddToolRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    更新工具集数据
    """
    try:
        async with db as session:
            # 根据 name 查找对应的工具
            result = await session.execute(
                select(Toolset).filter(Toolset.name == update_tool_data.name)
            )
            existing_tool = result.scalars().first()

            if not existing_tool:
                raise ClientVisibleException("工具不存在")

            # 更新工具信息
            if update_tool_data.type is not None:
                existing_tool.type = update_tool_data.type
            if update_tool_data.description is not None:
                existing_tool.description = update_tool_data.description
            if update_tool_data.main_function is not None:
                existing_tool.main_function = update_tool_data.main_function
            if update_tool_data.url is not None:
                existing_tool.url = update_tool_data.url
            if update_tool_data.recommendation_rating is not None:
                existing_tool.recommendation_rating = update_tool_data.recommendation_rating
            if update_tool_data.is_paid is not None:
                existing_tool.is_paid = update_tool_data.is_paid
            if update_tool_data.is_available_in_china is not None:
                existing_tool.is_available_in_china = update_tool_data.is_available_in_china
            if update_tool_data.origin_country is not None:
                existing_tool.origin_country = update_tool_data.origin_country
            if update_tool_data.document is not None:
                existing_tool.document = update_tool_data.document

            # 处理 image_url，上传到 OSS 并获取返回的 URL
            if update_tool_data.image_url and update_tool_data.image_url != existing_tool.image_url:
                try:
                    if update_tool_data.image_url.startswith(("http://", "https://")):
                        uploaded_image_url = await save_image_by_url(
                            update_tool_data.image_url
                        )
                    else:
                        uploaded_image_url = save_image_b64data(
                            update_tool_data.image_url.split(",")[1],
                            f"{update_tool_data.name}.png"
                        )
                    existing_tool.image_url = uploaded_image_url
                except Exception as e:
                    logger.error(f"Failed to upload image: {e}")
                    raise ClientVisibleException("上传图片失败") from e

            await session.commit()

            # 获取分页数据
            page = 1
            size = 10
            result = await session.execute(
                select(Toolset)
                .offset((page - 1) * size)
                .limit(size)
            )
            records = result.scalars().all()

            # 获取总数
            total = await get_total_count(session, Toolset)

            records_out = [ToolsetOut.from_orm(record) for record in records]

            return PaginatedResponse(
                data=PaginatedData(
                    records=records_out,
                    current=page,
                    size=size,
                    total=total
                ),
                code="0000"
            )

    except Exception as e:
        logger.error(f"Failed to update tool: {e}")
        raise ClientVisibleException("更新工具信息失败") from e


@router.post("/delete", response_model=PaginatedResponse, tags=["toolset"])
async def delete_tool(
  request: DeleteToolRequest,
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
  删除工具集数据
  """
  async with db as session:
    # 查找要删除的数据
    result = await session.execute(select(Toolset).where(Toolset.name == request.name))
    tool = result.scalars().first()

    if not tool:
        raise ClientVisibleException("删除失败")

    # 删除数据
    await session.delete(tool)
    await session.commit()

    # 获取删除后的分页数据
    result = await session.execute(
      select(Toolset)
      .offset((page - 1) * size)
      .limit(size)
    )
    records = result.scalars().all()

    # 获取总数
    total = await get_total_count(session, Toolset)

    records_out = [ToolsetOut.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )
