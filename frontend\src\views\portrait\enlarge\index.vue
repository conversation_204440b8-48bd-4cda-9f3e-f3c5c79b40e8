<script setup lang="ts">
import { ref } from 'vue';
import UseVolcengine from './modules/use-volcengine.vue';
import UseLocation from './modules/old-uselocation.vue';
//
const tabModel = ref<string>('new_enlarge');
</script>

<template>
  <div class="flex-row-stretch gap-16px">
    <NTabs v-model:value="tabModel" type="line" animated class="mainBox">
      <NTabPane name="new_enlarge" tab="图像增强" class="h-full">
        <UseVolcengine class="h-full" />
      </NTabPane>
      <NTabPane name="old_enlarge" tab="视频放大">
        <UseLocation />
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped lang="scss">
// :deep(.n-tab-pane),
:deep(.n-tabs-pane-wrapper)
// :deep(.image-enhance-container)
{
  height: 78vh !important;
}
</style>
