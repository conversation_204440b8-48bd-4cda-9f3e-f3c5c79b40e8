from sqlalchemy import Column, String, Text, TIMESTAMP
from utils.database import Base
import datetime

class GameManagement(Base):
    __tablename__ = "game_management"

    gamecode = Column(String(32), primary_key=True, nullable=False, comment='游戏编号，唯一标识每个游戏')
    gamename = Column(String(32), nullable=False, comment='游戏名称')
    english_name = Column(String(32), nullable=True, comment='游戏英文名称')
    info = Column(Text, nullable=True, comment='游戏资料，包括游戏简介、特色和角色介绍')
    lang = Column(String(32), nullable=True, comment='游戏支持的语言列表，例如 zh-cn,en')
    create_time = Column(TIMESTAMP, default=datetime.datetime.now, nullable=True, comment='数据创建时间')
    editor = Column(String(32), nullable=True, comment='修改者，记录最后修改该数据的用户')
    uptime = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True, comment='数据更新日期，自动更新为当前时间')
    company = Column(String(32), nullable=True, comment='游戏所属的公司')

