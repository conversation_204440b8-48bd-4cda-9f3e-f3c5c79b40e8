from datetime import datetime
from enum import StrEnum

from sqlalchemy import Column, DateTime, Enum, String, JSON
from sqlalchemy.dialects.mysql import INTEGER

from utils.database import Base


class ShareType(StrEnum):
    IMAGE = 'IMAGE'
    VIDEO = 'VIDEO'


def long_term_factory():
    """生成长期有效的过期时间"""
    return datetime.fromisoformat("2099-12-31T23:59:59+08:00")


class Share(Base):
    __tablename__ = "share_records"

    id = Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True, comment='分享 ID')
    share_key = Column(String(32), unique=True, nullable=False, comment='分享 Key，用于放到 URL 中标识一次分享')
    share_type = Column(Enum(ShareType, length=16), nullable=False, comment='分享类型：image/video')
    params = Column(JSON, default=dict, comment='生成参数')
    result = Column(JSON, default=dict, comment='生成成果')
    creator = Column(String(32), nullable=False, comment='创建分享的 username')
    expire_time = Column(DateTime, nullable=False, default=long_term_factory, comment='创建时间')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    def __repr__(self):
      return f"<Share(key={self.share_key}, share_type='{self.share_type}')>"
