import asyncio
import logging

from config import app_settings
from models.chat import chatContent
from typing import List
from openai import OpenAI
from service.openai.prompts import get_system_prompt
import tiktoken

logger = logging.getLogger(__name__)


def get_token():
  return app_settings.deepseek_api_key



def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:
  """返回给定字符串的token数量"""
  encoding = tiktoken.get_encoding(encoding_name)
  num_tokens = len(encoding.encode(string))
  return num_tokens


client = OpenAI(api_key=get_token(), base_url="https://api.deepseek.com")


def chat(
  model_name: str,
  contents: List[chatContent],
  systemPrompt: str = "",
  memory: str = "name is 胖大星",
  temperature: float = 0.8,
  mcp: bool = False,
  web_search: bool = False,
  loop: asyncio.AbstractEventLoop = None,
):
  system_prompt = get_system_prompt(model_name, memory)

  if systemPrompt:
    system_prompt = systemPrompt

  # print(system_prompt, flush=True)
  messages = [{"role": "system", "content": system_prompt}]
  input_text = system_prompt
  for r in contents:
    input_text += r.content
    content = {
      "role": "user" if r.role == "user" else "assistant",
      "content": [{"type": "text", "text": r.content}],
    }
    if r.files:
      for f in r.files:
        if f.type == "image":
          content["content"].append(
            {"type": "image_url", "image_url": {"url": f.url}}
          )
    messages.append(content)
  # 要发送的消息

  completion = client.chat.completions.create(
    model=model_name, messages=messages, max_tokens=4096, stream=True
  )

  all_text = ""
  for chunk in completion:
    logger.debug(chunk)
    text = chunk.choices[0].delta.content
    text = text if text is not None else ""
    all_text += text
    yield (text)

  logger.debug("-------------------------- done ")
  # 返回总token数
  yield (
    {
      "input_tokens": num_tokens_from_string(input_text),
      "output_tokens": num_tokens_from_string(all_text),
    }
  )
