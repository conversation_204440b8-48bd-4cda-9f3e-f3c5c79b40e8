from sqlalchemy import Column, Integer, String, Text, BigInteger
from utils.database import Base


class AuthGroup(Base):
    __tablename__ = "auth_group"

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    pid = Column(Integer, nullable=False, default=0, comment='父组别')
    name = Column(String(100), nullable=True, default='', comment='组名')
    rules = Column(Text, nullable=False, comment='规则ID')
    createtime = Column(BigInteger, nullable=True, comment='创建时间')
    updatetime = Column(BigInteger, nullable=True, comment='更新时间')
    status = Column(String(30), nullable=True, default='', comment='状态')

    def __repr__(self):
        return f"<AuthGroup(id={self.id}, name='{self.name}', status='{self.status}')>"
