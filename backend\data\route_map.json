{"/openapi.json": "openapi", "/docs": "swagger_ui_html", "/docs/oauth2-redirect": "swagger_ui_redirect", "/redoc": "redoc_html", "/upload": "upload", "/data": "data", "/auth/login": "login", "/auth/dingding_login": "dingding_login", "/auth/dingding_callback": "dingtalk_callback", "/auth/getUserInfo": "getUserInfo", "/auth/token_login": "token_login", "/auth/logout": "logout", "/auth/test": "test", "/text/get_files_presets": "get_files_presets", "/text/read_excel": "read_excel", "/text/translate": "translate", "/text/get_presets": "get_presets", "/text/save_presets": "save_presets", "/text/add_presets": "upload_presets", "/text/delete_presets": "delete_presets", "/text/search_presets": "search_presets", "/text/upload_translation_presets": "upload_translation_presets", "/text/stop_generation": "stop_generation", "/text/generate_copywriting": "generate_copywriting", "/text/get_my_history": "get_history", "/media/extract_subtitle": "extract_subtitle", "/media/translate_subtitle": "translate_subtitle", "/gameManage/all_game_management": "get_all_game_management", "/gameManage/search": "search_game_management", "/gameManage/delete": "delete_game_management", "/gameManage/addGame": "add_game_management", "/gameManage/update": "update_game_management", "/toolset/all_toolsets": "get_all_toolsets", "/toolset/type_toolsets": "get_type_toolsets", "/toolset/search": "search_toolset", "/toolset/addTool": "add_tool", "/toolset/update": "update_tool", "/toolset/delete": "delete_tool", "/common/upload/{upload_type}": "upload_file", "/common/upload": "upload", "/cutout/get_cutout_img": "get_cutout_img", "/media/voice_separate": "voice_separate", "/media/tts": "tts", "/media/chat_tts": "chat_tts", "/media/get_role_voice_example": "get_role_voice_example", "/media/get_voice/{role}/{voice}": "get_voice", "/enlarge/get_enlarge_file": "get_enlarge_file", "/cosy/speaker": "get_speaker", "/cosy/task": "task", "/cosy/zero_shot": "zero_shot", "/aidraw/get_resolution": "get_resolution", "/aidraw/get_preset": "get_preset", "/aidraw/get_style_image": "get_style_image", "/aidraw/predict": "predict", "/facecopy/upload": "upload", "/facecopy/send_prompt": "send_prompt", "/facecopy/queue_status": "get_queue_status", "/facecopy/history/{prompt_id}": "get_history", "/facecopy/view": "view_image", "/facecopy/isvedio": "generate_video", "/facecopy/Upload": "upload_file", "/facecopy/isAnimal": "generate_animalvideo", "/mimicbrush/task": "task", "/mimicbrush/history/{prompt_id}": "history", "/user/get_user_info": "get_user_info", "/user/set_user_info": "set_user_info", "/log/analyze": "get_all_log", "/log/get_route_map": "get_route_map", "/midjourney/get_my_tasks": "getMyTask", "/midjourney/submit/imagine": "imagine", "/midjourney/submit/change": "change", "/midjourney/get_task_state": "getTaskState", "/system/getUserList": "get_user", "/system/updateUser": "update_user", "/system/deleteUser": "delete_user", "/system/get_role": "get_role", "/system/get_role_menu": "get_role_menu", "/system/get_role_user": "get_role_user", "/system/set_role_user": "set_role_user", "/system/get_menu_role": "get_menu_role", "/system/set_menu_role": "set_menu_role", "/system/set_role_menu": "set_role_menu", "/system/save_role": "save_role", "/system/del_role": "del_role", "/system/getAllPages": "getAllPages", "/system/get_menu": "get_menu", "/system/get_menu_tree": "get_menu_tree", "/system/get_user_menu": "get_user_menu", "/system/get_const_menu": "get_const_menu", "/system/save_menu": "save_menu", "/system/del_menu": "del_menu", "/text/get_company_games": "get_company_games", "/log/task_analyze": "task_analyze", "/log/page_visit": "page_visit", "/midjourney/get_search_tasks": "get_search_tasks"}