{"6": {"inputs": {"text": ["62", 0], "clip": ["11", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "11": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "12": {"inputs": {"unet_name": "flux1-dev-kontext_fp8_scaled.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["42", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "25": {"inputs": {"noise_seed": 1019387518609790}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "26": {"inputs": {"guidance": 2.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "27": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "空Latent图像（SD3）"}}, "30": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 1024, "height": 1024, "model": ["12", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "采样算法（Flux）"}}, "39": {"inputs": {"pixels": ["40", 0], "vae": ["10", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "40": {"inputs": {"image": ["41", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "41": {"inputs": {"image": "mrz6942_miku_022dec67-d429-4bf4-9264-a257a7417a54.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "42": {"inputs": {"conditioning": ["26", 0], "latent": ["39", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "59": {"inputs": {"trans_switch": true, "trans_text": "把头发换成绿色", "translator": "Alibaba", "source_language": "auto", "target_language": "English(en)", "Show_Hide_button": "button_show"}, "class_type": "Text_Translation_V2", "_meta": {"title": "Text Translation V2"}}, "62": {"inputs": {"separator": "", "text1": ["59", 0]}, "class_type": "CR Text Concatenate", "_meta": {"title": "🔤 CR Text Concatenate"}}}