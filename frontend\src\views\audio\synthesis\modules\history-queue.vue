<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useScreenDetection } from '@/utils/detectionScreen';
import {
  // getSynthesisTask,
  getSynthesisTaskList
} from '@/service/api/audio';
import type { SynthesisTaskStatus, TaskItem } from '@/service/api/audio';

const { isLargeScreen } = useScreenDetection(1500);

// 定义 emit
const emit = defineEmits<{
  showAudioInfo: [item: AudioItem];
  deleteAudio: [item: AudioItem];
  downloadAudio: [item: AudioItem];
  retryAudio: [item: AudioItem];
  playAudio: [item: AudioItem];
  taskUpdated: [taskId: number, updatedTask: AudioItem];
}>();

// 定义音频项目接口，基于API返回的TaskItem
interface AudioItem {
  id: number;
  status: SynthesisTaskStatus;
  text: string;
  model: string;
  duration: string;
  createTime: string;
  url: string;
  isPlaying?: boolean;
  params: any;
}

// 音频列表数据
const audioList = ref<AudioItem[]>([]);
const loading = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);

// 注意：轮询逻辑已移至父组件 index.vue 中统一管理

// 将API返回的TaskItem转换为AudioItem
const transformTaskToAudioItem = (task: TaskItem): AudioItem => {
  // 提取文本内容（从params.segs中获取）
  const text = task.params?.segs?.map(seg => seg.text).join(' ') || '';

  // 提取音色信息（从第一个seg获取，优先显示音色名称）
  const firstSeg = task.params?.segs?.[0];
  const model = firstSeg?.tone_name || firstSeg?.model_name || '未知音色';

  // 格式化时长（秒转换为分:秒格式）
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 格式化创建时间
  const formatCreateTime = (dateString: string): string => {
    const date = new Date(dateString);
    return date
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
      .replace(/\//g, '-');
  };

  return {
    id: task.id,
    status: task.status,
    text,
    model,
    duration: formatDuration(task.duration),
    createTime: formatCreateTime(task.create_time),
    url: task.url,
    params: task.params,
    isPlaying: false
  };
};

// 更新单个任务的数据（由父组件调用）
const updateTaskData = (taskId: number, updatedTask: TaskItem) => {
  const index = audioList.value.findIndex(item => item.id === taskId);
  if (index !== -1) {
    const updatedAudioItem = transformTaskToAudioItem(updatedTask);
    audioList.value[index] = updatedAudioItem;

    // 通知父组件任务状态已更新
    emit('taskUpdated', taskId, updatedAudioItem);
  }
};

// 加载合成任务列表
const loadSynthesisTaskList = async (reset = false) => {
  if (loading.value) return;

  // 如果是重置，或者还有更多数据才继续加载
  if (!reset && !hasMore.value) return;

  loading.value = true;

  try {
    if (reset) {
      currentPage.value = 1;
      audioList.value = [];
      hasMore.value = true;
    }

    const { data } = await getSynthesisTaskList({
      size: pageSize.value,
      current: currentPage.value
    });

    if (!data) {
      console.warn('API返回的数据为空');
      return;
    }

    const records = data.records || [];
    const transformedRecords = records.map(transformTaskToAudioItem);

    if (reset) {
      audioList.value = transformedRecords;
    } else {
      audioList.value.push(...transformedRecords);
    }

    // 检查是否还有更多数据
    if (records.length < pageSize.value) {
      hasMore.value = false;
    } else {
      hasMore.value = true;
    }

    // 只有在非重置模式且成功加载数据后才增加页码
    if (!reset && records.length > 0) {
      currentPage.value += 1;
    }

    // 轮询逻辑已移至父组件，这里不再需要启动轮询
  } catch (error) {
    console.error('加载合成任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 无限滚动加载处理
const handleInfiniteLoad = () => {
  if (!hasMore.value || loading.value) {
    return;
  }

  // 直接调用loadSynthesisTaskList，不需要reset参数，因为这是追加加载
  loadSynthesisTaskList();
};

// 获取状态对应的图标
const getStatusIcon = (status: AudioItem['status'], isPlaying?: boolean) => {
  switch (status) {
    case 'SUCCESS':
      return isPlaying ? 'gridicons:pause' : 'lsicon:play-filled';
    case 'FAILURE':
      return 'lets-icons:info-fill';
    case 'IN_PROGRESS':
    case 'SUBMITTED':
      return 'eos-icons:loading';
    default:
      return 'eos-icons:loading';
  }
};

// 获取状态对应的文本
const getStatusText = (status: AudioItem['status']) => {
  switch (status) {
    case 'SUCCESS':
      return '';
    case 'FAILURE':
      return '生成失败！';
    case 'SUBMITTED':
      return '排队中...';
    case 'IN_PROGRESS':
      return '生成中..';
    default:
      return '';
  }
};

// 事件处理函数
const showAudioInfo = (item: AudioItem) => {
  emit('showAudioInfo', item);
};

const deleteAudio = (item: AudioItem) => {
  emit('deleteAudio', item);
};

const downloadAudio = (item: AudioItem) => {
  emit('downloadAudio', item);
};

const retryAudio = (item: AudioItem) => {
  emit('retryAudio', item);
};

const playAudio = (item: AudioItem) => {
  emit('playAudio', item);
};

// 组件挂载时初始化数据
onMounted(async () => {
  await loadSynthesisTaskList(true);
});

// 暴露重新初始化方法给父组件
const refreshData = async () => {
  await loadSynthesisTaskList(true);
};

// 重置所有音频的播放状态
const resetAllPlayingStates = () => {
  audioList.value.forEach(item => {
    item.isPlaying = false;
  });
};

// 更新指定音频的播放状态
const updatePlayingState = (id: number, isPlaying: boolean) => {
  const item = audioList.value.find(audio => audio.id === id);
  if (item) {
    item.isPlaying = isPlaying;
  }
};

// 暴露方法给父组件
defineExpose({
  refreshData,
  resetAllPlayingStates,
  updatePlayingState,
  updateTaskData
});
</script>

<template>
  <NInfiniteScroll
    class="w-full"
    :class="isLargeScreen ? 'h-46.5em' : 'h-35em'"
    :distance="50"
    @load="handleInfiniteLoad"
  >
    <NCard v-for="item in audioList" :key="item.id" class="audio_info mb-3">
      <NFlex justify="space-between" :wrap="false" align="center">
        <NFlex class="mr-3">
          <!-- 音频控制 播放 暂停 -->
          <NButton v-if="item.status === 'SUCCESS'" text @click="playAudio(item)">
            <SvgIcon :icon="getStatusIcon(item.status, item.isPlaying)" class="text-4xl" />
          </NButton>

          <SvgIcon v-else :icon="getStatusIcon(item.status)" class="text-4xl" />
        </NFlex>

        <NFlex vertical>
          <NEllipsis class="elipsis_text text-sm">
            {{ item.text }}
            <template #tooltip>
              <div class="max-w-320px text-center">
                <span>{{ item.text }}</span>
              </div>
            </template>
          </NEllipsis>

          <NFlex v-if="item.status === 'SUCCESS'">
            <NFlex :wrap="false" justify="center" align="center" class="success_info_text text-0.75em">
              <NText>{{ item.model }}</NText>
              <NDivider vertical />
              <NText>{{ item.duration }}</NText>
              <NDivider vertical />
              <NText>{{ item.createTime }}</NText>
            </NFlex>
          </NFlex>

          <NFlex v-else>
            <NText class="text-0.75em">{{ getStatusText(item.status) }}</NText>
          </NFlex>
        </NFlex>
      </NFlex>

      <!-- 交互按钮 -->
      <NFlex v-if="item.status === 'SUCCESS'">
        <NButton text @click="deleteAudio(item)">
          <SvgIcon icon="material-symbols-light:delete" class="text-xl" />
        </NButton>

        <NButton text @click="showAudioInfo(item)">
          <SvgIcon icon="material-symbols:text-ad-outline-rounded" class="text-xl" />
        </NButton>

        <NButton text @click="downloadAudio(item)">
          <SvgIcon icon="ep:download" class="text-xl" />
        </NButton>
      </NFlex>

      <NFlex v-else-if="item.status === 'FAILURE'">
        <NButton text @click="retryAudio(item)">
          <SvgIcon icon="ic:outline-refresh" class="text-2xl" />
        </NButton>

        <NButton text @click="deleteAudio(item)">
          <SvgIcon icon="material-symbols-light:delete" class="text-xl" />
        </NButton>
      </NFlex>
    </NCard>

    <!-- 加载状态 -->
    <NFlex v-if="loading" justify="center" class="py-8">
      <NSpin size="medium" />
      <NText class="ml-2 text-gray-400">加载中...</NText>
    </NFlex>

    <!-- 无更多数据提示 -->
    <NFlex v-if="!hasMore && audioList.length > 0" justify="center" class="py-4">
      <NText class="text-sm text-gray-400">没有更多数据了</NText>
    </NFlex>

    <!-- 空状态 -->
    <NFlex v-if="!loading && audioList.length === 0" justify="center" class="py-8">
      <NText class="text-gray-500">暂无历史记录</NText>
    </NFlex>
  </NInfiniteScroll>
</template>

<style scoped lang="scss">
.audio_info :deep(.n-card__content) {
  display: flex !important;
  justify-content: space-between;
  padding: 10px !important;
}

:deep(.elipsis_text) {
  max-width: v-bind('isLargeScreen ? "20em" : "12em"');
}

:deep(.success_info_text) {
  gap: 0px !important;
}
</style>
