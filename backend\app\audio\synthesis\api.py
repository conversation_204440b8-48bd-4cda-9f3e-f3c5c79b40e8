import logging
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Annotated, Sequence
from urllib import parse

from fastapi import APIRouter, Depends, Path, Query, BackgroundTasks
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.audio.tones import Lang
from models.assets import Assets
from models.audio_synthesis import AudioPretrainedTonesStatus, AudioSynthesisHistory, Status
from models.audio_tones import AudioTone
from models.users import User, get_request_user
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.response import SuccessRes, Pagination

from .http_model import (
    TaskItem,
    CreateTaskReq,
    Tone,
    ToneType,
    ModifyFavoriteStatusReq,
    PretrainedToneCategory,
    ModelFunction, ParamsOut, CreateTaskRes, pretrained_model_function_desc, fast_clone_model_function_desc
)
from .pretrained_tones import pretrained_tone_categories, pretrained_tones, get_pretrained_tone_by_id
from .runner import do_synthesis, SynthesisParams, biz_id

router = APIRouter()
logger = logging.getLogger(__name__)
RECENT_DELTA = timedelta(days=3)


@router.get(
    "/pretrained_tone_categories",
    dependencies=[Depends(get_request_user)],
    response_model=SuccessRes[list[PretrainedToneCategory]]
)
async def get_pretrained_tone_categories():
    """获取预训练音色的分类列表"""
    cs = [PretrainedToneCategory.model_validate(c)  for c in pretrained_tone_categories]
    return SuccessRes(data=cs)


@router.get(
    "/tones",
    dependencies=[Depends(get_request_user)],
    response_model=SuccessRes[Pagination[Tone]]
)
async def get_tones(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        tone_type: Annotated[ToneType | None, Query(description='音色类型')] = None,
        category: Annotated[str | None, Query(description='分类')] = None,
        is_recent: Annotated[bool | None, Query(description='是否获取最近使用音色')] = None,
        is_favorite: Annotated[bool | None, Query(description='是否获取已收藏音色')] = None,
        search_text: Annotated[str | None, Query(description="搜索字符串")] = None,
        size: Annotated[int, Query(ge=1, description='分页大小')] = 10,
        current: Annotated[int, Query(ge=1, description='页码')] = 1,
):
    """获取音频模块可用的所有音色列表，包含音色库音色和预训练音色，支持分页"""
    if search_text is not None:
        search_text = parse.unquote(search_text).strip()
        if not search_text:
            search_text = None

    now = datetime.now()
    tones: list[Tone] = []
    # 简单处理，先把所有音色拉回来，然后再分页处理
    recent_tone_ids: dict[tuple[ToneType, int | str], int] = defaultdict(lambda :0)  # { (type, 'id'): count }
    if is_recent is not None:
        async with db as session:
            recent_res = await session.execute(
                select(AudioSynthesisHistory)
                .where(AudioSynthesisHistory.user_id == user.id)
                .where(AudioSynthesisHistory.create_time >= now - RECENT_DELTA)
            )
            histories: Sequence[AudioSynthesisHistory] = recent_res.scalars().all()
            for history in histories:
                params = history.params
                segs = params.get('segs', [])
                for seg in segs:
                    _tone_type: ToneType | None = seg.get('tone_type', None)
                    _tone_id: str | int | None = seg.get('tone_id', None)
                    if _tone_type is not None and _tone_id is not None:
                        recent_tone_ids[(_tone_type, _tone_id)] += 1
    if (tone_type is None or tone_type == ToneType.CUSTOM) and category is None:
        async with db as session:
            tone_stmt = (
                select(AudioTone)
                .where(AudioTone.user_id == user.id)
                .order_by(AudioTone.update_time.desc())
            )
            if search_text is not None:
                tone_stmt = tone_stmt.where(AudioTone.name.like(f"%{search_text}%"))
            if is_favorite is not None:
                tone_stmt = tone_stmt.where(AudioTone.is_favorite == is_favorite)
            if is_recent is not None:
                custom_recent = [k[1] for k in recent_tone_ids.keys() if k[0] == ToneType.CUSTOM]
                tone_stmt = tone_stmt.where(AudioTone.id.in_(custom_recent))
            tone_res = await session.execute(tone_stmt)
            custom_tones: Sequence[AudioTone] = tone_res.scalars().all()
            for custom_tone in custom_tones:
                tone = Tone(
                    tone_type=ToneType.CUSTOM,
                    tone_id=custom_tone.id,
                    tone_name=custom_tone.name,
                    description=custom_tone.description,
                    prompt_text=custom_tone.prompt_text,
                    is_favorite=custom_tone.is_favorite,
                    url=custom_tone.audio_url,
                    model_function=ModelFunction.FAST_CLONE,
                    gender=custom_tone.gender,
                    lang=custom_tone.lang,
                )
                tones.append(tone)

    if tone_type is None or tone_type == ToneType.PRETRAINED:
        async with db as session:
            if is_favorite is not None:
                fav_res = await session.execute(
                    select(AudioPretrainedTonesStatus.tone_id)
                    .where(AudioPretrainedTonesStatus.user_id == user.id)
                    .where(AudioPretrainedTonesStatus.is_favorite == is_favorite)
                )
                fav_tone_ids = set(fav_res.scalars().all())
        for pretrained_tone in pretrained_tones:
            if is_favorite is not None and pretrained_tone['id'] not in fav_tone_ids:
                continue
            if is_recent:
                if (ToneType.PRETRAINED, pretrained_tone['id']) not in recent_tone_ids:
                    continue
            if category is not None and category not in pretrained_tone['categories']:
                continue
            if search_text is not None and search_text not in pretrained_tone['name']:
                continue
            tone = Tone(
                tone_type=ToneType.PRETRAINED,
                tone_id=pretrained_tone['id'],
                tone_name=pretrained_tone['name'],
                description=pretrained_tone['description'],
                prompt_text=pretrained_tone['prompt_text'],
                is_favorite=bool(is_favorite),
                url=pretrained_tone['audio_url'],
                model_function=pretrained_tone['model_function'],
                gender=pretrained_tone['gender'],
                lang=pretrained_tone['lang'],
            )
            tones.append(tone)

    # 开始排序
    if is_recent:
        tones.sort(key=lambda t: recent_tone_ids[(t.tone_type, t.tone_id)], reverse=True)

    start = (current - 1) * size
    return SuccessRes(
        data=Pagination(
            records=tones[start:(start + size)],
            size=size,
            current=current,
            total=len(tones),
        )
    )


@router.post(
    "/tones/{tone_id}/is_favorite",
    dependencies=[Depends(get_request_user)],
    response_model=SuccessRes[Tone]
)
async def change_tone_favorite_status(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        tone_id: Annotated[int | str, Path(description='音色标识')],
        payload: ModifyFavoriteStatusReq,
):
    """更改音色的收藏状态，支持音色库音色及预训练音色"""
    match payload.tone_type:
        case ToneType.PRETRAINED:
            _pretrained_tone = get_pretrained_tone_by_id(tone_id)
            if _pretrained_tone is None:
                raise ClientVisibleException(f"找不到音色：{tone_id}")
            async with db as session:
                stmt = (
                    select(AudioPretrainedTonesStatus)
                    .where(AudioPretrainedTonesStatus.user_id == user.id)
                    .where(AudioPretrainedTonesStatus.tone_id == tone_id)
                )
                res = await session.execute(stmt)
                _status: AudioPretrainedTonesStatus | None = res.scalars().first()
                if _status is None:
                    _status = AudioPretrainedTonesStatus(
                        user_id=user.id,
                        tone_id=tone_id,
                        is_favorite=payload.is_favorite,
                    )
                else:
                    _status.is_favorite = payload.is_favorite
                session.add(_status)
                await session.commit()
                return SuccessRes(
                    data=Tone(
                        tone_type=ToneType.PRETRAINED,
                        tone_id=_pretrained_tone['id'],
                        tone_name=_pretrained_tone['name'],
                        description=_pretrained_tone['description'],
                        prompt_text=_pretrained_tone['prompt_text'],
                        is_favorite=payload.is_favorite,
                        url=_pretrained_tone['audio_url'],
                        model_function=_pretrained_tone['model_function'],
                        gender=_pretrained_tone['gender'],
                        lang=_pretrained_tone['lang'],
                    )
                )
        case ToneType.CUSTOM:
            async with db as session:
                stmt = (
                    select(AudioTone)
                    .where(AudioTone.user_id == user.id)
                    .where(AudioTone.id == tone_id)
                )
                res = await session.execute(stmt)
                _tone: AudioTone | None = res.scalars().first()
                if _tone is None:
                    raise ClientVisibleException(f"找不到音色：{tone_id}")
                if payload.is_favorite != _tone.is_favorite:
                    _tone.is_favorite = payload.is_favorite
                    session.add(_tone)
                    await session.commit()
                    await session.refresh(_tone)
                return SuccessRes(
                    data=Tone(
                        tone_type=payload.tone_type,
                        tone_id=_tone.id,
                        tone_name=_tone.name,
                        description=_tone.description,
                        prompt_text=_tone.prompt_text,
                        is_favorite=bool(_tone.is_favorite),
                        url=_tone.audio_url,
                        model_function=ModelFunction.FAST_CLONE,
                        gender=_tone.gender,
                        lang=_tone.lang,
                    )
                )
        case _:
            raise ClientVisibleException(f"不支持的音色类型: {payload.tone_type}")

@router.post("/tasks", response_model=SuccessRes[CreateTaskRes])
async def create_task(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        payload: CreateTaskReq,
        background_tasks: BackgroundTasks,
):
    """创建新的合成任务，返回任务 ID，任务完成后资源进入资产库"""
    # 假设需要生成两条，这里先把每一条的参数生成好
    params: tuple[list[SynthesisParams], list[SynthesisParams]] = ([], [])
    # 标记是否需要生成两条
    need_2 = False
    custom_tones: dict[int, AudioTone] = {}
    for seg in payload.segs:
        if seg.model_function == ModelFunction.FAST_CLONE:
            # 是极速复刻，那就生成两条不一样的 param，同时标记上需要生成两条
            need_2 = True
            if seg.tone_id in custom_tones:
                custom_tone = custom_tones[seg.tone_id]
            else:
                custom_tone = None
                async with db as session:
                    res = await session.execute(
                        select(AudioTone)
                        .where(AudioTone.user_id == user.id)
                        .where(AudioTone.id == seg.tone_id)
                    )
                    custom_tone: AudioTone | None = res.scalar()
                    if custom_tone is None:
                        raise ClientVisibleException(f"找不到音色：{seg.tone_id}")
                    custom_tones[seg.tone_id] = custom_tone
            try:
                lang = Lang(custom_tone.lang).code
            except ValueError:
                lang = None
            p0: SynthesisParams = {
                'tone_type': ToneType.CUSTOM,
                'tone_id': custom_tone.id,  # type: ignore
                'tone_name': custom_tone.name,  # type: ignore
                'model_function': ModelFunction.FAST_CLONE,
                'model_name': fast_clone_model_function_desc[0][0],
                'function_name': fast_clone_model_function_desc[0][1],
                'text': seg.text,
                'speed': seg.speed,
                'pitch': seg.pitch,
                'prompt_audio_url': custom_tone.audio_url,  # type: ignore
                'prompt_text': custom_tone.prompt_text,  # type: ignore
                'prompt_lang': lang,
            }
            params[0].append(p0)
            p1 = p0.copy()
            p1['model_name'] = fast_clone_model_function_desc[1][0]
            p1['function_name'] = fast_clone_model_function_desc[1][1]
            params[1].append(p1)
        else:
            # 不是极速复刻，使用一样的 param
            pretrained_tone = get_pretrained_tone_by_id(seg.tone_id)
            if pretrained_tone is None:
                raise ClientVisibleException(f"找不到音色：{seg.tone_id}")
            model_desc = pretrained_model_function_desc.get(seg.model_function, None)
            if model_desc is None:
                raise ClientVisibleException(f"未知模型：{seg.model_function.value}")
            param: SynthesisParams = {
                'tone_type': ToneType.PRETRAINED,
                'tone_id': pretrained_tone['id'],
                'tone_name': pretrained_tone['name'],
                'model_function': seg.model_function,
                'model_name': model_desc[0],
                'function_name': model_desc[1],
                'text': seg.text,
                'speed': seg.speed,
                'pitch': seg.pitch,
                'prompt_audio_url': None,
                'prompt_text': pretrained_tone['prompt_text'],
                'prompt_lang': None,
            }
            params[0].append(param)
            params[1].append(param)
    # 生成占位的任务信息，任务完成后再更新
    task_ids = []
    async with db as session:
        history = AudioSynthesisHistory(
            params={ "segs": params[0] },
            url="",
            duration=0,
            user_id=user.id,
            status=Status.SUBMITTED,
        )
        session.add(history)
        await session.commit()
        await session.refresh(history)
    task_ids.append(history.id)
    background_tasks.add_task(
        do_synthesis,
        params=params[0],
        task_id=history.id,
        user=user,
        db=db,
    )
    if need_2:
        async with db as session:
            history_2 = AudioSynthesisHistory(
                params={ "segs": params[1] },
                url="",
                duration=0,
                user_id=user.id,
                status=Status.SUBMITTED,
            )
            session.add(history_2)
            await session.commit()
            await session.refresh(history_2)
        task_ids.append(history_2.id)
        background_tasks.add_task(
            do_synthesis,
            params=params[1],
            task_id=history_2.id,
            user=user,
            db=db,
        )
    return SuccessRes(data=CreateTaskRes(task_ids=task_ids))


@router.get("/tasks/{task_id}", response_model=SuccessRes[TaskItem])
async def get_task(
        task_id: Annotated[int, Path(description='任务 ID')],
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    """获取合成任务信息"""
    stmt = (
        select(AudioSynthesisHistory)
        .where(AudioSynthesisHistory.user_id == user.id)
        .where(AudioSynthesisHistory.id == task_id)
    )
    async with db as session:
        res = await session.execute(stmt)
        history = res.scalar()
        if history is None:
            raise ClientVisibleException(f"未找到记录：{task_id}")
        return SuccessRes(
            data=TaskItem(
                id=history.id,
                params=ParamsOut.model_validate(history.params),
                url=history.url,
                duration=history.duration,
                create_time=history.create_time,
                status=history.status,
            )
        )

@router.get("/tasks", response_model=SuccessRes[Pagination[TaskItem]])
async def list_tasks(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        size: Annotated[int, Query(ge=1, description='分页大小')] = 10,
        current: Annotated[int, Query(ge=1, description='页码')] = 1,
):
    """获取合成任务信息列表，可分页"""
    res_stmt = (
        select(AudioSynthesisHistory)
        .where(AudioSynthesisHistory.user_id == user.id)
        .order_by(AudioSynthesisHistory.create_time.desc())
        .offset((current - 1) * size).limit(size)
    )
    cnt_stmt = (
        select(func.count())
        .select_from(AudioSynthesisHistory)
        .where(AudioSynthesisHistory.user_id == user.id)
    )
    async with db as session:
        cnt_res = await session.execute(cnt_stmt)
        total = cnt_res.scalar()
        res_res = await session.execute(res_stmt)
        items: Sequence[AudioSynthesisHistory] = res_res.scalars().all()
        records = []
        for item in items:
            records.append(
                TaskItem(
                    id=item.id,
                    params=ParamsOut.model_validate(item.params),
                    url=item.url,
                    duration=item.duration,
                    create_time=item.create_time,
                    status=item.status,
                )
            )
        return SuccessRes(
            data=Pagination(
                records=records,
                size=size,
                current=current,
                total=total,
            )
        )

@router.delete("/tasks/{task_id}", response_model=SuccessRes[TaskItem])
async def delete_task(
        task_id: Annotated[int, Path(description='任务 ID')],
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    """删除某个合成任务，联动删除资产库"""
    async with db as session:
        history_stmt = (
            select(AudioSynthesisHistory)
            .where(AudioSynthesisHistory.user_id == user.id)
            .where(AudioSynthesisHistory.id == task_id)
        )
        history_res = await session.execute(history_stmt)
        history: AudioSynthesisHistory = history_res.scalar()
        if history is None:
            raise ClientVisibleException(f"未找到记录：{task_id}")
        await session.delete(history)
        logger.info(f"删除合成历史：{history.id}")
        asset_stmt = (
            select(Assets)
            .where(Assets.user_id == user.id)
            .where(Assets.taskid == int(task_id))
            .where(Assets.biz_id == biz_id)
        )
        asset_res = await session.execute(asset_stmt)
        assets: Sequence[Assets] = asset_res.scalars().all()
        for asset in assets:
            await session.delete(asset)
            logger.info(f"删除资产：{asset.id}")
        await session.commit()
        return SuccessRes(
            data=TaskItem(
                id=history.id,
                params=ParamsOut.model_validate(history.params),
                url=history.url,
                duration=history.duration,
                create_time=history.create_time,
                status=history.status,
            )
        )
