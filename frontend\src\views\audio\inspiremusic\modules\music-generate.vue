<script lang="ts" setup>
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { NEmpty, NScrollbar, NSpin, NText, type ScrollbarInst, useMessage } from 'naive-ui';
import { useThemeStore } from '@/store/modules/theme';
import { getMusicHistory, getMusicPrompts, getMusicTaskStatus, postMusicGenerate } from '@/service/api/audio';
import { downloadFile } from '@/utils/common';
import MusicInfoCard from './music-infoCard.vue';
import MusicPlayAudio from './music-playAudio.vue';

interface MusicTaskResponse {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_url?: string | null;
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

interface MusicTask {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_data?: { url: string; type: string; duration?: number; sample_rate?: number } | null;
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

interface InspireMusicGenerateData {
  text: string;
  music_title?: string;
  model_name?: string;
  chorus?: string;
  output_sample_rate?: number;
  max_generate_audio_seconds?: number;
}

const themeStore = useThemeStore();
const message = useMessage();

const borderColor = computed(() => {
  return !themeStore.darkMode ? 'rgb(239, 239, 245)' : 'rgba(255, 255, 255, 0.09)';
});

const backgroundColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(250, 250, 252, 1)' : 'rgba(24, 24, 28, 1)';
});

// const hoverBorderColor = computed(() => {
//   return !themeStore.darkMode ? 'rgb(224, 224, 230)' : 'rgba(255, 255, 255, 0.12)';
// });

const focusBorderColor = computed(() => {
  return !themeStore.darkMode ? 'rgb(26, 126, 251)' : 'rgb(70, 146, 251)';
});

// const placeholderColor = computed(() => {
//   return !themeStore.darkMode ? 'rgba(150, 150, 150, 0.6)' : 'rgba(194, 194, 194, 0.5)';
// });

const shadowColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(26, 126, 251, 0.1)' : 'rgba(70, 146, 251, 0.15)';
});

const textColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(31, 34, 37, 1)' : 'rgba(255, 255, 255, 0.9)';
});

const selectedValue = ref('InspireMusic-1.5B');
const songLyrics = ref('');

const selectOptions = ref([
  {
    label: '纯音乐',
    value: 'InspireMusic-1.5B'
  },
  {
    label: '歌曲',
    value: 'Song',
    disabled: true
  }
]);

const musicPrompts = ref<Record<string, string[]>>({});
const currentMusicGenre = ref<string>('');
const isLoadingPrompts = ref<boolean>(false);

const generatedTasks = ref<MusicTask[]>([]);
const pollingIntervals = ref<Map<string, number>>(new Map());

const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoadingHistory = ref(false);
const scrollbarRef = ref<ScrollbarInst | null>(null);

// 播放器相关状态
const currentPlayingTask = ref<MusicTask | null>(null);
const currentAudioUrl = ref<string>('');
const currentAudioTitle = ref<string>('');
const currentPlayingIndex = ref<number>(-1); // 当前播放的歌曲在列表中的索引
const searchTitle = ref<string>(''); // 搜索歌曲标题
const isCurrentlyPlaying = ref<boolean>(false); // 当前是否正在播放音乐

const fetchMusicPrompts = async () => {
  isLoadingPrompts.value = true;
  try {
    const response = await getMusicPrompts();
    if (response?.data) {
      musicPrompts.value = response.data;
      const genres = Object.keys(response.data);
      if (genres.length > 0) {
        currentMusicGenre.value = genres[0];
      }
    }
  } catch (error) {
    console.error('获取音乐提示词失败:', error);
  } finally {
    isLoadingPrompts.value = false;
  }
};

const handlePromptClick = (prompt: string) => {
  songLyrics.value = prompt;
};

const stopPolling = (taskId: string) => {
  const intervalId = pollingIntervals.value.get(taskId);
  if (intervalId) {
    window.clearInterval(intervalId);
    pollingIntervals.value.delete(taskId);
    console.log(`停止轮询任务: ${taskId}`);
  }
};

const startPolling = (taskId: string) => {
  if (!taskId || pollingIntervals.value.has(taskId)) {
    return;
  }

  console.log(`开始轮询任务: ${taskId}`);
  const intervalId = window.setInterval(async () => {
    try {
      console.log(`正在轮询任务 ${taskId} 状态...`);
      const response = await getMusicTaskStatus(taskId);

      // 如果没有数据返回，停止轮询
      if (!response.data) {
        stopPolling(taskId);
        return;
      }

      const taskIndex = generatedTasks.value.findIndex(t => t.taskid === taskId);
      // 如果找不到任务，停止轮询
      if (taskIndex === -1) {
        console.warn(`轮询时未在列表中找到任务 ${taskId}，停止轮询。`);
        stopPolling(taskId);
        return;
      }

      // 将API响应转换为任务模型格式
      const apiResponse = response.data as MusicTaskResponse;
      const updatedTask: MusicTask = { ...generatedTasks.value[taskIndex] };

      // 复制所有基础字段
      Object.assign(updatedTask, apiResponse);

      // 处理audio_url字段 (如果存在)
      if (apiResponse.audio_url) {
        try {
          updatedTask.audio_data = JSON.parse(apiResponse.audio_url);
        } catch (error) {
          console.error('解析audio_url失败:', error);
        }
      }

      // 更新任务状态
      generatedTasks.value[taskIndex] = updatedTask;
      console.log(`任务 ${taskId} 状态更新: ${apiResponse.status}`);

      // 如果任务完成或失败，停止轮询
      if (apiResponse.status === 'SUCCESS' || apiResponse.status === 'FAILURE') {
        stopPolling(taskId);
      }
    } catch (error: any) {
      console.error(`轮询任务 ${taskId} 出错:`, error);
      stopPolling(taskId);
    }
  }, 5000);

  pollingIntervals.value.set(taskId, intervalId);
};

const generateMusic = async () => {
  const params: InspireMusicGenerateData = {
    text: songLyrics.value,
    model_name: selectedValue.value,
    chorus: 'intro',
    output_sample_rate: 48000,
    max_generate_audio_seconds: 30.0
  };

  try {
    const response = await postMusicGenerate(params);
    if (response.data) {
      // 处理返回的任务数据
      const newTask = response.data as MusicTaskResponse;
      const processedTask = { ...newTask } as unknown as MusicTask;

      if (newTask.audio_url) {
        try {
          processedTask.audio_data = JSON.parse(newTask.audio_url);
        } catch (error) {
          console.error('解析新生成任务的audio_url失败:', error);
        }
      }

      generatedTasks.value.unshift(processedTask);
      message.success('音乐生成任务已提交');
      startPolling(processedTask.taskid);
    }
  } catch (error: any) {
    console.error('提交任务出错 (catch block): ', error);
  }
};

const fetchHistory = async (pageNum: number) => {
  if (isLoadingHistory.value) return;
  isLoadingHistory.value = true;
  try {
    // 对搜索标题进行trim处理，去除前后空格
    const trimmedTitle = searchTitle.value ? searchTitle.value.trim() : searchTitle.value;
    const response = await getMusicHistory(pageNum, pageSize.value, trimmedTitle);
    if (response.data) {
      const newTasks = response.data;

      // 处理每个任务的audio_url
      const processedTasks: MusicTask[] = newTasks.map((task: MusicTaskResponse) => {
        const processedTask = { ...task } as unknown as MusicTask;

        if (task.audio_url) {
          try {
            processedTask.audio_data = JSON.parse(task.audio_url);
          } catch (error) {
            console.error('解析历史记录中的audio_url失败:', error);
          }
        }

        return processedTask;
      });

      if (pageNum === 1) {
        generatedTasks.value = processedTasks;
      } else {
        generatedTasks.value.push(...processedTasks);
      }

      hasMore.value = newTasks.length === pageSize.value;
      processedTasks.forEach(task => {
        if (task.taskid && (task.status === 'SUBMITTED' || task.status === 'IN_PROGRESS')) {
          startPolling(task.taskid);
        }
      });
    } else if (pageNum > 1) {
      currentPage.value--;
    }
  } catch (error: any) {
    if (pageNum > 1) {
      currentPage.value--;
    }
    console.error('获取历史记录出错:', error);
  } finally {
    isLoadingHistory.value = false;
  }
};

// 处理搜索功能
const handleSearch = () => {
  // 重置页码和其他状态
  currentPage.value = 1;
  hasMore.value = true;

  // 清空现有任务列表
  generatedTasks.value = [];

  // 使用搜索条件请求数据
  fetchHistory(1);

  // const displayTitle = searchTitle.value ? searchTitle.value.trim() : '全部';
  // message.info(`搜索: ${displayTitle}`);
};

// 处理回车键搜索
const handleSearchKeyup = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    handleSearch();
  }
};

// 辅助函数：播放当前加载的音频
const playCurrentAudio = () => {
  const audioElement = document.querySelector('audio');
  if (!audioElement || !audioElement.paused) return;

  // 手动控制播放，并且捕获可能的错误
  const playPromise = audioElement.play();
  if (playPromise !== undefined) {
    playPromise.catch(error => {
      console.error('手动播放失败:', error);
      message.error('播放失败，请重试');
    });
  }
};

const handlePlay = (task: MusicTask) => {
  if (!task.audio_data?.url) return;

  try {
    // 查找当前任务在列表中的索引
    const taskIndex = generatedTasks.value.findIndex(t => t.taskid === task.taskid);
    if (taskIndex !== -1) {
      currentPlayingIndex.value = taskIndex;
    }

    // 检查是否点击了当前已加载的音频
    const isSameAudio = currentPlayingTask.value?.taskid === task.taskid;

    currentPlayingTask.value = task;
    currentAudioUrl.value = task.audio_data.url;
    currentAudioTitle.value = task.music_title;

    // 显示提示消息
    message.success(`正在播放: ${task.music_title}`);

    // 如果是同一首歌，则直接通过DOM控制播放
    if (isSameAudio) {
      playCurrentAudio();
    }
    // 如果不是同一首歌，audioUrl的变化会触发自动播放
  } catch (error) {
    console.error('设置播放任务时出错:', error);
    message.error('播放失败，请重试');
  }
};

// 处理切换到上一首歌曲
const handlePreviousTrack = () => {
  if (currentPlayingIndex.value <= 0 || generatedTasks.value.length === 0) {
    message.warning('已经是第一首歌曲');
    return;
  }

  const prevIndex = currentPlayingIndex.value - 1;
  const prevTask = generatedTasks.value[prevIndex];

  if (prevTask && prevTask.status === 'SUCCESS' && prevTask.audio_data?.url) {
    currentPlayingIndex.value = prevIndex;
    currentPlayingTask.value = prevTask;
    currentAudioUrl.value = prevTask.audio_data.url;
    currentAudioTitle.value = prevTask.music_title;
  } else {
    message.warning('上一首歌曲无法播放，可能正在生成中或已失败');
    // 尝试继续向前查找可播放的歌曲
    let foundPlayable = false;
    for (let i = prevIndex - 1; i >= 0; i--) {
      const task = generatedTasks.value[i];
      if (task.status === 'SUCCESS' && task.audio_data?.url) {
        currentPlayingIndex.value = i;
        currentPlayingTask.value = task;
        currentAudioUrl.value = task.audio_data.url;
        currentAudioTitle.value = task.music_title;
        foundPlayable = true;
        break;
      }
    }

    if (!foundPlayable) {
      message.warning('没有找到可播放的上一首歌曲');
    }
  }
};

// 处理切换到下一首歌曲
const handleNextTrack = () => {
  if (
    currentPlayingIndex.value === -1 ||
    currentPlayingIndex.value >= generatedTasks.value.length - 1 ||
    generatedTasks.value.length === 0
  ) {
    message.warning('已经是最后一首歌曲');
    return;
  }

  const nextIndex = currentPlayingIndex.value + 1;
  const nextTask = generatedTasks.value[nextIndex];

  if (nextTask && nextTask.status === 'SUCCESS' && nextTask.audio_data?.url) {
    currentPlayingIndex.value = nextIndex;
    currentPlayingTask.value = nextTask;
    currentAudioUrl.value = nextTask.audio_data.url;
    currentAudioTitle.value = nextTask.music_title;
  } else {
    message.warning('下一首歌曲无法播放，可能正在生成中或已失败');
    // 尝试继续向后查找可播放的歌曲
    let foundPlayable = false;
    for (let i = nextIndex + 1; i < generatedTasks.value.length; i++) {
      const task = generatedTasks.value[i];
      if (task.status === 'SUCCESS' && task.audio_data?.url) {
        currentPlayingIndex.value = i;
        currentPlayingTask.value = task;
        currentAudioUrl.value = task.audio_data.url;
        currentAudioTitle.value = task.music_title;
        foundPlayable = true;
        break;
      }
    }

    if (!foundPlayable) {
      message.warning('没有找到可播放的下一首歌曲');
    }
  }
};

// 歌曲播放完成后的处理
const handleTrackEnded = () => {
  // 自动播放下一首
  // handleNextTrack();
};

const handleDownload = (task: MusicTask) => {
  if (task.audio_data?.url) {
    downloadFile(task.audio_data.url, `${task.music_title || 'music'}.${task.audio_data.type || 'mp3'}`).catch(
      error => {
        console.error('下载音频时出错:', error);
      }
    );
  }
};

// 滚动事件处理函数 ---
const handleScroll = (e: Event) => {
  const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLElement;
  const threshold = 50; // 距离底部多少像素时触发加载

  // 检查是否滚动到底部附近、没有正在加载、且还有更多数据
  if (scrollTop + clientHeight >= scrollHeight - threshold && !isLoadingHistory.value && hasMore.value) {
    console.log('滚动到底部，加载下一页...');
    currentPage.value++;
    fetchHistory(currentPage.value);
  }
};

// 判断某个任务是否正在播放
const isTaskPlaying = (task: MusicTask): boolean => {
  return currentPlayingTask.value?.taskid === task.taskid && isCurrentlyPlaying.value;
};

// 处理暂停请求
const handlePause = (task: MusicTask) => {
  // 确保我们暂停的是当前正在播放的任务
  if (currentPlayingTask.value?.taskid === task.taskid && isCurrentlyPlaying.value) {
    const audioElement = document.querySelector('audio');
    if (audioElement) {
      audioElement.pause();
      // isCurrentlyPlaying会由MusicPlayAudio的playStatusChange事件更新
    }
  }
};

// 处理播放状态变化
const handlePlayStatusChange = (playing: boolean) => {
  isCurrentlyPlaying.value = playing;
};

onMounted(() => {
  fetchMusicPrompts();
  fetchHistory(1);
});

onBeforeUnmount(() => {
  pollingIntervals.value.forEach((intervalId, _taskId) => {
    window.clearInterval(intervalId);
  });
  pollingIntervals.value.clear();
  console.log('清除所有音乐任务轮询计时器');
});
</script>

<template>
  <NFlex :wrap="false">
    <NCard class="h-full max-w-[550px] w-1/2 flex flex-col px-2">
      <div class="mb-4 w-50">
        <NSelect v-model:value="selectedValue" :options="selectOptions"></NSelect>
      </div>

      <div class="w-full">
        <NInput
          v-model:value="songLyrics"
          type="textarea"
          :rows="8"
          placeholder="在此输入音乐描述"
          maxlength="3000"
          show-count
          class="description-input"
          clearable
        />
      </div>

      <NButton class="mt-3 w-full" type="info" @click="generateMusic">生成</NButton>

      <div class="mt-6 flex flex-col flex-1 overflow-hidden">
        <NText class="my-2">参考示例：</NText>

        <NTabs
          v-if="!isLoadingPrompts && Object.keys(musicPrompts).length > 0"
          v-model:value="currentMusicGenre"
          type="line"
          class="music-genre-tabs flex flex-col flex-1"
        >
          <NTabPane v-for="(prompts, genre) in musicPrompts" :key="genre" :name="genre" :tab="genre" class="flex-1">
            <NScrollbar class="max-h-[20em]">
              <NFlex vertical>
                <div
                  v-for="(prompt, index) in prompts"
                  :key="index"
                  class="prompt-card"
                  @click="handlePromptClick(prompt)"
                >
                  {{ prompt }}
                </div>
                <div v-if="prompts.length === 0" class="py-4 text-center">
                  <p class="text-gray-500">没有更多数据</p>
                </div>
              </NFlex>
            </NScrollbar>
          </NTabPane>
        </NTabs>

        <NSpin v-if="isLoadingPrompts" />
        <p v-if="!isLoadingPrompts && Object.keys(musicPrompts).length === 0" class="py-4 text-center text-gray-500">
          没有更多数据
        </p>
      </div>
    </NCard>

    <NFlex class="h-full flex flex-col flex-1 px-2">
      <!-- 搜索框 -->
      <NButtonGroup class="w-full">
        <NInput v-model:value="searchTitle" placeholder="输入歌曲标题" @keyup="handleSearchKeyup"></NInput>
        <NButton type="info" @click="handleSearch">
          <SvgIcon icon="dashicons:search" class="mr-1 text-[18px]" />
          查看
        </NButton>
      </NButtonGroup>

      <!-- 历史记录 -->
      <NScrollbar ref="scrollbarRef" class="h-[40em] max-h-[40em] flex flex-col" @scroll="handleScroll">
        <MusicInfoCard
          v-for="task in generatedTasks"
          :key="task.taskid"
          :task="task"
          :is-playing="isTaskPlaying(task)"
          class="mb-3"
          @play="handlePlay"
          @pause="handlePause"
          @download="handleDownload"
        />
        <NEmpty v-if="generatedTasks.length === 0 && !isLoadingHistory" description="暂无生成任务" class="mt-10" />

        <!-- 加载 -->
        <NFlex v-if="isLoadingHistory" justify="center" class="py-4">
          <NSpin size="small" />
          <NText depth="3" class="ml-2">加载中...</NText>
        </NFlex>

        <!-- 没有更多数据 -->
        <NFlex v-if="!hasMore && generatedTasks.length > 0" justify="center" class="py-4">
          <NText depth="3">没有更多数据了</NText>
        </NFlex>
      </NScrollbar>

      <!-- 音频播放组件 -->
      <MusicPlayAudio
        :audio-url="currentAudioUrl"
        :audio-title="currentAudioTitle"
        :auto-play="true"
        @previous-track="handlePreviousTrack"
        @next-track="handleNextTrack"
        @ended="handleTrackEnded"
        @play-status-change="handlePlayStatusChange"
      />
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
.borderStyle {
  border: 1px solid v-bind('borderColor');
}

// .description-input {
//   width: 100%;
//   border: 1px solid v-bind('borderColor');
//   border-radius: 3px;
//   background-color: v-bind('backgroundColor');
//   transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

//   &:hover {
//     border-color: v-bind('hoverBorderColor');
//   }

//   &:focus-within {
//     border-color: v-bind('focusBorderColor');
//     box-shadow: 0 0 0 2px v-bind('shadowColor');
//   }
// }

// .description-input :deep(.n-input) {
//   background-color: transparent !important;
// }

// .description-input :deep(.n-input__input) {
//   color: v-bind('textColor') !important;
// }

// .description-input :deep(.n-input .n-input__border),
// .description-input :deep(.n-input .n-input__state-border) {
//   border: none !important;
//   box-shadow: none !important;
// }

// .description-input :deep(.n-input__placeholder) {
//   color: v-bind('placeholderColor') !important;
// }

// .description-input :deep(.n-input-word-count) {
//   color: v-bind('placeholderColor') !important;
//   font-size: 12px;
//   margin-top: 4px;
// }

.prompt-card {
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 6px;
  background-color: v-bind('backgroundColor');
  border: 1px solid v-bind('borderColor');
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: v-bind('textColor');

  &:hover {
    border-color: v-bind('focusBorderColor');
    box-shadow: 0 0 0 1px v-bind('shadowColor');
  }
}

.music-genre-tabs {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.music-genre-tabs :deep(.n-tabs-tab-wrapper) {
  flex-shrink: 0;
}

.music-genre-tabs :deep(.n-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.music-genre-tabs :deep(.n-tabs-content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.nowrap {
  flex-wrap: nowrap !important;
}
</style>
