<script setup lang="ts">
const props = defineProps<{
  prompts: string[];
  cols: number;
}>();

const emit = defineEmits<{
  (e: 'sendPrompt', prompt: string): void;
}>();
</script>

<template>
  <NGrid x-gap="12" :cols="cols" y-gap="8">
    <!-- 遍历并渲染每一个 prompt，并添加点击事件 -->
    <NGi v-for="(prompt, index) in props.prompts" :key="index" class="propmt-template-item">
      <NCard size="small" class="cursor-pointer" @click="() => emit('sendPrompt', prompt)">
        <div class="flex flex-row justify-between">
          <div class="flex-1 truncate">
            <SvgIcon icon="bi:chat-dots" class="inline-block" />
            {{ prompt }}
          </div>
          <NTag type="info">
            <SvgIcon icon="ri:send-plane-fill" />
          </NTag>
        </div>
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped>
.propmt-template-item .n-tag {
  visibility: hidden;
}
.propmt-template-item:hover .n-tag {
  visibility: visible;
}
</style>
