import asyncio
import logging

from config import app_settings
from models.chat import chatContent
from typing import List
from openai import OpenAI
import tiktoken

logger = logging.getLogger(__name__)

def get_token():
    """获取腾讯云API Key"""
    return app_settings.tencent_api_key

def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:
    """返回给定字符串的token数量"""
    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens

# 初始化OpenAI客户端,使用腾讯云的API地址
client = OpenAI(
    api_key=get_token(),
    base_url="https://api.lkeap.cloud.tencent.com/v1"
)

def chat(
    model_name: str,
    contents: List[chatContent],
    systemPrompt: str = "",
    memory: str = "",
    temperature: float = 0.7,
    mcp: bool = False,
    web_search: bool = False,
    loop: asyncio.AbstractEventLoop = None,
):
    """
    调用腾讯云DeepSeek模型进行对话

    Args:
        model_name: 模型名称,如 deepseek-r1
        contents: 对话历史
        systemPrompt: 系统提示语
        memory: 记忆内容(DeepSeek-R1建议不使用system prompt)
    """
    messages = []
    input_text = ""

    logger.info(f"开始执行chat")
    # DeepSeek-R1建议不添加system prompt
    if systemPrompt and model_name != "deepseek-r1":
        logger.info(f"添加系统提示: {systemPrompt}")
        messages.append({"role": "system", "content": systemPrompt})
        input_text = systemPrompt

    logger.info(f"添加对话历史")
    # 添加对话历史
    for r in contents:
        input_text += r.content
        content = {
            "role": "user" if r.role == "user" else "assistant",
            "content": r.content
        }
        messages.append(content)

    logger.info(f"创建对话请求")
    # 创建对话请求
    completion = client.chat.completions.create(
        model=model_name,
        messages=messages,
        stream=True,
        temperature=temperature
    )

    logger.info(f"开始处理流式响应")
    all_text = ""
    reasoning_text = ""
    content_text = ""
    is_in_thinking = False  # 添加标志位，标记是否在思维链过程中

    logger.info(f"开始处理流式响应")
    for chunk in completion:
        try:
            # 处理思维链内容
            if hasattr(chunk.choices[0].delta, "reasoning_content"):
                text = chunk.choices[0].delta.reasoning_content
                if text:
                    reasoning_text += text
                    all_text += text
                    # 如果不在思维链中，先输出开始标签
                    if not is_in_thinking:
                        yield "<think>\n"
                        is_in_thinking = True
                    # 直接输出思维链内容
                    yield text

            # 处理普通回复内容
            elif hasattr(chunk.choices[0].delta, "content"):
                # 如果之前在思维链中，先输出结束标签
                if is_in_thinking:
                    yield "</think>"
                    is_in_thinking = False

                text = chunk.choices[0].delta.content
                if text:
                    content_text += text
                    all_text += text
                    yield text

            # 处理结束标志
            if chunk.choices[0].finish_reason is not None:
                # 如果在思维链中还未结束，确保输出结束标签
                if is_in_thinking:
                    yield "</think>"
                    is_in_thinking = False
                # 输出token统计
                yield {
                    "input_tokens": num_tokens_from_string(input_text),
                    "output_tokens": num_tokens_from_string(all_text),
                }

        except Exception as e:
            logger.error(f"Error processing chunk: {e}")
            continue

    logger.info("-------------------------- done ")
