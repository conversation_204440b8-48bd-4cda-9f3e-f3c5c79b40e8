import asyncio
import logging
import math
from datetime import datetime, timezone, timedelta
from typing import Sequence, Self, TypeAlias, Literal

import nanoid
from sqlalchemy import select, Result
from sqlalchemy.ext.asyncio import AsyncSession
from config import app_settings
from models.model_price import ModelPrice
from models.user_credit import UserCredit, UserCreditLog
from utils.exceptions import ClientVisibleException
from utils.redis import redis

logger = logging.getLogger(__name__)


AICapacity: TypeAlias = Literal['image-generation', 'video-generation']

NO_LOG_ID = -1
"""log id 的占位符，当有些操作不需要扣除积分是，pre_debit 方法会返回这个 id 统一处理，使用 -1 是因为数据库索引一般不会是负数。"""


class CreditOperator:
    def __init__(self, user_id: int, ip: str, editor: str, db: AsyncSession, operator_id: str | None = None):
        if operator_id is None:
            self._opt_id = nanoid.generate(size=16)
        else:
            self._opt_id = operator_id
        self.user_id = user_id
        self._db = db
        self._ip = ip
        self._editor = editor

    @property
    def id(self) -> str:
        return self._opt_id

    @classmethod
    async def from_id(cls, operator_id: str, db: AsyncSession) -> Self | None:
        """从 operator id 创建一个 Operator"""
        stmt = select(UserCreditLog).where(UserCreditLog.pre_debit_id == operator_id).limit(1)
        async with db as session:
            res = await session.execute(stmt)
            log: UserCreditLog | None = res.scalars().one_or_none()
        if not log:
            return None
        return cls(
            user_id=log.user_id,
            ip=log.ip,
            editor=log.editor,
            db=db,
            operator_id=log.pre_debit_id,
        )

    async def get_current_credit(self) -> UserCredit | None:
        """获取用户当前积分"""
        user_credit_stmt = (
            select(UserCredit)
            .where(UserCredit.user_id == self.user_id)
        )
        async with self._db as session:
            user_credit_res: Result[Sequence[UserCredit]] = await session.execute(user_credit_stmt)
            user_credit: UserCredit | None = user_credit_res.scalars().one_or_none()
        if not user_credit or user_credit.credit is None:
            return None
        return user_credit

    async def get_current_model_price(self, capacity: AICapacity, model: str = ''):
        """获取当前模型价格"""
        price_stmt = (
            select(ModelPrice)
            .where(ModelPrice.capacity == capacity)
            .where(ModelPrice.model == model)
        )
        async with self._db as session:
            price_res: Result[Sequence[ModelPrice]] = await session.execute(price_stmt)
            model_price: ModelPrice | None = price_res.scalars().one_or_none()

        if not model_price or model_price.credit is None:
            logger.info(f"未查询到模型价格，capacity：{capacity}，model: {model}")
            return 0, 1
        else:
            logger.info(f"查询到模型价格，capacity：{capacity}，model: {model}，price: {model_price.credit} unit: {model_price.unit}")
            return model_price.credit, model_price.unit

    async def pre_debit(self, amount: int, capacity: AICapacity, model: str = '') -> int:
        """
        预扣积分，积分不足时会抛出异常，预扣成功会返回日志记录 ID，用于回滚。
        """
        current_credit = await self.get_current_credit()
        if current_credit is None:
            logger.info(f"用户当前没有任何积分，operator_id: {self._opt_id}，user_id: {self.user_id}")
            raise ClientVisibleException("积分不足")
        per_opt, unit = await self.get_current_model_price(capacity, model)
        opt_credit = math.floor(amount / unit * per_opt)  # 积分扣除的时候向下取整
        # 本次操作不扣积分，则不写日志
        if opt_credit == 0:
            return NO_LOG_ID
        if opt_credit > current_credit.credit:
            logger.info(f"预扣积分不足，operator_id: {self._opt_id}，user_id: {self.user_id}, credit: {current_credit.credit}, opt_credit: {opt_credit}")
            raise ClientVisibleException("积分不足")
        after_credit = current_credit.credit - opt_credit
        log = UserCreditLog(
            user_id=self.user_id,
            credit=0 - opt_credit,  # 扣减积分，所以是负的
            after_credit=after_credit,
            capacity=capacity,
            model=model,
            ip=self._ip,
            matter="AI 能力使用",
            editor=self._editor,
            pre_debit_id=self._opt_id,
            pre_debit_status=1,
        )
        async with self._db as session:
            current_credit.credit = after_credit
            session.add(current_credit)
            session.add(log)
            await session.flush()
            await session.commit()
        return log.id


    async def done(self):
        """实际扣除预扣的积分"""
        if self._opt_id is None:
            logger.warning("没有进行任何积分扣减操作")
            return
        log_stmt = select(UserCreditLog).where(UserCreditLog.pre_debit_id == self._opt_id)
        total = 0
        async with self._db as session:
            log_res = await session.execute(log_stmt)
            logs: Sequence[UserCreditLog] = log_res.scalars().all()
            for log in logs:
                log.pre_debit_status = 0
                total += log.credit
            session.add_all(logs)
            await session.commit()
        logger.info(f"积分扣除成功，total: {total}, operator_id: {self._opt_id}")


    async def rollback(self, ids: list[int] = None):
        """回滚预扣的积分，如果 ids 为 None，则回滚所有记录，如果 ids 为 []，则不进行回滚操作"""
        if self._opt_id is None:
            logger.warning("没有进行任何积分回滚操作")
            return
        if isinstance(ids, list) and len(ids) == 0:
            logger.warning("ids 为空列表，不执行任何积分回滚操作")
            return
        current_credit = await self.get_current_credit()
        stmt = select(UserCreditLog).where(UserCreditLog.pre_debit_id == self._opt_id)
        if ids is not None:
            stmt = stmt.where(UserCreditLog.id.in_(ids))
        async with self._db as session:
            res = await session.execute(stmt)
            logs: Sequence[UserCreditLog] = res.scalars().all()
            for log in logs:
                await session.delete(log)
                if current_credit is not None:
                    # 如果是扣除操作，这里 log.credit 就是负数，所以这里要加回来
                    current_credit.credit = current_credit.credit - log.credit
            session.add(current_credit)
            await session.commit()
        logger.info(f"成功回滚预扣积分，operator_id: {self._opt_id}")


_lock = asyncio.Lock()
DAILY_GIFT_CREDIT_KEY = 'daily_gift_credit:{date}:{user_id}'

async def add_daily_gift_credit(
        user_id: int,
        ip: str,
        db: AsyncSession,
):
    """每日赠送积分"""
    async with _lock:
        now = datetime.now(timezone(timedelta(hours=8)))
        gift_key = DAILY_GIFT_CREDIT_KEY.format(date=now.date().isoformat(), user_id=user_id)
        gift_record = await redis.get(gift_key)

        if gift_record is not None:
            logger.info(f"今日已经送过积分")
            return

        matter = '每日重置'
        user_credit_stmt = (
            select(UserCredit)
            .where(UserCredit.user_id == user_id)
        )
        try:
            async with db as session:
                user_credit_res = await session.execute(user_credit_stmt)
                user_credit: UserCredit | None = user_credit_res.scalars().one_or_none()
                if user_credit is None:
                    user_credit = UserCredit(
                        user_id=user_id,
                        credit=0
                    )
                delta = app_settings.daily_gift_credit - user_credit.credit
                # 如果用户当前的积分不等于每日赠送的积分，那就重置成每日赠送的值
                if delta != 0:
                    user_credit.credit = app_settings.daily_gift_credit
                    log = UserCreditLog(
                        user_id=user_id,
                        credit=delta,
                        after_credit=user_credit.credit,
                        capacity='',
                        model='',
                        ip=ip,
                        matter=matter,
                        editor='system',
                    )
                    session.add(log)
                    session.add(user_credit)
                    await db.commit()
            await redis.set(gift_key, delta, ex=timedelta(days=1))
        except Exception as e:
            logger.error(f"每日积分赠送失败, Error: {e}")
