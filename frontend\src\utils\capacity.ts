/** 模型配置相关的 API 类型。 */
export enum Capacity {
  TEXT = 1 << 0,
  IMAGE = 1 << 1,
  DRAW = 1 << 2,
  TOOL_CALL = 1 << 3
}

export const capacityOp = {
  /**
   * 在 a 的基础上添加能力 b。
   *
   * @param a 原有的能力
   * @param b 需要添加的能力
   * @returns 新的能力
   */
  add: (a: number, b: number): number => {
    return a | b;
  },

  /**
   * 在 a 的基础上移除能力 b。
   *
   * @param a 原有的能力
   * @param b 需要移除的能力
   * @returns 新的能力
   */
  remove: (a: number, b: number): number => {
    return a & ~b;
  },

  /**
   * a 是否拥有 b 的能力。
   *
   * @param a 原有的能力
   * @param b 需要检查的能力
   * @returns 是否拥有
   */
  has: (a: number, b: number): boolean => {
    return (a & b) === b;
  },

  /** 将能力数组转换为单个数字。 */
  toNumber: (cs: number[]): number => {
    return cs.reduce((a, b) => a | b, 0);
  },

  /** 将单个数字转换为文字描述。 */
  toText: (c: number): string[] => {
    const cs: string[] = [];
    let pos = 0;
    while (c > 0) {
      const bit = c & 1;
      if (bit === 1) {
        const cap = Capacity[2 ** pos];
        cs.push(cap);
      }
      pos++;
      c >>= 1;
    }
    return cs;
  }
};
