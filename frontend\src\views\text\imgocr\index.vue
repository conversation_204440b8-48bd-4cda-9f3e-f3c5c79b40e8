<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import type { UploadCustomRequestOptions } from 'naive-ui';
import { useLoadingBar, useMessage } from 'naive-ui';
import { useNaiveForm } from '@/hooks/common/form';
import { ExtractText, fetchFilePresets, fetchSupportedModels, postUploadFile, translateStream } from '@/service/api';
import NullImg from '@/assets/imgs/暂无图片.png';

const { formRef } = useNaiveForm();
const wrapperRef = ref<HTMLElement>();
const loadingBar = useLoadingBar();
const message = useMessage();
const extractLoading = ref(false);
const translateLoading = ref(false);
const uploadLoading = ref(false);
const createDefaultModel = (): Api.text.ImgocrParams => {
  return {
    file_path: '',
    gamecode: '默认',
    format: 'txt',
    model: '',
    translate: '中文->英文'
  };
};
const imageBlobUrl = ref<string>('');
const fileName = ref<string>('');
const originText = ref<string>('');
const translatedText = ref<string>('');
const imageUrl = ref<string>('');

// const emptyAudioBlobUrl = computed(() => audioBlobUrl.value === '');
const emptyFileName = computed(() => fileName.value === '');
const emptyOriginText = computed(() => originText.value === '');
const emptyTranslatedText = computed(() => translatedText.value === '');
const model: Api.text.ImgocrParams = reactive(createDefaultModel());

const modelOptions = ref<CommonType.Option[]>([]);
const formatOption: CommonType.Option[] = [
  { label: 'txt', value: 'text' },
  { label: 'json', value: 'verbose_json' }
];
const translateOption: CommonType.Option[] = [
  { label: '中文->英文', value: '中文->英文' },
  { label: '英文->中文', value: '英文->中文' },
  { label: '简体->繁体', value: '简体->繁体' }
];

const gameOption = ref<CommonType.Option[]>([]);

// 流式翻译相关状态
const streamController = ref<{ cancel: () => void } | null>(null);
const usePresets = ref<boolean>(false);

// 停止翻译
const stopTranslation = () => {
  if (streamController.value) {
    streamController.value.cancel();
    streamController.value = null;
    translateLoading.value = false;
  }
};

// 在组件销毁前取消流式请求
onBeforeUnmount(() => {
  stopTranslation();
});

// 处理流式翻译消息
const handleStreamMessage = (messageData: string) => {
  try {
    // 解析JSON消息
    const data = JSON.parse(messageData);

    // 获取翻译内容和完成状态
    const { text, done, error } = data;

    if (error) {
      message.error(`翻译错误`);
      translateLoading.value = false;
      return;
    }

    // 更新翻译文本
    translatedText.value = text;

    // 如果翻译完成
    if (done) {
      translateLoading.value = false;
      message.success('翻译完成');
    }
  } catch (error) {
    console.error('处理流式消息失败:', error);
  }
};

// 下载原文或者译文
const download = (target: string = '') => {
  let content = '';
  const originFileName = fileName.value.split('.')[0];
  const filename = originFileName || '未命名';
  let downloadFileName = `${filename}.${model.format}`;
  if (target === 'origin') {
    content = originText.value;
  } else {
    content = translatedText.value;
  }
  if (model.format === 'json' || model.format === 'verbose_json') {
    downloadFileName = `${filename}.json`;
  }
  // return console.log(filename);
  // 创建 Blob 对象，指定文件内容和类型
  const blob = new Blob([content], { type: 'text/plain' });

  // 使用 URL.createObjectURL 创建一个 URL 来指向 Blob 对象
  const url = URL.createObjectURL(blob);

  // 创建一个链接元素
  const a = document.createElement('a');
  a.href = url;

  // 指定下载文件的文件名
  a.download = downloadFileName;

  // 将链接元素添加到文档中，这里使用 append 为了确保它确实加入了文档树
  document.body.appendChild(a);

  // 模拟用户点击链接元素来触发下载
  a.click();

  // 下载完成后移除链接元素和释放 URL 对象
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 获取支持的模型列表
async function getSupportedModels() {
  try {
    const response = await fetchSupportedModels();
    if (response.data) {
      modelOptions.value = response.data;
      if (modelOptions.value.length > 0) {
        model.model = modelOptions.value[0].value;
      }
    }
  } catch (error) {
    console.error('获取支持的模型列表失败:', error);
  }
}

onMounted(() => {
  // 获取预设列表
  fetchFilePresets().then(({ data }) => {
    if (data) {
      const opts: CommonType.Option[] = data.map(e => ({
        label: e.gamename,
        value: e.gamecode
      }));
      console.log(opts);
      opts.unshift({
        label: '默认',
        value: '默认'
      });
      gameOption.value = opts;
    }
  });

  // 获取支持的模型列表
  getSupportedModels();
});

const customRequest = async ({ file, data, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  console.log(file);

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }

  if (file.file) {
    const blobUrl = URL.createObjectURL(file.file); // 处理图片的 Blob URL
    imageBlobUrl.value = blobUrl; // 更新为显示图片
  }

  formData.append('file', file.file as File);

  loadingBar.start();
  uploadLoading.value = true;
  // 上传到tmp文件夹
  postUploadFile('tmp', formData, progressEvent => {
    if (progressEvent.progress) {
      onProgress({ percent: progressEvent!.progress * 100 });
    }
  })
    .then(res => {
      console.log(res);
      if (res.data) model.file_path = res.data.file_path;
      fileName.value = file.name;
      // message.success(JSON.stringify(json));
      onFinish();
    })
    .catch(error => {
      console.log(error);

      // message.success(error.message);
      onError();
    })
    .finally(() => {
      loadingBar.finish();
      uploadLoading.value = false;
    });
};

const onRemove = () => {
  imageBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};

const removeFile = () => {
  imageBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};

// 提取文本
const extractSubtitle = () => {
  extractLoading.value = true;
  originText.value = '';
  ExtractText(model)
    .then(res => {
      if (res.data) {
        originText.value = res!.data.text;
      }
    })
    .finally(() => {
      extractLoading.value = false;
    });
};

// 翻译 - 使用流式响应
const translate = async () => {
  if (!originText.value.trim()) {
    message.error('请先提取或输入要翻译的文本');
    return;
  }

  translateLoading.value = true;
  translatedText.value = '';

  try {
    const controller = await translateStream(
      {
        userinput: originText.value,
        target_language: model.translate,
        gamecode: model.gamecode,
        model: model.model,
        use_presets: usePresets.value
      },
      handleStreamMessage,
      error => {
        console.error('翻译错误:', error);
        message.error('翻译失败，请重试');
        translateLoading.value = false;
      },
      () => {
        console.log('翻译完成');
      },
      () => {
        console.log('开始翻译');
      }
    );

    if (controller) {
      streamController.value = controller;
    }
  } catch (error) {
    translateLoading.value = false;
    message.error('翻译失败，请重试');
  }
};

// 处理粘贴事件
const handlePaste = async (e: ClipboardEvent) => {
  const items = e.clipboardData?.items;
  if (!items) return;

  const imageItem = Array.from(items).find(item => item.type.startsWith('image/'));
  if (!imageItem) return;

  const file = imageItem.getAsFile();
  if (!file) return;

  const formData = new FormData();
  formData.append('file', file);

  loadingBar.start();
  uploadLoading.value = true;

  try {
    const blobUrl = URL.createObjectURL(file);
    imageBlobUrl.value = blobUrl;

    const res = await postUploadFile('tmp', formData, progressEvent => {
      if (progressEvent.progress) {
        console.log(progressEvent.progress * 100);
      }
    });
    if (res.data) {
      model.file_path = res.data.file_path;
      fileName.value = file.name || '粘贴的图片.png';
    }
  } catch (error) {
    console.error(error);
  } finally {
    loadingBar.finish();
    uploadLoading.value = false;
  }
};

// 处理URL输入
const handleUrlInput = async () => {
  if (!imageUrl.value) return;

  loadingBar.start();
  uploadLoading.value = true;

  try {
    // 从URL获取图片并转换为File对象
    const response = await fetch(imageUrl.value);
    const blob = await response.blob();
    const file = new File([blob], 'url-image.png', { type: blob.type });

    // 创建FormData并上传
    const formData = new FormData();
    formData.append('file', file);

    // 更新预览
    imageBlobUrl.value = URL.createObjectURL(blob);

    // 上传到服务器
    const res = await postUploadFile('tmp', formData, progressEvent => {
      if (progressEvent.progress) {
        console.log(progressEvent.progress * 100);
      }
    });
    if (res.data) {
      model.file_path = res.data.file_path;
      fileName.value = 'url-image.png';
    }
  } catch (error) {
    console.error(error);
  } finally {
    loadingBar.finish();
    uploadLoading.value = false;
    imageUrl.value = ''; // 清空输入框
  }
};
</script>

<template>
  <div ref="wrapperRef" class="flex-row-stretch gap-16px">
    <NLoadingBarProvider :to="wrapperRef" container-style="position: absolute;"></NLoadingBarProvider>
    <NGrid cols="24" :x-gap="8" :y-gap="8" item-responsive responsive="screen" class="h-full">
      <NGridItem span="24 m:10 l:8">
        <NCard>
          <NGrid cols="24" :x-gap="10">
            <NGridItem span="24">
              <NSpin :show="uploadLoading">
                <NUpload
                  :custom-request="customRequest"
                  accept=".jpg,.jpeg,.png,.gif,.bmp,.svg"
                  :on-remove="onRemove"
                  :show-file-list="false"
                >
                  <NUploadDragger>
                    <NText>拖放或点击图片到此处</NText>
                    <NP depth="3"></NP>
                    <div class="grid min-h-54px items-center justify-center">
                      <img :src="imageBlobUrl || NullImg" alt="uploaded image" class="h-64 min-w-63" />
                    </div>
                    <div class="n-upload-file-list pt-3">
                      <div class="n-upload-file n-upload-file--success-status n-upload-file--text-type">
                        <div class="n-upload-file-info">
                          <div class="n-upload-file-info__name">
                            <span v-if="emptyFileName">{{ $t('page.media.form.text.noFileSelected') }}</span>
                            <span v-else>{{ fileName }}</span>
                          </div>
                          <div
                            v-if="!emptyFileName"
                            class="n-upload-file-info__action n-upload-file-info__action--text-type"
                          >
                            <NButton text @click.stop="removeFile">{{ $t('page.media.form.btn.del') }}</NButton>
                          </div>
                        </div>
                      </div>
                    </div>
                  </NUploadDragger>
                </NUpload>
              </NSpin>
            </NGridItem>
            <NGridItem span="24">
              <NInput
                v-model:value="imageUrl"
                type="text"
                placeholder="请输入图片URL或直接粘贴图片 (Ctrl+V)"
                class="mt-1"
                @paste="handlePaste"
                @keyup.enter="handleUrlInput"
              >
                <template #suffix>
                  <NButton text :disabled="!imageUrl" @click="handleUrlInput">确认</NButton>
                </template>
              </NInput>
            </NGridItem>
          </NGrid>
        </NCard>

        <NCard size="small" class="mt-5 card-wrapper">
          <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
            <NGrid :x-gap="0" responsive="screen" item-responsive>
              <NFormItemGi span="24 s:12" :label="$t('page.media.subtitle.model')" class="pr-24px">
                <NSelect
                  v-model:value="model.model"
                  :placeholder="$t('page.media.subtitle.model')"
                  :options="modelOptions"
                />
              </NFormItemGi>
              <NFormItemGi span="24 s:12" :label="$t('page.media.subtitle.format')" class="pr-24px">
                <NSelect
                  v-model:value="model.format"
                  :placeholder="$t('page.media.subtitle.format')"
                  :options="formatOption"
                />
              </NFormItemGi>
              <NFormItemGi span="24 s:12" :label="$t('page.media.subtitle.game')" class="pr-24px">
                <NSelect
                  v-model:value="model.gamecode"
                  :placeholder="$t('page.media.subtitle.game')"
                  :options="gameOption"
                />
              </NFormItemGi>

              <NFormItemGi span="24 s:12" :label="$t('page.media.subtitle.translate')" class="pr-24px">
                <NSelect
                  v-model:value="model.translate"
                  :placeholder="$t('page.media.subtitle.translate')"
                  :options="translateOption"
                />
              </NFormItemGi>
            </NGrid>
            <NFormItem>
              <NFlex justify="space-around" class="w-full">
                <NButton :loading="extractLoading" :disabled="emptyFileName" type="info" @click="extractSubtitle">
                  提取文字
                </NButton>
                <NButton :loading="translateLoading" :disabled="emptyOriginText" type="info" @click="translate">
                  {{ $t('page.media.form.btn.translate') }}
                </NButton>
              </NFlex>
            </NFormItem>
          </NForm>
        </NCard>
      </NGridItem>
      <NGridItem span="24 m:14 l:16">
        <NCard size="small" class="h-full flex card-wrapper">
          <div class="h-1/2 flex flex-col pb-4">
            <NInput
              v-model:value="originText"
              class="mb-2 flex-1"
              type="textarea"
              :placeholder="$t('page.media.form.text.originalText')"
            />
            <NButton :disabled="emptyOriginText" type="info" @click="download('origin')">
              {{ $t('page.media.form.btn.download') }}
            </NButton>
          </div>
          <!-- <NDivider /> -->
          <div class="h-1/2 flex flex-col pt-4">
            <NInput
              v-model:value="translatedText"
              class="mb-2 flex-1"
              type="textarea"
              :placeholder="$t('page.media.form.text.translatedText')"
            />
            <NButton :disabled="emptyTranslatedText" type="info" @click="download('')">
              {{ $t('page.media.form.btn.download') }}
            </NButton>
          </div>
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>

<style scoped lang="scss">
.prosess {
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
