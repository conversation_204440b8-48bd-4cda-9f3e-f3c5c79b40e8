"""
ComfyUI工作流文件管理模块

提供工作流JSON文件的动态路径解析功能，消除硬编码路径依赖
"""

from pathlib import Path
from typing import Union
import logging

logger = logging.getLogger(__name__)


def get_workflow_file_path(filename: str) -> Path:
    """
    获取ComfyUI工作流文件的完整路径
    
    Args:
        filename: 工作流文件名，可以包含或不包含.json扩展名
        
    Returns:
        Path: 工作流文件的完整路径对象
        
    Raises:
        ValueError: 当filename参数无效时
        FileNotFoundError: 当指定的工作流文件不存在时
        
    Example:
        # 不带扩展名
        path = get_workflow_file_path("flux_kontext_fp8_api")
        # 带扩展名
        path = get_workflow_file_path("flux_kontext_fp8_api.json")
        # 返回: Path对象指向具体的JSON文件
    """
    # 参数验证
    if not filename:
        raise ValueError("filename参数不能为空")
    
    # 清理文件名，移除可能的空白字符
    cleaned_filename = filename.strip()
    if not cleaned_filename:
        raise ValueError("filename参数不能为空白字符串")
    
    # 处理文件扩展名：如果没有.json扩展名则自动添加
    if not cleaned_filename.endswith('.json'):
        cleaned_filename = f"{cleaned_filename}.json"
    
    logger.debug(f"处理工作流文件名: {filename} -> {cleaned_filename}")
    
    try:
        # 获取当前模块所在目录，构建文件完整路径
        current_dir = Path(__file__).parent
        file_path = current_dir / cleaned_filename
        
        logger.debug(f"构建的文件路径: {file_path}")
        
        # 验证文件是否存在
        if not file_path.exists():
            logger.error(f"ComfyUI工作流文件不存在: {file_path}")
            raise FileNotFoundError(f"图片生成失败")
        
        # 验证是否为文件（而非目录）
        if not file_path.is_file():
            logger.error(f"指定的路径不是文件: {file_path}")
            raise FileNotFoundError(f"图片生成失败")
        
        logger.debug(f"成功获取工作流文件路径: {file_path}")
        return file_path
        
    except Exception as e:
        # 重新抛出已知的异常类型
        if isinstance(e, (ValueError, FileNotFoundError)):
            raise e
        else:
            # 处理其他未预期的异常
            logger.error(f"获取工作流文件路径时发生未预期错误: {e}")
            raise RuntimeError(f"图片生成失败")


def list_available_workflows() -> list[str]:
    """
    列出所有可用的工作流文件
    
    Returns:
        list[str]: 可用的工作流文件名列表（不含扩展名）
        
    Example:
        workflows = list_available_workflows()
        # 返回: ["flux_kontext_fp8_api", "other_workflow"]
    """
    try:
        current_dir = Path(__file__).parent
        workflow_files = []
        
        # 扫描目录中的所有.json文件
        for json_file in current_dir.glob("*.json"):
            # 移除.json扩展名
            workflow_name = json_file.stem
            workflow_files.append(workflow_name)
            logger.debug(f"发现工作流文件: {workflow_name}")
        
        logger.info(f"总计发现 {len(workflow_files)} 个工作流文件")
        return sorted(workflow_files)  # 返回排序后的列表
        
    except Exception as e:
        logger.error(f"列出工作流文件时发生错误: {e}")
        return []  # 发生错误时返回空列表
