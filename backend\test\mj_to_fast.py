import logging

import requests

# 请求的URL
url = "http://10.0.1.9:9092/mj/account/1582341587193647104/action"

logger = logging.getLogger(__name__)

# 请求的参数
params = {
  "customId": "MJ::Settings::FastMode",
  "botType": "MID_JOURNEY"
}

# 发起POST请求
try:
  response = requests.post(url, params=params)

  # 检查响应状态码是否为 200
  if response.status_code == 200:
    # 获取响应内容
    response_data = response.json()

    # 检查 "code" 值是否为 0
    if response_data.get("code") == 0:
      logger.info("已切换至快速时间")
    else:
      # 获取失败原因
      code = response_data.get("code", "未知")
      description = response_data.get("description", "无描述")
      logger.info(f'切换快速时间失败，"code"：{code}，"description"：{description}')
  else:
    logger.info("切换失败，状态码:", response.status_code)

except Exception as e:
  logger.error("请求失败:", e)
