<script setup lang="ts">
import { computed, ref } from 'vue';
import BaseSetting from './modules/base-setting.vue';
import ServerSetting from './modules/server-setting.vue';
import AiModelsSetting from './modules/ai-models-setting/index.vue';

type TabItem = {
  key: string;
  name: string;
  desc: string;
};

const typeTabList: TabItem[] = [
  {
    name: '基础配置',
    desc: '前端站点基础配置',
    key: 'BaseSetting'
  },
  {
    name: '服务配置',
    desc: '后端服务环境配置',
    key: 'ServerSetting'
  },
  {
    name: 'AI 模型配置',
    desc: '各项服务可用的 AI 模型配置',
    key: 'AiModelsSetting'
  }
];

const typeTitle = ref(typeTabList[0].name);
const tab = ref(typeTabList[0].key);
const activeTab = computed(
  () =>
    ({
      BaseSetting,
      ServerSetting,
      AiModelsSetting
    })[tab.value]
);

//
const switchType = (e: TabItem) => {
  typeTitle.value = e.name;
  tab.value = e.key;
};
</script>

<template>
  <!--
 <NTabs type="card" animated placement="left">
    <NTabPane name="1" tab="文本"></NTabPane>
    <NTabPane name="2" tab="图片"></NTabPane>
    <NTabPane name="3" tab="音频"></NTabPane>
  </NTabs>
-->
  <NFlex>
    <div class="max-w-[400px] w-1/4">
      <NCard :bordered="false" size="small" class="proCard h-full p-5">
        <NThing
          v-for="item in typeTabList"
          :key="item.key"
          class="thing-cell"
          :class="{ 'thing-cell-on': tab === item.key }"
          @click="switchType(item)"
        >
          <template #header>{{ item.name }}</template>
          <template #description>{{ item.desc }}</template>
        </NThing>
      </NCard>
    </div>
    <div class="max-w-[1300px] flex-1">
      <NCard :bordered="false" :title="typeTitle" class="proCard h-full p-5">
        <component :is="activeTab" />
      </NCard>
    </div>
  </NFlex>
</template>

<style lang="scss" scoped>
.thing-cell {
  margin: 0 -16px 10px;
  padding: 5px 16px;

  &:hover {
    // background: #f3f3f3;
    cursor: pointer;
  }
}

.thing-cell-on {
  // background: #f0faff;
  // color: #2d8cf0;

  :deep(.n-thing-main .n-thing-header .n-thing-header__title) {
    color: #2d8cf0;
  }
  :deep(.n-thing-main .n-thing-main__description) {
    color: #2d8cf0;
  }
}
</style>
