<script setup lang="tsx">
// import { ref } from 'vue';
import dayjs from 'dayjs';
import { fetchPageAnalyze } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
// import { $t } from '@/locales';
import PageSearch from './modules/page-search.vue';
const appStore = useAppStore();

const { columns, data, loading, getData, searchParams, resetSearchParams } = useTable({
  apiFn: fetchPageAnalyze,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    month: dayjs().format('YYYYMM'),
    date: dayjs().format('YYYYMMDD'),
    start_date: '',
    end_date: '',
    filter_type: 'date'
  },
  columns: () => [
    {
      key: 'title',
      title: '页面',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'name',
      title: '路由名',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'path',
      title: '地址',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'count',
      title: '访问次数',
      minWidth: 120
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <PageSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard title="页面访问统计" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="row => row.id"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
