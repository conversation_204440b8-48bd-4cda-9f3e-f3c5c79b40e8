<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import ChatSidebar from './assistant-sidebar.vue';

const showDrawer = defineModel<boolean>('showDrawer', { default: false });
const selectedChatId = defineModel<number | null>('selectedChatId', { default: null });
const chatList = defineModel<{ id: number; uuid: number; title: string; create_time: string; isEdit: boolean }[]>(
  'chatList',
  {
    default: []
  }
);

defineProps<{
  isMobile?: boolean;
  selectedChatTitle: string;
}>();

const emit = defineEmits<{
  (e: 'chatAdd'): void;
  (e: 'tabChange', value: string): void;
  (e: 'chatDelete', value: number): void;
}>();

const handleChatAdded = () => {
  emit('chatAdd');
};

const handleChangeTab = (value: string) => {
  emit('tabChange', value);
};

const handleDeleteChat = (value: number) => {
  emit('chatDelete', value);
};

const showLogoutModal = ref(false);
const authStore = useAuthStore();
</script>

<template>
  <NDrawer v-model:show="showDrawer" placement="left">
    <NDrawerContent>
      <template #footer>
        <NFlex class="w-full" justify="space-between" align="center">
          <NFlex align="center" size="small">
            <SvgIcon class="text-xl" icon="material-symbols:account-circle" />
            <span>{{ authStore.userInfo.userName }}</span>
          </NFlex>
          <NText class="text-xl" @click="showLogoutModal = true">
            <SvgIcon icon="ic:baseline-log-out" />
          </NText>
          <NModal
            v-model:show="showLogoutModal"
            preset="dialog"
            title="确认"
            content="确定退出登录？"
            positive-text="确认"
            negative-text="取消"
            @positive-click="authStore.logout"
          />
        </NFlex>
      </template>
      <ChatSidebar
        v-model:selected-chat-id="selectedChatId"
        v-model:chat-list="chatList"
        :selected-chat-title="selectedChatTitle"
        :is-mobile="isMobile"
        @chat-added="handleChatAdded"
        @update:selected-chat-title="handleChangeTab"
        @chat-delete="handleDeleteChat"
      />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
