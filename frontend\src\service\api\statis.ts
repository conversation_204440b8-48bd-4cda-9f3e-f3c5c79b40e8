import { request } from '../request';

/** 读取AI接口调用统计 */
export function fetchTaskAnalyze(params: Api.Statis.TaskStatisSearchParams) {
  return request<Api.Statis.TaskStatis>({ url: '/log/task_analyze', params });
}
/** 读取AI接口调用统计 */
export function fetchTaskAnalyzeDetail(params: Api.Statis.TaskStatisSearchDetail) {
  return request<Api.Statis.TaskDetailStatis>({ url: '/log/task_analyze_detail', params });
}
/** 读取页面访问统计 */
export function fetchPageAnalyze(params: Api.Statis.TaskStatisSearchParams) {
  return request<Api.Statis.PageStatis>({ url: '/log/page_analyze', params });
}

/** 获取API响应时间统计 */
export function fetchApiTime(apiName?: string) {
  return request({
    url: '/api/time',
    params: apiName ? { api_name: apiName } : undefined
  });
}
