import { computed } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { $t } from '@/locales';
// import { REG_PHONE } from '@/constants/reg';
import { fetchSendEmailCode } from '@/service/api';

export function useCaptcha() {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(120);

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  // function isPhoneValid(phone: string) {
  //   if (phone.trim() === '') {
  //     window.$message?.error?.($t('form.phone.required'));

  //     return false;
  //   }

  //   if (!REG_PHONE.test(phone)) {
  //     window.$message?.error?.($t('form.phone.invalid'));

  //     return false;
  //   }

  //   return true;
  // }

  async function getCaptcha(email: string, randstr: string, ticket: string, type: 'register' | 'reset' = 'register') {
    if (loading.value || isCounting.value) {
      return;
    }

    startLoading();
    try {
      const { data } = await fetchSendEmailCode({ email, randstr, ticket, type });
      if (data?.email) {
        window.$message?.success($t('page.login.codeLogin.sendCodeSuccess'));
        start();
      }
    } catch (error: any) {
      window.$message?.error(error.message || '发送验证码失败');
    } finally {
      endLoading();
    }
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha
  };
}
