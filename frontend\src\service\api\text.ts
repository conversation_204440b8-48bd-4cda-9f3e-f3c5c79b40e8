import { chatSSEClient, createSSEClient, request } from '../request';

export interface AddPresetsPayload {
  id?: number;
  gamecode: string;
  zh: string;
  en: string;
}

export interface SavePresetsPayload {
  id?: number;
  zh: string;
  en: string;
}

export interface GenerateCopywritingPayload {
  game: string;
  style: string;
  duration: string;
  gender: string;
  age: string;
  region: string;
  tags: string[];
  emotion: string;
  scene: string;
  description: string;
  event: string;
  reference: string;
}

export interface CopywritingRecord {
  game: string;
  style: string;
  duration: string;
  gender: string;
  age: string;
  region: string;
  tags: string[];
  emotion: string;
  scene: string;
  description: string;
  event: string;
  reference: string;
  generated_copy: string;
  generated_time: string;
}

export interface LanguageOption {
  label: string;
  value: string;
}

export interface TranslateParams {
  formData: FormData;
  targetLanguage: string;
  gamecode: string;
  model: string;
  use_presets?: boolean;
}

export interface Presets {
  gamecode: string;
  gamename: string;
}

// export interface CompanyGamesResponse {
//   data{};
// }

/** 获取预设列表 */
export function fetchFilePresets() {
  return request<Presets[]>({
    url: '/text/get_files_presets',
    method: 'get'
  });
}

/** 读取用户上传的表格 */
export function readExcel(formData: FormData) {
  return request<{ code: string; text: string[]; translations: string[] }>({
    url: '/text/read_excel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 翻译 */
export function translateExcel({ formData, targetLanguage, gamecode, model, use_presets }: TranslateParams) {
  formData.append('target_language', targetLanguage);
  formData.append('gamecode', gamecode);
  formData.append('model', model);
  formData.append('use_presets', String(use_presets));
  return request({
    url: `/text/translate`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 查看预设 */
export function fetchPresets(params: Api.SystemManage.CommonSearchParams) {
  return request({
    url: `/text/get_presets`,
    method: 'get',
    params: {
      gamecode: params.gamecode,
      current: params.current,
      size: params.size
    }
  });
}

/** 保存预设 */
export function savePresets(payload: SavePresetsPayload) {
  return request({
    url: `/text/save_presets`,
    method: 'post',
    data: payload
  });
}

/** 新增预设 */
export function uploadPresets(payload: AddPresetsPayload) {
  return request({
    url: `/text/add_presets`,
    method: 'post',
    data: payload
  });
}

/** 预设搜索 */
export function searchPresets(params: {
  gamecode?: string;
  zh?: string;
  en?: string;
  current?: number;
  size?: number;
}) {
  return request({
    url: `/text/search_presets`,
    method: 'get',
    params
  });
}

/** 删除预设 */
export function deletePresets(id: number) {
  return request({
    url: `/text/delete_presets`,
    method: 'post',
    data: JSON.stringify({ id }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/** 生成文案 */
export function generateCopywriting(
  payload: any,
  onMessage: (message: string) => void,
  onComplete: () => void,
  onStart: () => void
) {
  const url = '/proxy-default/text/generate_copywriting';
  return createSSEClient({
    url,
    payload,
    onMessage,
    onError: error => {
      console.error('Copywriting generation error:', error);
    },
    onComplete,
    onStart
  });
}

/** 文案生成历史记录 */
export function copywritingHistory(page: number, pageSize: number) {
  return request({
    url: '/text/get_my_history',
    method: 'get',
    params: { page, page_size: pageSize }
  });
}

/** 预设管理列表 */
export function getFilesPresets() {
  return request({
    url: '/text/read_gametranslations',
    method: 'get'
  });
}

/** 停止生成文案 */
export function stopGeneration() {
  return request({
    url: '/text/stop_generation',
    method: 'post'
  });
}

// userinput: 我去
// target_language: 中文→英文
// gamecode: 默认
// model: Qwen

export function translate(data: any) {
  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      if (key === 'use_presets') {
        formData.append(key, String(data[key]));
      } else {
        formData.append(key, data[key]);
      }
    });
  }
  return request({
    url: `/text/translate`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
// 字幕翻译
export function translateSubtitle(data: any) {
  return request({
    url: `/media/translate_subtitle`,
    method: 'post',
    data
  });
}

// 表格上传翻译预设
export function uploadTranslationPresets(formData: FormData) {
  return request({
    url: '/text/upload_translation_presets',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 获取与用户所属公司相关的游戏名称列表 */
export function fetchCompanyGames() {
  return request({
    url: '/text/get_company_games',
    method: 'get'
  });
}

/** 图片ocr提取文字 */
export function ExtractText(data: Api.text.ImgocrParams) {
  return request({
    url: '/imgocr/extract_text',
    method: 'post',
    data
  });
}

/** 获取支持的翻译语言列表 */
export function fetchSupportedLanguages() {
  return request<LanguageOption[]>({
    url: '/text/get_supported_languages',
    method: 'get'
  });
}

/** 获取支持的翻译模型列表 */
export function fetchSupportedModels() {
  return request<LanguageOption[]>({
    url: '/text/get_supported_models',
    method: 'get'
  });
}

// 新增流式翻译方法
export function translateStream(
  params: {
    userinput: string;
    target_language: string;
    gamecode: string;
    model: string;
    use_presets: boolean;
  },
  onMessage: (message: any) => void,
  onError?: (error: any) => void,
  onComplete?: () => void,
  onStart?: () => void
) {
  // 根据环境判断使用的URL前缀
  const baseUrl = import.meta.env.PROD ? '/api' : '/proxy-default';

  // 构建formData
  const formData = new FormData();
  formData.append('userinput', params.userinput);
  formData.append('target_language', params.target_language);
  formData.append('gamecode', params.gamecode);
  formData.append('model', params.model);
  formData.append('use_presets', String(params.use_presets));

  return chatSSEClient({
    url: `${baseUrl}/text/translate_stream`,
    payload: formData,
    onMessage,
    onError,
    onComplete,
    onStart,
    isFormData: true
  });
}
