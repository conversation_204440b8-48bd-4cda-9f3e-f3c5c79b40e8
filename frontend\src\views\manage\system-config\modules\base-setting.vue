<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';

const message = useMessage();
const formRef = ref<FormInst | null>(null);

const formModel = reactive({
  siteName: 'AI HUB',
  loginCaptcha: true,
  siteEnabled: false,
  updateMessage: '这是一更新的测试信息'
});

const rules: FormRules = {
  siteName: [
    {
      required: true,
      message: '请输入网站名称',
      trigger: ['blur', 'input']
    }
  ]
};

const handleSubmit = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(errors => {
    if (!errors) {
      // 更新设置
    } else {
      message.error('请检查表单填写是否正确');
    }
  });
};
</script>

<template>
  <div class="base-config">
    <NForm ref="formRef" :model="formModel" :rules="rules" label-width="100" require-mark-placement="right-hanging">
      <NFormItem label="网站名称" path="siteName" required>
        <div class="w-20em">
          <NInput v-model:value="formModel.siteName" placeholder="请输入网站名称" disabled />
        </div>
      </NFormItem>

      <NFormItem label="登录验证码" path="loginCaptcha">
        <NSpace align="center">
          <NRadioGroup v-model:value="formModel.loginCaptcha" disabled>
            <NSpace>
              <NRadio :value="true">开启</NRadio>
              <NRadio :value="false">关闭</NRadio>
            </NSpace>
          </NRadioGroup>
        </NSpace>
      </NFormItem>

      <NFormItem label="网站开启访问" path="siteEnabled">
        <NSwitch v-model:value="formModel.siteEnabled" disabled />
      </NFormItem>

      <NFormItem label="更新信息弹窗" path="updateMessage">
        <NInput v-model:value="formModel.updateMessage" type="textarea" placeholder="请输入更新信息" disabled />
      </NFormItem>

      <NFormItem>
        <NFlex><NButton type="primary" @click="handleSubmit">更新基本信息</NButton></NFlex>
      </NFormItem>
    </NForm>
  </div>
</template>

<style scoped></style>
