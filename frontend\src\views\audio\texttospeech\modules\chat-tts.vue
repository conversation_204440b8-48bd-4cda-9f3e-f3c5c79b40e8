<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { ComponentPublicInstance } from 'vue';

import { postChatTTS } from '@/service/api';
import { downloadFile, getRandomValueInRange } from '@/utils/common';

const textInput = ref<ComponentPublicInstance | null>(null);
const loading = ref(false);
const createDefaultModel = (): Api.Audio.TextToSpeech => {
  return {
    text: '',
    model: '',
    role: '丁真',
    prompt_audio: '',
    voice: '',
    text_seed: 0,
    audio_seed: 0,
    custom_voice: '',
    prompt_text: '',
    language: '中文',
    file_path: '',
    timbre: '',
    tone: '[oral_2]',
    prompt_oral: 0,
    prompt_laugh: 0,
    prompt_break: 0,
    prompt_oral_status: false,
    prompt_laugh_status: false,
    prompt_break_status: false,
    temperature: 0.1,
    top_k: 20,
    top_p: 0.1
  };
};

const ttsAudioBlobUrl = ref<string>('');
const model: Api.Audio.TextToSpeech = reactive(createDefaultModel());

const timbreOption: CommonType.Option[] = [
  { label: '男:2222', value: '2222' },
  { label: '男:4444', value: '4444' },
  { label: '男:6653', value: '6653' },
  { label: '男:7869', value: '7869' },
  { label: '男:9999', value: '9999' },
  { label: '女:1111', value: '1111' },
  { label: '女:3333', value: '3333' },
  { label: '女:4099', value: '4099' },
  { label: '女:5099', value: '5099' },
  { label: '女:5555', value: '5555' }
];
const voiceOption: CommonType.Option[] = [
  { label: '1031.pt', value: '1031.pt' },
  { label: '11.pt', value: '11.pt' },
  { label: '1111.pt', value: '1111.pt' },
  { label: '12.pt', value: '12.pt' },
  { label: '1234.pt', value: '1234.pt' },
  { label: '125.pt', value: '125.pt' },
  { label: '13.pt', value: '13.pt' },
  { label: '14.pt', value: '14.pt' },
  { label: '1455.pt', value: '1455.pt' },
  { label: '1518.pt', value: '1518.pt' },
  { label: '1579.pt', value: '1579.pt' },
  { label: '16.pt', value: '16.pt' },
  { label: '1983.pt', value: '1983.pt' },
  { label: '2222.pt', value: '2222.pt' },
  { label: '2279.pt', value: '2279.pt' },
  { label: '2328.pt', value: '2328.pt' },
  { label: '3333.pt', value: '3333.pt' },
  { label: '4099.pt', value: '4099.pt' },
  { label: '4444.pt', value: '4444.pt' },
  { label: '4751.pt', value: '4751.pt' },
  { label: '4785.pt', value: '4785.pt' },
  { label: '491.pt', value: '491.pt' },
  { label: '492.pt', value: '492.pt' },
  { label: '5.pt', value: '5.pt' },
  { label: '5099.pt', value: '5099.pt' },
  { label: '5400.pt', value: '5400.pt' },
  { label: '5555.pt', value: '5555.pt' },
  { label: '5600.pt', value: '5600.pt' },
  { label: '6653.pt', value: '6653.pt' },
  { label: '6666.pt', value: '6666.pt' },
  { label: '7777.pt', value: '7777.pt' },
  { label: '7869.pt', value: '7869.pt' },
  { label: '8888.pt', value: '8888.pt' },
  { label: '9999.pt', value: '9999.pt' },
  { label: 'seed_2155_restored_emb-covert.pt', value: 'seed_2155_restored_emb-covert.pt' }
];

const chattts = async () => {
  loading.value = true;
  ttsAudioBlobUrl.value = '';
  model.voice ||= '';
  model.custom_voice ||= '';
  model.text_seed ||= 0;
  model.audio_seed ||= 0;
  const res = await postChatTTS(model);
  if (res && res.data) {
    ttsAudioBlobUrl.value = res.data.url;
  }
  loading.value = false;
};
const getShow = () => {
  return true;
};

// 在光标处插入
const cursorInsert = (str: string) => {
  console.clear();
  console.log('insert', str);
  // 前后加个空格
  const new_str = ` ${str} `;

  const strLength = new_str.length;
  // textInput.value.clear();
  if (textInput.value) {
    const textArea = textInput.value.$el.children[0].children[0].children[0];
    console.log(textArea);
    console.log(textArea.selectionStart);
    const selectionStart = textArea.selectionEnd | 0;

    if (model.text) {
      model.text = `${model.text.substring(0, selectionStart)}${new_str}${model.text.substring(selectionStart)}`;
    }
    textArea.focus();
    setTimeout(() => {
      textArea.setSelectionRange(selectionStart + strLength, selectionStart + strLength);
    }, 0);
  }
};
const randomTextSeed = () => {
  model.text_seed = getRandomValueInRange(0, 99999);
};
const randomAudioSeed = () => {
  model.audio_seed = getRandomValueInRange(0, 99999);
};

// 添加音频控制相关的状态
const resultAudio = ref<HTMLAudioElement | null>(null);

// 下载音频文件
const downloadAudio = (url: string) => {
  if (!url) {
    window.$message?.error('暂无音频文件');
    return;
  }
  downloadFile(url, `tts_audio_${Date.now()}.wav`);
};

// 设置音频播放速率
const setPlaybackRate = (rate: number) => {
  if (resultAudio.value) {
    resultAudio.value.playbackRate = rate;
  }
};
</script>

<template>
  <NScrollbar class="h-full">
    <div class="h-[calc(100vh-13em)] flex gap-16px">
      <!-- 左侧操作区域 -->
      <div class="panel-container shrink-0">
        <NCard class="h-full">
          <div class="max-w-full w-250 gap-16px">
            <NGrid cols="24" :x-gap="10">
              <NGridItem span="24">
                <NInput
                  ref="textInput"
                  v-model:value="model.text"
                  type="textarea"
                  placeholder="请输入要合成的文本"
                  class="custom-input min-h-200px"
                >
                  <template #suffix>
                    <div class="mb-2 h-7 w-full flex justify-end">
                      <NSpace class="">
                        <NButton size="small" type="info" @click="cursorInsert('[laugh]')">笑 声</NButton>
                        <NButton size="small" type="success" @click="cursorInsert('[uv_break]')">停 顿</NButton>
                        <NButton size="small" type="warning" @click="cursorInsert('[lbreak]')">休 息</NButton>
                      </NSpace>
                    </div>
                  </template>
                </NInput>
              </NGridItem>
            </NGrid>

            <NGrid :x-gap="20" :cols="2" class="mt-5">
              <NGi span="1">
                <NFormItemGi label="选择音色">
                  <NSelect v-model:value="model.custom_voice" :options="voiceOption" clearable size="medium" />
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="音色值">
                  <NAutoComplete
                    v-model:value="model.voice"
                    :options="timbreOption"
                    :get-show="getShow"
                    show-empty
                    blur-after-select
                    placeholder=""
                  />
                </NFormItemGi>
              </NGi>
            </NGrid>

            <!-- Seed 控件行 -->
            <NGrid :x-gap="12" :cols="2">
              <NGi span="1">
                <NFormItemGi label="Text Seed" class="mt-5">
                  <NInputGroup class="w-full">
                    <NInputNumber v-model:value="model.text_seed" clearable class="flex-1" />
                    <NButton type="primary" @click.stop="randomTextSeed">随机</NButton>
                  </NInputGroup>
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="Audio Seed" class="mt-5">
                  <NInputGroup class="w-full">
                    <NInputNumber v-model:value="model.audio_seed" clearable class="flex-1" />
                    <NButton type="primary" @click.stop="randomAudioSeed">随机</NButton>
                  </NInputGroup>
                </NFormItemGi>
              </NGi>
            </NGrid>

            <!-- 开关控件行 -->
            <NGrid :x-gap="12" :cols="3">
              <NGi span="1">
                <NFormItemGi label="口语" class="mt-5">
                  <NSwitch v-model:value="model.prompt_oral_status" />
                  <NInputNumber
                    v-model:value="model.prompt_oral"
                    :min="0"
                    :max="9"
                    :disabled="!model.prompt_oral_status"
                    class="ml-2 flex-1"
                  />
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="笑声" class="mt-5">
                  <NSwitch v-model:value="model.prompt_laugh_status" />
                  <NInputNumber
                    v-model:value="model.prompt_laugh"
                    :min="0"
                    :max="2"
                    :disabled="!model.prompt_laugh_status"
                    class="ml-2 flex-1"
                  />
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="停顿" class="mt-5">
                  <NSwitch v-model:value="model.prompt_break_status" />
                  <NInputNumber
                    v-model:value="model.prompt_break"
                    :min="0"
                    :max="7"
                    :disabled="!model.prompt_break_status"
                    class="ml-2 flex-1"
                  />
                </NFormItemGi>
              </NGi>
            </NGrid>

            <NGrid :x-gap="12" :cols="3">
              <NGi span="1">
                <NFormItemGi label="波动性" class="mt-5">
                  <NSlider v-model:value="model.temperature" :step="0.1" :min="0" :max="1" />
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="Top P" class="mt-5">
                  <NSlider v-model:value="model.top_p" :step="0.1" :min="0.1" :max="0.9" />
                </NFormItemGi>
              </NGi>
              <NGi span="1">
                <NFormItemGi label="Top K" class="mt-5">
                  <NSlider v-model:value="model.top_k" :step="1" :min="1" :max="20" />
                </NFormItemGi>
              </NGi>
            </NGrid>

            <NSpace justify="center">
              <NButton type="info" :loading="loading" class="mt-5 h-55px w-70 p-5" @click="chattts">生成</NButton>
            </NSpace>
          </div>
        </NCard>
      </div>

      <!-- 右侧结果展示区域 -->
      <div class="panel-container">
        <NCard class="h-full">
          <div class="flex flex-col gap-2">
            <NSpin :show="loading">
              <audio ref="resultAudio" :src="ttsAudioBlobUrl" controls class="w-full"></audio>
            </NSpin>
            <div class="flex justify-center">
              <NButtonGroup>
                <NButton size="small" @click="downloadAudio(ttsAudioBlobUrl)">
                  <template #icon>
                    <SvgIcon icon="mdi:download" />
                  </template>
                  下载
                </NButton>
                <NButton size="small" @click="setPlaybackRate(0.5)">0.5x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.0)">1.0x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.5)">1.5x</NButton>
              </NButtonGroup>
            </div>
          </div>
        </NCard>
      </div>
    </div>
  </NScrollbar>
</template>

<style scoped lang="scss">
:deep(.custom-input) {
  .n-input-wrapper {
    display: flex;
    flex-direction: column;
  }
}

// 添加响应式布局样式
.left-panel {
  width: 60em; // 默认宽度为60em

  @media screen and (min-width: 1440px) {
    width: 75em; // 在大屏幕(1440px及以上)时宽度为75em
  }
}

:deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;

  .n-card-header {
    flex-shrink: 0;
  }

  .n-card__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
}

:deep(.n-scrollbar-content) {
  padding-right: 14px;
}

// 左右均等布局样式
.panel-container {
  width: 50%;
  max-width: 820px;
  padding: 0 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  @media screen and (max-width: 1200px) {
    max-width: 600px;
  }
}
</style>
