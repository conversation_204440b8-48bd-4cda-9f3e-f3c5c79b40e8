# AIhub 项目
#### https://github.com/soybeanjs/soybean-admin/

## 项目结构
```
├── frontend/                 # 前端项目
│   ├── src/                 # 源代码
│   │   ├── components/     # 组件
│   │   ├── composables/    # 组合式函数
│   │   ├── layouts/       # 布局组件
│   │   ├── router/        # 路由配置
│   │   ├── service/       # 服务层
│   │   ├── store/         # 状态管理
│   │   ├── styles/        # 样式文件
│   │   └── views/         # 页面
│   └── package.json        # 项目依赖
├── backend/                 # 后端项目
│   ├── app/               # 应用代码
│   │   ├── auth/         # 认证模块
│   │   ├── common/       # 公共模块
│   │   ├── systemManage/ # 系统管理
│   │   └── text/         # 文本处理
│   ├── utils/            # 工具函数
│   └── requirements.txt   # Python 依赖
└── docker/                 # Docker 配置
    └── docker-compose.yml # Docker 编排配置
```

## 开发环境部署

### 前端项目

1. 进入前端目录
```bash
cd frontend
```

2. 安装依赖
```bash
pnpm install
```

3. 启动开发服务器
```bash
pnpm dev
```

4. 构建生产版本
```bash
pnpm build
```

### 后端项目

1. 进入后端目录
```bash
cd backend
```

2. 安装依赖
```bash
pip install -r requirements.txt  # 注意部分库需要修改成win环境
```

3. 启动开发服务器
```bash
uvicorn main:app --reload --port 5002
```

4.启动队列管理进程
```bash
python task_queue/run.py
```

## Docker 部署

使用 Docker Compose 一键部署整个应用:

```bash
cd docker
docker-compose up -d # 根据需求修改docker-compose.yml文件
```

默认端口:
- 前端: 9527
- 后端: 5002

## 环境配置

### 前端环境变量
在 frontend 目录下配置以下文件:
- `.env.dev` - 开发环境配置  
- `.env.prod` - 生产环境配置

### 后端环境变量
在 backend 目录下配置以下文件:
- `.env`  

主要配置项:
- API 地址/端口
- 数据库连接
- Redis 连接
- 第三方服务配置

## 技术栈

### 前端
- Vue 3
- TypeScript
- Vite
- Pinia
- UnoCSS
- Naive UI

### 后端
- Python
- FastAPI
- SQLAlchemy
- Redis
- MySQL

## Docs
### pip install volcengine-python-sdk
``` bash
 报错 AttributeError: module 'pkgutil' has no attribute 'ImpImporter'. Did you mean: 'zipimporter'
```
解决方法：
``` bash
python -m pip install --upgrade pip setuptools
```
