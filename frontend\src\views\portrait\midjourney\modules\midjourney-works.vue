<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { ChatMjTask } from '@/service/api/midjourney';
import { getMyTasks } from '@/service/api/midjourney';

const router = useRouter();

const message = useMessage();

const ossBaseUrl = import.meta.env.VITE_OSS_BASE_URL;

const goToMj = () => {
  router.push({ name: 'portrait_midjourney' });
};

// 存储从接口获取的任务数据
const tasks = ref<ChatMjTask[]>([]);

// 计算属性过滤掉没有图片的任务或图片URL不包含 oss_base_url
const filteredTasks = computed(() => {
  return tasks.value.filter(task => task.image_url && task.image_url.includes(ossBaseUrl));
});

// 获取任务列表并更新tasks
const fetchTasks = async (page: number = 1, page_size: number = 32) => {
  try {
    const response = await getMyTasks(page, page_size);
    tasks.value = response.data || []; // 确保 tasks 是数组，即使 response.data 是 null
  } catch (error) {
    console.error('获取任务列表失败:', error);
  }
};

// 格式化 prompt 函数，删除 "-- v" 之后的内容
const formatPrompt = (prompt: string) => {
  const index = prompt.indexOf('--');
  return index !== -1 ? prompt.substring(0, index).trim() : prompt;
};

// 复制 prompt 到剪贴板
const copyPromptToClipboard = (prompt: string) => {
  const formattedPrompt = formatPrompt(prompt);
  navigator.clipboard
    .writeText(formattedPrompt)
    .then(() => {
      message.success('提示词已复制');
    })
    .catch(err => {
      console.error('Failed to copy prompt:', err);
    });
};

// 组件挂载时调用接口获取数据
onMounted(() => {
  fetchTasks();
});
</script>

<template>
  <main class="h-screen flex flex-col gap-2">
    <NCard class="mb-1 h-19/20">
      <NButton class="mb-3 w-20" type="success" @click="goToMj">
        <SvgIcon icon="icon-park:return" class="mr-0.5" />
        返回
      </NButton>
      <NGrid x-gap="5" :cols="8" y-gap="10">
        <!-- 动态渲染任务图片 -->
        <NGi v-for="(task, index) in filteredTasks" :key="index" class="flex justify-center">
          <div class="imagecard h-40 w-40 flex flex-col justify-center">
            <NImage
              :src="task.image_url || undefined"
              class="imagebox h-full w-full rounded"
              object-fit="contain"
              preview-disabled
              @click="copyPromptToClipboard(task.prompt)"
            ></NImage>
          </div>
        </NGi>
      </NGrid>
    </NCard>
    <NSpace justify="end">
      <NPagination :page-count="100" @update:page="fetchTasks" />
    </NSpace>
  </main>
</template>

<style scoped>
#shiny-shadow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.imagebox {
  background: transparent;
  text-transform: uppercase;
  outline: none;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.imagebox:hover {
  transform: scale(1.1); /* 鼠标悬停时放大 */
}

.imagebox:after {
  content: '';
  display: block;
  position: absolute;
  top: -36px;
  left: -100px;
  background: white;
  width: 50px;
  height: 125px;
  opacity: 20%;
  transform: rotate(-45deg);
}

.imagebox:hover:after {
  left: 120%;
  transition: all 600ms cubic-bezier(0.5, 1, 0.2, 1);
  -webkit-transition: all 600ms cubic-bezier(0.5, 1, 0.2, 1);
}

.imagecard {
  overflow: hidden;
}
</style>
