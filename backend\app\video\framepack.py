import logging
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from models.users import User, get_request_user
from models.tasks import TaskStatus, TaskType
from service.credit import CreditOperator
from task_queue.task_manager import TaskManager
from utils.database import get_db
from app.api_request_time import record_time
from app.video.common import (
    VideoGenerationRequest, VideoTaskResponse, VideoHistoryResponse,
    CancelTaskRequest, CancelTaskResponse, DeleteTaskRequest, DeleteTaskResponse, process_video_generation
)
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/generate", tags=["framepack"], response_model=VideoTaskResponse)
@record_time(api_name="framepack_generate")
async def generate_video(
    request: Request,
    req: VideoGenerationRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    视频生成接口 - 创建生成任务
    """
    operator = CreditOperator(user.id, request.state.client_ip, user.username, db)
    await operator.pre_debit(1, 'video-generation', req.model)
    response = await process_video_generation(req, user, db, api_tag="framepack", credit_operator_id=operator.id)
    return response

@router.get("/task_status/{taskid}", tags=["framepack"], response_model=VideoTaskResponse)
@record_time(api_name="framepack_task_status")
async def get_task_status(
    taskid: str,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    查询任务状态
    """
    try:
        # 使用通用任务管理器查询任务
        task = await TaskManager.get_task_status(
            db=db,
            taskid=taskid,
            username=user.username
        )

        if not task:
            raise ClientVisibleException("任务不存在")

        # 超时检查逻辑
        try:
            # 检查任务状态是否需要进行超时检查
            if task.status in [TaskStatus.NOT_START, TaskStatus.SUBMITTED, TaskStatus.IN_PROGRESS]:
                current_time = datetime.now()
                timeout_detected = False
                
                # 优先检查 start_time 超时（如果存在）
                if task.start_time is not None and current_time - task.start_time > timedelta(hours=1):
                    timeout_detected = True
                    logger.info(f"任务 {taskid} 检测到 start_time 超时: 开始时间={task.start_time}, 当前时间={current_time}")
                # 次要检查 submit_time 超时
                elif current_time - task.submit_time > timedelta(hours=5):
                    timeout_detected = True
                    logger.info(f"任务 {taskid} 检测到 submit_time 超时: 提交时间={task.submit_time}, 当前时间={current_time}")
                
                # 如果检测到超时，更新任务状态
                if timeout_detected:
                    await TaskManager.update_task_status(
                        db=db,
                        task_id=task.id,
                        status=TaskStatus.FAILURE,
                        fail_reason="任务超时"
                    )
                    # 同步本地任务对象
                    task.status = TaskStatus.FAILURE
                    task.fail_reason = "任务超时"
                    logger.info(f"任务 {taskid} 已标记为超时失败")
        except Exception as e:
            logger.warning(f"任务 {taskid} 超时检查失败: {e}")

        # 构建输出数据结构
        task_out = {
            "id": task.id,
            "taskid": task.taskid,
            "username": task.username,
            "status": task.status,
            "action": task.action,
            "submit_time": task.submit_time.isoformat() if task.submit_time else None,
            "start_time": task.start_time.isoformat() if task.start_time else None,
            "finish_time": task.finish_time.isoformat() if task.finish_time else None,
            "queue_position": task.queue_position,
            "fail_reason": task.fail_reason,
            "prompt": task.task_params.get("prompt", "") if task.task_params else "",
            "task_params": task.task_params,
            "prompt_media_url": task.prompt_media_url
        }

        # 如果任务成功且有结果，解析结果JSON
        if task.status == TaskStatus.SUCCESS and task.task_result:
            task_out["video_data"] = task.task_result
            # 如果结果中包含result_url，添加video_url字段
            if "result_url" in task.task_result:
                task_out["video_url"] = task.task_result["result_url"]
        elif task.resource_url:
            # 如果只有资源URL，创建简单的结果对象
            task_out["video_data"] = {
                "result_url": task.resource_url
            }
            task_out["video_url"] = task.resource_url

        # 返回任务信息
        return VideoTaskResponse(
            code="0000",
            data=task_out
        )

    except Exception as e:
        logger.error(f"查询任务状态失败 for task {taskid}, user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("查询任务状态时发生内部错误") from e

@router.get("/history", tags=["framepack"], response_model=VideoHistoryResponse)
@record_time(api_name="framepack_history")
async def get_video_history(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的视频生成历史记录（分页）
    """
    try:
        # 构建过滤条件
        filter_params = {}

        # 添加任务类型过滤
        tasks, total_count = await TaskManager.get_user_tasks(
            db=db,
            username=user.username,
            task_type=TaskType.VIDEO,
            page=page,
            size=size,
            filter_params=filter_params
        )

        processed_tasks = []
        for task in tasks:
            # 构建输出数据结构
            task_out = {
                "id": task.id,
                "taskid": task.taskid,
                "username": task.username,
                "status": task.status,
                "action": task.action,
                "submit_time": task.submit_time.isoformat() if task.submit_time else None,
                "start_time": task.start_time.isoformat() if task.start_time else None,
                "finish_time": task.finish_time.isoformat() if task.finish_time else None,
                "queue_position": task.queue_position,
                "fail_reason": task.fail_reason,
                "prompt": task.task_params.get("prompt", "") if task.task_params else "",
                "task_params": task.task_params
            }

            # 如果任务成功且有结果，解析结果JSON
            if task.status == TaskStatus.SUCCESS and task.task_result:
                task_out["video_data"] = task.task_result
                # 如果结果中包含 result_url 或 video_url，添加 video_url 字段
                if isinstance(task.task_result, dict):
                    if "result_url" in task.task_result:
                        task_out["video_url"] = task.task_result["result_url"]
                    elif "video_url" in task.task_result:
                        task_out["video_url"] = task.task_result["video_url"]
            elif task.resource_url:
                # 如果只有资源URL，创建简单的结果对象
                task_out["video_data"] = {
                    "result_url": task.resource_url
                }
                task_out["video_url"] = task.resource_url

            processed_tasks.append(task_out)

        return VideoHistoryResponse(
            code="0000",
            data=processed_tasks
        )

    except Exception as e:
        logger.error(f"查询视频历史记录失败 for user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("查询历史记录时发生内部错误") from e

@router.post("/tasks/cancel", tags=["framepack"], response_model=CancelTaskResponse)
@record_time(api_name="framepack_cancel_task")
async def cancel_task(
    background_task: BackgroundTasks,
    req: CancelTaskRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    取消视频生成任务
    """
    try:
        taskid = req.taskid

        # 查询任务状态
        task = await TaskManager.get_task_status(
            db=db,
            taskid=taskid,
            username=user.username
        )

        if not task:
            raise ClientVisibleException("任务不存在")

        # 记录任务取消前的状态
        logger.info(f"取消任务 {taskid} 前状态: 状态={task.status}, 队列位置={task.queue_position}")

        # 检查任务是否可以取消（只有未开始或进行中的任务可以取消）
        if task.status not in [TaskStatus.NOT_START, TaskStatus.SUBMITTED, TaskStatus.IN_PROGRESS]:
            raise ClientVisibleException(f"无法取消状态为 {task.status} 的任务")

        # 保存原始状态用于后续逻辑判断
        original_status = str(task.status)

        # 先更新任务状态为已取消，确保后续处理不会再处理该任务
        await TaskManager.update_task_status(
            db=db,
            task_id=task.id,
            status=TaskStatus.CANCELED,
            fail_reason="用户取消"
        )

        # 根据原始任务状态选择不同的取消逻辑
        if original_status == TaskStatus.IN_PROGRESS.value:
            # 记录取消处理中任务的操作
            logger.info(f"开始取消处理中的任务 {taskid}")

            # 任务正在处理中，需要调用AI服务器的取消接口
            from task_handlers.video_handler import cancel_video_task

            # 调用取消函数
            cancel_result = await cancel_video_task({"taskid": taskid})

            if not cancel_result.get("success"):
                # 取消AI服务器任务失败，但数据库状态已更新为取消
                logger.warning(f"任务 {taskid} 已在数据库中标记为取消，但通知AI服务器取消失败: {cancel_result.get('error')}")

        background_task.add_task(TaskManager.notify_task_canceling_to_ai_server, task=task)
        return CancelTaskResponse(
            code="0000",
            msg="任务已取消"
        )

    except Exception as e:
        logger.error(f"取消任务失败 for task {req.taskid}, user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("任务终止失败") from e
    
@router.post("/tasks/delete", tags=["framepack"], response_model=DeleteTaskResponse)
@record_time(api_name="framepack_delete_task")
async def delete_task(
    req: DeleteTaskRequest,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除视频生成任务
    """
    try:
        taskid = req.taskid

        # 调用TaskManager删除任务
        success, error_msg = await TaskManager.delete_task(
            db=db,
            taskid=taskid,
            username=user.username
        )

        if success:
            return DeleteTaskResponse(
                code="0000",
                msg="任务已删除"
            )
        else:
            raise ClientVisibleException(error_msg)

    except Exception as e:
        logger.error(f"删除任务失败 for task {req.taskid}, user {user.username}: {e}", exc_info=True)
        raise ClientVisibleException("删除任务时发生内部错误") from e
