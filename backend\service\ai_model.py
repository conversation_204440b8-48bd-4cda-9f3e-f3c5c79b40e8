import logging
from enum import StrEnum
from typing import Sequence

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from models.model import Model
from models.system_settings_group import SystemSettingsGroup
from models.system_settings import SystemSettings


logger = logging.getLogger(__name__)


async def get_models(db: AsyncSession) -> dict[str, Model]:
    """
    获取所有可用的模型列表，返回值的 Key 是模型的 name。
    """
    result = dict()
    async with db as session:
        session.begin()
        stmt = select(Model).filter(Model.status.is_(True))
        res = await session.execute(stmt)
        models: Sequence[Model] = res.scalars().all()
        for model in models:
            result[model.name] = model
    return result


SYSTEM_SETTING_GROUP_CODE = 'AVALIABLE_AI_MODELS'


class AvailableModelKey(StrEnum):
    ASSISTANT = 'ASSISTANT_MODELS'  # AI 助手可用的模型


async def get_available_models(db: AsyncSession) -> dict[AvailableModelKey, list[Model]]:
    """
    获取具体模块对应的可用的模型列表。
    """
    result = dict()
    models = await get_models(db)
    id_map = await _get_available_model_ids(db)
    model_map = {model.id: model for model in models.values()}
    for key, ids in id_map.items():
        for id in ids:
            if id in model_map:
                result.setdefault(key, []).append(model_map[id])
    return result

async def remove_available_model(db: AsyncSession, model_id: int):
    model_id_map = await _get_available_model_ids(db)
    for key, ids in model_id_map.items():
        if model_id in ids:
            ids.remove(model_id)
            try:
                await _update_available_model_ids(db, key, ids)
            except Exception as e:
                logger.error(f"Remove available model {model_id} failed: {e}")

async def _get_available_model_ids(db: AsyncSession) -> dict[AvailableModelKey, list[int]]:
    """
    获取可用的模型 ID 列表。
    """
    result = dict()
    async with db as session:
        session.begin()
        stmt = (
            select(SystemSettings)
            .join(SystemSettingsGroup, SystemSettings.group_id == SystemSettingsGroup.id)
            .where(SystemSettingsGroup.group_code == SYSTEM_SETTING_GROUP_CODE)
        )
        res = await session.execute(stmt)
        settings: Sequence[SystemSettings] = res.scalars().all()
        for setting in settings:
            if setting.config_key in AvailableModelKey:
                value = setting.config_value
                vs = value.split(',')
                for v in vs:
                    if v.isdigit():
                        result.setdefault(AvailableModelKey(setting.config_key), []).append(int(v))
    return result


async def _update_available_model_ids(db: AsyncSession, key: AvailableModelKey, ids: list[int]):
    """
    更新可用的模型 ID 列表。
    """
    async with db as session:
        session.begin()
        stmt = (
            select(SystemSettings)
           .join(SystemSettingsGroup, SystemSettings.group_id == SystemSettingsGroup.id)
           .where(SystemSettingsGroup.group_code == SYSTEM_SETTING_GROUP_CODE)
           .where(SystemSettings.config_key == key)
        )
        res = await session.execute(stmt)
        setting: SystemSettings| None = res.scalars().first()
        if setting is not None:
            setting.config_value = ','.join(str(id) for id in ids)
            session.add(setting)
            await session.commit()
