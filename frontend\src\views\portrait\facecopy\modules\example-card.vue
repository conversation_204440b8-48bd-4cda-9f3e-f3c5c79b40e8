<script setup lang="ts">
import { ref } from 'vue';
import { urlToBase64 } from '../../../../utils/image';
interface Emits {
  (
    e: 'doTry',
    params: {
      source_b64: string;
      reference_b64: string;
      type: 'image' | 'video' | null;
      tab: string;
      sourceFileName: string;
      referenceFileName: string;
    }
  ): any;
}
const emit = defineEmits<Emits>();

type Example = {
  source: string;
  reference: string;
  reftype: 'image' | 'video' | null;
  tab: string;
};
const example = ref<Example[]>([
  {
    source: '/proxy-default/data/static/facecopy/s5.jpg',
    reference: '/proxy-default/data/static/facecopy/d7.mp4',
    reftype: 'video',
    tab: 'human'
  },
  {
    source: '/proxy-default/data/static/facecopy/s41.jpg',
    reference: '/proxy-default/data/static/facecopy/d14.mp4',
    reftype: 'video',
    tab: 'animal'
  },
  {
    source: '/proxy-default/data/static/facecopy/s5.jpg',
    reference: '/proxy-default/data/static/facecopy/d9.jpg',
    reftype: 'image',
    tab: 'human'
  }
]);
const tryExampla = async (exam: Example) => {
  const getFileName = (path: string) => {
    const parts = path.split('/');
    return parts[parts.length - 1];
  };

  const source_b64 = await urlToBase64(exam.source);
  const reference_b64 = await urlToBase64(exam.reference);

  const sourceFileName = getFileName(exam.source);
  const referenceFileName = getFileName(exam.reference);

  emit('doTry', {
    source_b64,
    reference_b64,
    type: exam.reftype,
    tab: exam.tab,
    sourceFileName,
    referenceFileName
  });
};
</script>

<template>
  <NCard header-style="padding:10px 24px;" :bordered="false">
    <NList hoverable>
      <NListItem v-for="item in example" :key="item.source">
        <NSpace justify="center">
          <!--
 <NImage object-fit="cover" :height="60" :width="60" :src="item.source"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.reference"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.mark"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.output"></NImage>
-->
          <NImage object-fit="cover" :height="60" :width="60" :src="item.source"></NImage>

          <template v-if="item.reftype === 'video'">
            <video :src="item.reference" :height="60" :width="60" controls></video>
          </template>
          <template v-else-if="item.reftype === 'image'">
            <NImage object-fit="cover" :height="60" :width="60" :src="item.reference"></NImage>
          </template>
        </NSpace>
        <template #suffix>
          <NButton type="primary" size="medium" text @click.stop="tryExampla(item)">试一试</NButton>
        </template>
      </NListItem>
    </NList>
  </NCard>
</template>

<style scoped></style>
