import logging

from asgi_user_agents import UAMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi import Depends, FastAPI, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError, HTTPException
from starlette.middleware import Middleware

from models.users import check_user_auth
from utils.exceptions import ClientVisibleException
from utils.log import setup_logging
import traceback
from dependencies import get_real_ip, ua_checker
from utils.database import AsyncSessionLocal
from utils.api_call_records import log_api_call
from middleware.semaphore_limit import SemaphoreLimitMiddleware # 并发控制中间件
import time
from utils.config_loader import ConfigLoader
from pydantic_core import PydanticSerializationError
from task.task import init_task
from config import app_settings
from contextlib import asynccontextmanager
from fastapi_limiter import FastAPILimiter
from utils.redis import get_redis


# 初始化日志设置
setup_logging()
logger = logging.getLogger(__name__)

logger.info("Starting FastAPI Server...")


@asynccontextmanager
async def lifespan(instance: FastAPI):
    await startup_event()
    await import_routes(instance)
    redis = get_redis()
    await FastAPILimiter.init(
        redis,
        prefix='ai-rate-limit-',
        http_callback=rate_limiter_callback,
    )
    yield
    await FastAPILimiter.close()


# noinspection PyTypeChecker
app = FastAPI(
    dependencies=[
        Depends(get_real_ip),
        Depends(ua_checker),
        Depends(check_user_auth),
    ],
    lifespan=lifespan,
    middleware=[
        Middleware(UAMiddleware),
    ],
)


async def startup_event():
    """应用启动时的初始化操作"""
    # 初始化系统配置
    logger.info("在应用启动事件中初始化系统配置...")
    await ConfigLoader.initialize()
    logger.info("系统配置初始化完成")

    # 初始化 MJ 任务
    init_task()

    # 注意：音乐生成和其他任务队列处理已迁移到独立进程
    # 使用命令启动任务队列处理进程: python task_queue/run.py
    logger.info("任务队列处理已迁移到独立进程，请确保已通过单独的命令启动任务队列服务")

app.mount("/upload", StaticFiles(directory="upload"), name="upload")
app.mount("/data", StaticFiles(directory="data"), name="data")


@app.middleware("http")
async def log_middleware(request: Request, call_next):
  start_time = time.time()
  response = await call_next(request)
  async with AsyncSessionLocal() as db:  # 手动获取数据库会话
    await log_api_call(
      request,
      response,
      time.time() - start_time,
      db=db
    )
  return response


@app.exception_handler(HTTPException)
async def unicorn_exception_handler(request: Request, exc: HTTPException):
  return JSONResponse(
    status_code=exc.status_code,
    content={"code": "1000", "msg": exc.detail},
  )


@app.exception_handler(ClientVisibleException)
async def client_visible_exception_handler(request: Request, exc: HTTPException):
  return JSONResponse(
    status_code=exc.status_code,
    content={"code": "1000", "msg": exc.detail},
  )


if not app_settings.debug:
  @app.exception_handler(RequestValidationError)
  async def validation_exception_handler(request, exc):
    return JSONResponse(
      status_code=402,
      content={"code": "1000", "msg": "文件格式有误"},
    )

app.add_middleware(  # 添加并发控制，指定路由控制并发执行数量
    SemaphoreLimitMiddleware,
    path_limits={"/volcano/ttstest": 2, "/volcano/ttstest": 2}
)

# app.add_middleware(UserCreditLogMiddleware) # 扣除用户积分

@app.exception_handler(500)
async def validation_exception_handler(request, exc):
  logger.error(traceback.format_exc())
  # 捕抓积分不足错误，显示给前端
  if exc.status_code == 402:
      return JSONResponse(
          status_code=402,
          content={"code": "402", "msg": "您的访问过于频繁，请24小时后再试"},
      )
  return JSONResponse(
    status_code=200,
    content={"code": "1000", "msg": "请求失败，请重新尝试"},
  )

@app.exception_handler(PydanticSerializationError)
async def pydantic_serialization_error_handler(_: Request, exc: PydanticSerializationError):
    """处理Pydantic序列化错误"""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": "1000",
            "msg": f"数据序列化错误: {str(exc)}",
            "data": None
        }
    )


# 将路由导入移到这里，确保在配置加载后才导入
# 在应用实例创建后导入路由模块
async def import_routes(instance: FastAPI):
    # 等待配置加载完成后再导入路由
    from routes import add_routes
    from app.systemManage.config import router as config_router
    # 添加路由
    add_routes(instance)


def rate_limiter_callback(req: Request, *_, **__):
    """限流中间件的回调，用于返回错误"""
    logger.warning(f"Rate limiter triggered: {req.state.client_ip}")
    raise ClientVisibleException("请求失败")
