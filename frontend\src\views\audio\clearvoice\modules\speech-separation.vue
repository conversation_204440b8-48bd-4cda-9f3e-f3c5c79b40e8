<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import type { UploadCustomRequestOptions } from 'naive-ui';
import { useLoadingBar, useMessage } from 'naive-ui';
import { useNaiveForm } from '@/hooks/common/form';
import { postSeparation, postUploadFile } from '@/service/api';
import { audioBufferToBlobUrl, audioBufferToFile, fileToArrayBuffer } from '@/utils/audio';
import { downloadFile, generateUUID } from '@/utils/common';

const message = useMessage();
const { formRef } = useNaiveForm();
// const wrapperRef = ref<HTMLElement>();
const loadingBar = useLoadingBar();
const uploadLoading = ref(false);
const loading = ref(false);
const speaker1Url = ref<string>('');
const speaker2Url = ref<string>('');
const createDefaultModel = (): Api.Audio.Separate => {
  return {
    file_path: '',
    model: 'MossFormer2_SS_16K'
  };
};
const audioBlobUrl = ref<string>('');
const fileName = ref<string>('');
const emptyFileName = computed(() => fileName.value === '');
const model: Api.Audio.Separate = reactive(createDefaultModel());

const modelOption: CommonType.Option[] = [{ label: 'MossFormer2_SS_16K', value: 'MossFormer2_SS_16K' }];

const customRequest = async ({ file, data, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  // console.log(file);
  loadingBar.start();
  uploadLoading.value = true;

  // file.type
  // mp3=>audio/mpeg
  // mp4=>video/mp4

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }
  // formData.append(file.name, file.file as File);
  // 如果是视频文件
  // if xxx='mp4' || mov
  // 如果是视频，先转音频
  if (file.type === 'video/mp4') {
    // mp3=>audio/mpeg
    // mp4=>video/mp4
    const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // 创建一个AudioContext
    const audioCtx = new AudioContext();
    // arrayBuffer转audioBuffer
    const audioBuffer = await audioCtx.decodeAudioData(buffer);
    const uuid = generateUUID();
    const newFileName = `${uuid}.wav`;
    const audioFile = audioBufferToFile(audioBuffer, newFileName);
    formData.append('file', audioFile as File);
    const blobUrl = await audioBufferToBlobUrl(audioBuffer);
    audioBlobUrl.value = blobUrl;
  } else {
    if (file.file) {
      const blobUrl = URL.createObjectURL(file!.file);
      audioBlobUrl.value = blobUrl;
    }
    formData.append('file', file.file as File);
  }

  postUploadFile('separate', formData, progressEvent => {
    if (progressEvent.progress) {
      onProgress({ percent: progressEvent!.progress * 100 });
    }
  })
    .then(res => {
      console.log(res);
      if (res.data) model.file_path = res.data.file_path;
      fileName.value = file.name;
      // message.success(JSON.stringify(json));
      onFinish();
    })
    .catch(error => {
      console.log(error);

      // message.success(error.message);
      onError();
    })
    .finally(() => {
      loadingBar.finish();
      uploadLoading.value = false;
    });
};

const onRemove = () => {
  // show-file-list
  audioBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};
const removeFile = () => {
  // show-file-list
  audioBlobUrl.value = '';
  fileName.value = '';
  console.log('remove');
};
const toSplit = () => {
  console.log('toSplit');

  if (!model.file_path) {
    message.error('未上传音频');
    return;
  }
  loading.value = true;
  console.log(model);
  postSeparation(model)
    .then(res => {
      console.log(res.data);
      if (res.data) {
        speaker1Url.value = `${res.data.type}base64,${res.data.speaker_1}`;
        speaker2Url.value = `${res.data.type}base64,${res.data.speaker_2}`;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 添加音频控制相关的状态
const speaker1Audio = ref<HTMLAudioElement>();
const speaker2Audio = ref<HTMLAudioElement>();

// 下载音频文件
const downloadAudio = (url: string, type: 'speaker1' | 'speaker2') => {
  if (!url) {
    message.error('暂无音频文件');
    return;
  }
  const fn = fileName.value.replace(/\.[^/.]+$/, '');
  downloadFile(url, `${fn}_${type}.wav`);
};

// 设置音频播放速率
const setPlaybackRate = (rate: number, type: 'speaker1' | 'speaker2') => {
  const audio = type === 'speaker1' ? speaker1Audio.value : speaker2Audio.value;
  if (audio) {
    audio.playbackRate = rate;
  }
};
</script>

<template>
  <div class="h-full">
    <NGrid :x-gap="20" :cols="2" class="min-h-[calc(100vh-13em)]">
      <!-- 左侧操作面板 -->
      <NGridItem>
        <NCard class="h-full">
          <NSpin :show="uploadLoading">
            <NUpload
              action=""
              :custom-request="customRequest"
              accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
              :on-remove="onRemove"
              :show-file-list="false"
            >
              <NUploadDragger>
                <NText>{{ $t('page.media.form.text.dndVideoOrAudio') }}</NText>
                <NP depth="3"></NP>
                <div class="grid min-h-54px items-center justify-center">
                  <audio :src="audioBlobUrl" controls class="min-w"></audio>
                </div>
                <div class="n-upload-file-list pt-3">
                  <div class="n-upload-file n-upload-file--success-status n-upload-file--text-type">
                    <div class="n-upload-file-info">
                      <div class="n-upload-file-info__name">
                        <span v-if="emptyFileName">{{ $t('page.media.form.text.noFileSelected') }}</span>
                        <span v-else>{{ fileName }}</span>
                      </div>
                      <div
                        v-if="!emptyFileName"
                        class="n-upload-file-info__action n-upload-file-info__action--text-type"
                      >
                        <NButton text @click.stop="removeFile">{{ $t('page.media.form.btn.del') }}</NButton>
                      </div>
                    </div>
                  </div>
                </div>
              </NUploadDragger>
            </NUpload>
          </NSpin>

          <NForm
            ref="formRef"
            :label-width="50"
            size="medium"
            :show-feedback="false"
            label-placement="left"
            class="mt-4 flex flex-col items-center gap-4"
          >
            <NFormItem path="model.model" class="w-70">
              <div class="w-15"><NText>模型：</NText></div>
              <NSelect v-model:value="model.model" :options="modelOption" />
            </NFormItem>
            <NFormItem class="w-70">
              <NButton type="info" :loading="loading" class="h-55px w-full" @click="toSplit">生成</NButton>
            </NFormItem>
          </NForm>
        </NCard>
      </NGridItem>

      <!-- 右侧结果展示 -->
      <NGridItem>
        <NCard class="h-full">
          <div class="flex flex-col gap-4">
            <NCard class="h-47" embedded>
              <NText class="text-[17px]">说话人1</NText>
              <div class="h-full flex flex-col justify-center gap-2">
                <audio ref="speaker1Audio" :src="speaker1Url" controls class="w-full"></audio>
                <div class="flex justify-center">
                  <NButtonGroup>
                    <NButton size="small" @click="downloadAudio(speaker1Url, 'speaker1')">
                      <template #icon>
                        <SvgIcon icon="mdi:download" />
                      </template>
                      下载
                    </NButton>
                    <NButton size="small" @click="setPlaybackRate(0.5, 'speaker1')">0.5x</NButton>
                    <NButton size="small" @click="setPlaybackRate(1.0, 'speaker1')">1.0x</NButton>
                    <NButton size="small" @click="setPlaybackRate(1.5, 'speaker1')">1.5x</NButton>
                  </NButtonGroup>
                </div>
              </div>
            </NCard>

            <NCard class="h-47" embedded>
              <NText class="text-[17px]">说话人2</NText>
              <div class="h-full flex flex-col justify-center gap-2">
                <audio ref="speaker2Audio" :src="speaker2Url" controls class="w-full"></audio>
                <div class="flex justify-center">
                  <NButtonGroup>
                    <NButton size="small" @click="downloadAudio(speaker2Url, 'speaker2')">
                      <template #icon>
                        <SvgIcon icon="mdi:download" />
                      </template>
                      下载
                    </NButton>
                    <NButton size="small" @click="setPlaybackRate(0.5, 'speaker2')">0.5x</NButton>
                    <NButton size="small" @click="setPlaybackRate(1.0, 'speaker2')">1.0x</NButton>
                    <NButton size="small" @click="setPlaybackRate(1.5, 'speaker2')">1.5x</NButton>
                  </NButtonGroup>
                </div>
              </div>
            </NCard>
          </div>
        </NCard>
      </NGridItem>
    </NGrid>
    <!-- <NLoadingBarProvider :to="wrapperRef" container-style="position: absolute;"></NLoadingBarProvider> -->
  </div>
</template>

<style scoped lang="scss">
.prosess {
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
