/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    //* * 只返回成功和失败 */
    interface ResultResponse {
      code: number | string;
      msg: string;
    }
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
      [key: string]: any;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
      gamecode?: string;
      statu?: number;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
      gamecode?: string;
      gamename?: string;
      lang?: string;
      tool_type?: string;
      name?: string;
      channel_name?: string;
      permission?: string;
    };

    /** role */
    type Role = Common.CommonRecord<{
      /** role name */
      roleName: string;
      /** role code */
      roleCode: string;
      /** role description */
      roleDesc: string;
    }>;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'status'> & CommonSearchParams
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    /** all role */
    type AllRole = Pick<Role, 'id' | 'roleName' | 'roleCode'>;

    /** 团队配置 */
    type Teams = Common.CommonRecord<{
      team_name: string;
      team_code: string;
      email: string;
      update_time: string;
    }>;

    type TeamSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Teams, 'team_name' | 'team_code' | 'email'> & CommonSearchParams
    >;
    type TeamList = Common.PaginatingQueryRecord<Teams>;

    /** 用户积分日志查询 */
    type CreditLogSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
      user_id?: string;
      capacity?: string;
      model?: string;
      matter?: string;
    };

    /** 用户积分查询 */
    type UserCreditSearch = Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
      user_id?: string;
      credit?: number;
    };

    /** 积分模型查询 */
    type ModelPriceSearch = Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
      capacity?: string;
      model?: string;
    };

    /** 系统配置查询 */
    type SettingSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
      key_type?: string;
      key_code?: string;
      value_type?: string;
      pid?: number;
    };

    /**
     * user gender
     *
     * - "0": "unknown"
     * - "1": "male"
     * - "2": "female"
     */
    type UserGender = '0' | '1' | '2';

    /** user */
    type User = Common.CommonRecord<{
      /** user name */
      username: string;
      /** user gender */
      gender: UserGender | null;
      /** user nick name */
      nickname: string;
      password: string;
      /** user phone */
      // userPhone: string;
      /** user email */
      email: string;
      // user status
      role_id: number | null;
      /** login ip */
      loginip: string | null;
      /** login time */
      logintime: number | null;

      role: number[] | string[] | null;
      /** user role code collection */
      userRoles: string[];
      company?: string;
      credit: number;
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'username' | 'gender' | 'nickname' | 'email' | 'status' | 'company'> &
        CommonSearchParams
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": directory
     * - "2": menu
     * - "3": button
     */
    type MenuType = '1' | '2';

    type MenuButton = {
      /**
       * button code
       *
       * it can be used to control the button permission
       */
      code: string;
      /** button description */
      desc: string;
    };

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      | 'i18nKey'
      | 'keepAlive'
      | 'constant'
      | 'order'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
      | 'query'
    >;

    type Menu = Common.CommonRecord<{
      /** parent menu id */
      parentId: number;
      /** menu type */
      menuType: MenuType;
      /** menu name */
      menuName: string;
      /** route name */
      routeName: string;
      /** route path */
      routePath: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      iconType: IconType;
      /** buttons */
      buttons?: MenuButton[] | null;
      /** children menu */
      children?: Menu[] | null;
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingQueryRecord<Menu>;

    type MenuTree = {
      id: number;
      label: string;
      pId: number;
      children?: MenuTree[];
    };

    type Model = {
      id: number;
      name: string;
      label: string;
      icon: string;
      description: string;
      publishers: string;
      capacity: number;
      status: boolean;
    };

    type ModelsPagination = Common.PaginatingQueryRecord<Model>;
  }

  namespace text {
    type ImgocrParams = {
      file_path?: string;
      prompt_image_b64?: string;
      model: string;
      format: string;
      gamecode: string;
      translate: string;
      url?: string;
    };
  }

  namespace Media {
    type Subtitle = {
      file_path?: string;
      prompt_audio_b64?: string;
      model: string;
      format: string;
      gamecode: string;
      translate: string;
    };
    type AiDraw = {
      file_path?: string;
      stop_flag?: boolean;
      resolution?: string;
      quantity?: string;
      seed?: number;
      sharpen?: number;
      guidance_scale?: number;
      FreeU?: number;
      skip_preprocessor?: number;
      positive?: string;
      negative_prompt?: string;
      style_tag?: string;

      lora?: (number | string)[]; // [lora,weight]
    };
    type MimicBrush = {
      source_b64: string;
      reference_b64: string;
      mask_b64: string;
      step: number;
      seed: number;
      guidance_scale: number;
      if_keep_shape: boolean;
    };
    type MimicBrushTaskRes = {
      prompt_id: string;
      number: number;
      node_errors: object;
    };
  }
  namespace Audio {
    type Separate = {
      file_path: string;
      model: string;
    };
    type Reduction = {
      audio_data: string;
      original_filename: string;
    };
    type TextToSpeech = {
      text?: string;
      model?: string;
      role?: string;
      voice?: string;
      custom_voice?: string;
      prompt_text?: string;
      prompt_audio?: string;
      language?: string;
      file_path?: string;
      tone?: string;
      text_seed?: number;
      audio_seed?: number;
      timbre?: string;
      prompt_oral?: number;
      prompt_laugh?: number;
      prompt_break?: number;
      prompt_oral_status?: boolean;
      prompt_laugh_status?: boolean;
      prompt_break_status?: boolean;

      temperature: number;
      top_k: number;
      top_p: number;
    };
    type ChatTTS = {
      text?: string;
      voice?: string;
      custom_voice?: string;
      tone?: string;
      text_seed?: number;
      audio_seed?: number;
      prompt_oral?: number;
      prompt_laugh?: number;
      prompt_break?: number;
      prompt_oral_status?: boolean;
      prompt_laugh_status?: boolean;
      prompt_break_status?: boolean;
      temperature: number;
      top_k: number;
      top_p: number;
    };
    type Cosy = {
      text: string;
      speaker: string;
      model?: string;
      new?: number;
      speed?: number;
      streaming?: number;
      prompt_text: string;
      prompt_audio?: string;
      prompt_audio_b64?: string;
      prompt_audio_start?: number;
      prompt_audio_end?: number;
      save_name: string;
      language?: string;
      seed?: number | undefined | null;
      instruct?: string;
    };
    type SoVITSZeroShot = {
      prompt_text: string;
      prompt_language: string;
      text: string;
      text_language: string;
      temperature: number;
      speed: number;
      top_k: number;
      top_p: number;
      seed?: number | undefined | null;
      prompt_audio_b64?: string;
      reference_id?: string | null;
      save_model?: boolean;
    };
    type VolcanoVoiceOptinItem = {
      name: string;
      gender: string;
      age: string;
      labels: string[];
      category: string[][];
      voice_config: {
        language: string;
        emotion: string;
        params: {
          voice_type: string;
        };
        text: string;
        ssml: string;
      }[];
    };
    type VolcanoVoiceOption = {
      voice_type: string;
      sample?: string;
      name: string;
      gender: string;
      age: string;
      labels: string[];
      language: string[];
      style: string[];
      category: string[][];
    };
    type VolcanoTTS = {
      text: string;
      voice_type: string;
      encoding: string;
      speed_ratio: number;
      volume_ratio: number;
      pitch_ratio: number;
      silence_duration: number;
      language: string;
      emotion: string;
    };
    type VolcanoTTSResult = {
      reqid: string;
      operation: string;
      message: string;
      data: string;
      addition: {
        duration: string;
        first_pkg: string;
      };
    };
    type InspireMusic = {
      text?: string;
      model_name: string;
      chorus?: string;
      output_sample_rate?: number;
      max_generate_audio_seconds?: number;
      audio_base64?: string;
    };
  }
  namespace Statis {
    type TaskStatisSearchParams = CommonType.RecordNullable<
      Pick<Common.PaginatingCommonParams, 'current' | 'size'> & {
        year?: string;
        month?: string;
        date?: string;
        start_date?: string;
        end_date?: string;
        filter_type?: string;
        company?: string;
      }
    >;
    // 创建TaskStatisSearchDetail类型，继承TaskStatisSearchParams类型，并添加api_path属性
    type TaskStatisSearchDetail = CommonType.RecordNullable<TaskStatisSearchParams & { api_name: string }>;
    // type TaskStatis = Common.PaginatingQueryRecord<{
    //   name: string;
    //   path: string;
    //   count: number;
    // }>;
    type TaskStatis = Common.PaginatingQueryRecord<{
      api_name: string;
      api_path: string;
      count_api: number;
      count_user: number;
      count_company: number;
      avg_duration: number;
      nickname: string;
      count: number;
    }>;
    type TaskDetailStatis = Common.PaginatingQueryRecord<{
      nickname: string;
      count: number;
    }>;
    type PageStatis = Common.PaginatingQueryRecord<{
      title: string;
      path: string;
      name: string;
      i18nKey: string;
      count: number;
    }>;

    /** API响应时间统计数据 */
    interface ApiTimeResponse {
      code: string;
      data?: { [key: string]: number | undefined };
    }
  }
}
