import logging

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, Generic, TypeVar, Any, Union
from models.system_settings import SystemSettings
from models.system_settings_group import SystemSettingsGroup
from utils.database import get_db
from pydantic import BaseModel, field_serializer
from datetime import datetime
from models.users import User, get_request_user, get_roles_checker
from utils.exceptions import ClientVisibleException

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
logger = logging.getLogger(__name__)

# 定义通用响应模型
T = TypeVar('T')
class ResponseModel(BaseModel, Generic[T]):
    code: str
    data: Optional[T] = None
    msg: str = ""
    
    @field_serializer('data')
    def serialize_data(self, data: Any) -> Any:
        """自定义数据序列化"""
        if hasattr(data, '__table__'):  # 检查是否为SQLAlchemy模型
            return {c.name: getattr(data, c.name) for c in data.__table__.columns}
        if isinstance(data, list) and data and hasattr(data[0], '__table__'):
            return [
                {c.name: getattr(item, c.name) for c in item.__table__.columns}
                for item in data
            ]
        return data

class SettingGroupBase(BaseModel):
    group_code: str
    group_name: str
    description: Optional[str] = None
    seq: int = 0
    status: bool = True

class SettingGroupCreate(SettingGroupBase):
    pass

class SettingGroup(SettingGroupBase):
    id: int
    crtime: datetime
    edittime: datetime
    edituser: Optional[str] = None

    class Config:
        from_attributes = True

class SettingBase(BaseModel):
    group_id: int
    config_key: str
    config_value: Optional[Union[str, bool, int, float]] = None
    data_type: str = "string"
    form_type: str = "input"
    options: Optional[str] = None
    placeholder: Optional[str] = None
    description: Optional[str] = None
    is_required: bool = False
    is_sensitive: bool = False
    seq: int = 0
    status: bool = True

class SettingCreate(SettingBase):
    pass

class Setting(SettingBase):
    id: int
    crtime: datetime
    edittime: datetime
    edituser: Optional[str] = None

    class Config:
        from_attributes = True

# 配置分组相关接口
@router.get("/groups")
async def get_setting_groups(
    user: User = Depends(get_request_user),
    status: bool = Query(True),
    db: AsyncSession = Depends(get_db)
):
    """获取所有配置分组"""
    try:
        query = select(SystemSettingsGroup).where(SystemSettingsGroup.status == status).order_by(SystemSettingsGroup.seq)
        result = await db.execute(query)
        groups = result.scalars().all()
        
        # 将 ORM 对象转换为 Pydantic 模型
        pydantic_groups = [SettingGroup.model_validate(group) for group in groups]
        
        return ResponseModel(code="0000", data=pydantic_groups, msg="")
    except Exception as e:
        logger.error(f"Failed to get setting groups: {e}")
        raise ClientVisibleException() from e

@router.post("/groups")
async def create_setting_group(
    group: SettingGroupCreate, 
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """创建配置分组"""
    try:
        db_group = SystemSettingsGroup(**group.model_dump())
        db.add(db_group)
        await db.commit()
        await db.refresh(db_group)
        
        # 将 ORM 对象转换为 Pydantic 模型
        pydantic_group = SettingGroup.model_validate(db_group)
        
        return ResponseModel(code="0000", data=pydantic_group, msg="创建成功")
    except Exception as e:
        logger.error(f"Failed to create setting group: {e}")
        raise ClientVisibleException() from e

# 配置项相关接口
@router.get("/group/{group_id}")
async def get_group_settings(
    group_id: int, 
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定分组的所有配置项"""
    try:
        query = select(SystemSettings).where(
            SystemSettings.group_id == group_id,
            SystemSettings.status == True
        ).order_by(SystemSettings.seq)
        result = await db.execute(query)
        settings = result.scalars().all()
        
        # 将 ORM 对象转换为 Pydantic 模型
        pydantic_settings = [Setting.model_validate(setting) for setting in settings]
        
        return ResponseModel(code="0000", data=pydantic_settings, msg="")
    except Exception as e:
        logger.error(f"Failed to get group settings: {e}")
        raise ClientVisibleException() from e

@router.post("/setting")
async def create_setting(
    setting: SettingCreate, 
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """创建配置项"""
    try:
        # 检查分组是否存在
        group_query = select(SystemSettingsGroup).where(
            SystemSettingsGroup.id == setting.group_id,
            SystemSettingsGroup.status == True
        )
        group_result = await db.execute(group_query)
        group = group_result.scalar_one_or_none()
        
        if not group:
            raise ClientVisibleException("配置分组不存在")
            
        # 检查配置键是否已存在
        key_query = select(SystemSettings).where(
            SystemSettings.group_id == setting.group_id,
            SystemSettings.config_key == setting.config_key,
            SystemSettings.status == True
        )
        key_result = await db.execute(key_query)
        if key_result.scalar_one_or_none():
            raise ClientVisibleException("配置键已存在")
        
        # 确保配置值为字符串类型
        setting_dict = setting.model_dump()
        if setting.config_value is not None:
            setting_dict["config_value"] = str(setting.config_value)
            
        # 创建配置项
        db_setting = SystemSettings(**setting_dict)
        db.add(db_setting)
        await db.commit()
        await db.refresh(db_setting)
        
        # 将 ORM 对象转换为 Pydantic 模型
        pydantic_setting = Setting.model_validate(db_setting)
        
        return ResponseModel(code="0000", data=pydantic_setting, msg="创建成功")
    except Exception as e:
        logger.error(f"Failed to create setting: {e}")
        raise ClientVisibleException() from e

@router.put("/setting/{setting_id}")
async def update_setting(
    setting_id: int, 
    setting: SettingCreate, 
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)
):
    """更新配置项"""
    try:
        query = select(SystemSettings).where(SystemSettings.id == setting_id)
        result = await db.execute(query)
        db_setting = result.scalar_one_or_none()
        
        if not db_setting:
            raise ClientVisibleException("配置项不存在")
        
        # 检查配置键是否与其他配置项重复
        if db_setting.config_key != setting.config_key:
            key_query = select(SystemSettings).where(
                SystemSettings.group_id == setting.group_id,
                SystemSettings.config_key == setting.config_key,
                SystemSettings.id != setting_id,
                SystemSettings.status == True
            )
            key_result = await db.execute(key_query)
            if key_result.scalar_one_or_none():
                raise ClientVisibleException("配置键已存在")
        
        # 更新配置项
        setting_dict = setting.model_dump()
        
        # 确保配置值为字符串类型
        if setting.config_value is not None:
            setting_dict["config_value"] = str(setting.config_value)
        
        for key, value in setting_dict.items():
            setattr(db_setting, key, value)
        
        db_setting.edittime = datetime.now()
        await db.commit()
        await db.refresh(db_setting)
        
        # 将 ORM 对象转换为 Pydantic 模型
        pydantic_setting = Setting.model_validate(db_setting)
        
        return ResponseModel(code="0000", data=pydantic_setting, msg="更新成功")
    except Exception as e:
        logger.error(f"Failed to update setting: {e}")
        raise ClientVisibleException() from e
