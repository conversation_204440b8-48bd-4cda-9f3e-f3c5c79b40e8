<script setup lang="ts">
import { computed, reactive } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ModelAddsettingSearch'
});

interface Emits {
  (e: 'search', params: Api.SystemManage.SettingSearchParams): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  key_type: '',
  key_code: '',
  value_type: '',
  pid: 0
});

const rules = computed(() => {
  return {};
});

const valueTypeOptions = [
  { label: 'Text', value: 'text' },
  { label: 'Json', value: 'json' }
];

async function search() {
  await validate();
  // console.log('gamecode:', model);
  const searchParams = {
    key_type: model.key_type,
    key_code: model.key_code,
    value_type: model.value_type,
    pid: model.pid,
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="字典分类" path="key_type" class="pr-24px">
          <NInput v-model:value="model.key_type" placeholder="请输入字典分类" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="键名" path="gamename" class="pr-24px">
          <NInput v-model:value="model.key_code" placeholder="键名" />
        </NFormItemGi>
        <NFormItem label="Value类型" path="value_type">
          <NSelect v-model:value="model.value_type" :options="valueTypeOptions" placeholder="请选择Value类型" />
        </NFormItem>
        <NFormItemGi span="24 s:12 m:6" label="Pid" path="model" class="pr-24px">
          <NInputNumber v-model:value="model.pid" type="number" placeholder="请输入父ID" clearable />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
