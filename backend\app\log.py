import logging
import re
from datetime import datetime
import os
import json
from fastapi import Query
from fastapi import APIRouter, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from utils.database import get_db
from pydantic import BaseModel
from sqlalchemy.future import select
from sqlalchemy import func, desc,text
from typing import Tuple
from collections import Counter
from config import endpoint_config
from models.users import User, get_request_user
from models.user_role import UserRole
from models.roles import Role
import pydash
from models.api_call_records import APICallRecord
import calendar

from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


class TaskAnalyze(BaseModel):
  year: str = ''
  month: str = ''
  date: str = ''
  start_date: str = ''
  end_date: str = ''
  filter_type: str = ''


def parse_line(lineContent: str, trace_type: str) -> Tuple[bool, dict]:
  pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} \[[A-Z]+\]: TraceType: ' + trace_type
  match = re.match(pattern, lineContent)
  if match is None:
    return False, {}

  lineSplit = lineContent.split(f" [INFO]: TraceType: {trace_type} | ", 1)
  timestamp = datetime.strptime(lineSplit[0], "%Y-%m-%d %H:%M:%S,%f").strftime('%Y-%m-%d')
  log_dict = {"timestamp": timestamp}
  for pair in lineSplit[1].split(" | "):
    key, value = pair.split(":", 1)
    log_dict[key.strip()] = value.strip()
  return True, log_dict


def read_log_file(file_path):
  """
    逐行读取日志文件，使用生成器 yield 返回每一行。

    参数:
    - file_path: 日志文件的路径

    返回:
    - 每一行日志内容作为生成器返回
    """
  try:
    with open(file_path, 'r', encoding='utf-8') as file:
      for line in file:
        yield line.strip()  # 去除行尾的换行符
  except FileNotFoundError:
    logger.error(f"文件 {file_path} 未找到")
  except Exception as e:
    logger.error(f"读取文件 {file_path} 时发生错误: {e}")


def get_file_data(file_path: str, trace_type: str):
  for l in read_log_file(file_path):
    isTraceLog, log_dict = parse_line(l, trace_type)
    if isTraceLog:
      yield log_dict
    else:
      continue


def find_log_files(directory, start_date=None, end_date=None, year=None, month=None):
  """
    在指定目录下查找符合条件的日志文件。

    参数:
    - directory: 日志文件所在目录的路径
    - start_date: 开始日期，格式为 "YYYYMMDD"
    - end_date: 结束日期，格式为 "YYYYMMDD"
    - year: 年份，格式为 "YYYY"
    - month: 月份，格式为 "YYYYMM"

    返回:
    - 符合条件的日志文件名列表
    """

  def parse_date_from_filename(filename):
    date_str = filename.split(".")[-1]
    try:
      return datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
      return None

  log_files = []
  for filename in os.listdir(directory):
    if filename.startswith("app.log"):
      file_date = parse_date_from_filename(filename)
      if file_date:
        if start_date and end_date:
          start_dt = datetime.strptime(start_date, "%Y%m%d")
          end_dt = datetime.strptime(end_date, "%Y%m%d")
          if start_dt <= file_date <= end_dt:
            log_files.append(filename)
        elif year:
          if file_date.year == int(year):
            log_files.append(filename)
        elif month:
          try:
            month_dt = datetime.strptime(month, "%Y%m")
            if file_date.year == month_dt.year and file_date.month == month_dt.month:
              log_files.append(filename)
          except ValueError:
            pass

  return log_files


#根据时间类型，获取开始和结束时间
def get_start_end_date(filter_type: str, date: str,month:str) -> Tuple[datetime, datetime]:
    # 日期过滤逻辑
    if filter_type == 'date':
      input_date = datetime.strptime(date, "%Y%m%d")
      return (
        input_date.strftime("%Y-%m-%d 00:00:00"),
        input_date.strftime("%Y-%m-%d 23:59:59")
      )
    elif filter_type == 'month':
      input_date = datetime.strptime(month, "%Y%m")
      first_day = datetime(input_date.year, input_date.month, 1)
      last_day = datetime(input_date.year, input_date.month,
                          calendar.monthrange(input_date.year, input_date.month)[1], 23, 59, 59)
      return (first_day, last_day)
    elif filter_type == 'year':
      input_date = datetime.strptime(month, "%Y%m")
      first_day = datetime(input_date.year, 1, 1)
      last_day = datetime(input_date.year, 12, 31, 23, 59, 59)
      return (first_day, last_day)
    else:
      raise ClientVisibleException("Invalid filter_type")

@router.get("/task_analyze_detail", tags=["log"])
async def task_analyze_detail(    
    api_name: str = Query(),
    filter_type: str = Query(''),
    month: str = Query(''),
    date: str = Query(''),
    company: str = Query(''),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db)):
    '''
    查询接口的具体人员使用情况
    '''
    process_user=user.id
    result = await db.execute(
        select(Role.roleCode)
        .select_from(UserRole)
        .join(Role, UserRole.role_id == Role.id)
        .where(UserRole.user_id == process_user)
      )
    roles = result.scalars().all()

    if (not 'super_admin' in roles) and (not 'general-admin' in roles):
      raise ClientVisibleException("无权操作")

    # 获取分页数据
    datas = get_start_end_date(filter_type, date,month)
    # sql = '''
    # select b.nickname,count(*) as count
    # from api_call_records a
    # left join users b on a.username=b.username
    # where a.created_at between :startdate and :enddate
    # and api_path = :api_path
    # '''
    sql = '''
    select b.nickname,count(*) as count
    from api_call_records a
    left join users b on a.username=b.username
    where a.created_at between :startdate and :enddate
    and api_name LIKE :api_name_pattern
    '''
    params = {
      "startdate":datas[0],
      "enddate":datas[1],
      "api_name_pattern": f'{api_name}%'
    }
    # if company:
    if company and 'super_admin' in roles:
      sql += f' and a.company=:company'
      params["company"] = company
    elif 'super_admin' not in roles:
      companyrecord = await db.execute(select(User.company).where(User.id == process_user))
      companyown = companyrecord.scalars().first()
      sql += f' and a.company=:companyown'
      params["companyown"] = companyown

    sql += ' group by a.username order by count desc'
    # 查询统计数据
    result = await db.execute(text(sql),params)
    records = result.mappings().all()
    return {
      "code": "0000",
      "msg": "success",
      "data": {
        "records": records
      }
    }

@router.get("/task_analyze", tags=["log"])
async def task_analyze_v1(filter_type: str = Query(''), month: str = Query(''), date: str = Query(''),
                          company: str = Query(''), user: User = Depends(get_request_user), db: AsyncSession = Depends(get_db)):
  process_user=user.id
  where = []
  async with db as session:
    session.begin()
    result = await session.execute(
        select(Role.roleCode)
        .select_from(UserRole)
        .join(Role, UserRole.role_id == Role.id)
        .where(UserRole.user_id == process_user)
      )
    roles = result.scalars().all()

    if (not 'super_admin' in roles) and (not 'general-admin' in roles):
      raise ClientVisibleException("无权操作")

    # 获取分页数据
    if company and 'super_admin' in roles:
      where.append(APICallRecord.company == company)
    elif 'super_admin' not in roles:
      companyrecord = await session.execute(select(User.company).where(User.id == process_user))
      companyown = companyrecord.scalars().first()
      where.append(APICallRecord.company == companyown)
    # 日期过滤逻辑
    if filter_type == 'date':
      input_date = datetime.strptime(date, "%Y%m%d")
      where.append(APICallRecord.created_at.between(
        input_date.strftime("%Y-%m-%d 00:00:00"),
        input_date.strftime("%Y-%m-%d 23:59:59")
      ))
    elif filter_type == 'month':
      input_date = datetime.strptime(month, "%Y%m")
      first_day = datetime(input_date.year, input_date.month, 1)
      last_day = datetime(input_date.year, input_date.month,
                          calendar.monthrange(input_date.year, input_date.month)[1], 23, 59, 59)
      where.append(APICallRecord.created_at.between(first_day, last_day))
    elif filter_type == 'year':
      input_date = datetime.strptime(month, "%Y%m")
      first_day = datetime(input_date.year, 1, 1)
      last_day = datetime(input_date.year, 12, 31, 23, 59, 59)
      where.append(APICallRecord.created_at.between(first_day, last_day))
    else:
      raise ClientVisibleException("参数错误")

    # 查询统计数据
    countResult = await session.execute(
      select(
        APICallRecord.api_name.label('api_name'),
        # APICallRecord.api_path.label('api_path'),
        func.count(func.distinct(APICallRecord.id)).label('count_api'),
        # func.array_agg(func.distinct(APICallRecord.user_id)).label('user_ids'), # Replaced
        # func.array_agg(func.distinct(APICallRecord.company)).label('companies'), # Replaced
        func.group_concat(func.distinct(APICallRecord.user_id)).label('user_ids_str'), # Added group_concat
        func.group_concat(func.distinct(APICallRecord.company)).label('companies_str'), # Added group_concat
        func.round(func.avg(APICallRecord.duration), 3).label('avg_duration'),
      )
      .where(*where)
      # .group_by(APICallRecord.api_name, APICallRecord.api_path)
      # .group_by(APICallRecord.api_name)
      .group_by(APICallRecord.api_name)
      .order_by(desc('count_api'))
    )

    # 转换结果为字典
    keys = countResult.keys()
    map_array = [dict(zip(keys, item)) for item in countResult.all()]

    # --- Begin Secondary Aggregation in Python ---
    aggregated_data = {}
    # Structure: {category_name: {'count_api': 0, 'total_duration': 0.0, 'user_ids': set(), 'companies': set()}}

    for record in map_array:
        detailed_name = record['api_name']
        # Determine category by splitting at the first '-'
        category = detailed_name.split('-', 1)[0] if '-' in detailed_name else detailed_name

        if category not in aggregated_data:
            # Initialize category data including sets for unique users and companies
            aggregated_data[category] = {'count_api': 0, 'total_duration': 0.0, 'user_ids': set(), 'companies': set()}

        aggregated_data[category]['count_api'] += record['count_api']
        # Accumulate total duration for weighted average calculation
        # Explicitly convert avg_duration (which might be Decimal) to float before calculation
        aggregated_data[category]['total_duration'] += record['count_api'] * float(record.get('avg_duration', 0.0)) # Use get with default

        # Update sets with unique user IDs and companies from the current record
        # aggregated_data[category]['user_ids'].update(record.get('user_ids') or []) # Old array_agg logic
        # aggregated_data[category]['companies'].update(record.get('companies') or []) # Old array_agg logic

        # Parse group_concat strings and update sets
        user_ids_str = record.get('user_ids_str')
        if user_ids_str:
            aggregated_data[category]['user_ids'].update(user_ids_str.split(','))

        companies_str = record.get('companies_str')
        if companies_str:
            aggregated_data[category]['companies'].update(companies_str.split(','))


    final_records = []
    for category, data in aggregated_data.items():
        avg_duration = round(data['total_duration'] / data['count_api'], 3) if data['count_api'] > 0 else 0.0
        final_records.append({
            'api_name': category,
            'count_api': data['count_api'],
            'count_user': len(data['user_ids']), # Calculate count of unique users
            'count_company': len(data['companies']), # Calculate count of unique companies
            'avg_duration': avg_duration
            # Note: count_user and count_company are omitted as they cannot be accurately aggregated here # <-- This comment is now obsolete
        })

    # Sort by count_api descending
    final_records.sort(key=lambda x: x['count_api'], reverse=True)
    # --- End Secondary Aggregation ---

    return {
      "code": "0000",
      "msg": "success",
      "data": {
        # Return the aggregated records instead of the original map_array
        "records": final_records
      }
    }


# 下面这是读文件版本
async def task_analyze_v1(filter_type: str = Query(''), month: str = Query(''), date: str = Query(''),
                          company: str = Query('')):
  log_files = []

  if filter_type == 'month':
    log_files = find_log_files('logs', month=month)
    if datetime.now().strftime("%Y%m") == month:
      log_files.append('app.log')
  elif filter_type == 'date':
    file = 'app.log'
    if datetime.now().strftime("%Y%m%d") != date:
      file += f'.{date}'

    if os.path.exists(os.path.join('logs', file)):  # 检查文件是否存在
      log_files.append(file)
    else:
      raise ClientVisibleException("无记录")
  else:
    raise ClientVisibleException("参数错误")
  # return find_log_files('logs',month='202407')
  # 倒序逐行读取文件，并判断条件

  # 读取名字配置
  # route_map_file='data/route_map.json'
  # with open(route_map_file, 'r', encoding='utf-8') as file:
  #   route_map_str = file.read()

  # route_map = json.loads(route_map_str

  api_map = endpoint_config["api_map"]

  # month = "202408"
  # log_files=find_log_files('logs',month=month)
  # log_files.append('app.log')
  # log_files=['app.log']
  # 每个file统计数据
  file_value = []

  # 使用 Counter 计算每个键的值的总和
  total_counts = Counter()
  for filename in log_files:
    log_file_path = os.path.join('logs', filename)

    if company != '':
      values = [item["Path"] for item in get_file_data(log_file_path, 'Api') if
                "Company" in item and item["Company"] == company]
    else:
      values = [item["Path"] for item in get_file_data(log_file_path, 'Api')]

    counter = Counter(values)
    total_counts.update(counter)
    file_value.append(counter)

  # 配置名字，过滤一些不必要的
  records = []
  for path, name in api_map.items():
    if path in total_counts:
      records.append({
        "name": name,
        "path": path,
        "count": total_counts[path]
      })
  sorted_data = sorted(records, key=lambda x: x['count'], reverse=True)
  return {
    "code": "0000",
    "msg": "success",
    "data": {
      "log_files": log_files,
      "file_value": file_value,
      "total_counts": total_counts,
      "records": sorted_data
    }
  }


@router.get("/get_route_map", tags=["log"])
async def get_route_map(request: Request):
  route_map_file = 'data/route_map.json'

  # 读取JSON文件
  with open(route_map_file, 'r+', encoding='utf-8') as file:
    route_map_str = file.read()
    route_map = json.loads(route_map_str)
    for route in request.app.routes:
      if route.path not in route_map:
        route_map[route.path] = route.name
    file.seek(0)
    logger.debug(json.dumps(route_map))
    json.dump(route_map, file, ensure_ascii=False, indent=2)
    return route_map


@router.post("/page_visit", tags=["log"])
async def page_visit(request: Request):
  request_body = await request.json()
  username = pydash.get(request.state, "user.username", "unknown")
  for item in request_body:
    logger.info(
      f'TraceType: Page | '
      f'User: {username} | '
      f'title: {item["title"]} | '
      f'Path: {item["path"]} | '
      f'RouteName: {item["name"]} | '
      f'i18nKey: {item["i18nKey"]} '
    )

  return {
    "code": "0000",
    "msg": "success"
  }

def find_log_files(directory, year=None, month=None):
    log_files = []
    for file in os.listdir(directory):
      if file == "app.log":  # 当前日志文件
        # 仅在没有指定年份和月份的时候才加入当前日志
        if not year and not month:
          log_files.append(file)
      elif file.startswith("app.log."):  # 历史日志文件
        date_part = file.split('.')[-1]  # 提取日期部分
        if len(date_part) == 8:  # 确保日期部分为 YYYYMMDD 格式
          file_year = date_part[:4]
          file_month = date_part[:6]
          if year and file_year == year:
            log_files.append(file)
          elif month and file_month == month:
            log_files.append(file)
    return log_files


@router.get("/page_analyze", tags=["log"])
async def page_analyze(
  filter_type: str = Query(''),
  current: int = Query(1),
  size: int = Query(10),
  month: str = Query(''),  # 前端传入的 month，如 202412
  date: str = Query(''),  # 前端传入的 date，如 20241224
  start_date: str = Query(''),  # 暂未使用
  end_date: str = Query(''),  # 暂未使用
  company: str = Query('')  # 暂未使用
):
  # 统一转为字符串处理，防止前端传入整型时导致 len() 判断报错
  month_str = str(month).strip()
  date_str = str(date).strip()

  log_files = []

  # 根据 filter_type 不同，执行不同的日志筛选逻辑
  if filter_type == 'year':
    # 如果要按"年"维度查询，则需保证 month_str 是 6 位，如 202412
    if not month_str or len(month_str) != 6:
      raise ClientVisibleException("参数错误: 缺少或无效的month参数")
    year = month_str[:4]  # 从 month_str 提取年份，如 2024
    log_files = find_log_files('logs', year=year)
    # 如果查询的年份是当前年份，则包含当前日志文件
    if datetime.now().strftime("%Y") == year:
      log_files.append('app.log')

  elif filter_type == 'month':
    # 如果要按"月"维度查询，则需保证 month_str 是 6 位，如 202412
    if not month_str or len(month_str) != 6:
      raise ClientVisibleException("参数错误: 缺少或无效的month参数")
    log_files = find_log_files('logs', month=month_str)
    # 如果查询的年月是当前年月，则包含当前日志文件
    if datetime.now().strftime("%Y%m") == month_str:
      log_files.append('app.log')

  elif filter_type == 'date':
    # 如果要按"日"维度查询，则需保证 date_str 是 8 位，如 20241224
    if not date_str or len(date_str) != 8:
      raise ClientVisibleException("参数错误: 缺少或无效的date参数")
    file_name = 'app.log'
    # 如果不是当天，则尝试读取历史日志文件 app.log.YYYYMMDD
    if datetime.now().strftime("%Y%m%d") != date_str:
      file_name += f'.{date_str}'

    if os.path.exists(os.path.join('logs', file_name)):
      log_files.append(file_name)
    else:
      raise ClientVisibleException("无记录")

  else:
    raise ClientVisibleException("参数错误")

  file_value = []

  # 使用 Counter 计算每个页面（RouteName）的访问次数
  total_counts = Counter()
  page_map = {}

  for filename in log_files:
    values = []
    log_file_path = os.path.join('logs', filename)
    for item in get_file_data(log_file_path, 'Page'):
      if item["RouteName"] not in page_map:
        page_map[item["RouteName"]] = {
          "title": item["title"],
          "path": item["Path"],
          "name": item["RouteName"],
          "i18nKey": item["i18nKey"],
        }
      values.append(item["RouteName"])
    counter = Counter(values)
    total_counts.update(counter)
    file_value.append(counter)

  # 将累计的访问次数放回 page_map
  for name, count in total_counts.items():
    if name in page_map:
      page_map[name]["count"] = count

  # 实现分页
  records = list(page_map.values())
  total_records = len(records)
  start = (current - 1) * size
  end = start + size
  paginated_records = records[start:end]

  return {
    "code": "0000",
    "msg": "success",
    "data": {
      "records": paginated_records,
      "total": total_records,
      "current": current,
      "size": size,
    }
  }
