<script setup lang="tsx">
import { ref, watch } from 'vue';
import { N<PERSON>utton, NPopconfirm } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import {
  fetchAddManagerSetting,
  fetchDelManagerSetting,
  fetchManagerSetting,
  fetchManagerSettingSearch,
  fetchUpdataManagerSetting
} from '@/service/api';
import AppSetting from './modules/app-setting.vue';
import AppSettingSearch from './modules/app-setting-search.vue';

const appStore = useAppStore();

enum ValueType {
  TYPE_TEXT = 'text',
  TYPE_JSON = 'json'
}

interface ManagerSettingData extends NaiveUI.TableData {
  id: number;
  key_type: string;
  key_code?: string;
  value_type: ValueType;
  key_value: string;
  sync_redis: number;
  pid?: number;
  remark?: string;
  crtime: string;
  edituser?: string;
  seq?: number;
}

// 获取配置信息
const fetchTyped: NaiveUI.TableApiFn<ManagerSettingData, Api.SystemManage.SettingSearchParams> = async params => {
  const response = await fetchManagerSetting(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<ManagerSettingData>>;
};

const searchParams = ref<Api.SystemManage.SettingSearchParams>({
  current: 1,
  size: 10
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      minWidth: 10
    },
    {
      key: 'pid',
      title: '父ID',
      align: 'center',
      minWidth: 10
    },
    {
      key: 'key_type',
      title: '类型',
      align: 'center',
      minWidth: 40
    },
    {
      key: 'key_code',
      title: '键名',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'value_type',
      title: '值类型',
      align: 'center',
      minWidth: 40,
      render(row) {
        const statusMap: { [key: string]: string } = {
          text: 'text',
          json: 'json'
        };
        return statusMap[row.value_type];
      }
    },
    {
      key: 'key_value',
      title: '键值',
      align: 'left',
      minWidth: 300
    },
    {
      key: 'sync_redis',
      title: '是否同步redis',
      align: 'center',
      minWidth: 300,
      render(row) {
        const statusMap: { [key: number]: string } = {
          0: '否',
          1: '是'
        };
        return statusMap[row.sync_redis];
      }
    },
    {
      key: 'seq',
      title: '排序',
      align: 'left',
      minWidth: 50
    },
    {
      key: 'edituser',
      title: '编辑人',
      align: 'left',
      minWidth: 80
    },
    {
      key: 'crtime',
      title: '更新日期',
      align: 'center',
      width: 200,
      render: row => row.crtime.replace('T', ' ')
    },
    {
      key: 'remark',
      title: '备注',
      align: 'left',
      minWidth: 200
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确认删除？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  // editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  // onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

// 删除
async function handleDelete(id: number) {
  try {
    // console.log(gamecode);
    await fetchDelManagerSetting(id);

    // 如果请求成功，调用 onDeleted
    onDeleted();
  } catch {}
}

// 编辑信息表单数据
const editData = ref<any>(null);
function edit(id: number) {
  const rowData = data.value.find(item => item.id === id);
  if (rowData) {
    editData.value = rowData;
  }
  handleEdit(id as any);
}

// 查询
async function handleSearch(params: Api.SystemManage.CommonSearchParams) {
  searchParams.value = params;
  // const newParams = { ...searchParams.value };
  const response = await fetchManagerSettingSearch(searchParams.value);
  data.value = response.data.records; // 更新表格数据
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

// 添加信息
async function handleSubmit(creditData: any) {
  try {
    if (operateType.value === 'add') {
      await fetchAddManagerSetting(creditData);
    } else if (operateType.value === 'edit') {
      await fetchUpdataManagerSetting(creditData);
    }
    getData();
  } finally {
    drawerVisible.value = false;
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <AppSettingSearch v-model:model="searchParams" @search="handleSearch" />
    <NCard title="系统配置管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns as DataTableColumns "
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1024"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <AppSetting
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editData"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
