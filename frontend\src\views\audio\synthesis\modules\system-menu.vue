<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { SaveTone } from '@/service/api/audio';
import TimbresImport from './timbres-import.vue';

// 通用音色数据类型，兼容SaveTone和SynthesisTone
interface TimbreInfo {
  id?: number | string;
  name: string;
  gender?: 0 | 1 | null;
  lang?: string | null;
  description: string;
  audio_url: string;
  is_favorite?: boolean;
  prompt_text?: string;
}

// 定义 props
const props = defineProps<{
  currentPlayingTimbreId?: string | null;
  isTimbrePlaying?: boolean;
}>();

// 定义 emit 事件
const emit = defineEmits<{
  'show-model-select': [];
  'play-timbre': [timbreData: TimbreInfo];
  'clear-audio-content-tone': [audioId: number | null];
  'timbre-imported': [toneData: SaveTone];
}>();

const speed = ref(1);
const pitch = ref(0);

const isTimbre = ref(false);

// 音色信息状态
const timbreData = ref<TimbreInfo | null>(null);

// 记录当前音色数据来源的 audio-content ID（如果是从 audio-content 进入的）
const sourceAudioContentId = ref<number | null>(null);

// 计算属性
const timbreName = computed(() => timbreData.value?.name || '选择音色');
const timbreDescription = computed(() => timbreData.value?.description || timbreData.value?.prompt_text);
// const timbreGender = computed(() => (timbreData.value?.gender === 1 ? '男' : '女'));
// const timbreLang = computed(() => timbreData.value?.lang || '');

// 设置音色数据（供外部调用，支持SaveTone和SynthesisTone）
const setTimbreData = (toneData: SaveTone | TimbreInfo, audioContentId?: number | null) => {
  timbreData.value = toneData;
  isTimbre.value = true;
  // 记录来源的 audio-content ID
  sourceAudioContentId.value = audioContentId || null;
};

// 处理音色导入成功
const handleTimbreSubmitted = (toneData: SaveTone) => {
  // 使用真实的API响应数据
  setTimbreData(toneData);

  // 通知父组件有音色被导入，让父组件将音色应用到最后一个AudioContent组件
  emit('timbre-imported', toneData);
};

// 暴露方法和数据供外部调用
defineExpose({
  setTimbreData,
  speed,
  pitch
});

// 播放音色示例（播放状态由父组件统一管理）
const isPlaying = computed(() => props.isTimbrePlaying || false);

const handlePlayTimbre = () => {
  if (!timbreData.value?.audio_url) {
    console.warn('音色音频URL不存在');
    return;
  }

  // 通过 emit 事件通知父组件播放音频，让父组件的统一音频播放器处理
  emit('play-timbre', timbreData.value);
};

// 关闭音色选择
const handleCloseTimbre = () => {
  // 如果是从 audio-content 进入的，通知父组件清除对应的音色数据
  if (sourceAudioContentId.value !== null) {
    emit('clear-audio-content-tone', sourceAudioContentId.value);
  }

  // 播放状态由父组件统一管理，这里只需要重置本地状态
  isTimbre.value = false;
  timbreData.value = null;
  sourceAudioContentId.value = null;
};

// 处理选择音色按钮点击
const handleSelectTimbre = () => {
  emit('show-model-select');
};
</script>

<template>
  <NForm>
    <NFormItem v-if="!isTimbre" path="" label="音色">
      <NTag size="large" class="model_select_tag h-10 w-2/3 cursor-pointer">
        <NButton text class="w-full" @click="handleSelectTimbre">
          <NFlex justify="space-between" align="center" :wrap="false">
            <SvgIcon icon="mingcute:voice-line" class="mr-3 text-xl" />
            <NText class="text-base">选择音色</NText>
          </NFlex>
          <SvgIcon icon="ant-design:right-outlined" class="text-xl" />
        </NButton>
      </NTag>

      <TimbresImport @timbre-imported="handleTimbreSubmitted" />
    </NFormItem>

    <NFormItem v-else path="" label="音色">
      <NFlex class="w-full" justify="space-between" align="center">
        <NCard class="audio_generate_task_card">
          <NFlex justify="space-between" align="center" :wrap="false">
            <NButton text @click="handlePlayTimbre">
              <!-- 播放/暂停图标 -->
              <SvgIcon :icon="isPlaying ? 'gridicons:pause' : 'lsicon:play-filled'" class="text-4xl" />
            </NButton>

            <!-- 任务信息 -->
            <NFlex vertical class="ml-3">
              <!-- 音色名称 -->
              <NEllipsis :line-clamp="1">{{ timbreName }}</NEllipsis>

              <!-- 音色描述 -->
              <NEllipsis :line-clamp="1">
                {{ timbreDescription }}
                <template #tooltip>
                  <div class="max-w-100 text-center">{{ timbreDescription }}</div>
                </template>
              </NEllipsis>

              <!-- 音色 其他信息 -->
              <!--
 <NFlex align="center" :wrap="false">
                <NTag v-if="timbreGender" type="info" round>{{ timbreGender }}</NTag>
                <NTag v-if="timbreLang" type="info" round>{{ timbreLang }}</NTag>
              </NFlex>
-->
            </NFlex>
          </NFlex>

          <!-- 任务结束显示 下载 -->
          <NButton text class="ml-3" @click="handleCloseTimbre">
            <SvgIcon icon="carbon:close-filled" class="text-3xl" />
          </NButton>
        </NCard>
      </NFlex>
    </NFormItem>

    <NFormItem path="" label="语速" class="mt-3">
      <NSlider v-model:value="speed" :step="0.1" :min="0.5" :max="2.0" />
    </NFormItem>

    <NFormItem path="" label="声调">
      <NSlider v-model:value="pitch" :step="1" :min="-12" :max="12" />
    </NFormItem>
  </NForm>
</template>

<style scoped lang="scss">
.model_select_tag :deep(.n-tag__content) {
  width: 100%;
}

.model_select_tag :deep(.n-tag__content) .n-button .n-button__content {
  width: 100%;
  justify-content: space-between !important;
}

:deep(.n-form-item-label__text) {
  font-size: 1.1em !important;
  margin-bottom: 0.5em !important;
}

.audio_generate_task_card > :deep(.n-card__content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
