import type {
  LocationQueryRaw,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationRaw,
  Router
} from 'vue-router';
import type { RouteKey, RoutePath } from '@elegant-router/types';
import MobileDetect from 'mobile-detect';
import { useAuthStore } from '@/store/modules/auth';
import { useRouteStore } from '@/store/modules/route';
import { localStg, sessionStg } from '@/utils/storage';
import { DEFAULT_COOKIE_OPTIONS, deleteCookie, getCookie } from '@/utils/cookie';
import { useCreditStore } from '@/store/modules/credit';
import { fetchtokenLogin } from '@/service/api/auth';
// import { extractFramepackFormData } from '@/utils/formCache';

/**
 * create route guard
 *
 * @param router router instance
 */
export function createRouteGuard(router: Router) {
  let timer: ReturnType<typeof setInterval> | undefined;
  // eslint-disable-next-line complexity
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore();
    const creditStore = useCreditStore();
    if (timer) {
      clearInterval(timer);
      timer = undefined;
    }

    // 如果目标路由是登录页，直接放行
    if (to.name === 'login') {
      next();
      return;
    }

    // 检测是否移动端，如果是就且当前不在移动端页面，强行跳转移动端
    if (!to.path.startsWith('/mobile')) {
      const md = new MobileDetect(window.navigator.userAgent);
      if (md.mobile() && !md.tablet()) {
        console.log('检测到移动端访问，跳转移动端页面');
        next('/mobile');
        return;
      }
    }

    // 处理token验证
    const tokenValidationResult = await validateToken();
    if (tokenValidationResult.redirect) {
      next(tokenValidationResult.redirectLocation!);
      return;
    }

    const token = tokenValidationResult.token;

    // 如果已登录,每次路由切换都更新用户信息和积分
    if (token) {
      const ok = await authStore.getUserInfo();
      if (!ok) {
        // token验证失败,清除认证信息并跳转到登录页
        authStore.resetStore();
        window.location.href = '/login';
        return;
      }
      await creditStore.updateCreditInfo();
      // 每 15 秒钟自动更新一次积分值
      timer = setInterval(() => {
        creditStore.updateCreditInfo();
      }, 15 * 1000);
    }

    const location = await initRoute(to);

    // record page visits
    if (to.name !== 'not-found') {
      const visitData = sessionStg.get('visited') || [];
      const {
        name,
        path,
        meta: { i18nKey, title }
      } = to;
      visitData.push({
        name,
        path,
        i18nKey,
        title
      });
      sessionStg.set('visited', visitData);
    }

    if (location) {
      next(location);
      return;
    }

    const rootRoute: RouteKey = 'root';
    const loginRoute: RouteKey = 'login';
    const noAuthorizationRoute: RouteKey = '403';

    const isLogin = Boolean(token);

    const needLogin = !to.meta.constant;
    const routeRoles = to.meta.roles || [];

    const hasRole = authStore.userInfo.roles.some(role => routeRoles.includes(role));

    const hasAuth = authStore.isStaticSuper || !routeRoles.length || hasRole;

    const routeSwitches: CommonType.StrategicPattern[] = [
      // if it is login route when logged in, then switch to the root page
      {
        condition: isLogin && to.name === loginRoute,
        callback: () => {
          next({ name: rootRoute });
        }
      },
      // if it is constant route, then it is allowed to access directly
      {
        condition: !needLogin,
        callback: () => {
          handleRouteSwitch(to, from, next);
        }
      },
      // if the route need login but the user is not logged in, then switch to the login page
      {
        condition: !isLogin && needLogin,
        callback: () => {
          next({ name: loginRoute, query: { redirect: to.fullPath } });
        }
      },
      // if the user is logged in and has authorization, then it is allowed to access
      {
        condition: isLogin && needLogin && hasAuth,
        callback: () => {
          handleRouteSwitch(to, from, next);
        }
      },
      // if the user is logged in but does not have authorization, then switch to the 403 page
      {
        condition: isLogin && needLogin && !hasAuth,
        callback: () => {
          next({ name: noAuthorizationRoute });
        }
      }
    ];

    routeSwitches.some(({ condition, callback }) => {
      if (condition) {
        callback();
      }

      return condition;
    });
  });
}

/**
 * initialize route
 *
 * @param to to route
 */
async function initRoute(to: RouteLocationNormalized): Promise<RouteLocationRaw | null> {
  const authStore = useAuthStore();
  const routeStore = useRouteStore();

  const notFoundRoute: RouteKey = 'not-found';
  const isNotFoundRoute = to.name === notFoundRoute;

  // if the constant route is not initialized, then initialize the constant route
  if (!routeStore.isInitConstantRoute) {
    await routeStore.initConstantRoute();

    // the route is captured by the "not-found" route because the constant route is not initialized
    // after the constant route is initialized, redirect to the original route
    if (isNotFoundRoute) {
      const path = to.fullPath;

      const location: RouteLocationRaw = {
        path,
        replace: true,
        query: to.query,
        hash: to.hash
      };

      return location;
    }
  }

  // if the route is the constant route but is not the "not-found" route, then it is allowed to access.
  if (to.meta.constant && !isNotFoundRoute) {
    return null;
  }

  // the auth route is initialized
  // it is not the "not-found" route, then it is allowed to access
  if (routeStore.isInitAuthRoute && !isNotFoundRoute) {
    return null;
  }
  // it is captured by the "not-found" route, then check whether the route exists
  if (routeStore.isInitAuthRoute && isNotFoundRoute) {
    const exist = await routeStore.getIsAuthRouteExist(to.path as RoutePath);
    const noPermissionRoute: RouteKey = '403';

    if (exist) {
      const location: RouteLocationRaw = {
        name: noPermissionRoute
      };

      return location;
    }

    return null;
  }

  // if the auth route is not initialized, then initialize the auth route
  let token = localStg.get('token');
  if (!token) {
    token = getCookie('token');
    if (token) {
      localStg.set('token', token); // Store token back into local storage
    }
  }
  const isLogin = Boolean(token);
  // initialize the auth route requires the user to be logged in, if not, redirect to the login page
  if (!isLogin) {
    const loginRoute: RouteKey = 'login';
    const redirect = to.fullPath;

    const query: LocationQueryRaw = to.name !== loginRoute ? { redirect } : {};

    const location: RouteLocationRaw = {
      name: loginRoute,
      query
    };

    return location;
  }

  await authStore.initUserInfo();

  // initialize the auth route
  await routeStore.initAuthRoute();

  // the route is captured by the "not-found" route because the auth route is not initialized
  // after the auth route is initialized, redirect to the original route
  if (isNotFoundRoute) {
    const rootRoute: RouteKey = 'root';
    const path = to.redirectedFrom?.name === rootRoute ? '/' : to.fullPath;

    const location: RouteLocationRaw = {
      path,
      replace: true,
      query: to.query,
      hash: to.hash
    };

    return location;
  }

  return null;
}

function handleRouteSwitch(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
  // 处理表单缓存逻辑
  handleFormCache(to, from);

  // route with href
  if (to.meta.href) {
    window.open(to.meta.href, '_blank');

    next({ path: from.fullPath, replace: true, query: from.query, hash: to.hash });

    return;
  }

  next();
}

/**
 * 处理表单缓存逻辑
 *
 * @param to 目标路由
 * @param from 来源路由
 */
function handleFormCache(to: RouteLocationNormalized, from: RouteLocationNormalized) {
  // 检测从framepack页面离开时，触发表单数据缓存
  if (from.name === 'video_framepack') {
    // 设置全局标记，通知framepack组件保存表单数据
    (window as any).FRAMEPACK_CACHE_SAVE = true;
    console.log('检测到离开framepack页面，设置缓存保存标记');
  }

  // 检测进入framepack页面时，设置恢复标记
  if (to.name === 'video_framepack') {
    // 设置全局标记，通知framepack组件恢复表单数据
    (window as any).FRAMEPACK_CACHE_RESTORE = true;
    console.log('检测到进入framepack页面，设置缓存恢复标记');
  }
}

/** 验证token并处理相关逻辑 */
async function validateToken() {
  let token = localStg.get('token');
  let redirect = false;
  const redirectLocation: RouteLocationRaw = { name: 'login', replace: true };

  if (!token) {
    token = getCookie('token');
    if (token) {
      try {
        // 验证从cookie获取的token
        const { error } = await fetchtokenLogin(token);
        if (error) {
          throw new Error('Token validation failed');
        }
        localStg.set('token', token);
      } catch (e) {
        // token验证失败，确保清除所有认证信息
        localStg.remove('token');
        deleteCookie('token', DEFAULT_COOKIE_OPTIONS);
        // 强制重定向到登录页
        redirect = true;
      }
    }
  }

  return { token, redirect, redirectLocation };
}
