import logging
import os
import json
from fastapi import APIRouter

from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get('/get_resolution',tags=["aidraw"])
async def get_resolution():
  try:
    # 读取JSON文件
    with open('data/static/resolution.json', 'r', encoding='utf-8') as file:
        resolution_str = file.read()
    resolution_list = json.loads(resolution_str)
    return {"code": "0000","data":{"resolution":resolution_list},"msg":"success"}
  except Exception as e:
    logger.error(f"An error occurred: {e}")
    raise ClientVisibleException()

@router.get('/get_preset',tags=["aidraw"])
async def get_preset():
  try:
    # 读取JSON文件
    with open('data/static/presets.json', 'r', encoding='utf-8') as file:
        presets_json_data = file.read()
    presets_list = json.loads(presets_json_data)
    return {"code": "0000","data":{"preset":presets_list},"msg":"success"}
  except Exception as e:
    logger.error(f"An error occurred: {e}")
    raise ClientVisibleException()



@router.get('/get_style_image',tags=["aidraw"])
async def get_style_image():
  try:
    # 读取JSON文件
    with open('data/static/style.json', 'r', encoding='utf-8') as file:
        style_json_data = file.read()
    categories = json.loads(style_json_data)

    # 新数组
    new_list = []
    # 依次添加每个类别的 key 到新数组中
    for category_values in categories.values():
      for item in category_values:
        key = list(item.keys())[0]
        modified_string = key.replace(" ", "_")
        new_list.append(modified_string)

    # 每张图片的路径
    file_path_result_list = []
    # 循环风格图片
    for file in new_list:
      # 文件夹拼接
      file_path_result_temp = "data/static/images_styles/" + file + ".jpg"
      file_path_result_list.append(file_path_result_temp)

    return {"code": "0000","data":{"image":file_path_result_list},"msg":"success"}
  except Exception as e:
    logger.error(f"An error occurred: {e}")
    raise ClientVisibleException()

# @router.post("/aidraw")
# async def aidraw(
#     prompt: str,
#     model: str = "stable-diffusion-xl-base-0.9",
#     num_inference_steps: int = 50,
#     guidance_scale: float = 7.5,
#     num_outputs: int = 1,
#     width: int = 512,
#     height: int = 512,)
#     # db: AsyncSession = Depends(get_db)

# 图生文，反向推理出图片标签
@router.get('/predict',tags=["aidraw"])
async def predict():
    try:
        uploaded_file = request.files['file']
        if uploaded_file:
            username = session.get('username')

            # 清空（个人上传-反推）文件夹
            for file in os.listdir(setting.Fooocus_input_pushBack + username):
                file_path = os.path.join(setting.Fooocus_input_pushBack + username, file)
                if os.path.isfile(file_path):
                    # 如果是文件，直接删除
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    # 如果是文件夹，递归删除
                    shutil.rmtree(file_path)

            # 上传的图片保存到文件夹
            filename = os.path.basename(uploaded_file.filename)
            logger.info(f"文件名：{filename}")
            # 去除 .png .jpg
            filename_temp = filename[:-4]
            if common.check_input(filename_temp):
                file_push_back_path = setting.Fooocus_input_pushBack + username + "/" + filename
                uploaded_file.save(file_push_back_path)
                user_data[username]["file_push_back_path"] = file_push_back_path

                # 执行虚拟环境文件
                cmd = f"""d: && cd D:/AIGC/WD14Tagger && activate wd14tagger && C:/Users/<USER>/miniconda3/envs/wd14tagger/python tag_images_by_wd14_tagger.py "D:/AI_Web/input/fooocus/pushBack/{username}" --batch_size 8 --caption_extension .txt"""
                os.system(cmd)

                # 拼接生成的txt路径
                push_name = filename.split('.')[0]
                push_txt_name = setting.Fooocus_input_pushBack + username + "/" + push_name + '.txt'
                # 判断生成的txt文件是否存在
                if os.path.isfile(push_txt_name):
                    with open(push_txt_name, 'r') as file:
                        txt_content = file.read()

                # # 图片类型
                # image_type_temp = request.form['type']
                # image_type = "Anime"
                # if image_type_temp == "2":
                #     image_type = "Photo"
                # else:
                #     image_type = "Anime"
                #
                # # 打开图片
                # with open(user_data[username]["file_push_back_path"], "rb") as image:
                #     image_bytes = image.read()
                # response = requests.post(url=f"{setting.FOOOCUS_API_URL}/v1/tools/describe-image",
                #                          params={"type": f"{image_type}"},
                #                          files={"image": image_bytes}, timeout=30)
                #
                # txt_content = response.json()["describe"]
                return txt_content
            else:
                return jsonify({'errorMessage': common_error.error_file_name}), 500
    except Exception as e:
        return jsonify({'errorMessage': common_error.error_message + str(e)}), 500


