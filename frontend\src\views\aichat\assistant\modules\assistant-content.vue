<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { NTag, useMessage } from 'naive-ui';
import useClipboard from 'vue-clipboard3';
import SvgIcon from '@/components/custom/svg-icon.vue';
import Message from './message-text.vue';
import MessageTools from "@/views/aichat/assistant/modules/MessageTools.vue";

const emit = defineEmits<{
  (e: 'regenerateMessage', message: ChatMessage): void;
  (e: 'modelChange', message: ChatMessage, selectedModel: string): void;
}>();

interface ChatMessage {
  uuid: number;
  inversion: boolean;
  content: string;
  timestamp: string;
  files?: Array<{ ext: string; url: string; type: string }>;
  qid?: number;
  model?: string;
  modelLabel?: string;
  modelDesc?: string;
}

const { messages, models, isLoading } = defineProps<{
  messages: ChatMessage[];
  models: Array<{ label: string; value: string; desc: string; icon: string }>;
  isLoading?: boolean;
  model?: string;
  isMobile?: boolean;
}>();

const tips = useMessage();
const asRawText = ref(false);

function handleModelChange(message: ChatMessage, selectedModel: string) {
  // 如果正在加载中或没有问题ID，不允许切换模型
  if (isLoading || !message.qid) {
    return;
  }

  // 如果选择的模型与当前模型相同，不需要切换
  if (message.model === selectedModel) {
    return;
  }

  // 发送事件到父组件处理模型切换逻辑
  emit('modelChange', message, selectedModel);
}

function handleSelect(key: 'copyText' | 'toggleRenderType', message?: ChatMessage) {
  switch (key) {
    case 'copyText':
      if (message) {
        handleCopy(message.content);
      }
      return;
    case 'toggleRenderType':
      asRawText.value = !asRawText.value;
      break;
    default:
  }
}

const { toClipboard } = useClipboard();

async function handleCopy(content: string): Promise<void> {
  return toClipboard(content)
    .then(() => {
      tips.success('复制成功');
    })
    .catch(error => {
      tips.error('复制失败');
      throw error;
    });
}

function handleRegenerate(message: ChatMessage) {
  // 如果正在加载中，不允许重新生成
  if (isLoading) {
    return;
  }

  // 直接触发父组件的重新生成事件
  emit('regenerateMessage', message);
}

// 添加 watch 来监听消息变化
watch(
  () => messages,
  () => {
    nextTick(() => {
      // 可以在这里添加需要的更新逻辑
    });
  },
  { deep: true }
);
</script>

<template>
  <div class="chat-content">
    <div
      v-for="message in messages"
      :key="`${message.uuid}-${message.content}`"
      class="message-item"
      :class="[message.inversion ? 'user-message' : 'assistant-message', isMobile ? '' : 'max-w-[1280px] mx-auto']"
    >
      <div class="message-body">
        <div v-if="!message.inversion">
          <div class="max-w-[calc(100%-2px)] flex flex-col">
            <p class="timestamp">{{ message.timestamp }}</p>
            <Message
              :text="message.content"
              :inversion="false"
              :files="message.files"
              :stream-rendering="false"
              :loading="isLoading"
              :is-mobile="isMobile"
            />
            <MessageTools
              :is-mobile="isMobile"
              :models="models"
              :current-model="message.model"
              :is-loading="isLoading"
              :message="message.content"
              @copy-text="handleSelect('copyText', message)"
              @regenerate="handleRegenerate(message)"
              @model-change="(selectedModel: string) => handleModelChange(message, selectedModel)"
            />
          </div>
        </div>

        <div v-if="message.inversion" class="flex flex-col items-end">
          <div class="flex items-start">
            <div class="relative w-fit flex flex-col">
              <p class="timestamp absolute right-0 mb-1 whitespace-nowrap text-right">
                {{ message.timestamp }}
              </p>
              <div v-if="message.files?.length" class="mt-5 flex justify-end">
                <Message :inversion="true" :loading="isLoading" class="py-1.5" :files="message.files" />
              </div>
              <div class="mt-5 flex justify-end">
                <Message :text="message.content" :inversion="true" :loading="isLoading" class="py-1.5" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-content {
  padding: 16px;
}

.message-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 60px;
}

.message-item:last-child {
  margin-bottom: 0;
}

.timestamp {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}
</style>
