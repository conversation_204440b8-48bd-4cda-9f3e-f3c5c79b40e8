<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue';
// import volc_tts from '@/assets/json/volc_tts.json';
import { message } from 'ant-design-vue';
import voice_format from '@/assets/json/voice_format.json';
// import voice_sample from '@/assets/json/voice_sample.json';
import { postVolcanoTTS } from '@/service/api';
import { downloadFile } from '@/utils/common';

interface CatGroup {
  cat_name: string;
  items: Api.Audio.VolcanoVoiceOption[];
}

const voice_opt = voice_format as Api.Audio.VolcanoVoiceOption[];
// const voice_sample_opt = voice_sample as { [key: string]: string };
const doubao = [] as Api.Audio.VolcanoVoiceOption[];
const assistant = [] as Api.Audio.VolcanoVoiceOption[];
const declaim = [] as Api.Audio.VolcanoVoiceOption[];
const special = [] as Api.Audio.VolcanoVoiceOption[];
const plurilingual = [] as Api.Audio.VolcanoVoiceOption[];
const dialects = [] as Api.Audio.VolcanoVoiceOption[];

const dialectsMap = {} as { [key: string]: CatGroup };
const plurilingualMap = {} as { [key: string]: CatGroup };
const assistantMap = {} as { [key: string]: CatGroup };
const specialMap = {} as { [key: string]: CatGroup };

voice_opt.forEach(e => {
  // e.sample ||= voice_sample_opt[e.voice_type];
  const cat_str = JSON.stringify(e.category);
  cat_str.includes('豆包同款') && doubao.push(e);
  cat_str.includes('智能助手') && assistant.push(e);
  cat_str.includes('有声阅读') && declaim.push(e);
  cat_str.includes('特色配音') && special.push(e);
  cat_str.includes('多语种') && plurilingual.push(e);
  cat_str.includes('方言') && dialects.push(e);

  e.category.forEach(cat_item => {
    if (cat_item[0] === '智能助手') {
      const lang = cat_item[1];
      if (!assistantMap[lang]) {
        assistantMap[lang] = {
          cat_name: lang,
          items: []
        };
      }
      assistantMap[lang].items.push(e);
    }
    if (cat_item[0] === '特色配音') {
      const lang = cat_item[1];
      if (!specialMap[lang]) {
        specialMap[lang] = {
          cat_name: lang,
          items: []
        };
      }
      specialMap[lang].items.push(e);
    }
    if (cat_item[0] === '多语种') {
      const lang = cat_item[1];
      if (!plurilingualMap[lang]) {
        plurilingualMap[lang] = {
          cat_name: lang,
          items: []
        };
      }
      plurilingualMap[lang].items.push(e);
    }
    if (cat_item[0] === '方言') {
      const lang = cat_item[1];
      if (!dialectsMap[lang]) {
        dialectsMap[lang] = {
          cat_name: lang,
          items: []
        };
      }
      dialectsMap[lang].items.push(e);
    }
  });
});
// console.log(voice_opt);

const plurilingualGroup = Object.values(plurilingualMap) as CatGroup[];
const dialectsGroup = Object.values(dialectsMap) as CatGroup[];
const assistantGroup = Object.values(assistantMap) as CatGroup[];
const specialGroup = Object.values(specialMap) as CatGroup[];

// console.log(voice_opt);
// import CardSelect from '@/components/custom/card-select.vue';

// import VoiceSelect from './modules/voice-select.vue';
// import TextEditor from './modules/text-editor.vue';

// provide('voice_options', volc_tts.voice_options);

// name: string;
// gender: string;
// age: string;
// labels: string[];
// category: { level1: string; level2?: undefined | string }[];
// voice_config: {
//   language: string;
//   emotion: string;
//   params: {
//     voice_type: string;
//   };
//   text: string;
//   ssml: string;
// }[];

// interface VoiceMap {
//   [key: string]: Api.Audio.VolcanoVoiceOptinItem;
// }
// const voice_options: Api.Audio.VolcanoVoiceOptinItem[] = volc_tts.voice_options;
// const voice_options_map = voice_options.reduce((accumulator, current) => {
//   accumulator[current.name] = current;
//   return accumulator;
// }, {} as VoiceMap);
// console.log(voice_options_map);
const loading = ref<boolean>(false);

// const emotionKey = [
//   'pleased',
//   'sorry',
//   'annoyed',
//   'happy',
//   'sad',
//   'angry',
//   'scare',
//   'hate',
//   'surprise',
//   'tear',
//   'novel_dialog'
// ];
// const styleKey = [
//   'customer_service',
//   'professional',
//   'serious',
//   'narrator',
//   'narrator_immersive',
//   'comfort',
//   'lovey-dovey',
//   'energetic',
//   'conniving',
//   'tsundere',
//   'charming',
//   'storytelling',
//   'radio',
//   'yoga',
//   'advertising',
//   'assistant',
//   'chat'
// ];
const emotionMap: { [key: string]: string } = {
  pleased: '愉悦',
  sorry: '抱歉',
  annoyed: '嗔怪',
  happy: '开心',
  sad: '悲伤',
  angry: '愤怒',
  scare: '害怕',
  hate: '厌恶',
  surprise: '惊讶',
  tear: '哭腔',
  novel_dialog: '平和'
};
const styleMap: { [key: string]: string } = {
  pleased: '愉悦',
  sorry: '抱歉',
  annoyed: '嗔怪',
  happy: '开心',
  sad: '悲伤',
  angry: '愤怒',
  scare: '害怕',
  hate: '厌恶',
  surprise: '惊讶',
  tear: '哭腔',
  novel_dialog: '平和',

  general: '通用',
  customer_service: '客服',
  professional: '专业',
  serious: '严肃',
  neutral: '中立',
  narrator: '旁白-舒缓',
  narrator_immersive: '旁白-沉浸',
  comfort: '安慰鼓励',
  energetic: '可爱元气',
  conniving: '绿茶',
  tsundere: '傲娇',
  charming: '娇媚',
  storytelling: '讲故事',
  radio: '情感电台',
  yoga: '瑜伽',
  advertising: '广告',
  assistant: '助手',
  chat: '自然对话',
  'lovey-dovey': '撒娇'
};
const langMap: { [key: string]: string } = {
  cn: '中文',
  en: '英语',
  ja: '日语',
  thth: '泰语',
  vivn: '越南语',
  id: '印尼语',
  ptbr: '葡萄牙语',
  esmx: '西班牙语',
  zh_dongbei: '东北',
  zh_yueyu: '粤语',
  zh_shanghai: '上海',
  zh_xian: '西安',
  zh_chengdu: '成都',
  zh_taipu: '台湾普通话',
  zh_guangxi: '广西普通话'
};
// const langMap2: { [key: string]: string } = {
//   中文: 'cn',
//   英语: 'en',
//   日语: 'ja',
//   泰语: 'thth',
//   越南语: 'vivn',
//   印尼语: 'id',
//   葡萄牙语: 'ptbr',
//   西班牙语: 'esmx',
//   东北: 'zh_dongbei',
//   粤语: 'zh_yueyu',
//   上海: 'zh_shanghai',
//   西安: 'zh_xian',
//   成都: 'zh_chengdu',
//   台湾普通话: 'zh_taipu',
//   广西普通话: 'zh_guangxi'
// };
// 格式化 volc_tts.json
// const newList = [];
// volc_tts.voice_options.forEach(e => {
//   const { name, gender, age, voice_config, category, labels } = e;
//   const item = {
//     voice_type: voice_config[0].params.voice_type,
//     name,
//     gender,
//     age,
//     labels,
//     category: [],
//     language: [],
//     emotion: [],
//     style: []
//   };
//   const langSet = new Set();
//   const emotionSet = new Set();
//   const styleSet = new Set();
//   voice_config.forEach(element => {
//     // 没有配置就放一个通用的
//     if (element.params.language) {
//       langSet.add(element.params.language);
//     } else if (langMap2[element.language]) {
//       langSet.add(langMap2[element.language]);
//     }

//     if (element.params.style_name) {
//       styleSet.add(element.params.style_name);
//     } else if (element.emotion === '通用') {
//       styleSet.add('general');
//     }
//     if (element.params.emotion) {
//       emotionSet.add(element.params.emotion);
//     }
//   });
//   category.forEach(element => {
//     item.category.push(Object.values(element));
//   });
//   item.language = [...langSet];
//   item.emotion = [...emotionSet];
//   item.style = [...styleSet];
//   newList.push(item);
// });
// console.log('newList', newList);

// 情感演绎配置
const emotionOption = ref<CommonType.Option[]>([
  { label: '愉悦', value: 'pleased' },
  { label: '抱歉', value: 'sorry' },
  { label: '嗔怪', value: 'annoyed' },
  { label: '开心', value: 'happy' },
  { label: '悲伤', value: 'sad' },
  { label: '愤怒', value: 'angry' },
  { label: '害怕', value: 'scare' },
  { label: '厌恶', value: 'hate' },
  { label: '惊讶', value: 'surprise' },
  { label: '哭腔', value: 'tear' },
  { label: '平和', value: 'novel_dialog' }
]);

// 风格
const styleOption = ref<CommonType.Option[]>([
  { label: '客服', value: 'customer_service' },
  { label: '专业', value: 'professional' },
  { label: '严肃', value: 'serious' },
  { label: '旁白-舒缓', value: 'narrator' },
  { label: '旁白-沉浸 ', value: 'narrator_immersive' },
  { label: '安慰鼓励', value: 'comfort' },
  { label: '撒娇', value: 'lovey-dovey' },
  { label: '可爱元气', value: 'energetic' },
  { label: '绿茶', value: 'conniving' },
  { label: '傲娇', value: 'tsundere' },
  { label: '娇媚', value: 'charming' },
  { label: '讲故事', value: 'storytelling' },
  { label: '情感电台', value: 'radio' },
  { label: '瑜伽', value: 'yoga' },
  { label: '广告', value: 'advertising' },
  { label: '助手', value: 'assistant' },
  { label: '自然对话', value: 'chat' }
]);
// 方言
// const dialectOption = ref<CommonType.Option[]>([
//   { label: '中文', value: 'cn' },
//   { label: '东北', value: 'zh_dongbei' },
//   { label: '粤语', value: 'zh_yueyu' },
//   { label: '上海', value: 'zh_shanghai' },
//   { label: '西安', value: 'zh_xian' },
//   { label: '成都', value: 'zh_chengdu' },
//   { label: '台湾普通话', value: 'zh_taipu' },
//   { label: '广西普通话', value: 'zh_guangxi' }
// ]);
const languageOption = ref<CommonType.Option[]>([
  { label: '中文', value: 'cn' },
  { label: '英语', value: 'en' },
  { label: '日语', value: 'ja' },
  { label: '泰语', value: 'thth' },
  { label: '越南语', value: 'vivn' },
  { label: '印尼语', value: 'id' },
  { label: '葡萄牙语', value: 'ptbr' },
  { label: '西班牙语', value: 'esmx' }
]);

const wrapperRef = ref();
const audioRef = ref<HTMLAudioElement | null>(null);
const audioUrl = ref<string>('');
const voice_type = ref<string>('灿灿2.0');
const createDefaultModel = (): Api.Audio.VolcanoTTS => {
  return {
    text: '今天天气可好了，我打算和朋友一起去野餐，你要不要和我们一起呀？',
    // text: 'Not to know what happened before one was born is always to be a child.',
    // text: 'Life is not merely living but living in health.',
    // text: '朱に交われば赤くなる',
    // text: 'ความพยายามอยู่ที่ไหน ความสำเร็จอยู่ที่นั่น',//泰语
    // text: 'Não deixe para amanhã o que você pode fazer hoje.', // 葡萄牙
    // text: 'Dar el primer paso de la gran marcha', // 西班牙
    voice_type: 'BV700_V2_streaming',
    encoding: 'mp3',
    speed_ratio: 1.0,
    volume_ratio: 1.0,
    pitch_ratio: 1.0,
    silence_duration: 300,
    language: '',
    emotion: ''
  };
};
const model: Api.Audio.VolcanoTTS = reactive(createDefaultModel());
const doTTS = () => {
  audioUrl.value = '';
  loading.value = true;

  const params_data = { ...model };
  params_data.emotion = params_data.emotion === 'general' ? '' : params_data.emotion;
  // return console.log(params_data);
  postVolcanoTTS(params_data)
    .then(({ error, data }) => {
      if (!error) {
        audioUrl.value = data.data;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
// 设置当前音色
// const setCurrCoiceType = (opt: Api.Audio.VolcanoVoiceOptinItem) => {
//   // 读取支持语言，情感演绎，情感风格 ，voice_type 参数
//   console.log(opt);
// };
// 选择一个音色
// const selectVoiceType = (name: string) => {
//   const voice_opt: Api.Audio.VolcanoVoiceOptinItem | undefined = voice_options_map[name] || null;
//   if (voice_opt) {
//     setCurrCoiceType(voice_opt);
//   }
// };
// watch(
//   () => voice_type.value,
//   () => {
//     console.log('watch', Date.now());
//     // selectVoiceType(voice_type.value);
//   }
// );
// watchEffect(() => {
//   selectVoiceType(voice_type.value);
// });
const updateLanguageOption = async (keyList: string[]) => {
  languageOption.value = [];
  keyList.forEach(e => {
    languageOption.value.push({
      label: langMap[e] || '无',
      value: e
    });
  });
  model.language = languageOption.value[0]?.value || '';
};
const updateStyleOption = async (keyList: string[]) => {
  console.log(keyList);

  // const tmp: CommonType.Option[] = [];

  styleOption.value = [];
  emotionOption.value = [];
  keyList.forEach(e => {
    // 处理情感风格选项
    if (styleMap[e]) {
      styleOption.value.push({
        label: styleMap[e],
        value: e
      });
    }
    // 处理情感演绎选项
    if (emotionMap[e]) {
      emotionOption.value.push({
        label: emotionMap[e],
        value: e
      });
    }
  });
  // console.log(keyList);
  // console.log(styleOption.value);
  // console.log(emotionOption.value);
  model.emotion = styleOption.value[0]?.value || '';
};
const selectVoice = (opt: Api.Audio.VolcanoVoiceOption) => {
  console.log('selectVoice');
  console.log(opt.voice_type);
  updateLanguageOption(opt.language as string[]);
  updateStyleOption(opt.style as string[]);

  model.voice_type = opt.voice_type;
};

// const makeSample = async () => {
//   const params = { ...model };
//   [...doubao, ...assistant, ...declaim].forEach(async e => {
//     params.voice_type = e.voice_type;
//     params.emotion = params.emotion === 'general' ? '' : params.emotion;
//     console.log(JSON.stringify(params));
//     // return console.log(params_data);
//     await postVolcanoTTS(params);
//   });
// };
// const backgroundAudio = new Audio();
const playSample = (url: string) => {
  audioUrl.value = `/proxy-default/${url}`;
  nextTick(() => {
    audioRef.value?.load(); // 重新加载音频源
    audioRef.value?.play(); // 播放音频
  });
};

// 下载音频文件
const downloadAudio = (url: string, type: string) => {
  if (!url) {
    message.error('暂无音频文件');
    return;
  }
  downloadFile(url, `audio_${type}.wav`);
};

// 设置音频播放速率
const setPlaybackRate = (rate: number) => {
  if (audioRef.value) {
    audioRef.value.playbackRate = rate;
  }
};
</script>

<!--
 cat_str.includes('豆包同款') && doubao.push(e);
cat_str.includes('智能助手') && assistant.push(e);
cat_str.includes('有声阅读') && declaim.push(e);
cat_str.includes('特色配音') && special.push(e);
cat_str.includes('多语种') && plurilingual.push(e);
cat_str.includes('方言') && dialects.push(e);
-->
<template>
  <div ref="wrapperRef" class="h-vh flex flex-col gap-16px">
    <NScrollbar class="h-full">
      <NGrid cols="24" x-gap="20" class="min-h-[calc(100vh-13em)]">
        <NGi span="16">
          <NCard class="h-full">
            <div class="h-full flex flex-col">
              <!-- 上半部分音频列表 -->
              <NScrollbar class="mb-4 h-75">
                <NTabs animated class="">
                  <NTabPane name="豆包同款" tab="豆包同款">
                    <!-- <NScrollbar class="h-[calc(100vh-400px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <NGrid :x-gap="15" class="w-full">
                        <NGi v-for="item in doubao" :key="item.name" span="8">
                          <NRadioButton :value="item.name" class="my-2 w-full py-1" @click.stop="selectVoice(item)">
                            <template #default>
                              <div class="flex items-center justify-between">
                                <div class="overflow-hidden">
                                  {{ item.name }}
                                </div>
                                <div>
                                  <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                    <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                  </div>
                                </div>
                              </div>
                            </template>
                          </NRadioButton>
                        </NGi>
                      </NGrid>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                  <NTabPane class="" name="智能助手" tab="智能助手">
                    <!-- <NScrollbar class="h-[calc(100vh-240px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <template v-for="catItem in assistantGroup" :key="catItem.cat_name">
                        <NText class="block font-extrabold">
                          {{ catItem.cat_name }}
                        </NText>
                        <NGrid :x-gap="20" class="mb-5">
                          <NGi v-for="item in catItem.items" :key="item.name" span="8">
                            <NRadioButton :value="item.name" class="my-2 w-full py-1" @click="selectVoice(item)">
                              <template #default>
                                <div class="flex items-center justify-between">
                                  <div class="overflow-hidden">
                                    {{ item.name }}
                                  </div>
                                  <div>
                                    <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                      <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </NRadioButton>
                          </NGi>
                        </NGrid>
                      </template>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                  <NTabPane class="" name="有声阅读" tab="有声阅读">
                    <!-- <NScrollbar class="h-[calc(100vh-240px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <NGrid :x-gap="15" class="w-full">
                        <NGi v-for="item in declaim" :key="item.name" span="8">
                          <NRadioButton :value="item.name" class="my-2 w-full py-1" @click="selectVoice(item)">
                            <template #default>
                              <div class="flex items-center justify-between">
                                <div class="overflow-hidden">
                                  {{ item.name }}
                                </div>
                                <div>
                                  <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                    <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                  </div>
                                </div>
                              </div>
                            </template>
                          </NRadioButton>
                        </NGi>
                      </NGrid>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                  <NTabPane class="" name="特色配音" tab="特色配音">
                    <!-- <NScrollbar class="h-[calc(100vh-240px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <template v-for="catItem in specialGroup" :key="catItem.cat_name">
                        <NText class="block font-extrabold">
                          {{ catItem.cat_name }}
                        </NText>
                        <NGrid :x-gap="20" class="mb-5">
                          <NGi v-for="item in catItem.items" :key="item.name" span="8">
                            <NRadioButton :value="item.name" class="my-2 w-full py-1" @click="selectVoice(item)">
                              <template #default>
                                <div class="flex items-center justify-between">
                                  <div class="overflow-hidden">
                                    {{ item.name }}
                                  </div>
                                  <div>
                                    <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                      <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </NRadioButton>
                          </NGi>
                        </NGrid>
                      </template>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                  <NTabPane class="" name="多语种" tab="多语种">
                    <!-- <NScrollbar class="h-[calc(100vh-240px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <template v-for="catItem in plurilingualGroup" :key="catItem.cat_name">
                        <NText class="block font-extrabold">
                          {{ catItem.cat_name }}
                        </NText>
                        <NGrid :x-gap="20" class="mb-5">
                          <NGi v-for="item in catItem.items" :key="item.name" span="8">
                            <NRadioButton :value="item.name" class="my-2 w-full py-1" @click="selectVoice(item)">
                              <template #default>
                                <div class="flex items-center justify-between">
                                  <div class="overflow-hidden">
                                    {{ item.name }}
                                  </div>
                                  <div>
                                    <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                      <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </NRadioButton>
                          </NGi>
                        </NGrid>
                      </template>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                  <NTabPane class="" name="方言" tab="方言">
                    <!-- <NScrollbar class="h-[calc(100vh-240px)] max-w-full"> -->
                    <NRadioGroup v-model:value="voice_type" class="w-full">
                      <template v-for="catItem in dialectsGroup" :key="catItem.cat_name">
                        <NText class="block font-extrabold">
                          {{ catItem.cat_name }}
                        </NText>
                        <NGrid :x-gap="20" class="mb-5">
                          <NGi v-for="item in catItem.items" :key="item.name" span="8">
                            <NRadioButton :value="item.name" class="my-2 w-full py-1" @click="selectVoice(item)">
                              <template #default>
                                <div class="flex items-center justify-between">
                                  <div class="overflow-hidden">
                                    {{ item.name }}
                                  </div>
                                  <div>
                                    <div v-if="item.sample" @click.stop="playSample(item.sample)">
                                      <SvgIcon icon="akar-icons:sound-on" class="h-5 w-5 text-inherit" />
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </NRadioButton>
                          </NGi>
                        </NGrid>
                      </template>
                    </NRadioGroup>
                    <!-- </NScrollbar> -->
                  </NTabPane>
                </NTabs>
              </NScrollbar>

              <!-- 下半部分操作区域 -->
              <div class="flex-1">
                <NCard>
                  <!-- 文本输入框 -->
                  <NInput
                    v-model:value="model.text"
                    type="textarea"
                    placeholder="输入要转换的文字"
                    class="mb-3 min-h-200px"
                  />

                  <!-- 参数设置区域 -->
                  <NSpace justify="space-around" class="my-3">
                    <NFormItem size="small" label-placement="left" label="音调" class="grid justify-items-center">
                      <NInputNumber v-model:value="model.pitch_ratio" :min="0.1" :max="3" :step="0.1" class="w-20" />
                    </NFormItem>
                    <NFormItem size="small" label-placement="left" label="音量" class="grid justify-items-center">
                      <NInputNumber v-model:value="model.volume_ratio" :min="0.2" :max="3" :step="0.1" class="w-20" />
                    </NFormItem>
                    <NFormItem size="small" label-placement="left" label="语速" class="grid justify-items-center">
                      <NInputNumber v-model:value="model.speed_ratio" :min="0.2" :max="3" :step="0.1" class="w-20" />
                    </NFormItem>
                  </NSpace>
                  <NSpace justify="space-around" class="my-3">
                    <NFormItem size="small" label-placement="left" label="情感风格">
                      <NSelect v-model:value="model.emotion" :options="styleOption" trigger="click" class="w-40" />
                    </NFormItem>
                    <NFormItem size="small" label-placement="left" label="跨语言">
                      <NSelect v-model:value="model.language" :options="languageOption" trigger="click" class="w-40" />
                    </NFormItem>
                  </NSpace>

                  <!-- 生成按钮 -->
                  <div class="flex justify-center">
                    <NButton type="primary" :loading="loading" class="h-55px w-70" @click="doTTS">立即合成</NButton>
                  </div>
                </NCard>
              </div>
            </div>
          </NCard>
        </NGi>

        <!-- 右侧结果展示区域 -->
        <NGi span="8">
          <NCard class="h-full">
            <div class="mt-[0.5em] flex flex-col gap-2">
              <audio ref="audioRef" controls :src="audioUrl" class="w-full"></audio>
              <div class="flex justify-center">
                <NButtonGroup>
                  <NButton size="small" @click="downloadAudio(audioUrl, 'audio')">
                    <template #icon>
                      <SvgIcon icon="mdi:download" />
                    </template>
                    下载
                  </NButton>
                  <NButton size="small" @click="setPlaybackRate(0.5)">0.5x</NButton>
                  <NButton size="small" @click="setPlaybackRate(1.0)">1.0x</NButton>
                  <NButton size="small" @click="setPlaybackRate(1.5)">1.5x</NButton>
                </NButtonGroup>
              </div>
            </div>
          </NCard>
        </NGi>
      </NGrid>
    </NScrollbar>
  </div>
</template>

<style scoped lang="scss">
// .custom-radio-group {
//   display: flex;
//   gap: 15px;
//   align-items: center;
// }

// :deep(.custom-radio) {
//   display: flex;
//   align-items: center;
// }

// .custom-radio input[type='radio'] {
//   display: none;
// }

// .custom-radio-label {
//   display: flex;
//   align-items: center;
//   cursor: pointer;
//   border: 2px solid transparent;
//   padding: 10px;
//   transition: border 0.3s ease;
// }

// .radio-img {
//   max-width: 50px;
//   height: auto;
//   margin-right: 10px;
// }

// .custom-radio-label.checked {
//   border: 2px solid #007bff;
// }

// 添加新样式
:deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.n-card-header) {
  flex-shrink: 0;
}

:deep(.n-card__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.n-scrollbar-rail) {
  right: 2px !important;
}

:deep(.n-scrollbar-content) {
  padding-right: 14px;
}
</style>
