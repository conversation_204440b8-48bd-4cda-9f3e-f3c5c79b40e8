<script setup lang="ts">
import { ref } from 'vue';
import { urlToBase64 } from '@/utils/image';
interface Emits {
  (e: 'doTry', source_b64: string, reference_b64: string): any;
}
const emit = defineEmits<Emits>();

type Example = {
  source: string;
  reference: string;
  mark: string;
  output: string;
};
const example = ref<Example[]>([
  {
    source: '/proxy-default/data/static/mimicbrush/005_source.png',
    reference: '/proxy-default/data/static/mimicbrush/005_reference.png',
    mark: '/proxy-default/data/static/mimicbrush/005_mask.webp',
    output: '/proxy-default/data/static/mimicbrush/005_output.webp'
  },
  {
    source: '/proxy-default/data/static/mimicbrush/001_source.png',
    reference: '/proxy-default/data/static/mimicbrush/001_reference.png',
    mark: '/proxy-default/data/static/mimicbrush/001_mask.webp',
    output: '/proxy-default/data/static/mimicbrush/001_output.webp'
  },
  {
    source: '/proxy-default/data/static/mimicbrush/002_source.png',
    reference: '/proxy-default/data/static/mimicbrush/002_reference.png',
    mark: '/proxy-default/data/static/mimicbrush/002_mask.webp',
    output: '/proxy-default/data/static/mimicbrush/002_output.webp'
  }
]);
const tryExampla = async (exam: Example) => {
  const source_b64 = await urlToBase64(exam.source);
  const reference_b64 = await urlToBase64(exam.reference);
  emit('doTry', source_b64, reference_b64);
};
</script>

<template>
  <NCard title="例子" header-style="padding:10px 24px;" :bordered="false">
    <NList hoverable>
      <NListItem v-for="item in example" :key="item.source">
        <NSpace justify="center">
          <NImage object-fit="cover" :height="60" :width="60" :src="item.source"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.reference"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.mark"></NImage>
          <NImage object-fit="cover" :height="60" :width="60" :src="item.output"></NImage>
        </NSpace>
        <template #suffix>
          <NButton type="primary" size="medium" text @click.stop="tryExampla(item)">试一试</NButton>
        </template>
      </NListItem>
    </NList>
  </NCard>
</template>

<style scoped></style>
