import logging

from sqlalchemy import create_engine
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm import sessionmaker
from sqlalchemy.inspection import inspect
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from config import app_settings

logger = logging.getLogger(__name__)

# 异步数据库连接URL
SQLALCHEMY_DATABASE_URL = (
    "mysql+aiomysql://{username}:{password}@{host}:{port}/{database}".format(
        username=app_settings.db_user,
        password=app_settings.db_password,
        host=app_settings.db_host,
        port=app_settings.db_port,
        database=app_settings.db_name,
    )
)

# 同步数据库连接URL
SYNC_SQLALCHEMY_DATABASE_URL = (
    "mysql+pymysql://{username}:{password}@{host}:{port}/{database}".format(
        username=app_settings.db_user,
        password=app_settings.db_password,
        host=app_settings.db_host,
        port=app_settings.db_port,
        database=app_settings.db_name,
    )
)

# 异步引擎
engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    # echo=app_settings.debug,
    pool_recycle=60,  # SQLAlchemy 会在指定时间内回收连接，单位为秒。
    pool_size=1000,   # 最大连接数
)

# 同步引擎
sync_engine = create_engine(
    SYNC_SQLALCHEMY_DATABASE_URL,
    # echo=app_settings.debug,
    pool_recycle=60,
    pool_size=1000,
)

# 异步会话
AsyncSessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# 同步会话
SessionLocal = sessionmaker(
    bind=sync_engine,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


class Base(DeclarativeBase):

    def assign_dict(self, data):
        """
        安全地将字典数据分配给SQLAlchemy模型实例。
        只更新模型中实际存在的属性。

        :param model_instance: SQLAlchemy模型实例，如 A()
        :param data: 包含要赋值的数据的字典，如 a
        """
        # 获取模型的所有属性（即列名）
        attr_names = {c.key for c in inspect(self.__class__).mapper.column_attrs}

        # 遍历字典，仅更新存在于模型中的属性
        for key, value in data.items():
            if key in attr_names:
                setattr(self, key, value)


async def get_db():
  async with AsyncSessionLocal() as session:
    try:
      yield session
    except Exception as e:
      logger.error(f"Database session error: {e}")
      raise
    finally:
      await session.close()
