import logging
import os
import sys
import asyncio


# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.log import setup_logging
from task_queue.task_processor import task_processor, init_task_processors


from utils.config_loader import ConfigLoader

setup_logging()
logger = logging.getLogger(__name__)


# 定义应用生命周期管理器
# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     """应用生命周期管理"""
#     # 启动事件
#     logger.info("启动任务队列管理服务...")
    
#     # 初始化任务处理器
#     try:
#         init_task_processors()
#         logger.info("任务处理器初始化完成")
#     except Exception as e:
#         logger.error(f"初始化任务处理器时出错: {e}", exc_info=True)
    
#     yield  # 这里是应用运行期间
    
#     # 关闭事件
#     logger.info("正在关闭任务队列管理服务...")
    
#     # 停止所有任务处理器
#     for task_type in list(task_processor.active_processors.keys()):
#         task_processor.stop_processor(task_type)
    
#     logger.info("等待所有任务处理器完成当前任务...")
#     await asyncio.sleep(3)  # 给处理器一些时间完成当前任务
#     logger.info("任务队列管理服务已关闭")

# # 创建应用实例
# app = FastAPI(
#     title="任务队列管理服务",
#     description="处理异步任务队列的后台服务",
#     version="1.0.0",
#     lifespan=lifespan
# )


# @app.get("/health")
# async def health_check():
#     """
#     健康检查接口
#     """
#     active_processors = []
#     for task_type, is_active in task_processor.active_processors.items():
#         if is_active:
#             active_processors.append(task_type)
    
#     return {
#         "status": "running",
#         "active_processors": active_processors
#     }


# @app.get("/status")
# async def get_processor_status():
#     """
#     获取处理器状态
#     """
#     registered_handlers = list(task_processor.task_handlers.keys())
#     active_handlers = [
#         task_type for task_type, is_active in task_processor.active_processors.items()
#         if is_active
#     ]
    
#     return {
#         "registered_handlers": registered_handlers,
#         "active_handlers": active_handlers
#     }


# @app.post("/start/{task_type}")
# async def start_processor(task_type: str):
#     """
#     启动指定类型的处理器
#     """
#     if task_type not in task_processor.task_handlers:
#         return JSONResponse(
#             status_code=404,
#             content={"error": f"未找到 {task_type} 类型的处理器"}
#         )
    
#     task_processor.start_processor(task_type)
#     return {"message": f"{task_type} 处理器已启动"}


# @app.post("/stop/{task_type}")
# async def stop_processor(task_type: str):
#     """
#     停止指定类型的处理器
#     """
#     if task_type not in task_processor.active_processors or not task_processor.active_processors[task_type]:
#         return JSONResponse(
#             status_code=404,
#             content={"error": f"{task_type} 处理器未运行"}
#         )
    
#     task_processor.stop_processor(task_type)
#     return {"message": f"{task_type} 处理器将在完成当前任务后停止"}


# @app.exception_handler(Exception)
# async def global_exception_handler(request: Request, exc: Exception):
#     """全局异常处理器"""
#     logger.error(f"全局异常: {exc}", exc_info=True)
#     return JSONResponse(
#         status_code=500,
#         content={"error": f"服务内部错误: {str(exc)}"}
#     )


async def main():
    """主函数，启动任务处理器"""
    logger.info("启动任务队列管理服务...")
    
    # 初始化任务处理器
    try:
        init_task_processors()
        logger.info("任务处理器初始化完成")
    except Exception as e:
        logger.error(f"初始化任务处理器时出错: {e}", exc_info=True)
    
    # 保持程序运行
    try:
        logger.info("任务处理器正在运行...")
        while True:
            # 每60秒输出一次活跃处理器状态
            active_processors = []
            for task_type, is_active in task_processor.active_processors.items():
                if is_active:
                    active_processors.append(task_type)
                    
            logger.info(f"当前活跃的处理器: {active_processors}")
            await asyncio.sleep(60)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，准备停止服务...")
    except Exception as e:
        logger.error(f"主循环中遇到错误: {e}", exc_info=True)
    finally:
        # 停止所有任务处理器
        logger.info("正在关闭任务队列管理服务...")
        for task_type in list(task_processor.active_processors.keys()):
            task_processor.stop_processor(task_type)
        
        logger.info("等待所有任务处理器完成当前任务...")
        await asyncio.sleep(3)  # 给处理器一些时间完成当前任务
        logger.info("任务队列管理服务已关闭")

async def init():
    logger.info("在应用启动事件中初始化系统配置...")
    await ConfigLoader.initialize()
    logger.info("系统配置初始化完成")
    await main()


if __name__ == "__main__":
    # 启动主程序
    asyncio.run(init()) 