<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import VueDrawingCanvas from 'vue-drawing-canvas';
import { type UploadCustomRequestOptions, type UploadFileInfo, type UploadInst, useMessage } from 'naive-ui';
import { useIntervalFn } from '@vueuse/core';
import { isEmpty } from 'lodash-es';
import type { TourProps } from 'ant-design-vue';
import { getTaskProgress, postMimicBrushTask } from '@/service/api';
import { scaleDimensions } from '@/utils/common';
import ExampleCard from './modules/example-card.vue';
// import { localforage } from '@/utils/storage';

const message = useMessage();
const VueCanvasDrawing = ref();
const loading = ref(false);
const shotTooltip = ref(false);
const collapsedExample = ref(false);

const uploadRef = ref<UploadInst>();
const referenceUploadRef = ref<UploadInst>();

const referenceDefaultFileList = ref<UploadFileInfo[]>([]);

// tour 引导相关
const uploadwrap = ref(null);
const referenceUploadWrap = ref(null);
const dashRef = ref(null);
const eraserRef = ref(null);
const lineWithRef = ref(null);
const controllerRef = ref(null);
const postTaskRef = ref(null);
const outputRef = ref(null);
const exampleCardRef = ref(null);

const delOriginImageRef = ref(null);
const openTour = ref<boolean>(false);

const currentTour = ref(0);
const handleOpenTour = (val: boolean): void => {
  currentTour.value = 0;
  openTour.value = val;
  collapsedExample.value = false;
};
const steps: TourProps['steps'] = [
  {
    title: '上传源图',
    description: '上传一张要编辑的图片',
    placement: 'right',
    target: () => uploadwrap.value
  },
  {
    title: '上传参考图',
    description: '上传一张要取样的参考图',
    target: () => referenceUploadWrap.value
  },
  {
    title: '选择画笔 或者 橡皮 ',
    description: '在要编辑的图画出要编辑的区域 ,或者橡皮擦除区域',
    target: () => dashRef.value
  },
  {
    title: '调整笔刷大小',
    description: '手动滑块，调整笔刷大小',
    target: () => lineWithRef.value
  },
  {
    title: '画出要编辑的区域',
    description: '用画笔，随意画出要编辑的区域',
    placement: 'right',
    target: () => uploadwrap.value
  },
  {
    title: '调整参数',
    target: () => controllerRef.value
  },
  {
    title: '点击生成图片',
    description: '提交任务到后台',
    target: () => postTaskRef.value
  },
  {
    title: '参考例子',
    description: '从参考例子展示工作过程，也可以依照例子试一试',
    target: () => exampleCardRef.value
  }
];

// 画板中默认显示的图片路径
const showImgSrc = '';
const option = reactive({
  scale: 1,
  maxWidth: 512,
  width: 512,
  height: 512,
  image: '',
  eraser: false,
  disabled: false,
  fillShape: false,
  line: 40,
  color: 'rgba(253 ,56, 109 ,.5)',
  strokeType: 'dash',
  lineCap: 'round',
  lineJoin: 'round',
  backgroundColor: 'rgba(255,255,255,0)',
  backgroundImage: showImgSrc,
  watermark: null,
  additionalImages: [],
  initialImage: []
});
const taskRes = ref<Api.Media.MimicBrushTaskRes>({
  prompt_id: '',
  number: 0,
  node_errors: {}
});
const taskImageRes = ref<string[]>([
  // '/proxy-default/data/static/mimicbrush/005_source.png',
  // '/proxy-default/data/static/mimicbrush/005_source.png'
  // 'http://************:8965/view?filename=ComfyUI_temp_hlkzc_00121_.png&type=temp'
]);
const hasOriginImage = computed(() => {
  return option.backgroundImage !== '';
});

const createDefaultModel = (): Api.Media.MimicBrush => {
  return {
    source_b64: '',
    reference_b64: '',
    mask_b64: '',
    step: 50,
    seed: -1,
    guidance_scale: 5, // -30 -> 30
    if_keep_shape: false
  };
};
const model: Api.Media.MimicBrush = reactive(createDefaultModel());

const initVueDraw = async (base64String: string) => {
  option.backgroundImage = '';
  await nextTick();
  const img = new Image();
  img.src = base64String as string;
  img.onload = () => {
    option.width = img.width;
    option.height = img.height;
    const dim = scaleDimensions(img.width, img.height, 512, 512);
    option.scale = dim.scale;

    option.backgroundImage = base64String as string;

    // 如果是在引导状态
    if (openTour.value) {
      currentTour.value++;
    }
  };
};
const customRequestReference = async ({ file }: UploadCustomRequestOptions) => {
  console.log('customRequestReference');
  if (file.file) {
    const reader = new FileReader();
    reader.onloadend = () => {
      model.reference_b64 = reader.result as string;

      referenceDefaultFileList.value = [
        {
          id: 'reference_b64',
          name: 'reference_b64',
          status: 'finished',
          url: model.reference_b64
        }
      ];

      // 如果是在引导状态
      if (openTour.value) {
        currentTour.value++;
      }
    };
    reader.readAsDataURL(file.file);
  }
  // uploadRef.value?.clear();
};
const customRequestOriginImage = async ({ file }: UploadCustomRequestOptions) => {
  console.log('customRequestOriginImage');
  if (file.file) {
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result;
      initVueDraw(base64String as string);
    };
    reader.readAsDataURL(file.file);
  }
};

type Coordinates = {
  x: number;
  y: number;
};

const {
  pause: stop,
  resume: start,
  isActive
} = useIntervalFn(
  () => {
    if (!taskRes.value.prompt_id) {
      stop();
      return;
    }
    getTaskProgress(taskRes.value.prompt_id).then(res => {
      if (isEmpty(res.data)) {
        console.log('isEmpty start');
      } else {
        const taskGropressRes = res.data[taskRes.value.prompt_id];
        if (taskGropressRes.status.completed) {
          console.log('成功', res.data);
          taskImageRes.value = [];
          taskGropressRes.outputs['4'].images.forEach(e => {
            const url = `http://************:8965/view?filename=${e.filename}&type=${e.type}`;
            taskImageRes.value.push(url);
            console.log(url);
          });
          // 处理结果
          stop();
        } else if (taskGropressRes.status.status_str === 'error') {
          message.error('任务失败');
          stop();
        }
      }
    });
  },
  2000,
  { immediate: false }
);

const postTask = () => {
  console.clear();
  if (!VueCanvasDrawing.value) {
    message.error('请先上传源图');
  }
  // console.log(option);
  console.log(VueCanvasDrawing.value);
  const originalCanvas = VueCanvasDrawing.value.$el;

  const ctx = VueCanvasDrawing.value.context;

  if (VueCanvasDrawing.value) {
    // 创建一个新的

    // 用于复制原画布
    const copyCanvas = document.createElement('canvas');
    copyCanvas.width = originalCanvas.width;
    copyCanvas.height = originalCanvas.height;

    // 处理画笔
    const dashCanvas = document.createElement('canvas');
    dashCanvas.width = originalCanvas.width;
    dashCanvas.height = originalCanvas.height;
    // dashCanvas.style.backgroundColor = 'transparent';
    dashCanvas.style.overflow = 'hidden';

    const copyCtx = copyCanvas.getContext('2d');

    const dashCtx = dashCanvas.getContext('2d');

    if (!copyCtx) {
      console.log("copyCtx.getContext('2d') false");
      return;
    }
    if (!dashCtx) {
      console.log("dashCtx.getContext('2d') false");
      return;
    }
    // 填充,复制原来的画布
    // copyCtx?.drawImage(originalCanvas, 0, 0);
    // copyCtx.globalCompositeOperation = 'destination-out';
    // 设置填充颜色为白色
    copyCtx.fillStyle = '#FFFFFF';
    // 填充整个画布
    copyCtx.fillRect(0, 0, copyCanvas.width, copyCanvas.height);

    const strokes = VueCanvasDrawing.value.getAllStrokes();
    // console.log(strokes);
    strokes.forEach((e: any) => {
      // e.type="dash" //画笔
      // e.type="eraser"//橡皮
      if (e.type === 'eraser') {
        dashCtx.globalCompositeOperation = 'destination-out';
      } else if (e.type === 'dash') {
        dashCtx.globalCompositeOperation = 'source-over';
      }
      // if (e.type === 'eraser') {
      //   console.log('eraser');
      // } else {
      // e.width
      dashCtx.lineWidth = e.width;
      dashCtx.strokeStyle = '#fff';
      dashCtx.lineCap = e.lineCap;
      dashCtx.lineJoin = e.lineJoin;
      dashCtx.beginPath();
      dashCtx.moveTo(e.from.x, e.from.y);

      e.coordinates.forEach((coordinates: Coordinates) => {
        dashCtx.lineTo(coordinates.x, coordinates.y);
      });
      dashCtx.stroke();
    });

    dashCtx.clip();

    // copy画布上应用所有画笔，扣掉画过的地方

    copyCtx.globalCompositeOperation = 'destination-out';
    // copyCtx.imageSmoothingEnabled = false; // 禁用图像平滑以确保像素感
    // copyCtx?.drawImage(dashCanvas, 0, 0, copyCanvas.width, copyCanvas.height, 0, 0, 1024, 1024);
    copyCtx?.drawImage(dashCanvas, 0, 0);
    // 缩放为源图片大小

    // copyCtx.translate(0, 0);
    // copyCtx.scale(10, 10);

    console.log('imageData');
    const imageData = copyCanvas.toDataURL('image/png', 1);
    // model.source_b64 = imageData;
    model.source_b64 = option.backgroundImage;
    model.mask_b64 = imageData;

    if (model.source_b64 === '') {
      message.error('未上传源图');
    }
    if (model.reference_b64 === '') {
      message.error('未上传参考图');
    }
    taskImageRes.value = [];
    collapsedExample.value = true;
    loading.value = true;
    postMimicBrushTask(model)
      .then(res => {
        if (res.data) {
          taskRes.value = res.data;
          start();
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 恢复画笔
  ctx.globalCompositeOperation = 'source-over';
};
const resetAll = () => {
  option.backgroundImage = '';
  referenceDefaultFileList.value = [];
  Object.assign(model, createDefaultModel());
};

onMounted(() => {
  // const check = localforage.getItem('mimicbrush_tour');
  // if(!check){
  //   check
  // }
  nextTick(() => {
    shotTooltip.value = true;
    setTimeout(() => {
      shotTooltip.value = false;
    }, 5000);
  });
});
const doTry = async (source_b64: string, reference_b64: string) => {
  console.log('doTry');
  option.backgroundImage = '';
  nextTick(() => {
    option.backgroundImage = source_b64;
    model.reference_b64 = reference_b64;

    initVueDraw(source_b64);

    referenceDefaultFileList.value = [
      {
        id: 'reference_b64',
        name: 'reference_b64',
        status: 'finished',
        url: reference_b64
      }
    ];
  });
};
const removeReference = () => {
  referenceDefaultFileList.value = [];
  return true;
};
const beforeUploadReference = (options: { file: UploadFileInfo }) => {
  console.log('beforeUploadReference');
  referenceDefaultFileList.value = [];
  if (options.file && options.file.file) {
    const reader = new FileReader();
    reader.onloadend = () => {
      model.reference_b64 = reader.result as string;

      referenceDefaultFileList.value = [
        {
          id: 'reference_b64',
          name: 'reference_b64',
          status: 'finished',
          url: model.reference_b64
        }
      ];

      // 如果是在引导状态
      if (openTour.value) {
        currentTour.value++;
      }
    };
    reader.readAsDataURL(options.file.file);
  }
  return true;
};
// const updateCollapsed = (value: boolean): void => {
//   collapsedExample.value = value;
// };
const guideTooltipUpdate = (value: boolean): void => {
  shotTooltip.value = value;
};
</script>

<template>
  <div class="flex gap-x-12px">
    <ATour v-model:current="currentTour" :open="openTour" :steps="steps" @close="handleOpenTour(false)" />
    <NCard class="h-full w-560px" content-style="" header-style="padding:10px 24px;" title="Mimic Brush">
      <template #header-extra>
        <div class="flex items-center">
          <NTooltip
            :show="shotTooltip"
            placement="top"
            trigger="hover"
            keep-alive-on-hover
            :on-update:show="guideTooltipUpdate"
          >
            <span>新手教程</span>
            <template #trigger>
              <NButton circle class="mr-5" @click="handleOpenTour(true)">
                <SvgIcon icon="mdi:question-mark" />
              </NButton>
            </template>
          </NTooltip>
          <div ref="dashRef" class="">
            <NButton circle :type="option.eraser ? 'default' : 'warning'" @click="option.eraser = false">
              <SvgIcon icon="icon-park-outline:writing-fluently" />
            </NButton>
            <NButton
              ref="eraserRef"
              circle
              :type="option.eraser ? 'warning' : 'default'"
              class="ml-1"
              @click="option.eraser = true"
            >
              <SvgIcon icon="oui:eraser" />
            </NButton>
          </div>
          <div ref="lineWithRef" class="mx-2">
            <NSlider v-model:value="option.line" :step="1" :min="1" :max="100" class="w-40" />
          </div>
        </div>
        <NButton ref="delOriginImageRef" circle @click.stop="resetAll">
          <SvgIcon icon="carbon:reset" />
        </NButton>
      </template>
      <NAlert v-if="!hasOriginImage" :show-icon="false" type="info" class="my-3">
        该功能允许用户通过简单操作在源图像上指定需要编辑的区域，并提供一个参考图像来实现期望的效果。它支持对象替换、样式转换、纹理调整等多种图像编辑操作，让用户轻松实现所需的图像修改
        点击上传源图
      </NAlert>

      <div ref="uploadwrap" class="h-auto max-h-512px flex items-center justify-center">
        <NUpload
          v-if="!hasOriginImage"
          ref="uploadRef"
          class="custom-upload"
          action=""
          list-type="image-card"
          :multiple="false"
          :max="1"
          :custom-request="customRequestOriginImage"
          accept=".jpg,.jpeg,.png"
        ></NUpload>
        <VueDrawingCanvas
          v-if="hasOriginImage"
          ref="VueCanvasDrawing"
          v-model:image="option.image"
          class="cursor-crosshair"
          :style="{
            transform: `scale(${option.scale})`
          }"
          :width="option.width"
          :height="option.height"
          :stroke-type="option.strokeType"
          :line-cap="option.lineCap"
          :line-join="option.lineJoin"
          :fill-shape="option.fillShape"
          :eraser="option.eraser"
          :line-width="option.line"
          :color="option.color"
          :background-image="option.backgroundImage"
          save-as="png"
          :lock="option.disabled"
          :initial-image="option.initialImage"
          :additional-images="option.additionalImages"
        />
      </div>
      <div class="flex justify-between py-5">
        <div ref="controllerRef">
          <NForm :show-feedback="false" label-placement="top">
            <NGrid x-gap="10" class="max-w-full">
              <NFormItemGi span="12" label="步骤">
                <NSlider v-model:value="model.step" :step="1" :min="0" :max="100" />
              </NFormItemGi>
              <NFormItemGi span="12" label="指导量">
                <NSlider v-model:value="model.guidance_scale" :step="1" :min="-30" :max="30" />
              </NFormItemGi>
              <NFormItemGi span="12" label="种子">
                <NSlider v-model:value="model.seed" :step="1" :min="-1" :max="999999999" />
              </NFormItemGi>
              <NFormItemGi span="12" label="保护外形">
                <NSwitch v-model:value="model.if_keep_shape" />
              </NFormItemGi>
            </NGrid>
          </NForm>
        </div>
        <div ref="referenceUploadWrap" class="items-center">
          <NUpload
            ref="referenceUploadRef"
            class=""
            action=""
            list-type="image-card"
            :multiple="false"
            :max="1"
            :custom-request="customRequestReference"
            accept=".jpg,.jpeg,.png"
            :file-list="referenceDefaultFileList"
            :on-remove="removeReference"
            :on-before-upload="beforeUploadReference"
          >
            参考图
          </NUpload>
        </div>
      </div>

      <template #footer>
        <div ref="postTaskRef">
          <NButton type="primary" class="h-60px w-full" :loading="isActive || loading" size="large" @click="postTask">
            生成
          </NButton>
        </div>
      </template>
    </NCard>
    <div class="flex-row-stretch flex-1">
      <NCard ref="outputRef" class="h-3/5" title="输出" :bordered="false" header-style="padding:10px 24px;">
        <div v-if="isActive" class="loading-container">
          <div class="outer-circle">
            <div class="inner-circle"></div>
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="my-5">任务进行中</div>
        </div>
        <div v-if="!isActive" class="flex flex-1 items-center justify-center overflow-hidden">
          <NImage class="h-400px" :src="taskImageRes[0]" />
        </div>
        <!--
 <template #footer>
          <div class="flex justify-center gap-5">
            <NImageGroup>
              <NImage v-for="url in taskImageRes" :key="url" :width="35" :height="35" :src="url" object-fit="cover" />
            </NImageGroup>
          </div>
        </template>
-->
      </NCard>
      <div ref="exampleCardRef">
        <ExampleCard class="mt-2 h-2/5 overflow-auto" @do-try="doTry" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#g-pointer {
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  background: #000;
  border-radius: 50%;
}
:deep(.custom-upload) {
  .n-upload-file-list {
    aspect-ratio: 16/9 !important;
    height: 80% !important;
    grid-template-columns: unset;
    .n-upload-file,
    .n-upload-trigger {
      width: 100% !important;
      height: 100% !important;
      // height: 150px !important;
      // aspect-ratio: 1 !important;
    }
    .n-upload-file {
      .n-upload-file-info {
        .n-upload-file-info__thumbnail {
          .n-image {
            img {
              object-fit: cover !important;
            }
          }
        }
      }
    }
  }
}

// loading

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  flex-direction: column;
}

.outer-circle {
  position: relative;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
  animation: rotate 1.5s linear infinite;
}

.outer-circle span {
  position: absolute;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
}

.outer-circle span:nth-child(1) {
  filter: blur(5px);
}

.outer-circle span:nth-child(2) {
  filter: blur(10px);
}

.outer-circle span:nth-child(3) {
  filter: blur(25px);
}

.outer-circle span:nth-child(4) {
  filter: blur(150px);
}

.inner-circle {
  height: 180px;
  width: 180px;
  position: absolute;
  background: black;
  top: 10px;
  left: 10px;
  border-radius: 50%;
  z-index: 9;
}

@keyframes rotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}
</style>
