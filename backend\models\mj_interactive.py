from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, DateTime, SmallInteger, ForeignKey
from sqlalchemy.orm import relationship
from utils.database import Base
from datetime import datetime


class MjInteractive(Base):
  __tablename__ = "mj_interactive"

  id = Column(Integer, primary_key=True, index=True, autoincrement=True)
  user_id = Column(Integer, nullable=False, comment="用户ID")
  taskid = Column(Integer, nullable=False, comment="对应的mj数据ID")
  is_like = Column(SmallInteger, nullable=False, comment="是否点赞：0 或 1")
  create_time = Column(DateTime, default=datetime.utcnow, nullable=True, comment="记录创建时间")
  update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True,
                       comment="记录更新时间")

