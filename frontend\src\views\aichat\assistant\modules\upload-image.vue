<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui';
import { ref, watch } from 'vue';
import type { Ref } from 'vue';
import { getImageData } from '@/utils/uploadimage';

defineProps<{
  isMobile?: boolean;
}>();

const emit = defineEmits<{
  (event: 'change', args: string[]): void;
}>();
const fileList = ref<Array<UploadFileInfo>>([]);
const uploader = ref<Ref | null>(null);

const openFileDialog = function openFileDialog() {
  uploader.value?.clear();
  uploader.value?.openOpenFileDialog();
};

watch(fileList, (n, __o) => {
  if (n.length === 0) {
    emit('change', []);
  } else {
    const file = n[0];
    if (file.file) {
      getImageData(file.file)
        .then(base64 => {
          emit('change', [base64 as string]);
        })
        .catch(error => {
          window.console.log(error);
          fileList.value = [];
        });
    }
  }
});

defineExpose({
  openFileDialog,
  clear() {
    // uploader.value?.clear()
    fileList.value = [];
  },

  // 粘贴图片
  async changeFile(file: File) {
    // uploader.value?.clear()
    fileList.value = [
      {
        id: 'file',
        name: file.name,
        status: 'finished',
        url: URL.createObjectURL(file),
        file
      }
    ];
  }
});
</script>

<template>
  <NUpload
    ref="uploader"
    v-model:file-list="fileList"
    class="image-uploader py-3"
    :class="{ 'aichat-image-uploader': isMobile }"
    :list-type="isMobile ? 'image' : 'image-card'"
    accept="image/png, image/jpeg"
    :max="1"
    :show-trigger="false"
  >
    点击上传
  </NUpload>
</template>

<style>
.aichat-image-uploader {
  & .n-upload-file-list .n-upload-file {
    background-color: var(--n-item-color-hover);
  }

  & .n-upload-file-list .n-upload-file .n-upload-file-info .n-upload-file-info__action {
    opacity: 1;
  }

  & .n-upload-file-info__thumbnail {
    overflow: hidden;
  }
}
</style>
