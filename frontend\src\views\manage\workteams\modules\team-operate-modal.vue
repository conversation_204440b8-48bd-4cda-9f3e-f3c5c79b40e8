<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { postSaveTeam } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'TeamOperateModal'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Teams | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增',
    edit: '编辑'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.SystemManage.Teams, 'id' | 'team_name' | 'team_code' | 'email'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    team_name: '',
    team_code: '',
    email: ''
  };
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData, { status: `${props.rowData.status}` });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // request
  postSaveTeam(model).then(() => {
    window.$message?.success($t('common.updateSuccess'));
    closeDrawer();
    emit('submitted');
  });
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    display-directive="show"
    :width="360"
    :title="title"
    preset="dialog"
    :native-scrollbar="false"
    closable
    :positive-text="$t('common.confirm')"
    :negative-text="$t('common.cancel')"
    class="min-w-1000px"
    @positive-click="handleSubmit"
    @negative-click="closeDrawer"
  >
    <NScrollbar class="max-h-500px p-10" :size="5" :x-scrollable="false">
      <NForm ref="formRef" :model="model" label-placement="left" label-align="right" label-width="80">
        <NFormItem label="团队名称" path="team_name">
          <NInput v-model:value="model.team_name" placeholder="团队名称" />
        </NFormItem>
        <NFormItem label="团队编码" path="team_code">
          <NInput v-model:value="model.team_code" placeholder="团队编码" />
        </NFormItem>
        <NFormItem label="邮箱" path="email">
          <NInput v-model:value="model.email" placeholder="邮箱" />
        </NFormItem>
      </NForm>
    </NScrollbar>
  </NModal>
</template>

<style scoped></style>
