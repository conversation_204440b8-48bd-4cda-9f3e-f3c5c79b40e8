<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { useMessage } from 'naive-ui';
import useClipboard from 'vue-clipboard3'; // 添加剪贴板工具

import { getMyAssets, toggleCollect } from '@/service/api/assets';
import { downloadFile } from '@/utils/common';
import AssetInfo from '@/views/asset-manage/asset-info/index.vue'; // 导入AssetInfo组件

// 创建消息实例
const message = useMessage();
// 添加剪贴板工具
const { toClipboard } = useClipboard();

// 类型定义
interface AssetItem {
  // id: number;
  type: 'IMAGE' | 'VIDEO' | 'AUDIO';
  url: string;
  // user_id: number;
  create_time: string;
  iscollect: number;
  parameter: Record<string, any> | null;
  taskid: string | null;
}

interface AssetGroup {
  timeLabel: string;
  date: string;
  assets: AssetItem[];
}

// URL 解析工具函数
function parseAssetUrl(url: string): string {
  // 检查是否为 JSON 数组字符串格式
  if (url.startsWith('[') && url.endsWith(']')) {
    try {
      const urls = JSON.parse(url) as string[];
      return urls[0] || url;
    } catch (error) {
      console.warn('URL 解析失败，使用原始值:', url, error);
      return url;
    }
  }
  return url;
}

// 规范化URL函数，去除查询参数等
function normalizeUrl(url: string): string {
  try {
    // 提取URL的主要部分，去除查询参数
    const baseUrl = url.split('?')[0];
    return baseUrl;
  } catch (error) {
    console.warn('URL规范化失败:', url, error);
    return url;
  }
}

// 媒体类型检测工具函数
function detectMediaTypeFromUrl(url: string): 'IMAGE' | 'VIDEO' {
  if (!url) return 'IMAGE';

  const lowerUrl = url.toLowerCase();

  // 视频文件扩展名
  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mkv'];

  // 检查URL是否包含视频扩展名
  const isVideo = videoExtensions.some(ext => lowerUrl.includes(ext));

  return isVideo ? 'VIDEO' : 'IMAGE';
}

// 响应式状态管理
const activeTab = ref<string>('image');
const currentPage = ref<number>(1);
const pageSize = ref<number>(20);
const loading = ref<boolean>(false);
const hasMore = ref<boolean>(true);
const assetGroups = ref<AssetGroup[]>([]);
const allAssets = ref<AssetItem[]>([]);
// 仅显示收藏的状态
const showCollectionOnly = ref<boolean>(false);
// 下载状态
const downloading = ref<boolean>(false);

// 鼠标悬停状态
const hoveredAssetUrl = ref<string | null>(null);

// 当前分享数据，用于传递给AssetInfo组件
const currentShareData = ref<any>(null);
// 模态框显示状态
const showAssetInfoModal = ref(false);

// 设置悬停资产
function setHoveredAsset(assetUrl: string | null): void {
  hoveredAssetUrl.value = assetUrl;
}

// 统一的参考图片提取函数
function extractPromptImages(parameter: Record<string, any> | null): string[] {
  if (!parameter) return [];

  let promptImgArray: string[] = [];

  try {
    // 优先检查 parameter.prompt_img（保持向后兼容）
    if (parameter.prompt_img) {
      if (typeof parameter.prompt_img === 'string') {
        try {
          const parsed = JSON.parse(parameter.prompt_img);
          promptImgArray = processParsedPromptImg(parsed);
        } catch (e) {
          // 解析失败，将字符串作为单个元素
          promptImgArray = [parameter.prompt_img];
        }
      } else if (Array.isArray(parameter.prompt_img)) {
        // 处理数组中可能包含JSON字符串的情况
        promptImgArray = processParsedPromptImg(parameter.prompt_img);
      }
      console.debug('从prompt_img提取到参考图片:', promptImgArray.length, '张', promptImgArray);
    }

    // 如果没有找到prompt_img，检查 parameter.task_params.image_base64（适配视频数据）
    if (promptImgArray.length === 0 && parameter.task_params?.image_base64) {
      const imageBase64 = parameter.task_params.image_base64;
      if (typeof imageBase64 === 'string') {
        try {
          // 尝试解析为JSON数组
          const parsed = JSON.parse(imageBase64);
          promptImgArray = Array.isArray(parsed) ? parsed : [imageBase64];
        } catch (e) {
          // 解析失败，将字符串作为单个元素
          promptImgArray = [imageBase64];
        }
      } else if (Array.isArray(imageBase64)) {
        promptImgArray = imageBase64;
      } else if (!imageBase64) {
        // 其他类型转为字符串（确保不为null或undefined）
        promptImgArray = [String(imageBase64)];
      }
      console.debug('从task_params.image_base64提取到参考图片:', promptImgArray.length, '张');
    }

    // 过滤空值和无效URL
    const filteredArray = promptImgArray.filter(url => {
      return url && typeof url === 'string' && url.trim().length > 0;
    });

    console.debug('参考图片提取完成，有效图片数量:', filteredArray.length);
    return filteredArray;
  } catch (error) {
    console.error('提取参考图片时发生错误:', error);
    return [];
  }
}

// 新增: 辅助函数，用于处理解析后的JSON数据
function processParsedPromptImg(parsed: any): string[] {
  if (Array.isArray(parsed)) {
    // 处理嵌套的JSON字符串
    const flattened: string[] = [];
    parsed.forEach(item => {
      if (typeof item === 'string') {
        try {
          const innerParsed = JSON.parse(item);
          if (Array.isArray(innerParsed)) {
            flattened.push(...innerParsed);
          } else {
            flattened.push(innerParsed.toString());
          }
        } catch (e) {
          flattened.push(item);
        }
      } else {
        flattened.push(item.toString());
      }
    });
    return flattened;
  }
  return [parsed.toString()];
}

// 图片点击处理函数
function handleImageClick(asset: AssetItem): void {
  try {
    // 构建分享参数
    const params: Record<string, any> = {
      prompt: ''
    };

    // 处理 parameter 中的 prompt
    if (asset.parameter?.prompt) {
      params.prompt = asset.parameter.prompt;
    }

    // 使用统一的参考图片提取函数
    const promptImgArray = extractPromptImages(asset.parameter);
    if (promptImgArray.length > 0) {
      params.prompt_img = promptImgArray;
    }

    // 构建结果数据
    const result = {
      url: asset.url
    };

    // 构建临时分享数据
    const shareData = {
      type: activeTab.value.toUpperCase(), // 使用当前选中的标签页类型
      prompt: params.prompt,
      prompt_img: params.prompt_img,
      result,
      taskid: asset.taskid // 添加taskid信息
    };

    // 构建URL编码后的参数
    const encodedParams = encodeURIComponent(JSON.stringify(shareData));

    // 保存当前分享数据并显示模态框
    currentShareData.value = {
      temp: 'true',
      data: encodedParams
    };

    // 显示模态框
    showAssetInfoModal.value = true;
  } catch (error) {
    console.error('打开资产详情失败:', error);
  }
}

// 视频点击处理函数
function handleVideoClick(asset: AssetItem): void {
  try {
    // 构建分享参数
    const params: Record<string, any> = {
      prompt: ''
    };

    // 处理 parameter 中的 prompt
    if (asset.parameter?.prompt) {
      params.prompt = asset.parameter.prompt;
    }

    // 使用统一的参考图片提取函数
    const promptImgArray = extractPromptImages(asset.parameter);
    if (promptImgArray.length > 0) {
      params.prompt_img = promptImgArray;
    }

    // 构建结果数据
    const result = {
      url: asset.url
    };

    // 构建临时分享数据
    const shareData = {
      type: activeTab.value.toUpperCase(), // 使用当前选中的标签页类型
      prompt: params.prompt,
      prompt_img: params.prompt_img,
      result,
      taskid: asset.taskid // 添加taskid信息
    };

    // 构建URL编码后的参数
    const encodedParams = encodeURIComponent(JSON.stringify(shareData));

    // 保存当前分享数据并显示模态框
    currentShareData.value = {
      temp: 'true',
      data: encodedParams
    };

    // 显示模态框
    showAssetInfoModal.value = true;
  } catch (error) {
    console.error('打开资产详情失败:', error);
  }
}

// 下载文件函数
async function handleDownload(asset: AssetItem, event: MouseEvent): Promise<void> {
  // 阻止事件冒泡，防止触发父级点击事件
  event.stopPropagation();

  if (!asset.url) {
    message.error('下载地址无效');
    return;
  }

  downloading.value = true;
  try {
    // 使用通用下载函数，统一使用fetch方式下载
    const fileType = isVideoAsset(asset) ? 'video' : 'image';
    const ext = asset.url.split('.').pop()?.split('?')[0] || (isVideoAsset(asset) ? 'mp4' : 'png');
    const customFileName = `${fileType}.${ext}`;

    await downloadFile(asset.url, customFileName);
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error('下载失败');
  } finally {
    downloading.value = false;
  }
}

// 分享资产函数（作为集中处理函数）
function handleShare(asset: AssetItem, event: MouseEvent): void {
  // 阻止事件冒泡
  event.stopPropagation();

  try {
    // 构建分享参数
    const params: Record<string, any> = {
      prompt: ''
    };

    // 处理 parameter 中的 prompt
    if (asset.parameter?.prompt) {
      params.prompt = asset.parameter.prompt;
    }

    // 使用统一的参考图片提取函数
    const promptImgArray = extractPromptImages(asset.parameter);
    if (promptImgArray.length > 0) {
      params.prompt_img = promptImgArray;
    }

    // 构建结果数据
    const result = {
      url: asset.url
    };

    // 构建临时分享数据
    const shareData = {
      type: activeTab.value.toUpperCase(), // 使用当前选中的标签页类型
      prompt: params.prompt,
      prompt_img: params.prompt_img,
      result,
      taskid: asset.taskid // 添加taskid信息
    };

    // 构建URL编码后的参数
    const encodedParams = encodeURIComponent(JSON.stringify(shareData));

    // 构建指向share-page的链接
    const baseUrl = `${window.location.origin}/share-page`;
    const shareUrl = `${baseUrl}?temp=true&data=${encodedParams}`;

    // 复制链接到剪贴板
    toClipboard(shareUrl)
      .then(() => {
        message.success('分享链接已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败');
      });
  } catch (error) {
    console.error('分享资产失败:', error);
    message.error('分享失败');
  }
}

// 视频悬停播放处理函数
function handleVideoMouseenter(event: Event): void {
  const video = event.target as HTMLVideoElement;
  if (video && video.tagName === 'VIDEO') {
    video.play().catch(error => {
      console.warn('视频播放失败:', error);
    });
  }
}

// 视频离开暂停处理函数
function handleVideoMouseleave(event: Event): void {
  const video = event.target as HTMLVideoElement;
  if (video && video.tagName === 'VIDEO') {
    video.pause();
    video.currentTime = 0; // 重置到开始位置
  }
}

// 视频加载错误处理函数
function handleVideoError(event: Event): void {
  const video = event.target as HTMLVideoElement;
  console.error('视频加载失败:', video.src);
  // 可以在这里添加错误统计逻辑
}

// 时间格式化函数
function formatTimeLabel(dateStr: string): string {
  const targetDate = dayjs(dateStr);
  const today = dayjs();
  const currentYear = today.year();

  // 判断是否为今天
  if (targetDate.isSame(today, 'day')) {
    return '今天';
  }

  // 判断是否为本年
  if (targetDate.year() === currentYear) {
    return targetDate.format('M月D日');
  }

  // 不同年份
  const year = targetDate.year() % 100; // 获取年份后两位
  return `${year}年${targetDate.format('M月D日')}`;
}

// 数据分组函数
function groupAssetsByTime(assets: AssetItem[]): AssetGroup[] {
  const groups: { [key: string]: AssetGroup } = {};

  // 确保在分组过程中也进行去重
  const processedUrls = new Set<string>();

  // 先过滤掉AUDIO类型资产，再进行后续处理
  assets
    .filter(asset => asset.type !== 'AUDIO')
    .forEach(asset => {
      // 规范化URL用于去重检查
      const normalizedUrl = normalizeUrl(asset.url);

      // 如果该URL已处理过，则跳过
      if (processedUrls.has(normalizedUrl)) {
        return;
      }

      // 将URL标记为已处理
      processedUrls.add(normalizedUrl);

      const dateKey = dayjs(asset.create_time).format('YYYY-MM-DD');
      const timeLabel = formatTimeLabel(asset.create_time);

      if (!groups[dateKey]) {
        groups[dateKey] = {
          timeLabel,
          date: dateKey,
          assets: []
        };
      }

      groups[dateKey].assets.push(asset);
    });

  // 记录分组后去重结果
  console.debug(`分组处理：原始资产 ${assets.length}个，去重后 ${processedUrls.size}个`);

  // 按日期倒序排列
  return Object.values(groups).sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf());
}

// 数据加载函数
async function loadAssets(page: number = 1, append: boolean = false): Promise<void> {
  if (loading.value) return;

  loading.value = true;

  try {
    // 确保当 showCollectionOnly 为 false 时，传递 undefined 而不是 0
    // 这样后端将返回所有资产，而不仅仅是未收藏的资产
    const response = await getMyAssets(
      page,
      pageSize.value,
      activeTab.value.toUpperCase() === 'ALL' ? 'ALL' : activeTab.value.toUpperCase(),
      showCollectionOnly.value ? 1 : undefined
    );

    // 防御性编程：支持两种可能的数据结构
    let records: AssetItem[] = [];
    if (response.data) {
      if (response.data.records) {
        // 直接访问 records
        records = response.data.records;
      } else if (response.data.data && response.data.data.records) {
        // 嵌套访问 data.records
        records = response.data.data.records;
      }
    }

    if (records && records.length >= 0) {
      // 处理资产 URL，确保格式正确，并过滤掉类型为AUDIO的资产
      const processedRecords = records
        .filter(asset => asset.type !== 'AUDIO') // 跳过处理AUDIO类型的资产
        .map(asset => ({
          ...asset,
          url: parseAssetUrl(asset.url)
        }));

      if (append) {
        // 创建现有URL规范化集合用于去重（使用规范化URL）
        const existingNormalizedUrls = new Set(allAssets.value.map(asset => normalizeUrl(asset.url)));

        // 过滤掉已存在URL的资产（基于规范化URL比较）
        const uniqueNewRecords = processedRecords.filter(asset => !existingNormalizedUrls.has(normalizeUrl(asset.url)));

        // 记录去重统计
        console.debug(`追加数据：${processedRecords.length}条，规范化URL去重后：${uniqueNewRecords.length}条`);

        // 只添加唯一的新记录
        allAssets.value.push(...uniqueNewRecords);
      } else {
        // 初始加载时也进行URL去重
        const uniqueUrls = new Set<string>();
        const uniqueRecords = processedRecords.filter(asset => {
          const normalizedUrl = normalizeUrl(asset.url);
          if (uniqueUrls.has(normalizedUrl)) {
            return false;
          }
          uniqueUrls.add(normalizedUrl);
          return true;
        });

        console.debug(`初始加载：${processedRecords.length}条，规范化URL去重后：${uniqueRecords.length}条`);
        allAssets.value = uniqueRecords;
      }

      // 重新分组
      assetGroups.value = groupAssetsByTime(allAssets.value);

      // 判断是否还有更多数据
      hasMore.value = records.length === pageSize.value;

      if (append && records.length > 0) {
        currentPage.value = page;
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error('加载资产失败:', error);
    hasMore.value = false;
  } finally {
    loading.value = false;
  }
}

// 无限滚动处理函数
function handleInfiniteScroll(): void {
  if (hasMore.value && !loading.value) {
    loadAssets(currentPage.value + 1, true);
  }
}

// tab切换监听
watch(activeTab, () => {
  // 重置状态
  currentPage.value = 1;
  allAssets.value = [];
  assetGroups.value = [];
  hasMore.value = true;
  // 加载新数据
  loadAssets(1, false);
});

// 添加：收藏过滤监听
watch(showCollectionOnly, () => {
  // 重置状态
  currentPage.value = 1;
  allAssets.value = [];
  assetGroups.value = [];
  hasMore.value = true;
  // 加载新数据
  loadAssets(1, false);
});

// 判断资产类型函数
function isVideoAsset(asset: AssetItem): boolean {
  // 首先根据资产的类型字段判断
  if (asset.type === 'VIDEO') return true;

  // 如果没有明确的类型字段或类型字段不准确，则通过URL进行判断
  return detectMediaTypeFromUrl(asset.url) === 'VIDEO';
}

// 资产收藏处理函数
async function handleToggleCollect(asset: AssetItem, event: MouseEvent): Promise<void> {
  // 阻止事件冒泡，防止触发父级点击事件
  event.stopPropagation();

  // 检查资产是否有任务ID
  if (!asset.taskid) {
    message.warning('该资产没有任务ID，无法切换收藏状态');
    return;
  }

  try {
    // 调用收藏切换API
    const response = await toggleCollect(asset.taskid);

    // 如果请求成功
    if (response.data) {
      // 更新本地收藏状态
      asset.iscollect = response.data.iscollect;
      // 显示成功消息
      message.success(asset.iscollect === 1 ? '收藏成功' : '已取消收藏');

      // 如果当前是"仅收藏"模式且取消了收藏，则需要重新加载数据
      if (showCollectionOnly.value && asset.iscollect === 0) {
        loadAssets(currentPage.value, false);
      }
    } else {
      // 显示错误消息
      message.error('收藏失败');
      console.error('切换收藏状态失败:', response.data?.msg || '未知错误');
    }
  } catch (error) {
    console.error('切换收藏请求失败:', error);
  }
}

// 页面初始化
onMounted(() => {
  loadAssets(1, false);
});

// 添加视频时间格式化函数
function formatVideoTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 视频元数据加载处理函数
function handleVideoLoadedMetadata(event: Event): void {
  const video = event.target as HTMLVideoElement;
  if (video && video.tagName === 'VIDEO') {
    // 获取当前视频元素对应的时间显示元素
    const timeDisplay = video.parentElement?.querySelector('.video-time-display') as HTMLElement;
    if (timeDisplay) {
      // 显示视频总时长而非当前播放时间
      timeDisplay.textContent = formatVideoTime(video.duration);
    }
  }
}

// 保证整个 Tab 的高度在屏幕内
const tabHeight = 'h-[calc(100vh-56px-44px-39px-16px)]';
</script>

<template>
  <div class="user-assets-container">
    <!-- 添加模态框 -->
    <NModal
      v-model:show="showAssetInfoModal"
      preset="card"
      :title="null"
      :header-style="{ display: 'none' }"
      :mask-closable="false"
      class="modalBox hide-header h-vh w-vw"
    >
      <div class="asset-info-container relative">
        <NButton text class="back-button text-3xl" @click="showAssetInfoModal = false">
          <SvgIcon icon="material-symbols:close" />
        </NButton>
        <AssetInfo
          :key="currentShareData ? JSON.stringify(currentShareData) : 'no-data'"
          :modal-query-params="currentShareData"
        />
      </div>
    </NModal>

    <div class="tabs-header">
      <NTabs v-model:value="activeTab" type="segment" animated class="w-full">
        <NTabPane name="image" tab="图片" :class="tabHeight">
          <NInfiniteScroll :distance="50" @load="handleInfiniteScroll">
            <!-- 时间分组展示 -->
            <template v-for="(group, groupIndex) in assetGroups" :key="group.date">
              <!-- 时间标签 -->
              <NH2>{{ group.timeLabel }}</NH2>

              <!-- 只显示一个 BackTop -->
              <NBackTop v-if="groupIndex === 0" class="z-9999" />

              <!-- 瀑布流图片容器 -->
              <div class="asset-waterfall">
                <div
                  v-for="(asset, index) in group.assets"
                  :key="asset.taskid ?? `asset-${index}-${asset.create_time}`"
                  class="image-asset-item"
                  @mouseenter="setHoveredAsset(asset.url)"
                  @mouseleave="setHoveredAsset(null)"
                >
                  <!-- 图片双层容器 -->
                  <div class="image-container" @click="handleImageClick(asset)">
                    <!-- 底层：背景图片（模糊效果） -->
                    <div class="image-background">
                      <img :src="asset.url + '?imageView2/1/h/250'" class="blur-image" />
                    </div>

                    <!-- 上层：清晰图片 -->
                    <div class="image-overlay">
                      <img :src="asset.url + '?imageView2/1/h/250'" class="clear-image" />
                    </div>
                  </div>

                  <!-- 收藏状态按钮 -->
                  <NCard
                    :bordered="false"
                    class="collect-button"
                    :class="{ 'always-show': asset.iscollect === 1, 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleToggleCollect(asset, e)"
                  >
                    <SvgIcon
                      :icon="asset.iscollect === 1 ? 'fluent:star-32-filled' : 'fluent:star-32-regular'"
                      class="star-icon"
                      :class="{ filled: asset.iscollect === 1 }"
                    />
                  </NCard>

                  <!-- 下载按钮 -->
                  <NCard
                    :bordered="false"
                    class="download-button"
                    :class="{ 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleDownload(asset, e)"
                  >
                    <SvgIcon icon="material-symbols:download" class="action-icon" />
                  </NCard>

                  <!-- 分享按钮 -->
                  <NCard
                    :bordered="false"
                    class="share-button"
                    :class="{ 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleShare(asset, e)"
                  >
                    <SvgIcon icon="material-symbols:share-outline" class="action-icon" />
                  </NCard>
                </div>
              </div>
            </template>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
              <NSpin size="medium" />
              <span class="ml-2">加载中...</span>
            </div>

            <!-- 无更多数据提示 -->
            <NDivider v-if="!hasMore && assetGroups.length > 0" title-placement="center">
              <span>没有更多了 🤪</span>
            </NDivider>

            <!-- 空状态 -->
            <NEmpty v-if="assetGroups.length === 0 && !loading" description="暂无图片资产" />
          </NInfiniteScroll>
        </NTabPane>

        <NTabPane name="video" tab="视频" :class="tabHeight">
          <NInfiniteScroll :distance="50" @load="handleInfiniteScroll">
            <!-- 时间分组展示 -->
            <template v-for="(group, groupIndex) in assetGroups" :key="group.date">
              <!-- 时间标签 -->
              <NH2>{{ group.timeLabel }}</NH2>

              <!-- 只显示一个 BackTop -->
              <NBackTop v-if="groupIndex === 0" class="z-9999" />

              <!-- 瀑布流视频容器 -->
              <div class="asset-waterfall">
                <div
                  v-for="(asset, index) in group.assets"
                  :key="asset.taskid ?? `asset-${index}-${asset.create_time}`"
                  class="video-asset-item"
                  @mouseenter="setHoveredAsset(asset.url)"
                  @mouseleave="setHoveredAsset(null)"
                >
                  <!-- 视频双层容器 -->
                  <div class="video-container">
                    <!-- 底层：NCard 背景 -->
                    <NCard :bordered="false" class="video-background" />

                    <!-- 上层：视频元素 -->
                    <video
                      :src="asset.url"
                      class="video-overlay"
                      preload="metadata"
                      muted
                      loop
                      @mouseenter="handleVideoMouseenter"
                      @mouseleave="handleVideoMouseleave"
                      @click="handleVideoClick(asset)"
                      @error="handleVideoError"
                      @loadedmetadata="handleVideoLoadedMetadata"
                    />

                    <!-- 添加视频时间显示 -->
                    <div class="video-time-display">00:00</div>
                  </div>

                  <!-- 收藏状态按钮 -->
                  <NCard
                    embedded
                    :bordered="false"
                    class="collect-button"
                    :class="{ 'always-show': asset.iscollect === 1, 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleToggleCollect(asset, e)"
                  >
                    <SvgIcon
                      :icon="asset.iscollect === 1 ? 'fluent:star-32-filled' : 'fluent:star-32-regular'"
                      class="star-icon"
                      :class="{ filled: asset.iscollect === 1 }"
                    />
                  </NCard>

                  <!-- 下载按钮 -->
                  <NCard
                    embedded
                    :bordered="false"
                    class="download-button"
                    :class="{ 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleDownload(asset, e)"
                  >
                    <SvgIcon icon="material-symbols:download" class="action-icon" />
                  </NCard>

                  <!-- 分享按钮 -->
                  <NCard
                    embedded
                    :bordered="false"
                    class="share-button"
                    :class="{ 'hover-show': hoveredAssetUrl === asset.url }"
                    @click="(e: MouseEvent) => handleShare(asset, e)"
                  >
                    <SvgIcon icon="material-symbols:share-outline" class="action-icon" />
                  </NCard>
                </div>
              </div>
            </template>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
              <NSpin size="medium" />
              <span class="ml-2">加载中...</span>
            </div>

            <!-- 无更多数据提示 -->
            <NDivider v-if="!hasMore && assetGroups.length > 0" title-placement="center">
              <span>没有更多了 🤪</span>
            </NDivider>

            <!-- 空状态 -->
            <NEmpty v-if="assetGroups.length === 0 && !loading" description="暂无视频资产" />
          </NInfiniteScroll>
        </NTabPane>

        <!-- 预留音频tab -->
        <!--
 <NTabPane name="audio" tab="音乐" :disabled="true">

        </NTabPane>
-->
      </NTabs>
      <!-- 收藏过滤器 -->
      <div class="collection-filter">
        <NCheckbox v-model:checked="showCollectionOnly">仅收藏</NCheckbox>
      </div>
      <NBackTop />
    </div>
  </div>
</template>

<style scoped lang="scss">
.user-assets-container {
  height: calc(100vh - 100px);
  padding: 16px;
}

.tabs-header {
  position: relative;
  width: 100%;
}

.collection-filter {
  position: absolute;
  top: 8px;
  right: 0;
  z-index: 10;
}

// .time-label {
//   font-weight: 500;
//   color: var(--n-text-color);
//   font-size: 14px;
// }

.asset-waterfall {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* 统一的网格布局 - 应用于图片和视频 */
.asset-waterfall {
  grid-template-columns: repeat(auto-fill, 20em);
  justify-content: start;
  gap: 20px;
}

/* 图片项目样式 */
.image-asset-item {
  position: relative;
}

.image-container {
  position: relative;
  width: 20em;
  height: 12em;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
}

.image-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.blur-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(10px);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.clear-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 视频专用grid布局 */
.video-asset-item {
  position: relative;
}

.video-container {
  position: relative;
  width: 20em;
  height: 12em;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
}

/* 添加视频时间显示样式 */
.video-time-display {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 5;
  font-family: monospace;
}

.asset-item {
  position: relative;
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 2;
  border-radius: 8px;
  object-fit: contain;
}

.collect-button {
  position: absolute;
  top: 8px;
  right: 8px;
  // border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  z-index: 10;
}

/* 新增：下载按钮样式 */
.download-button {
  position: absolute;
  bottom: 8px;
  right: 48px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  z-index: 10;
}

/* 新增：分享按钮样式 */
.share-button {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  z-index: 10;
}

.collect-button.always-show {
  opacity: 1;
}

.collect-button.hover-show,
.download-button.hover-show,
.share-button.hover-show {
  opacity: 1;
}

.star-icon {
  font-size: 25px;
}

/* 新增：操作图标样式 */
.action-icon {
  font-size: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

:deep(.n-tabs-rail) {
  width: 20% !important;
  border-radius: 2em !important;
}

:deep(.n-tabs-capsule) {
  border-radius: 2em !important;
}

:deep(.collect-button) .n-card__content svg {
  font-size: 20px !important;
}

:deep(.collect-button) .n-card__content .filled {
  color: #faad14;
}

/* 新增：下载和分享按钮内容样式 */
:deep(.download-button) .n-card__content svg,
:deep(.share-button) .n-card__content svg {
  font-size: 18px !important;
}

/* 添加模态框样式 */
.modalBox.hide-header :deep(.n-card-header) {
  display: none !important;
}

.asset-info-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 16px;
  right: 30px;
  z-index: 100;
}

.carousel-img {
  width: 100%;
  height: 240px;
  object-fit: cover;
}
</style>

<style>
.modalBox.hide-header .n-card__content {
  padding: 0 !important;
}
</style>
