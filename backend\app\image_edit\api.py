import logging

from fastapi import APIRouter, Depends

from models.users import get_request_user
from utils.response import SuccessRes

from .http_models import OperationReq, History, TransformStyle

router = APIRouter(dependencies=[Depends(get_request_user)])
logger = logging.getLogger(__name__)


@router.post(
    "/records",
    response_model=SuccessRes[History]
)
async def create_record(
        payload: OperationReq
):
    pass


@router.get(
    "/transform_styles",
    response_model=SuccessRes[list[TransformStyle]]
)
async def list_styles():
    pass
