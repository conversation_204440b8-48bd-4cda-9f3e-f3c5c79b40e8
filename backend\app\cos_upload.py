import logging

from fastapi import APIRouter
from fastapi.responses import JSONResponse
from service.tencent.cos import generate_temp_credentials, set_lifecycle, get_lifecycle, delete_lifecycle
from typing import Dict, Any, Optional

from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/get_temp_credentials")
async def get_temp_credentials(
    duration: Optional[int] = 600
) -> JSONResponse:
    """
    获取腾讯云COS临时身份令牌
    
    Args:
        prefix: 允许访问的对象前缀，如 'uploads/'
        duration: 临时密钥有效期，单位秒
        
    Returns:
        JSONResponse: 包含临时身份令牌的响应
    """
    try:
        # 调用服务函数获取临时身份令牌
        credentials = generate_temp_credentials(
            duration_seconds=duration
        )

        logger.info(f"身份令牌信息: {credentials}")
        
        # 成功返回
        return JSONResponse(
            status_code=200,
            content={
                "code": "0000",
                "data": credentials,
                "msg": "success"
            }
        )
    except Exception as e:
        # 记录错误日志
        logger.error(f"获取临时身份令牌失败: {str(e)}")
        raise ClientVisibleException("身份验证失败") from e

@router.post("/lifecycle")
async def set_bucket_lifecycle(
    lifecycle_rules: Dict[str, Any]
) -> JSONResponse:
    """
    设置存储桶生命周期规则
    
    Args:
        lifecycle_rules: 生命周期规则配置
        
    Returns:
        JSONResponse: 操作结果响应
    """
    try:
        # 调用服务函数设置生命周期规则
        response = set_lifecycle(lifecycle_rules)
        
        # 成功返回
        return JSONResponse(
            status_code=200,
            content={
                "code": "0000",
                "data": response,
                "msg": "设置生命周期规则成功"
            }
        )
    except Exception as e:
        # 记录错误日志
        logger.error(f"设置生命周期规则失败: {str(e)}")
        raise ClientVisibleException("设置生命周期规则失败") from e

@router.get("/lifecycle")
async def get_bucket_lifecycle() -> JSONResponse:
    """
    查询存储桶生命周期规则
    
    Returns:
        JSONResponse: 包含生命周期规则的响应
    """
    try:
        # 调用服务函数查询生命周期规则
        lifecycle_config = get_lifecycle()
        
        # 成功返回
        return JSONResponse(
            status_code=200,
            content={
                "code": "0000",
                "data": lifecycle_config,
                "msg": "查询生命周期规则成功"
            }
        )
    except Exception as e:
        # 记录错误日志
        logger.error(f"查询生命周期规则失败: {str(e)}")
        raise ClientVisibleException("查询生命周期规则失败") from e

@router.delete("/lifecycle")
async def delete_bucket_lifecycle() -> JSONResponse:
    """
    删除存储桶生命周期规则
    
    Returns:
        JSONResponse: 操作结果响应
    """
    try:
        # 调用服务函数删除生命周期规则
        response = delete_lifecycle()
        
        # 成功返回
        return JSONResponse(
            status_code=200,
            content={
                "code": "0000",
                "data": response,
                "msg": "删除生命周期规则成功"
            }
        )
    except Exception as e:
        # 记录错误日志
        logger.error(f"删除生命周期规则失败: {str(e)}")
        raise ClientVisibleException("删除生命周期规则失败") from e
