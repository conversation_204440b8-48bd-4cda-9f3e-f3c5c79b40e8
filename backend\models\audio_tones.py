from datetime import datetime
from typing import Literal

from sqlalchemy.dialects.mysql import INTEGER, VARCHAR, TINYINT, TEXT, DATETIME, BOOLEAN
from sqlalchemy.orm import Mapped, mapped_column

from utils.database import Base


class AudioTone(Base):
    __tablename__ = "audio_tones"

    id: Mapped[int] = mapped_column(INTEGER, primary_key=True, nullable=False, comment="音色标识")
    name: Mapped[str] = mapped_column(VARCHAR(20), nullable=False, comment="音色名称")
    gender: Mapped[Literal[0, 1] | None] = mapped_column(TINYINT, nullable=True, default=None,
                                                  comment="音色性别：0（男性）、1（女性）")
    lang: Mapped[str | None] = mapped_column(VARCHAR(32), nullable=True, default=None, comment='对应语言')
    description: Mapped[str] = mapped_column(TEXT, comment='音色描述')
    create_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, comment='创建时间')
    update_time: Mapped[datetime] = mapped_column(DATETIME, nullable=False, default=datetime.now, onupdate=datetime.now,
                                                  comment='更新时间')
    is_favorite: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否收藏')
    user_id: Mapped[int] = mapped_column(INTEGER, nullable=False, comment='所属用户 ID')
    audio_url: Mapped[str] = mapped_column(TEXT, nullable=False, comment='音频 URL')
    prompt_text: Mapped[str] = mapped_column(TEXT, nullable=False, comment='音频对应的文字')
    is_reduction: Mapped[bool] = mapped_column(BOOLEAN, nullable=False, default=False, comment='是否已经降噪处理')
