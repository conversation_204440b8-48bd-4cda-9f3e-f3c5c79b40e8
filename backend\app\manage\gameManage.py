import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Query, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from utils.database import get_db
from models.game_management import GameManagement
from pydantic import BaseModel
from typing import List, Optional
import datetime
from sqlalchemy.sql import or_, and_
from models.users import User, get_token_key
from utils.exceptions import ClientVisibleException
from utils.redis import redis


router = APIRouter()
logger = logging.getLogger(__name__)


class GameManagementOut(BaseModel):
  gamecode: str
  gamename: str
  info: Optional[str]
  lang: Optional[str]
  create_time: datetime.datetime
  editor: Optional[str]
  uptime: datetime.datetime
  company: Optional[str]
  english_name: Optional[str]

  class Config:
    from_attributes = True


class PaginatedData(BaseModel):
  records: List[GameManagementOut]
  current: int
  size: int
  total: int


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str


class DeleteGamesRequest(BaseModel):
  gamecodes: List[str]


class AddGameRequest(BaseModel):
  gamecode: str
  gamename: str
  info: Optional[str] = ""
  lang: Optional[str] = ""
  editor: Optional[str] = ""
  company: Optional[str] = ""
  english_name: Optional[str] = ""


async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.gamecode)))
  return result.scalar()


@router.get("/all_game_management", response_model=PaginatedResponse, tags=["manage"])
async def get_all_game_management(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
    获取游戏管理数据
    """
  async with db as session:
    # 获取分页数据
    result = await session.execute(
      select(GameManagement)
      .offset((page - 1) * size)
      .limit(size)
    )
    records = result.scalars().all()

    # 获取总数
    total = await get_total_count(session, GameManagement)

    records_out = [GameManagementOut.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


@router.get("/search", response_model=PaginatedResponse, tags=["manage"])
async def search_game_management(
  gamecode: Optional[str] = Query(None),
  gamename: Optional[str] = Query(None),
  lang: Optional[str] = Query(None),
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
    查询游戏管理数据
    """
  # 构建查询语句
  query = select(GameManagement)
  if gamecode:
    query = query.where(GameManagement.gamecode == gamecode)
  if gamename:
    query = query.where(GameManagement.gamename == gamename)
  if lang:
    langs = lang.split(',')
    lang_conditions = [GameManagement.lang.like(f'%{single_lang}%') for single_lang in langs]
    query = query.where(and_(*lang_conditions))

  async with db as session:
    # 获取分页数据
    result = await session.execute(query.offset((page - 1) * size).limit(size))
    records = result.scalars().all()

    # 获取总数
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await session.execute(count_query)
    total = total_result.scalar()

    records_out = [GameManagementOut.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


@router.post("/delete", response_model=PaginatedResponse, tags=["manage"])
async def delete_game_management(
  request: DeleteGamesRequest,
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
    删除游戏管理数据
    """
  async with db as session:
    # 查找要删除的数据
    result = await session.execute(select(GameManagement).where(GameManagement.gamecode.in_(request.gamecodes)))
    games = result.scalars().all()

    if not games:
      raise ClientVisibleException("删除失败")

    # 删除数据
    for game in games:
      await session.delete(game)
    await session.commit()

    # 获取删除后的分页数据
    result = await session.execute(
      select(GameManagement)
      .offset((page - 1) * size)
      .limit(size)
    )
    records = result.scalars().all()

    # 获取总数
    total = await get_total_count(session, GameManagement)

    records_out = [GameManagementOut.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


@router.post("/addGame", response_model=PaginatedResponse, tags=["manage"])
async def add_game_management(
  add_game_data: AddGameRequest,
  db: AsyncSession = Depends(get_db),
  authorization: str = Header(None)
):
  """
    添加新的游戏管理数据
    """
  credentials_exception = ClientVisibleException("Could not validate credentials")

  try:
    # 从 Authorization 头中提取 token
    token = authorization.split(" ")[1]

    # 验证 token
    token_key = get_token_key(token)
    userid = await redis.get(token_key)

    if userid is None:
      raise credentials_exception

    # 转换 Redis 返回的字节字符串为整数
    userid = int(userid.decode("utf-8"))

    async with db as session:
      # 获取用户信息
      result = await session.execute(select(User).filter(User.id == userid))
      user = result.scalars().first()

      if user is None or user.status != 1:
        raise credentials_exception

      # 验证gamecode或者gamename是否在数据库中存在
      result = await session.execute(
        select(GameManagement).filter(
          or_(
            GameManagement.gamecode == add_game_data.gamecode,
            GameManagement.gamename == add_game_data.gamename
          )
        )
      )
      existing_game = result.scalars().first()
      if existing_game:
        raise ClientVisibleException("游戏已存在")

      # 添加新的游戏管理数据
      new_game = GameManagement(
        gamecode=add_game_data.gamecode,
        gamename=add_game_data.gamename,
        info=add_game_data.info,
        lang=add_game_data.lang,
        editor=user.username,  # 使用从数据库获取的用户名
        create_time=datetime.datetime.now(),
        uptime=datetime.datetime.now(),
        company=add_game_data.company,
        english_name=add_game_data.english_name
      )
      session.add(new_game)
      await session.commit()

      # 获取分页数据
      page = 1
      size = 10
      result = await session.execute(
        select(GameManagement)
        .offset((page - 1) * size)
        .limit(size)
      )
      records = result.scalars().all()

      # 获取总数
      total = await get_total_count(session, GameManagement)

      records_out = [GameManagementOut.from_orm(record) for record in records]

      return PaginatedResponse(
        data=PaginatedData(
          records=records_out,
          current=page,
          size=size,
          total=total
        ),
        code="0000"
      )

  except Exception as e:
    logger.error(f"Failed to add game: {e}")
    raise ClientVisibleException("添加游戏失败") from e


@router.post("/update", response_model=PaginatedResponse, tags=["manage"])
async def update_game_management(
  update_game_data: AddGameRequest,
  db: AsyncSession = Depends(get_db),
  authorization: str = Header(None)
):
  """
    更新游戏管理数据
    """
  credentials_exception = ClientVisibleException("Could not validate credentials")

  try:
    # 从 Authorization 头中提取 token
    token = authorization.split(" ")[1]

    # 验证 token
    token_key = get_token_key(token)
    userid = await redis.get(token_key)

    if userid is None:
      raise credentials_exception

    # 转换 Redis 返回的字节字符串为整数
    userid = int(userid.decode("utf-8"))

    async with db as session:
      # 获取用户信息
      result = await session.execute(select(User).filter(User.id == userid))
      user = result.scalars().first()

      if user is None or user.status != 1:
        raise credentials_exception

      # 根据 gamecode 查找对应的游戏
      result = await session.execute(
        select(GameManagement).filter(GameManagement.gamecode == update_game_data.gamecode)
      )
      existing_game = result.scalars().first()

      if not existing_game:
        raise ClientVisibleException("游戏不存在")

      # 更新游戏信息
      existing_game.gamename = update_game_data.gamename
      existing_game.info = update_game_data.info
      existing_game.lang = update_game_data.lang
      existing_game.editor = user.username
      existing_game.company = update_game_data.company
      existing_game.english_name = update_game_data.english_name

      await session.commit()

      # 获取分页数据
      page = 1
      size = 10
      result = await session.execute(
        select(GameManagement)
        .offset((page - 1) * size)
        .limit(size)
      )
      records = result.scalars().all()

      # 获取总数
      total = await get_total_count(session, GameManagement)

      records_out = [GameManagementOut.from_orm(record) for record in records]

      return PaginatedResponse(
        data=PaginatedData(
          records=records_out,
          current=page,
          size=size,
          total=total
        ),
        code="0000"
      )

  except Exception as e:
    logger.error(f"Failed to update game: {e}")
    raise ClientVisibleException("更新游戏失败") from e


