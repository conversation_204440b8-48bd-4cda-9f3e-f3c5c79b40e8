<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{ model: Api.SystemManage.Model }>();

const displayName = computed(() => {
  return props.model.label || props.model.name;
});
</script>

<template>
  <NTooltip>
    <template #trigger>
      <NFlex class="w-fit">
        <NAvatar class="bg-white" size="small" :src="props.model.icon" />
        <span>{{ displayName }}</span>
      </NFlex>
    </template>
    <NFlex vertical class="max-w-72">
      <span>{{ props.model.description }}</span>
      <span>{{ $t('page.manage.model.name') }}：{{ props.model.name }}</span>
    </NFlex>
  </NTooltip>
</template>

<style scoped></style>
