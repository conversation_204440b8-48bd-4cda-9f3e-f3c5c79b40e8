<script setup lang="tsx">
import { N<PERSON><PERSON><PERSON>, NPopconfirm, NTag } from 'naive-ui';
import type { Ref } from 'vue';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchAllModels, fetchDeleteModel } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import CapacityTags from './capacity-tags.vue';
import ModelName from './model-name.vue';
import publishersMap from './publishersMap';
import OperateModal from './operate-modal.vue';

const appStore = useAppStore();

const { columns, data, getData, loading, columnChecks, mobilePagination } = useTable({
  apiFn: fetchAllModels,
  apiParams: {
    current: 1,
    size: 15
  },
  columns: () => [
    {
      key: 'name',
      title: $t('page.manage.model.name_col'),
      width: 64,
      align: 'left',
      render: row => <ModelName model={row} />
    },
    {
      key: 'publishers',
      title: $t('page.manage.model.publishers'),
      width: 28,
      align: 'center',
      render: row => {
        if (row.publishers in publishersMap) {
          return publishersMap[row.publishers as keyof typeof publishersMap];
        }
        return row.publishers;
      }
    },
    {
      key: 'capacity',
      title: $t('page.manage.model.capacity'),
      width: 72,
      align: 'center',
      render: row => <CapacityTags capacity={row.capacity} />
    },
    {
      key: 'status',
      title: $t('page.manage.model.status'),
      width: 24,
      align: 'center',
      render: row => {
        const enabledText = $t('page.manage.model.statusText.enabled');
        const disabledText = $t('page.manage.model.statusText.disabled');
        const type = row.status ? 'success' : 'error';
        const text = row.status ? enabledText : disabledText;
        return <NTag type={type}>{text}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 36,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data as unknown as Ref<NaiveUI.TableData[]>, getData);

const handleDelete = (id: number) => {
  fetchDeleteModel(id).then(() => {
    onDeleted();
  });
};
</script>

<template>
  <NCard class="h-vh max-h-full" :title="$t('page.manage.model.title')" :bordered="false" size="small">
    <template #header-extra>
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        @add="handleAdd"
        @delete="onBatchDeleted"
        @refresh="getData"
      />
    </template>
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      :row-key="(row: Api.SystemManage.Model) => row.id"
      :columns="columns"
      :data="data"
      size="small"
      :flex-height="!appStore.isMobile"
      :scroll-x="702"
      remote
      class="h-full"
      :loading="loading"
      :pagination="mobilePagination"
    />
    <OperateModal
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData as Api.SystemManage.Model | null"
      @submitted="getData"
    />
  </NCard>
</template>

<style scoped></style>
