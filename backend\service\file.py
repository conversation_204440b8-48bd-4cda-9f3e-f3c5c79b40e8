import logging
import tempfile
import os

from utils.exceptions import ClientVisibleException
from .tencent.cos import upload_file
from utils.hash.md5 import md5
import time
from base64 import b64decode, b64encode
from PIL import Image
import io
from fastapi import UploadFile
from models.file import fileModel
import httpx


logger = logging.getLogger(__name__)

project_name = "aiadmin"
images_path = f"{project_name}/images/"
audio_path = f"{project_name}/audio/"

upload_image_max_size = 1024 * 1024 * 10  # 10MB


def generate_filename(original_filename, max_width=None):
  file_ext = original_filename.split(".")[-1]
  hash_part = md5(original_filename)
  timestamp = time.strftime("%Y%m%d%H%M%S")
  if max_width:
    return f"{hash_part}_{max_width}_{timestamp}.{file_ext}"
  else:
    return f"{hash_part}_{timestamp}.{file_ext}"


def save_base64_data(image_data, original_filename, file_type, path_prefix, max_width: int = 1024):
  with tempfile.NamedTemporaryFile(delete=False) as tmp:
    try:
      if "," in image_data:
        image_data = image_data.split(",")[1]

        # 处理 base64 padding 问题
      image_data += "=" * ((4 - len(image_data) % 4) % 4)

      tmp.write(b64decode(image_data.encode()))
      tmp.flush()
      filename = generate_filename(original_filename)
      path = f"{path_prefix}p/{time.strftime('%Y%m')}/{filename}"
      if file_type == "image":
        file = resize_image(tmp.name, max_width)
        try:
          url = upload_file(file.name, path)
        finally:
          os.remove(file.name)
      else:
        url = upload_file(tmp.name, path)
      return url

    except Exception as e:
      logger.error(f"保存图片失败：{e}")
    finally:
      tmp.close()
      os.remove(tmp.name)


def resize_image(file: str | bytes, max_width: int = 1024):
  if isinstance(file, str) and not os.path.isfile(file):
    raise ClientVisibleException("File not found")

  if isinstance(file, bytes):
    file = io.BytesIO(file)

  img = Image.open(file)
  width, height = img.size
  if width > max_width:
    scale = max_width / width
    new_height = int(height * scale)
    new_size = (max_width, new_height)
    resized_img = img.resize(new_size)
  else:
    resized_img = img

  with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmpfile:
    resized_img.save(tmpfile.name, format="png")

  return tmpfile


async def get_upload_image_url(file: UploadFile, max_width: int = 1024, height=None) -> fileModel:
  if file.size > upload_image_max_size:
    raise ClientVisibleException("image is too large")
  if file.content_type.find("image") != 0:
    raise ClientVisibleException("File is not an image")

  filename = generate_filename(file.filename, max_width)
  path = f"{images_path}p/{time.strftime('%Y%m')}/{filename}"
  contents = await file.read()
  try:
    if height:
      tmpfile = center_crop_resize(contents, max_width, height)
    else:
      tmpfile = resize_image(contents, max_width=max_width)
    url = upload_file(tmpfile.name, path)
  finally:
    tmpfile.close()
    os.remove(tmpfile.name)
  return fileModel(url=url, name=filename, size=file.size, mimetype=file.content_type)


def save_image_b64data(image_data, original_filename, max_width: int = 1024):
  return save_base64_data(
    image_data,
    original_filename,
    path_prefix=images_path,
    file_type="image",
    max_width=max_width,
  )


def save_audio(audio_data, original_filename):
  return save_base64_data(
    audio_data, original_filename, file_type="audio", path_prefix=audio_path
  )


async def save_image_by_url(url, ext="png"):
  try:
    async with httpx.AsyncClient() as client:
      response = await client.get(url, follow_redirects=True)
      response.raise_for_status()  # 检查请求是否成功
      content = b64encode(response.content).decode("utf-8")
      url_md5 = md5(url)  # 使用封装的md5函数
      return save_image_b64data(content, f"{url_md5}.{ext}")
  except httpx.RequestError as e:
    logger.error(f"HTTP请求错误: {e}")
  except Exception as e:
    logger.error(f"保存图像时发生错误: {e}")


def center_crop_resize(file, target_width, target_height, output_path=None):
  if isinstance(file, bytes):
    file = io.BytesIO(file)
  with Image.open(file) as img:
    original_width, original_height = img.size
    target_ratio = target_width / target_height
    original_ratio = original_width / original_height

    if original_ratio > target_ratio:
      new_height = target_height
      new_width = int(new_height * original_ratio)
    else:
      new_width = target_width
      new_height = int(new_width / original_ratio)

    img = img.resize((new_width, new_height), Image.LANCZOS)
    left = (new_width - target_width) / 2
    top = (new_height - target_height) / 2
    right = (new_width + target_width) / 2
    bottom = (new_height + target_height) / 2

    img = img.crop((left, top, right, bottom))

    if output_path:
      img.save(output_path)
      return output_path
    else:
      with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmpfile:
        img.save(tmpfile.name, format="png")
      return tmpfile
