import logging

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from utils.database import get_db
from models.user_credit import UserCreditLog
from pydantic import BaseModel
from typing import List, Optional
import datetime
from models.users import User, get_roles_checker

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
logger = logging.getLogger(__name__)

class UserCreditLogOut(BaseModel):
  id: int
  user_id: int
  username : str
  capacity: str
  model: str
  credit: int
  after_credit:int
  createtime: datetime.datetime
  matter: Optional[str]
  detail: Optional[str]
  editor: Optional[str]
  ip: Optional[str]

  class Config:
    from_attributes = True


class PaginatedData(BaseModel):
  records: List[UserCreditLogOut]
  current: int
  size: int
  total: int

class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()

@router.get("/all", response_model=PaginatedResponse, tags=["manage"])
async def get_all_credit_log(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  async with db as session:
    # 获取分页数据
    result = await session.execute(
      select(
        UserCreditLog.id,
        UserCreditLog.user_id,
        UserCreditLog.credit,
        UserCreditLog.after_credit,
        UserCreditLog.capacity,
        UserCreditLog.model,
        UserCreditLog.matter,
        UserCreditLog.detail,
        UserCreditLog.ip,
        UserCreditLog.editor,
        UserCreditLog.createtime,
        User.nickname.label('username'))
      .join(User, UserCreditLog.user_id == User.id )
      .offset((page -1)*size)
      .limit(size)
      .order_by(UserCreditLog.id.desc())
    )
    records = result.all()

    # 获取总数
    total = await get_total_count(session, UserCreditLog)

    records_out = [UserCreditLogOut.model_validate(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


@router.get("/search", response_model=PaginatedResponse, tags=["manage"])
async def search_game_management(
  user_id: Optional[str] = Query(None),
  capacity: Optional[str] = Query(None),
  username : Optional[str] = Query(None),
  matter : Optional[str] = Query(None),
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  # 构建查询语句
  query = select(
    UserCreditLog.id,
    UserCreditLog.user_id,
    UserCreditLog.credit,
    UserCreditLog.after_credit,
    UserCreditLog.capacity,
    UserCreditLog.model,
    UserCreditLog.matter,
    UserCreditLog.detail,
    UserCreditLog.ip,
    UserCreditLog.editor,
    UserCreditLog.createtime,
    User.nickname.label('username'))
  if user_id :
    query = query.where(UserCreditLog.user_id == user_id)
  if username :
    query = query.where(User.nickname.like(f'%{username}%'))
  if capacity :
    query = query.where(UserCreditLog.capacity == capacity)
  if matter :
    query = query.where(UserCreditLog.matter.like(f'%{matter}%'))

  async with db as session:
    # 获取分页数据
    result = await session.execute(query.join(User, User.id == UserCreditLog.user_id).offset((page - 1) * size).limit(size).order_by(UserCreditLog.id.desc()))
    records = result.all()

    # 获取总数
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await session.execute(count_query)
    total = total_result.scalar()

    records_out = [UserCreditLogOut.model_validate(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )


