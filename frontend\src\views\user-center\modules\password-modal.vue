<script setup lang="ts">
import { reactive, watch } from 'vue';
import type { FormItemRule } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { postSetUserInfo } from '@/service/api';
import { $t } from '@/locales';
import { encrypt } from '@/utils/crypto';

defineOptions({
  name: 'UserPasswordModal'
});

interface Emits {
  (e: 'submitted'): void;
}
const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { formRules, defaultRequiredRule } = useFormRules();

type Model = {
  oldPassword: string;
  password: string;
  confirmPassword: string;
};
const createDefaultModel = (): Model => {
  return {
    oldPassword: '',
    password: '',
    confirmPassword: ''
  };
};

const model: Model = reactive(createDefaultModel());

type RuleKey = Extract<keyof Model, 'oldPassword' | 'password' | 'confirmPassword'>;

const rules: Record<RuleKey, any> = {
  oldPassword: formRules.pwd,
  password: [
    ...formRules.pwd,
    {
      validator(_rule: FormItemRule, value: string) {
        if (model.oldPassword === value) {
          return new Error('新密码不能与原密码相同！');
        }
        return true;
      },
      trigger: 'change'
    }
  ],
  confirmPassword: [
    defaultRequiredRule,
    {
      validator(_rule: FormItemRule, value: string) {
        if (model.password !== value) {
          return new Error('两次密码输入不一致！');
        }
        return true;
      },
      trigger: 'change'
    }
  ]
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());
}

function closeModal() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  try {
    // 加密密码后再提交
    const encryptedData = {
      oldPassword: encrypt(model.oldPassword),
      password: encrypt(model.password)
    };

    // request
    postSetUserInfo(encryptedData).then(({ error }) => {
      if (!error) {
        window.$message?.success($t('common.updateSuccess'));
        closeModal();
        emit('submitted');
      }
    });
  } catch (error: any) {
    window.$message?.error(error.message || '修改密码失败');
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    display-directive="show"
    :width="360"
    title="修改密码"
    preset="dialog"
    :native-scrollbar="false"
    closable
    :positive-text="$t('common.confirm')"
    :negative-text="$t('common.cancel')"
    class="min-w-600px"
    @positive-click="handleSubmit"
    @negative-click="closeModal"
  >
    <NScrollbar class="max-h-500px p-10" :size="5" :x-scrollable="false">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="top">
        <NFormItem label="原密码" path="oldPassword">
          <NInput v-model:value="model.oldPassword" placeholder="请输入原密码" type="password" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.password')" path="password">
          <NInput
            v-model:value="model.password"
            :placeholder="$t('page.login.common.passwordPlaceholder')"
            type="password"
          />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.form.repetition_password')" path="confirmPassword">
          <NInput
            v-model:value="model.confirmPassword"
            :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
            type="password"
          />
        </NFormItem>
      </NForm>
    </NScrollbar>
  </NModal>
</template>

<style scoped>
/* L42_slfjs32_ */
</style>
