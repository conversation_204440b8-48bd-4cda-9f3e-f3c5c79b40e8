<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue';
import { useMessage } from 'naive-ui';
// import type { FormValidationError } from 'naive-ui';
import { fetchGetCurrUserInfo, postSetUserInfo } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
const message = useMessage();
defineOptions({
  name: 'UserOperate'
});

const { formRef, validate } = useNaiveForm();
const { patternRules, defaultRequiredRule } = useFormRules();
type Model = Pick<Api.SystemManage.User, 'username' | 'nickname' | 'email' | 'company'>;
type RuleKey = Extract<keyof Model, 'nickname' | 'email' | 'company'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  nickname: [defaultRequiredRule],
  email: [defaultRequiredRule, patternRules.email],
  company: [defaultRequiredRule]
};

const createDefaultModel = (): Model => {
  return {
    username: '',
    nickname: '',
    email: '',
    company: ''
  };
};
const model: Model = reactive(createDefaultModel());

async function formSubmit() {
  await validate();
  // console.log(model);
  // console.log('验证通过');

  postSetUserInfo(model).then(({ error }) => {
    if (!error) {
      message.success('修改成功');
    } else {
      message.error('修改失败');
    }
  });
}
onBeforeMount(() => {
  fetchGetCurrUserInfo().then(({ data }) => {
    if (data) {
      Object.assign(model, createDefaultModel(), data);
      // console.log(data);
      // console.log(model);
    }
  });
});
</script>

<template>
  <NGrid cols="2 s:2 m:2 l:3 xl:3 2xl:3" responsive="screen">
    <NGridItem>
      <NForm ref="formRef" :label-width="80" :model="model" :rules="rules">
        <NFormItem label="昵称" path="nickname">
          <NInput v-model:value="model.nickname" placeholder="请输入昵称" />
        </NFormItem>
        <NFormItem label="邮箱" path="email">
          <NInput v-model:value="model.email" disabled placeholder="请输入邮箱" />
        </NFormItem>
        <NFormItem label="公司" path="mobile">
          <NInput v-model:value="model.company" disabled placeholder="请输入联系电话" />
        </NFormItem>
        <div>
          <NSpace>
            <NButton type="primary" @click="formSubmit">更新基本信息</NButton>
          </NSpace>
        </div>
      </NForm>
    </NGridItem>
  </NGrid>
</template>
