from sqlalchemy import Column, Integer, String,DECIMAL, DateTime,BigInteger,Text, Date, TIMESTAMP, ForeignKey, UniqueConstraint, Index,Date
from sqlalchemy.dialects.mysql import TINYINT
from utils.database import Base, get_db
import datetime
from sqlalchemy.future import select
from pydantic import  condecimal

class APICallRecord(Base):
  __tablename__ = "api_call_records"
  id = Column(Integer, primary_key=True, index=True, autoincrement=True)
  api_name = Column(String(255), nullable=False)
  api_path = Column(String(255), nullable=False)
  request_method = Column(String(10), nullable=False)
  response_status_code = Column(Integer, nullable=False)
  user_id = Column(Integer, nullable=True,default=0)
  username = Column(String(32), nullable=False,default='')
  company = Column(String(255), nullable=True, default='')
  user_agent = Column(String(255), nullable=True,default='')
  duration = Column(DECIMAL(10,3), nullable=False, default=0)
  created_at = Column(DateTime, default=datetime.datetime.now)
