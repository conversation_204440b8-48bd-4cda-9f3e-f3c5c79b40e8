import { request } from '../request';
export interface MimicBrushTaskResult {
  prompt_id: string;
  number: number;
  node_errors: object;
}
export interface MimicBrushTasProgresskResult {
  [key: string]: {
    prompt: string;
    outputs: {
      [key: string]: {
        images: {
          filename: string;
          subfolder: string;
          type: string;
        }[];
      };
    };
    status: {
      status_str: string;
      completed: boolean;
      messages: any[];
    };
  };
}
// **  */
export function postMimicBrushTask(data: Api.Media.MimicBrush) {
  return request<MimicBrushTaskResult>({
    url: '/mimicbrush/task',
    method: 'post',
    data
  });
}
export function getTaskProgress(prompt_id: string) {
  return request<MimicBrushTasProgresskResult>({
    url: `/mimicbrush/history/${prompt_id}`,
    method: 'get'
  });
}
