import logging
import os
import time
import json
from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from utils.database import get_db
from pydantic import BaseModel
from openai import AsyncOpenAI
from utils.common import convert_to_mp3, base64_mime_to_file
from app.text.translate import translate_text
import uuid
from service.openai.chat import get_token, get_base_url
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

OPENAI_API_KEY = get_token()
API_BASE_URL = get_base_url()


class ExtractRequest(BaseModel):
  file_path: str = ''
  prompt_audio_b64: str = ''
  format: str
  mode: str = ''


class TranslateRequest(BaseModel):
  userinput: str
  target_language: str
  gamecode: str
  model: str


async def del_file(file_path):
  if os.path.exists(file_path):
    os.remove(file_path)


def delete_old_files(directory, hours=3):
  # 计算时间阈值，当前时间减去指定小时数
  time_threshold = time.time() - hours * 3600

  # 遍历目录中的所有文件和子目录
  for root, dirs, files in os.walk(directory):
    for file in files:
      file_path = os.path.join(root, file)
      # 获取文件的最后修改时间
      file_mod_time = os.path.getmtime(file_path)

      # 检查文件的最后修改时间是否早于时间阈值
      if file_mod_time < time_threshold:
        try:
          os.remove(file_path)
          logger.info(f'已删除文件: {file_path}')
        except OSError as e:
          logger.error(f'删除文件时出错: {file_path} - {e}')


@router.post("/extract_subtitle", tags=["media"])
async def extract_subtitle(req: ExtractRequest, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)):
  final_path = ''
  logger.debug(req.file_path)
  if req.file_path != '':
    if not os.path.exists(req.file_path):
      raise ClientVisibleException("文件不存在，请重新上传")

    final_path = req.file_path.split('.')[0] + '.mp3'
    logger.debug(final_path)
    if not os.path.exists(final_path):
      logger.debug('生成mp3')
      await convert_to_mp3(req.file_path, final_path)

  elif req.prompt_audio_b64:
    # 检查并确保路径存在
    upload_dir = 'upload/subtitle'
    if not os.path.exists(upload_dir):
      os.makedirs(upload_dir)

    # 文件路径
    final_path = f'{upload_dir}/{uuid.uuid4()}.wav'
    base64_mime_to_file(req.prompt_audio_b64, final_path)
  else:
    raise ClientVisibleException("未上传音频文件")

  client = AsyncOpenAI(
    # This is the default and can be omitted
    api_key=OPENAI_API_KEY,
    base_url=API_BASE_URL,
  )

  if not os.path.exists(final_path):
    raise ClientVisibleException("文件转换失败")

  try:
    f = open(final_path, "rb")

    docs = await client.audio.transcriptions.create(
      file=f,
      model="whisper-1",
      response_format=req.format,
      temperature=0.1,
      # language        = "zh"
    )
    f.close()

    returnText = None

    # return docs
    if req.format == 'verbose_json':
      # res: list[VerboseJsonRes] = {VerboseJsonRes.model_validate(item) for item in docs.segments}
      res = []
      for item in docs.segments:
        res.append({
          "start": round(item["start"], 2),
          "end": round(item["end"], 2),
          "text": item['text'],
        })
      returnText = json.dumps(res, indent=4, ensure_ascii=False)
    else:
      # vtt,text,srt 直接返回
      returnText = docs

    logger.debug('删除旧文件')
    # 删除文件
    # os.remove(final_path)
    # os.remove(req.file_path)
    background_tasks.add_task(delete_old_files, './upload/subtitle', 5)

    return {"code": "0000", "msg": "success", "data": {"text": returnText}, "reg": req}
  except Exception as e:
    logger.error(e)
    raise ClientVisibleException("生成失败，请重试") from e


@router.post("/translate_subtitle", tags=["media"])
async def translate_subtitle(req: TranslateRequest, db: AsyncSession = Depends(get_db)):
  prompt_text = f"""
                    -.如果输入是json 格式，请用对象数组形式返回json格式
                    - 请注意最终返回在格式不使用markdown格式，如```json {...}```\n等
                    """
  translateText: str = await translate_text(db, text=req.userinput, target_language=req.target_language,
                                            gamecode=req.gamecode, model_name=req.model, ext_prompt_text=prompt_text)
  return {"code": "0000", "msg": "success", "data": {"translations": translateText}, "reg": req}
