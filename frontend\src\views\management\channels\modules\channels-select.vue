<script setup lang="ts">
import { h, ref } from 'vue';
import type { MenuOption } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

const channelsMenu = ref<MenuOption[]>([
  { label: 'Midjourney', key: '1', icon: () => h(SvgIcon, { icon: 'icon-park-outline:sailboat' }) },
  { label: 'Channel 2', key: '2', icon: () => h(SvgIcon, { icon: 'mdi-account' }) }
]);
</script>

<template>
  <div class="h-full">
    <NCard class="h-full">
      <NMenu :options="channelsMenu"></NMenu>
    </NCard>
  </div>
</template>

<style scoped></style>
