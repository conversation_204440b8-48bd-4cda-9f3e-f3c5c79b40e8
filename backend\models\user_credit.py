from datetime import datetime

from sqlalchemy import Column, Integer, TIMESTAMP, String
from sqlalchemy.dialects.mysql import TINYINT, TEXT

from utils.database import Base

class UserCredit(Base):
    __tablename__ = "user_credit"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment='用户ID users.id')
    credit = Column(Integer, nullable=True,default=0)
    begin_date = Column(TIMESTAMP, nullable=True)
    end_date = Column(TIMESTAMP, nullable=True)
    updatetime = Column(TIMESTAMP, nullable=True)

    def __repr__(self):
        return f"<UserCredit(id={self.id}, user_id={self.user_id})>"

class UserCreditLog(Base):
    __tablename__ = "user_credit_log"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment='用户ID')
    credit = Column(Integer, nullable=False,default=0, comment="扣除 / 增加的积分，整数表示增加，负数表示扣除。")
    after_credit = Column(Integer, nullable=True)
    capacity = Column(String(100), default='', nullable=False, comment="能力")
    model = Column(String(100), default='', nullable=False, comment="模型名称")
    ip = Column(String(128), nullable = False)
    matter = Column(String(64), nullable=True)
    detail = Column(TEXT, comment="备注信息，审计用途，不会显示给普通用户")
    editor = Column(String(64), nullable=True)
    createtime = Column(TIMESTAMP, default=datetime.now, nullable=True)
    pre_debit_id = Column(String(64), default="", nullable=False, comment="预扣操作 ID")
    pre_debit_status = Column(TINYINT, default=0, nullable=False, comment="预扣状态，1 表示预扣，0 表示已经扣除")



