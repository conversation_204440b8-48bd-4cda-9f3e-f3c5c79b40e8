from sqlalchemy import Column, Integer, String, Text, Boolean
from utils.database import Base
from sqlalchemy.sql import func
from pydantic import BaseModel


class Toolset(Base):
  __tablename__ = "toolset"

  id = Column(Integer, primary_key=True, autoincrement=True, index=True)
  name = Column(String(32), nullable=False, comment='AI工具名称')
  type = Column(String(10), nullable=True, comment='工具类型')
  description = Column(Text, nullable=True, comment='工具简介描述')
  main_function = Column(Text, nullable=True, comment='主要功能')
  url = Column(String(200), nullable=True, comment='官方地址')
  recommendation_rating = Column(Integer, nullable=True, comment='推荐指数，范围0-5')
  is_paid = Column(Boolean, nullable=True, comment='是否收费')
  is_available_in_china = Column(Boolean, nullable=True, comment='国内是否可用')
  origin_country = Column(String(10), nullable=True, comment='工具来源国家')
  image_url = Column(Text, nullable=True, comment='工具图片地址')
  document = Column(String(255), nullable=True, comment='工具说明文档')
