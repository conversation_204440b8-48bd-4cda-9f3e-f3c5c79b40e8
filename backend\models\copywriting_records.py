from sqlalchemy import Column, Integer, String, JSON, Text, Float, TIMESTAMP, ForeignKey
from sqlalchemy.dialects.mysql import TINYINT
from utils.database import Base
import datetime


class CopywritingRecord(Base):
    __tablename__ = "copywriting_records"

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    username = Column(String(255), nullable=False, comment='用户名')
    game = Column(String(255), nullable=False, comment='游戏名称')
    style = Column(String(255), nullable=False, comment='文案风格')
    duration = Column(String(255), nullable=False, comment='视频时长')
    gender = Column(String(255), nullable=False, comment='性别')
    age = Column(String(255), nullable=False, comment='年龄')
    region = Column(String(255), nullable=False, comment='地区')
    tags = Column(JSON, nullable=False, comment='卖点标签列表')
    emotion = Column(Text, nullable=False, comment='引导情感深度描述')
    scene = Column(Text, nullable=False, comment='具体化场景描述')
    description = Column(Text, nullable=False, comment='补充说明')
    event = Column(Text, nullable=False, comment='特定事件描述')
    reference = Column(Text, nullable=False, comment='案例参考')
    generated_copy = Column(Text, nullable=False, comment='生成的文案')
    generated_time = Column(TIMESTAMP, default=datetime.datetime.now, nullable=False, comment='生成时间')
    time_taken = Column(Float, nullable=False, comment='生成文案用时（秒）')
    created_at = Column(TIMESTAMP, default=datetime.datetime.now, nullable=False, comment='创建时间')
    updated_at = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')

    def __repr__(self):
        return f"<CopywritingRecord(id={self.id}, username='{self.username}, generated_copy='{self.generated_copy}')>"
