import logging
from functools import partial

from fastapi import APIRouter, Depends, HTTPException, Body, Request
from fastapi.responses import StreamingResponse

from dependencies import userid_rate_limiter, ip_rate_limiter, signature_checker
from models.response import success
from models.model_capacity import Capacity
from service.ai_model import get_models
from utils.asset_storage import sync_store_asset_directly
from utils.exceptions import ClientVisibleException
from .prompts import templates as prompt_templates
from .prompts import generate_prompt, IMG_TO_DESC_PROMPT, IMG_DESC_PROMPT
import random
from models.users import User, get_request_user
from models.chat import (
  chatState,
  chatHistory,
  ChatConversations,
  ChatHistory,
  get_user_chat_history,
  chatContent,
  ChatR<PERSON>s,
  ChatUserMemory,
)
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Annotated, Optional
from utils.database import get_db, AsyncSessionLocal, SessionLocal
from sqlalchemy import text, update
import json
import pydash
from pydantic import BaseModel
from sqlalchemy.future import select
import importlib
from datetime import datetime, timedelta
from sqlalchemy import update as sqlalchemy_update
from service.file import save_image_b64data
from utils.hash.md5 import md5
import asyncio
import re
import time
from service.ai_model import get_available_models, AvailableModelKey

router = APIRouter()
logger = logging.getLogger(__name__)
generation_flags = {}


class PromptTemplatesResponse(success):
  data: list[str]


class getChatStateResponse(success):
  data: chatState


SETTING_GROUP_KEY = 'AI_MODELS'
SETTING_KEY = 'CHAT_MODELS'


@router.get("/getModels", tags=["assistant"], response_model=success)
async def getModels(
  db: AsyncSession = Depends(get_db),
):
  """
  获取模型列表
  """
  all_models = await get_available_models(db)
  models = all_models.get(AvailableModelKey.ASSISTANT)
  data = []
  for v in models:
    data.append({
      "value": v.name,
      "label": v.label or v.name,
      "icon": v.icon,
      "desc": v.description,
      "capacity": v.capacity
    })
  return success(data=data)


@router.get("/get_prompt_templates", tags=["assistant"], response_model=PromptTemplatesResponse)
async def get_prompt_templates():
  """
    获取prompt模板,在新建会话时显示给用户
    """
  chats = random.sample(prompt_templates["chat"], 4)
  random.shuffle(chats)
  return PromptTemplatesResponse(data=chats)


@router.get("/getChatState", tags=["chat"], response_model=getChatStateResponse)
async def getChatState(
  offset: int = 0,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
  ## 获取用户的会话记录
  """
  data = chatState()
  res = await db.execute(
    text("""
        SELECT * FROM chat_conversations
        WHERE state = 1 AND username = :username AND create_time IS NOT NULL
        ORDER BY create_time DESC
        LIMIT :limit OFFSET :offset
        """),
    {"username": user.username, "limit": 30, "offset": offset}
  )
  results = res.fetchall()

  for r in results:
    data.history.append(
      chatHistory(
        id=r.id,
        title=r.title,
        uuid=r.chatid,
        create_time=r.create_time,
        roleid=r.roleid,
        memory=r.last_message,
      )
    )
  if data.history:
    data.active = data.history[0].uuid

  return getChatStateResponse(data=data)


class getChatResponseData(BaseModel):
  uuid: int
  data: List


class getChatResponse(success):
  data: getChatResponseData


class typeMediaFiles(BaseModel):
  type: str
  url: str


@router.get("/getChat", tags=["chat"], response_model=getChatResponse)
async def getChat(
  chatid: int,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  query = (
    select(ChatHistory)
    .where(ChatHistory.chatid == chatid)
    .where(ChatHistory.username == user.username)
    .where(ChatHistory.state == 1)
    .order_by(ChatHistory.qid.asc(), ChatHistory.id.asc())
  )

  result = await db.execute(query)
  res = result.scalars().all()

  answers = []
  contents = []
  for r in res:
    # 保持一个问题只有一个回答
    if r.qid != r.id:
      if r.qid in answers:
        continue
      answers.append(r.qid)
    contents.append(
      {
        "dateTime": (
          r.create_time.strftime("%Y-%m-%d %H:%M:%S") if r.create_time else None
        ),
        "text": r.content,
        "inversion": r.role == "user",
        "error": False,
        "loading": False,
        "conversationOptions": None,
        "qid": r.qid,
        "id": r.id,
        "model": r.model,
        "files": json.loads(r.files) if r.files else [],
        "requestOptions": {"prompt": r.content, "options": None},
        "memory": r.memory,
      }
    )
  return getChatResponse(data=getChatResponseData(uuid=chatid, data=contents))


class processData(BaseModel):
  id: int
  qid: int
  role: str
  text: str
  files: Optional[List] = []
  memory: Optional[str] = ""


class processResponse(BaseModel):
  data: processData
  code: str


@router.post(
  "/process",
  tags=["chat"],
  response_model=processResponse,
  dependencies=[
    Depends(signature_checker),
    Depends(ip_rate_limiter(times=50, duration=timedelta(seconds=1))),
    Depends(userid_rate_limiter(times=1, duration=timedelta(seconds=1))),
    Depends(userid_rate_limiter(times=20, duration=timedelta(minutes=1))),
  ],
)
async def process(
  request: Request,
  chatid: Annotated[int, Body()],
  gpt_model: Annotated[str, Body()],
  prompt: Annotated[str, Body()],
  roleid: Annotated[int, Body()] = 1,
  qid: Annotated[int, Body()] = None,
  regenerate: Annotated[int, Body()] = None,
  switchModel: Annotated[bool, Body()] = False,
  files: list[typeMediaFiles] = Body(None),
  use_web: Annotated[bool, Body(alias='useWeb')] = False,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  try:
    ip = pydash.get(request.state, "client_ip", '')

    # 检查模型是否存在
    ai_models = await get_models(db)
    if ai_models.get(gpt_model) is None:
      raise ClientVisibleException("模型不存在")

    # 如果是模型切换或重新生成，先添加短暂延迟确保之前的事务已释放
    if switchModel or regenerate:
      logger.info(f"模型切换/重新生成操作，添加短暂延迟确保资源释放")
      await asyncio.sleep(0.5)  # 短暂延迟

    # 如果是切换模型请求，检查是否存在历史回答
    if switchModel and qid:
      # 使用try-except块包裹数据库操作
      try:
        # 查找该问题下指定模型的历史回答
        existing_reply = await db.execute(
          select(ChatHistory)
          .filter(ChatHistory.qid == qid)
          .filter(ChatHistory.model == gpt_model)
          .filter(ChatHistory.role == "assistant")
          .order_by(ChatHistory.create_time.desc())
        )
        existing_reply = existing_reply.scalar_one_or_none()

        if existing_reply:
          # 如果存在历史回答，直接返回
          files = json.loads(existing_reply.files) if existing_reply.files else []
          return processResponse(
            code="0000",
            data=processData(
              id=existing_reply.id,
              qid=qid,
              role="assistant",
              text=existing_reply.content,
              files=files,
              memory=existing_reply.memory or ""
            )
          )
      except Exception as e:
        logger.error(f"查询历史回答时发生错误: {str(e)}")
        await db.rollback()
        # 继续处理，不中断操作

      # 获取用户的原始问题
      try:
        user_question = await db.execute(
          select(ChatHistory)
          .filter(ChatHistory.id == qid)
          .filter(ChatHistory.role == "user")
        )
        user_question = user_question.scalar_one_or_none()

        if user_question:
          prompt = user_question.content
          # 这里也需要确保正确解析存储的files
          files_data = json.loads(user_question.files) if user_question.files else []
          # 将JSON数据转回typeMediaFiles对象
          files = [typeMediaFiles(type=f["type"], url=f["url"]) for f in files_data]
        else:
          # 如果找不到原始问题，返回错误
          return processResponse(
            code="0000",
            data=processData(
              id=0,
              qid=qid,
              role="assistant",
              text="无法找到原始问题",
              files=[],
            )
          )
      except Exception as e:
        logger.error(f"获取原始问题时发生错误: {str(e)}")
        await db.rollback()
        # 返回错误响应
        return processResponse(
          code="0000",
          data=processData(
            id=0,
            qid=qid if qid else 0,
            role="assistant",
            text="查询数据库时发生错误，请稍后再试",
            files=[],
          )
        )

    # 检查角色并获取系统提示词
    systemPrompt = await get_role_prompt(db, roleid)

    # 获取或创建会话
    try:
      Conversation, is_new_conversation = await get_or_create_conversation(
        db, user.username, chatid, prompt, roleid, systemPrompt
      )
    except Exception as e:
      logger.error(f"获取或创建会话时发生错误: {str(e)}")
      await db.rollback()
      # 创建新会话出错，但可以继续处理
      is_new_conversation = False
      # 尝试再次获取会话
      try:
        conv_query = await db.execute(
          select(ChatConversations)
          .filter(ChatConversations.username == user.username)
          .filter(ChatConversations.chatid == chatid)
        )
        Conversation = conv_query.scalar_one_or_none()
        if not Conversation:
          raise ClientVisibleException("无法获取会话信息")
      except Exception as inner_e:
        logger.error(f"重试获取会话时发生错误: {str(inner_e)}")
        raise ClientVisibleException("无法获取会话信息") from inner_e

    # 处理问题记录
    try:
      Question, contents = await handle_question_record(
        db, user.username, chatid, prompt, gpt_model, qid, regenerate, files, ip=request.state.client_ip
      )
    except Exception as e:
      logger.error(f"处理问题记录时发生错误: {str(e)}")
      # 已经在函数内部处理了异常
      raise

    # 如果历史记录为空，重新获取历史记录
    # 这是一个额外的安全措施，确保我们有完整的历史记录
    if not contents and not is_new_conversation:
      try:
        contents = await get_user_chat_history(user.username, chatid, Question.id)
        logger.info(f"重新获取历史记录, 数量: {len(contents)}")
      except Exception as e:
        logger.error(f"重新获取历史记录时发生错误: {str(e)}")
        # 继续处理，即使获取历史记录失败

    # 获取模型配置
    model_config = ai_models.get(gpt_model)

    # 检查文件类型支持
    if files:
      for file in files:
        file_type = file.type if hasattr(file, "type") else file["type"]
        # 之后会通过支持图片的模型来将图片处理成相对应的文字描述
        if file_type == 'image':
          continue
        else:
          # 如果有不支持的文件类型，创建一条回复并返回
          try:
            # 创建回复记录
            return await create_assistant_reply(
              "目前本模型无法识别图片中的内容，建议您尝试切换至支持图像识别的模型 。", db,
              user.username, chatid, Question.qid, gpt_model, ip=request.state.client_ip)
          except Exception as e:
            logger.error(f"创建不支持文件类型回复时发生错误: {str(e)}")
            await db.rollback()
            # 继续处理，不中断操作

    # 处理用户上传的文件
    send_files = await process_user_files(files)

    # 如果当前模型不支持图片，则通过 4o-mini 将图片文件转换为文字描述
    if send_files and not Capacity(model_config.capacity).has(Capacity.IMAGE):
      result = await convert_img_to_desc(send_files)
      if result:
        # 如果转换成功，将转换后的文字描述添加到用户的问题中
        prompt = IMG_DESC_PROMPT.format(prompt=prompt, desc=result)
      else:
        # 如果转换失败，创建一条回复并返回
        try:
          return await create_assistant_reply(
              "图片识别失败。", db, user.username,
              chatid, Question.qid, gpt_model, ip=request.state.ip)
        except Exception as e:
          logger.error(f"创建图片识别失败回复时发生错误: {str(e)}")
          await db.rollback()
      # 同时将图片文件清空，因为后续的处理不需要图片了
      send_files = []

    # 添加用户内容到对话历史
    contents.append(chatContent(role="user", content=prompt, files=send_files))

    # 创建或获取回复记录
    Reply = await create_or_get_reply(db, user.username, chatid, Question.id, gpt_model, ip=request.state.client_ip)

    # 返回流式响应
    loop = asyncio.get_running_loop()
    return StreamingResponse(
      reply_generator(
        Conversation.id, Question.id, Reply.id, user.username,
        model_config, contents, systemPrompt, ip, user.id, loop=loop, use_web=use_web
        # cost_credit
      ),
      media_type="application/octet-stream",
    )
  except Exception as e:
    # 确保数据库事务回滚
    try:
      await db.rollback()
    except:
      pass

    # 处理特定类型的错误
    if isinstance(e, HTTPException):
      raise e

    # 记录并返回通用错误
    logger.error(f"处理请求时发生错误: {str(e)}")
    raise ClientVisibleException("处理请求时发生错误，请稍后再试") from e

async def create_assistant_reply(content, db, username, chatid, qid, model, ip):
  # 创建回复记录
  Reply = ChatHistory(
    username=username,
    chatid=chatid,
    qid=qid,
    content=content,
    role="assistant",
    state=1,
    model=model,
    create_time=datetime.now(),
    ip=ip,
  )
  db.add(Reply)
  await db.commit()
  return processResponse(
    code="0000",
    data=processData(
      id=Reply.id,
      qid=qid,
      role="assistant",
      text=content,
      files=[],
    )
  )

# 辅助函数：将图片转换为相应的文字描述
async def convert_img_to_desc(files) -> str:
  img_content = chatContent(role="user", content="", files=files)
  chatServer = importlib.import_module("service.openai.chat")
  chat_gen = chatServer.chat(
      "gpt-4o-mini",
      [img_content],
      systemPrompt=IMG_TO_DESC_PROMPT,
      memory="",
  )
  result = await asyncio.to_thread(list, chat_gen)
  meta = result.pop()
  logger.info(f"图片转换文字描述: {meta}")
  return "".join(result)

# 辅助函数：获取角色提示词
async def get_role_prompt(db, roleid):
  if roleid != 1 and roleid:
    roleInfo = await db.execute(select(ChatRoles).filter(ChatRoles.id == roleid))
    roleInfo = roleInfo.scalar_one_or_none()
    if not roleInfo:
      raise ClientVisibleException("角色不存在")
    return roleInfo.prompt
  return ""

# 辅助函数：获取或创建会话
async def get_or_create_conversation(db, username, chatid, prompt, roleid, systemPrompt):
  Conversation = await db.execute(
    select(ChatConversations)
    .filter(ChatConversations.username == username)
    .filter(ChatConversations.chatid == chatid)
  )
  Conversation = Conversation.scalar_one_or_none()

  if not Conversation:
    # 创建新会话
    roleInfo = None
    if roleid != 1 and roleid:
      roleInfo = await db.execute(select(ChatRoles).filter(ChatRoles.id == roleid))
      roleInfo = roleInfo.scalar_one_or_none()

    Conversation = ChatConversations(
      username=username,
      chatid=chatid,
      title=("(%s)" % roleInfo.rolename if roleInfo else "") + prompt[:30],
      state=1,
      roleid=roleid,
      last_message=prompt[:15],
      create_time=datetime.now(),
    )
    db.add(Conversation)
    await db.flush()
    return Conversation, True
  else:
    # 更新现有会话
    await db.execute(
      sqlalchemy_update(ChatConversations)
      .where(ChatConversations.id == Conversation.id)
      .values(last_message=prompt[:15])
    )
    return Conversation, False

# 辅助函数：处理问题记录
async def handle_question_record(db, username, chatid, prompt, gpt_model, qid, regenerate, files, ip):
  contents = []
  Question = None

  if qid:
    try:
      # 查找用户问题
      query = select(ChatHistory).filter(
        ChatHistory.chatid == chatid,
        ChatHistory.username == username,
        ChatHistory.role == "user",
        ChatHistory.state == 1
      )

      if qid:
        query = query.filter(ChatHistory.id == qid)
      else:
        query = query.order_by(ChatHistory.create_time.desc())

      Question = await db.execute(query)
      Question = Question.scalar_one_or_none()

      if not Question:
        # 创建新问题记录
        # 预处理文件数据，确保base64图片数据被转换为URL
        processed_files = []
        if files:
          for file in files:
            file_type = file.type if hasattr(file, "type") else file["type"]
            file_url = file.url if hasattr(file, "url") else file["url"]

            # 如果是base64图片数据，先保存为文件并获取URL
            if file_type == "image" and file_url.startswith('data:'):
              try:
                tmp, file_content = file_url.split(",", 1)
                mimetype = tmp.split(";")[0].split(":")[1] if ";" in tmp else "image/png"
                # 使用已有的方法保存图片并获取URL
                url = save_image_b64data(
                  file_content, md5(file_content[:1000]) + ".png", max_width=1024
                )
                processed_files.append({
                  "type": "image",
                  "url": url
                })
              except Exception as e:
                logger.error(f"预处理图片时发生错误: {str(e)}")
                # 如果处理失败，跳过该图片
                continue
            else:
              # 对于其他类型或已经是URL的图片，直接保存
              processed_files.append({
                "type": file_type,
                "url": file_url
              })

        # 插入新的用户提问，使用处理后的文件数据
        Question = ChatHistory(
          username=username,
          chatid=chatid,
          content=prompt,
          role="user",
          state=1,
          model=gpt_model,
          files=json.dumps(processed_files) if processed_files else None,
          create_time=datetime.now(),
          ip=ip
        )
        db.add(Question)
        await db.flush()
        Question.qid = Question.id

      # 更新旧回复状态
      await db.execute(
        update(ChatHistory)
        .where(ChatHistory.qid == Question.id)
        .where(ChatHistory.state == 1)
        .where(ChatHistory.role == "assistant")
        .values(state=0)
      )

      # 获取历史记录
      history_data = await get_user_chat_history(username, chatid, Question.id)
      contents = history_data

      # 记录历史数据中的文件内容，用于调试
      for item in contents:
          if item.files:
              logger.info(f"历史记录含文件: role={item.role}, files={item.files}")

    except Exception as e:
      logger.error(f"处理问题记录时发生错误: {str(e)}")
      await db.rollback()
      raise ClientVisibleException("处理问题记录时发生错误") from e
  else:
    try:
      # 预处理文件数据，确保base64图片数据被转换为URL
      processed_files = []
      if files:
        for file in files:
          file_type = file.type if hasattr(file, "type") else file["type"]
          file_url = file.url if hasattr(file, "url") else file["url"]

          # 如果是base64图片数据，先保存为文件并获取URL
          if file_type == "image" and file_url.startswith('data:'):
            try:
              tmp, file_content = file_url.split(",", 1)
              mimetype = tmp.split(";")[0].split(":")[1] if ";" in tmp else "image/png"
              # 使用已有的方法保存图片并获取URL
              url = save_image_b64data(
                file_content, md5(file_content[:1000]) + ".png", max_width=1024
              )
              processed_files.append({
                "type": "image",
                "url": url
              })
            except Exception as e:
              logger.error(f"预处理图片时发生错误: {str(e)}")
              # 如果处理失败，跳过该图片
              continue
          else:
            # 对于其他类型或已经是URL的图片，直接保存
            processed_files.append({
              "type": file_type,
              "url": file_url
            })

      # 插入新的用户提问，使用处理后的文件数据
      Question = ChatHistory(
        username=username,
        chatid=chatid,
        content=prompt,
        role="user",
        state=1,
        model=gpt_model,
        files=json.dumps(processed_files) if processed_files else None,
        create_time=datetime.now(),
        ip=ip,
      )
      db.add(Question)
      await db.flush()
      Question.qid = Question.id
    except Exception as e:
      logger.error(f"创建问题记录时发生错误: {str(e)}")
      await db.rollback()
      raise ClientVisibleException("创建问题记录时发生错误") from e

  return Question, contents

# 辅助函数：处理用户上传的文件
async def process_user_files(files):
    send_files = []
    if not files:
        return send_files

    logger.info(f"处理用户上传文件")
    for file in files:
        try:
            # 确保file是typeMediaFiles对象或兼容的dict
            file_type = file.type if hasattr(file, "type") else file["type"]
            file_url = file.url if hasattr(file, "url") else file["url"]

            # 仅处理支持的文件类型
            if file_type == "image":
                # 检查是否为Base64数据
                if file_url.startswith('data:'):
                    try:
                        tmp, file_content = file_url.split(",", 1)
                        mimetype = tmp.split(";")[0].split(":")[1] if ";" in tmp else "image/png"
                        # 使用md5生成唯一文件名并保存图片
                        url = save_image_b64data(
                            file_content, md5(file_content[:1000]) + ".png", max_width=1024
                        )
                        # 只存储文件URL而非完整Base64数据
                        send_files.append(
                            {
                                "type": "image",
                                "ext": "png",
                                "mimetype": mimetype,
                                "url": url,
                                "gs_uri": None,
                            }
                        )
                    except Exception as e:
                        logger.error(f"处理图片时发生错误: {str(e)}")
                        continue
                else:
                    # 已经是URL的情况直接添加
                    send_files.append(
                        {
                            "type": "image",
                            "ext": "png",
                            "mimetype": "image/png",
                            "url": file_url,
                            "gs_uri": None,
                        }
                    )
            elif file_type == "video":
                # 视频处理逻辑
                send_files.append({
                    "type": "video",
                    "url": file_url,
                    "ext": "mp4",
                    "mimetype": "video/mp4",
                    "gs_uri": None,
                })
        except Exception as e:
            logger.error(f"处理文件时发生错误: {str(e)}")
            # 继续处理下一个文件

    logger.info(f"处理后的文件列表: {send_files}")
    return send_files

# 辅助函数：创建或获取回复记录
async def create_or_get_reply(db, username, chatid, qid, gpt_model, ip):
  try:
    # 获取该模型的回复记录
    Reply = await db.execute(
      select(ChatHistory)
      .where(ChatHistory.qid == qid)
      .where(ChatHistory.model == gpt_model)
      .where(ChatHistory.role == "assistant")
    )
    Reply = Reply.scalar_one_or_none()

    # 如果回复过则将状态改成1
    if Reply:
      Reply.state = 1
      await db.commit()
    else:
      # 插入一个新回复
      Reply = ChatHistory(
        username=username,
        chatid=chatid,
        qid=qid,
        content="思考中...",
        role="assistant",
        state=1,
        model=gpt_model,
        create_time=datetime.now(),
        ip=ip,
      )
      db.add(Reply)
      await db.commit()

    return Reply
  except Exception as e:
    logger.error(f"创建或获取回复记录时发生错误: {str(e)}")
    await db.rollback()
    # 尝试使用新的数据库会话重试一次
    try:
      async with AsyncSessionLocal() as new_db:
        Reply = await new_db.execute(
          select(ChatHistory)
          .where(ChatHistory.qid == qid)
          .where(ChatHistory.model == gpt_model)
          .where(ChatHistory.role == "assistant")
        )
        Reply = Reply.scalar_one_or_none()

        if Reply:
          Reply.state = 1
          await new_db.commit()
        else:
          Reply = ChatHistory(
            username=username,
            chatid=chatid,
            qid=qid,
            content="思考中...",
            role="assistant",
            state=1,
            model=gpt_model,
            create_time=datetime.now(),
            ip=ip,
          )
          new_db.add(Reply)
          await new_db.commit()
        return Reply
    except Exception as retry_error:
      logger.error(f"重试创建回复记录时发生错误: {str(retry_error)}")
      raise ClientVisibleException("创建回复记录失败") from retry_error

#  函数中的 SQL 执行方式
def safe_update_conversation(conversation_id, last_message):
    """使用独立事务安全地更新会话记录"""
    max_retries = 3
    retry_delay = 0.5  # 初始延迟（秒）

    for attempt in range(max_retries):
        # 每次重试使用新的数据库连接，避免事务堆积
        with SessionLocal() as session:
            try:
                # 使用短事务和明确的锁超时设置
                session.execute(text("SET SESSION innodb_lock_wait_timeout = 5"))  # 设置较短的锁等待超时

                # 开始事务，执行更新
                session.execute(
                    sqlalchemy_update(ChatConversations)
                    .where(ChatConversations.id == conversation_id)
                    .values(last_message=last_message[:30])
                )
                session.commit()
                return True
            except Exception as e:
                # 捕获所有异常，包括锁超时异常
                logger.warning(f"更新会话记录失败(尝试 {attempt+1}/{max_retries}): {str(e)}")
                logger.info(f"新代码test")
                try:
                    session.rollback()
                except:
                    pass  # 忽略回滚错误

                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error(f"更新会话记录达到最大重试次数: {str(e)}")
                    return False
    return False


def run_async_task(func, *args):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(func(*args))
    loop.close()

# 回复生成器函数
def reply_generator(
  conversation_id, qid, rid, username, model_config, contents, systemPrompt, ip, userid, loop, use_web
  # cost_credit
):
    # 日志记录
    logger.info(f"开始生成回复: 会话ID={conversation_id}, 问题ID={qid}, 回复ID={rid}")
    logger.info(f"历史内容数量: {len(contents)}")
    for idx, content in enumerate(contents):
        has_files = bool(content.files and len(content.files) > 0)
        logger.info(f"历史内容[{idx}]: role={content.role}, 有文件={has_files}")
        if has_files:
            for fidx, file in enumerate(content.files):
                logger.info(f"  文件[{fidx}]: 类型={file.type}, URL={file.url[:30]}...")

    # 创建新的同步数据库会话
    db = SessionLocal()
    try:
        # 重置停止标志
        generation_flags[username] = False

        action = "chat"
        image_key = "!img:"
        action_key = "<{"
        pattern = r"<%s>(.*?)</%s>"

        # 记录传递给模型的内容
        logger.info(f"向模型发送的内容数量: {len(contents)}")
        for idx, content in enumerate(contents):
            logger.info(f"内容[{idx}]: role={content.role}, files存在={bool(content.files)}")
            if content.files:
                logger.info(f"files内容: {content.files}")

        # 更新回复内容的函数 - 改为同步
        def update_db(text: str, files: list = [], memory: str = ""):
            db.execute(
                sqlalchemy_update(ChatHistory)
                .where(ChatHistory.id == rid)
                .values({"content": text, "files": json.dumps(files), "memory": memory})
            )
            db.commit()

        # 构建响应的函数
        def get_response(text, files=[], memory: str = ""):
            return (
                processResponse(
                    code="0000",
                    data=processData(
                        id=rid,
                        qid=qid,
                        role="assistant",
                        text=text,
                        files=files,
                        memory=memory,
                    )
                ).model_dump_json() + "\n"
            )

        # 先发送初始响应
        yield get_response("思考中...", [])
        all_text = "思考中..."  # 初始化为"思考中..."
        files = []  # 最终的文件列表
        i = 0
        new_memory = ""

        try:
            # 获取用户的记忆 - 改为同步
            UserMem = db.execute(
                select(ChatUserMemory).filter(ChatUserMemory.username == username)
            ).scalar_one_or_none()
            memory = UserMem.memory if UserMem else ""

            # 加载处理模块
            # chatServer = importlib.import_module("service.%s.chat" % model_config["publishers"])
            chatServer = importlib.import_module("service.openai.chat")

            # 增强对历史对话记录的日志记录
            if len(contents) > 1:
                logger.info("发送到模型的历史对话:")
                for i, msg in enumerate(contents):
                    # 显示更多内容，便于调试
                    content_preview = msg.content[:103] + "..." if len(msg.content) > 50 else msg.content
                    # content_preview = msg.content
                    logger.info(f"[{i}] {msg.role}: {content_preview}")
                    if msg.files and msg.files:
                        logger.info(f"包含文件: {len(msg.files)}个")
            else:
                logger.info("没有历史对话记录，仅发送当前消息")

            # 调用AI模型获取回复
            assets_storage_func = partial(
              sync_store_asset_directly,
              user_id=userid,
              biz_id='assistant_chat',
            )
            for reply_text in chatServer.chat(
                model_config.name,
                contents,
                systemPrompt=systemPrompt,
                memory=memory,
                web_search=use_web,
                loop=loop,
                assets_storage_func=assets_storage_func,
            ):
                # 检查是否需要停止生成
                if generation_flags.get(username):
                    all_text += "\n[已停止生成]"
                    update_db(all_text)
                    break

                if not reply_text:
                    continue

                # 处理元数据信息
                if isinstance(reply_text, dict):
                    db.execute(
                        sqlalchemy_update(ChatHistory)
                        .where(ChatHistory.id == rid)
                        .values({
                            "input_tokens": reply_text.get("input_tokens", 0),
                            "output_tokens": reply_text.get("output_tokens", 0),
                        })
                    )
                    db.commit()
                    continue

                if not isinstance(reply_text, str):
                    continue

                # 如果是第一次收到实际内容，清除"思考中..."
                if all_text == "思考中...":
                    all_text = ""

                all_text += reply_text

                # 处理特殊命令
                # !开头是需要执行其他命令,10个字符内不返回给用户
                if (all_text.find("<") == 0 or all_text.find("!img") == 0) and len(all_text) < 10:
                    continue

                # 处理图片生成命令
                key_pos = all_text.find(image_key)
                if key_pos >= 0:
                    action = "image"
                    if key_pos == 0:
                        yield get_response(all_text[key_pos + len(image_key):])
                    else:
                        yield get_response(all_text[:key_pos])
                    continue

                # 处理记忆命令
                if all_text.find("<mem>") == 0:
                    action = "mem"
                    match = re.search(pattern % ("mem", "mem"), all_text)
                    if match:
                        action_content = match.group(1)
                        all_text = all_text[len(match.group(0)):]

                        # 处理记忆内容 - 改为同步
                        if not UserMem:
                            new_memory = action_content
                            UserMem = ChatUserMemory(
                                username=username, memory=action_content
                            )
                            db.add(UserMem)
                            db.commit()
                        else:
                            new_memorys = [item.strip() for item in action_content.split(";")]
                            current_memory = [item.strip() for item in UserMem.memory.split(";")]
                            # 取两个list的差集,得到新的memory
                            new_memory = ";".join(list(set(new_memorys) - set(current_memory)))
                            if len(new_memory) == 0:
                                # 取两个list的差集,得到去掉的记忆
                                new_memory = ";".join(list(set(current_memory) - set(new_memorys)))
                                if len(new_memory) > 0:
                                    new_memory = "消除记忆：" + new_memory
                            # 更新记忆
                            UserMem.memory = ";".join(new_memorys)
                            db.commit()

                        yield get_response("", [], new_memory)
                        continue
                    continue

                # 定期更新数据库
                if i % 15 == 0:
                    update_db(all_text, files, new_memory)
                i += 1

                # 发送响应
                yield get_response(all_text, files, new_memory)

            # 处理图片生成
            if action == "image":
                key_pos = all_text.find(image_key)
                prompt = all_text[key_pos + len(image_key):]
                # 调整最终保存的内容
                all_text = all_text.replace(image_key, "")
                yield get_response(prompt + " --- 创作中(By OpenAi Dall-e-3) ...")

                # 需要实现图片生成功能
                # res = create_image(prompt, "dall-e-3", "1024x1024", 1, None)
                # logger.info("create image done: %s" % json.dumps(res))
                # yield get_response("创作完成,正在为您展示.")
                # try:
                #   url = save_image_by_url(res["data"][0]["url"])
                # except Exception as e:
                #   logger.error("error: %s" % e)
                #   url = res["data"][0]["url"]
                # files = [
                #   {
                #     "type": "image",
                #     "ext": "png",
                #     "mimetype": "image/png",
                #     "url": url,
                #     "gs_uri": None,
                #   }
                # ]
                # yield get_response(prompt, files)

            # 使用安全的更新函数更新会话最后消息
            if not safe_update_conversation(conversation_id, all_text):
                logger.warning(f"无法更新会话ID={conversation_id}的最后消息")
            # 更新会话最后消息 - 改为同步
            db.execute(
                sqlalchemy_update(ChatConversations)
                .where(ChatConversations.id == conversation_id)
                .values({"last_message": all_text[:30]})
            )

        except Exception as e:
            logger.error(f"生成回复时发生错误: {str(e)}")
            if action == "image":
                all_text = "图片创建失败，请稍后再试"
            elif all_text == "思考中...":
                all_text = "请求错误，请稍后再试"

            yield get_response(all_text, [])
        finally:
            # 清理标志
            generation_flags.pop(username, None)
            # 最终更新数据库
            update_db(all_text, files, new_memory)
    finally:
        # 确保关闭数据库会话
        db.close()


@router.post("/delete", tags=["chat"], response_model=success)
async def delete(
  chatid: Annotated[int, Body()],
  xx: Annotated[str, Body()] = None,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
    删除一个会话,更新会话状态为 0 ,不会删除记录
    xx是一个无效的参数,因为只有一个参数的时候会报参数错误
    """
  # 更新 ChatHistory 的状态
  await db.execute(
    sqlalchemy_update(ChatHistory)
    .where(ChatHistory.username == user.username)
    .where(ChatHistory.chatid == chatid)
    .values(state=0)
  )

  # 更新 ChatConversations 的状态
  await db.execute(
    sqlalchemy_update(ChatConversations)
    .where(ChatConversations.username == user.username)
    .where(ChatConversations.chatid == chatid)
    .values(state=0)
  )

  # 提交更改
  await db.commit()

  return success()


@router.post("/updateConversationTitle", tags=["chat"], response_model=success)
async def updateConversationTitle(
  chatid: int = Body(...),
  title: str = Body(...),
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  await db.execute(
    sqlalchemy_update(ChatConversations)
    .where(ChatConversations.username == user.username)
    .where(ChatConversations.chatid == chatid)
    .values(title=title)
  )
  await db.commit()
  return success()


class textInput(BaseModel):
  text: str


@router.post("/meta_prompt", tags=["chat"])
async def meta_prompt(input: textInput):
  prompt = await generate_prompt(input.text)

  return {"code": "0000", "data": {"meta_prompt": prompt}}


@router.post("/stop_generation", tags=["chat"], response_model=success)
async def stop_generation(
  user: User = Depends(get_request_user),
):
  """
  停止当前正在进行的文本生成
  """
  try:
    # 设置停止标志
    generation_flags[user.username] = True

    # 给流处理一些时间来响应停止标志
    await asyncio.sleep(0.5)

    logger.info(f"用户 {user.username} 停止了文本生成")
    return success(data={"status": "stopped"})
  except Exception as e:
    logger.error(f"停止生成时发生错误: {str(e)}")
    return success(data={"status": "error", "message": str(e)})
