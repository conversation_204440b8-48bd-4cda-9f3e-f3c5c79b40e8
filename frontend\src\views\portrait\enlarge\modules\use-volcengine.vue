<script setup lang="ts">
import { computed, onBeforeUnmount, ref } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { fetchVolcengineEnlarge } from '@/service/api/portrait';
import type { VolcengineEnlargeRequest } from '@/service/api/portrait';
// import { $t } from '@/locales';

// 消息提示
const message = useMessage();

// 图片上传预览数据
const originalImage = ref<{ url: string; name: string } | null>(null);
const processedImage = ref<string>('');

// 处理参数
const originalSize = ref<string>('default');
const enableHDR = ref<boolean>(false);
const enableWB = ref<boolean>(false);
const hdrStrength = ref<number>(1.0);
const resultFormat = ref<number>(0); // 0:png, 1:jpeg
const jpgQuality = ref<number>(95);
const isLoading = ref<boolean>(false);

// 滑块控制
const sliderPosition = ref<number>(50);
const isSliding = ref<boolean>(false);
const sliderRef = ref<HTMLElement | null>(null);

// 上传限制
const beforeUpload = (data: { file: UploadFileInfo }): boolean => {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (!allowedImageTypes.includes(data.file.file?.type || '')) {
    message.error('请上传正确的图片格式（JPG、PNG、GIF、WEBP）');
    return false;
  }

  // 清除之前的处理结果
  processedImage.value = '';

  return true;
};

// 自定义上传请求
const customRequest = ({ file, onFinish }: { file: UploadFileInfo; onFinish: () => void }) => {
  const reader = new FileReader();
  reader.onload = e => {
    if (e.target && e.target.result && file.file) {
      originalImage.value = {
        url: e.target.result as string,
        name: file.file.name
      };
    }
    onFinish();
  };

  if (file.file) {
    reader.readAsDataURL(file.file);
  } else {
    message.error('图片上传失败');
    onFinish();
  }
};

// 移除已上传图片
const handleRemove = () => {
  originalImage.value = null;
  processedImage.value = '';
};

// // 原始尺寸选项
// const sizeOptions = [
//   { label: '原始尺寸', value: 'default' },
//   { label: '2倍', value: '2x' },
//   { label: '3倍', value: '3x' },
//   { label: '4倍', value: '4x' }
// ];

// 图片尺寸
const originalImageSize = ref<string>('');
const enlargeImageSize = ref<string>('');

// 生成处理
const handleGenerate = async () => {
  if (!originalImage.value) {
    message.error('请先上传图片');
    return;
  }

  try {
    isLoading.value = true;

    // 构建请求数据
    const requestData: VolcengineEnlargeRequest = {
      file: [
        {
          filename: originalImage.value.name,
          data: originalImage.value.url
        }
      ],
      enlarge: originalSize.value,
      enable_hdr: enableHDR.value,
      enable_wb: enableWB.value,
      result_format: resultFormat.value,
      jpg_quality: jpgQuality.value,
      hdr_strength: hdrStrength.value
    };

    // 调用API
    const response = await fetchVolcengineEnlarge(requestData);

    if (response.data.image_url) {
      processedImage.value = response.data.image_url;
      originalImageSize.value = response.data.original_size;
      enlargeImageSize.value = response.data.enlarge_size;
      message.success('图片处理成功');
    } else {
      throw new Error('处理失败');
    }
  } catch (error) {
    console.error('处理错误:', error);
  } finally {
    isLoading.value = false;
  }
};

// 处理下载
const handleDownload = () => {
  if (!processedImage.value) {
    message.error('没有可下载的图片');
    return;
  }

  // 显示加载状态
  isLoading.value = true;

  // 使用fetch获取图片内容
  fetch(processedImage.value)
    .then(response => response.blob())
    .then(blob => {
      // 创建Blob URL
      const blobUrl = URL.createObjectURL(blob);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `enhanced_${originalImage.value?.name || 'image.png'}`;
      link.style.display = 'none';
      document.body.appendChild(link);

      // 触发下载
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    })
    .catch(error => {
      console.error('下载错误:', error);
    })
    .finally(() => {
      isLoading.value = false;
    });
};

// 是否显示对比视图
const showComparison = computed(() => {
  return originalImage.value && processedImage.value;
});

// 是否启用处理按钮
const canProcess = computed(() => {
  return Boolean(originalImage.value) && !isLoading.value;
});

// 是否启用下载按钮
const canDownload = computed(() => {
  return Boolean(processedImage.value) && !isLoading.value;
});

// 处理滑块拖动
const onSlideMove = (e: MouseEvent) => {
  if (!isSliding.value || !sliderRef.value) return;

  const sliderRect = sliderRef.value.getBoundingClientRect();
  const newPosition = ((e.clientX - sliderRect.left) / sliderRect.width) * 100;

  // 限制在0-100范围内
  sliderPosition.value = Math.max(0, Math.min(100, newPosition));
};

const onSlideEnd = () => {
  isSliding.value = false;
  document.removeEventListener('mousemove', onSlideMove);
  document.removeEventListener('mouseup', onSlideEnd);
};

const onSlideStart = (e: MouseEvent) => {
  isSliding.value = true;
  document.addEventListener('mousemove', onSlideMove);
  document.addEventListener('mouseup', onSlideEnd);
  e.preventDefault();
};

// 处理触摸事件（适配移动端）
const onTouchMove = (e: TouchEvent) => {
  if (!isSliding.value || !sliderRef.value || !e.touches[0]) return;

  const sliderRect = sliderRef.value.getBoundingClientRect();
  const newPosition = ((e.touches[0].clientX - sliderRect.left) / sliderRect.width) * 100;

  // 限制在0-100范围内
  sliderPosition.value = Math.max(0, Math.min(100, newPosition));
};

const onTouchEnd = () => {
  isSliding.value = false;
  document.removeEventListener('touchmove', onTouchMove);
  document.removeEventListener('touchend', onTouchEnd);
};

const onTouchStart = (e: TouchEvent) => {
  isSliding.value = true;
  document.addEventListener('touchmove', onTouchMove);
  document.addEventListener('touchend', onTouchEnd);
  e.preventDefault();
};

// 移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', onSlideMove);
  document.removeEventListener('mouseup', onSlideEnd);
  document.removeEventListener('touchmove', onTouchMove);
  document.removeEventListener('touchend', onTouchEnd);
});
</script>

<template>
  <div class="image-enhance-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <NCard title="图像增强选项" class="options-card">
        <NForm label-placement="left" label-width="100">
          <!--
 <NFormItem label="图片尺寸">
            <NSelect v-model:value="originalSize" :options="sizeOptions" />
          </NFormItem> 
-->

          <NFormItem label="HDR增强">
            <NSwitch v-model:value="enableHDR">
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </NSwitch>
          </NFormItem>

          <NFormItem label="白平衡优化">
            <NSwitch v-model:value="enableWB">
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </NSwitch>
          </NFormItem>

          <NFormItem v-if="enableHDR" label="HDR强度">
            <NSlider v-model:value="hdrStrength" :min="0.1" :max="2" :step="0.1" />
          </NFormItem>

          <NFormItem label="输出格式">
            <NRadioGroup v-model:value="resultFormat">
              <NRadio :value="0">PNG</NRadio>
              <NRadio :value="1">JPEG</NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem v-if="resultFormat === 1" label="JPEG质量">
            <NSlider v-model:value="jpgQuality" :min="70" :max="100" :step="1" />
          </NFormItem>

          <NFormItem>
            <NButton type="primary" :disabled="!canProcess" :loading="isLoading" block @click="handleGenerate">
              开始生成
            </NButton>
          </NFormItem>

          <NFormItem>
            <NButton :disabled="!canDownload" block @click="handleDownload">下载</NButton>
          </NFormItem>
        </NForm>
      </NCard>
    </div>

    <!-- 图片上传和对比预览区域 -->
    <div class="preview-section">
      <div v-if="!showComparison" class="upload-area">
        <NUpload
          :custom-request="customRequest"
          accept="image/jpeg,image/png,image/gif,image/webp"
          :show-file-list="false"
          @before-upload="beforeUpload"
        >
          <NUploadDragger>
            <div class="upload-content">
              <div v-if="originalImage" class="image-preview">
                <NImage :src="originalImage.url" object-fit="contain" preview-disabled :width="300" />
                <div class="remove-btn">
                  <NButton text type="error" @click.stop="handleRemove">
                    <template #icon>
                      <icon-ic-round-delete class="text-icon" />
                    </template>
                    删除
                  </NButton>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <icon-mdi-cloud-upload class="upload-icon" />
                <div class="upload-text">
                  <p>上传图片</p>
                  <p class="upload-hint">可拖入文件或者点击上传</p>
                </div>
              </div>
            </div>
          </NUploadDragger>
        </NUpload>
      </div>

      <!-- 对比预览 -->
      <div v-if="showComparison" class="comparison-view">
        <div class="comparison-labels">
          <!-- {{ originalImageSize }}  {{ enlargeImageSize }}  -->
          <div class="label">放大后</div>
          <div class="label">放大前</div>
        </div>
        <div ref="sliderRef" class="comparison-slider">
          <div class="image-container">
            <div class="original-image" :style="{ backgroundImage: `url(${originalImage?.url})` }"></div>
            <div
              class="processed-image"
              :style="{
                backgroundImage: `url(${processedImage})`,
                clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`
              }"
            ></div>
            <div
              class="slider-divider"
              :style="{ left: `${sliderPosition}%` }"
              @mousedown="onSlideStart"
              @touchstart="onTouchStart"
            >
              <div class="slider-handle"></div>
            </div>
          </div>
        </div>
        <div class="comparison-actions">
          <NButton size="small" @click="handleRemove">重新上传</NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.image-enhance-container {
  display: flex;
  gap: 24px;

  .preview-section {
    flex: 1;
    min-height: 500px;
    border-radius: 8px;
    overflow: hidden;

    .upload-area {
      height: 100%;
      min-height: 500px;

      :deep(.n-upload) {
        height: 100%;
      }

      :deep(.n-upload-trigger) {
        height: 100%;
      }

      :deep(.n-upload-dragger) {
        height: 100%;
        min-height: 500px;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .image-preview {
          position: relative;

          .remove-btn {
            position: absolute;
            bottom: 5px;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            padding: 4px;
          }
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;

          .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--primary-color);
          }

          .upload-text {
            text-align: center;

            p {
              margin: 4px 0;
            }

            .upload-hint {
              font-size: 12px;
              color: var(--text-color-3);
            }
          }
        }
      }
    }

    .comparison-view {
      height: 100%;
      min-height: 500px;
      position: relative;
      overflow: hidden;

      .comparison-labels {
        position: absolute;
        top: 10px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        z-index: 10;

        .label {
          background: rgba(0, 0, 0, 0.5);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
      }

      .comparison-slider {
        position: relative;
        height: 100%;
        overflow: hidden;

        .image-container {
          position: relative;
          width: 100%;
          height: 100%;

          .original-image,
          .processed-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
          }

          .slider-divider {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 4px;
            background: white;
            cursor: ew-resize;
            z-index: 5;

            .slider-handle {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 30px;
              height: 30px;
              background: white;
              border-radius: 50%;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            }
          }
        }
      }

      .comparison-actions {
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 10;
      }
    }
  }

  .control-panel {
    width: 300px;

    .options-card {
      height: 100%;
    }
  }
}
</style>
