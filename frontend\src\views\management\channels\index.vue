<script setup lang="ts">
import { h, ref } from 'vue';
import { NButton, NCard, NPopconfirm, NSwitch, useMessage } from 'naive-ui';
import { useTable, useTableOperate } from '@/hooks/common/table';
// 引入接口
import { addChannel, fetchChannelList, fetchDelChannel /*, updateChannel */ } from '@/service/api';

// 频道管理的搜索组件、抽屉组件
import ChannelsSearch from './modules/channels-search.vue';
import ChannelsDrawer from './modules/channels-operate-drawer.vue';

// 定义接口返回的单条频道数据类型，继承自 NaiveUI.TableData 以配合 useTable
interface ChannelItem extends NaiveUI.TableData {
  id: number;
  channel_name?: string | undefined;
  permission?: 'public' | 'private';
  creator?: string;
  creator_team?: string;
  create_time?: string;
  update_time?: string;
  modifier?: number;
}

// 获取 Naive UI 全局消息
const message = useMessage();

/**
 * 1. 定义获取频道列表的 API 函数
 *
 *    - 约定请求参数、返回数据的类型，方便 useTable 做泛型推断
 *    - 返回数据时，useTable 会将内部的 records / current / size / total 做统一处理
 */
const fetchChannelListFn: NaiveUI.TableApiFn<
  ChannelItem, // 表格每行的数据类型
  {
    current: number;
    size: number;
    channel_name?: string;
    permission?: string;
  }
> = async params => {
  const { current, size } = params;
  const res = await fetchChannelList(current, size);
  return {
    data: {
      records: res.data.channels ?? [],
      current,
      size,
      total: res.data.total ?? 0
    },
    error: null,
    code: '0000'
  };
};

const editHandler = ref<(id: number) => void>(() => {});
// const deleteHandler = ref<(id: number) => void>(() => {});

/**
 * 2. 使用 useTable 管理表格数据
 *
 *    - 传入对应的 apiFn
 *    - 传入表格列配置（columns）可以是一个函数
 *    - 传入初始的搜索参数（apiParams）
 *    - 开启 showTotal 后，分页组件会在表格下方展示总数等信息
 */
const {
  loading,
  data, // 响应式的表格数据
  columns, // 响应式的列配置
  getData, // 调用以获取接口数据
  updateSearchParams, // 更新搜索参数
  pagination, // 桌面端分页
  mobilePagination // 移动端分页（若你的项目没有移动端需求，可直接使用 pagination）
} = useTable({
  apiFn: fetchChannelListFn,
  apiParams: {
    current: 1,
    size: 10
  },
  showTotal: true,
  // 定义表格列，写法和你在第一个示例中类似
  columns: () => [
    {
      key: 'channel_name',
      title: '频道名称',
      align: 'center',
      render: (row: ChannelItem) => {
        return h('span', {}, row.channel_name);
      }
    },
    {
      key: 'permission',
      title: '权限',
      align: 'center',
      render: (row: ChannelItem) => {
        // 这里用 h() 来渲染
        return h('span', {}, row.permission === 'public' ? '公共频道' : '私有频道');
      }
    },
    {
      key: 'creator',
      title: '创建者',
      align: 'center',
      render: (row: ChannelItem) => {
        return h('span', {}, row.creator ?? '-');
      }
    },
    {
      key: 'create_time',
      title: '创建时间',
      align: 'center',
      render: (row: ChannelItem) => {
        return h('span', {}, row.create_time ?? '-');
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: (row: ChannelItem) => {
        return h(
          NSwitch,
          {
            disabled: true,
            value: row.statu === 1,
            onUpdateValue: async (value: boolean) => {
              try {
                const newStatus = value ? 1 : 2;
                // 调用接口更新状态
                // await updateChannelStatus(row.id, newStatus);
                message.success('状态更新成功');
                row.statu = newStatus; // 更新表格数据
              } catch (error) {
                console.error(error);
                message.error('状态更新失败');
              }
            }
          },
          { default: () => (row.statu === 1 ? '启用' : '禁用') }
        );
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: (row: ChannelItem) => {
        // 这里展示两个按钮：编辑、删除
        return h('div', { class: 'flex-center gap-8px' }, [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => editHandler.value(row.id)
            },
            { default: () => '编辑' }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id)
            },
            {
              default: () => '确认删除？',
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    ghost: true
                  },
                  { default: () => '删除' }
                )
            }
          )
        ]);
      }
    }
  ]
});

const {
  handleAdd, // 新增操作
  handleEdit, // 编辑操作
  drawerVisible,
  operateType,
  editingData, // 当前正在编辑的行数据
  checkedRowKeys, // 已勾选的行
  // onBatchDeleted, // 批量删除后回调
  onDeleted // 单条删除后回调
} = useTableOperate<ChannelItem>(data, getData);

editHandler.value = handleEdit;

/**
 * 4. 处理删除
 *
 *    - 可以直接调用 onDeleted()，内部会调用 getData 并清空选中行
 */
async function handleDelete(id: number) {
  try {
    await fetchDelChannel(id);
    // message.success('删除成功');
    onDeleted();
  } catch (error) {
    console.error(error);
    // message.error('删除失败');
  }
}

/**
 * 5. 添加/编辑后提交抽屉表单
 *
 *    - 根据 operateType 判断是添加还是编辑
 */
async function handleSubmit(formData: { channel_name: string; permission: string }) {
  try {
    if (operateType.value === 'add') {
      await addChannel(formData.channel_name, formData.permission);
      message.success('添加成功');
    } else {
      // 如果要对接编辑接口，可自行添加
      // await updateChannel(editingData.value!.id, formData.channel_name, formData.permission)
      message.success('编辑成功');
    }
    getData();
  } catch (error) {
    console.error(error);
    message.error(operateType.value === 'add' ? '添加失败' : '编辑失败');
  } finally {
    drawerVisible.value = false;
  }
}

/**
 * 6. 处理搜索
 *
 *    - 调用 updateSearchParams 更新搜索条件
 *    - 将页码重置为 1，并再次请求数据
 */
function handleSearch(params: Partial<{ channel_name: string; permission: string }>) {
  updateSearchParams({
    current: 1, // 重置到第一页
    size: pagination.pageSize, // 保持每页大小不变
    ...params
  });
  getData();
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <!-- 搜索组件 -->
    <ChannelsSearch class="mb-5" @search="handleSearch" />

    <NCard title="频道管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <!-- v-model:columns="columnChecks" 列设置参数 -->
        <TableHeaderOperation :loading="loading" @add="handleAdd" @refresh="getData" />

        <!--
 <NButton type="primary" class="mr-2" @click="handleAdd">新增频道</NButton>
        <NButton :loading="loading" @click="getData">刷新</NButton>
-->
      </template>

      <!-- 表格组件 -->
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />

      <!-- 抽屉组件：新增/编辑 -->
      <ChannelsDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
