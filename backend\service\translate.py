import requests


async def get_translate(text, from_lang="auto", to_lang="zh-CN"):
    """Translates text using Google Translate API."""

    url = "https://translate.googleapis.com/translate_a/single?client=gtx&sl={}&tl={}&dt=t&q={}".format(
        from_lang, to_lang, requests.utils.quote(text)
    )

    response = requests.get(url)
    response.raise_for_status()  # Raise an error for bad status codes

    data = response.json()

    translated_text = ""
    if data and isinstance(data[0], list):
        for item in data[0]:
            translated_text += item[0]
    else:
        raise Exception("Translation error")

    return translated_text
