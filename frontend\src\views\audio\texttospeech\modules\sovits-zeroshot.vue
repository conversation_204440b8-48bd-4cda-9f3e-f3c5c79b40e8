<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import type { UploadCustomRequestOptions, UploadInst } from 'naive-ui';
import WaveSurfer from 'wavesurfer.js';
import type { Region } from 'wavesurfer.js/dist/plugins/regions.js';
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js';
// import HoverPlugin from 'wavesurfer.js/dist/plugins/Hover.js';
import { blobToBase64, blobUrlToAudioBuffer, bufferToWave, fileToArrayBuffer, sliceAudio } from '@/utils/audio';
// import ZoomPlugin from 'wavesurfer.js/dist/plugins/zoom.js';
import { useThemeStore } from '@/store/modules/theme';
import { getUserModels, postExtractSubtitle, postSoVITSZeroShotTast } from '@/service/api';
import { downloadFile } from '@/utils/common';
// import { useFormRules } from '@/hooks/common/form';
// import { getRandomValueInRange } from '@/utils/common';

const themeStore = useThemeStore();
let wavesurfer: WaveSurfer | null = null;
const uploadRef = ref<UploadInst>();
const message = useMessage();
const textInput = ref<HTMLElement>();
// const promptAudioRef = ref(null);
const loading = ref(false);
const playing = ref(false);
const extract_loading = ref(false);
// const uploadLoading = ref(false);
const ttsAudioBlobUrl = ref<string>('');
const promptAudioBlobUrl = ref<string>(''); // 原始的
const tmpPromptAudioBlobUrl = ref<string>(''); // 临时的，编辑后的
// const tmpPromptAudioB64 = ref<string>(''); // 临时的，编辑后的b64
const fileName = ref<string>('');
const model_options = ref<{ label: string; value: string }[]>([]);

// 添加响应式屏幕尺寸检测
const windowWidth = ref(window.innerWidth);
const isSmallScreen = computed(() => windowWidth.value < 1500);

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  getUserModels().then(response => {
    const data = response.data;
    if (Array.isArray(data)) {
      model_options.value = data.map(item => ({ label: item, value: item }));
    }
  });

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 添加onUnmounted生命周期钩子
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const audioStartTime = ref<number>(0); // 音频开始时间
const audioEndTime = ref<number>(0); // 音频结束时间

// # zero_shot usage, <|zh|><|en|><|jp|><|yue|><|ko|> for Chinese/English/Japanese/Cantonese/Korean
const languageOption: CommonType.Option[] = [
  { label: '中文', value: 'zh' },
  { label: '粤语', value: 'yue' },
  { label: '英文', value: 'en' },
  { label: '日文', value: 'jp' },
  { label: '韩语', value: 'ko' }
];
const createDefaultModel = (): Api.Audio.SoVITSZeroShot => {
  return {
    text: '零样本文本到语音 输入 5 秒的声音样本 即刻体验文本到语音转换',
    speed: 1,
    seed: null,
    prompt_text: '',
    prompt_language: 'zh',
    text_language: 'zh',
    temperature: 0.6,
    top_k: 20,
    top_p: 0.6,
    save_model: false
  };
};
const model: Api.Audio.SoVITSZeroShot = reactive(createDefaultModel());

// 是否选择了音频
const hasAudioFile = computed(() => {
  return promptAudioBlobUrl.value !== '';
});
// const clipInfo = computed(() => {
//   if (audioStartTime.value < audioEndTime.value) {
//     return `${audioStartTime.value} - ${audioEndTime.value}`;
//   }
//   return '';
// });
// const { defaultRequiredRule } = useFormRules();
// type GTPRuleKey = Extract<keyof Api.Audio.Cosy, 'text' | 'speaker'>;
// const GPTRules: Record<GTPRuleKey, App.Global.FormRule> = {
//   text: defaultRequiredRule,
//   speaker: defaultRequiredRule
// };

// type ZeroShotRuleKey = Extract<keyof Api.Audio.Cosy, 'prompt_text' | 'prompt_audio' | 'text' | ''>;

onMounted(() => {});
const customRequest = async ({ file, data }: UploadCustomRequestOptions) => {
  fileName.value = '';
  tmpPromptAudioBlobUrl.value = '';
  promptAudioBlobUrl.value = '';
  // console.log(file);
  // uploadLoading.value = true;
  // let duration = 0;
  // file.type
  // mp3=>audio/mpeg
  // mp4=>video/mp4

  const formData = new FormData();
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key as keyof UploadCustomRequestOptions['data']]);
    });
  }
  console.log(file.type);
  if (file.type === 'video/mp4') {
    // mp3=>audio/mpeg
    // mp4=>video/mp4
    const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // 创建一个AudioContext
    const audioCtx = new AudioContext();
    // arrayBuffer转audioBuffer
    await audioCtx.decodeAudioData(buffer, audioBuffer => {
      console.log('audioCtx.decodeAudioData');
      // audioBuffer就是AudioBuffer
      const blob = bufferToWave(audioBuffer, audioBuffer.sampleRate * audioBuffer.duration);
      // duration = audioBuffer.duration;
      // 音频文件生成file上传
      const uuid = Date.now();
      const newFileName = `${uuid}.wav`;
      const audioFile = new File([blob], newFileName, { type: 'audio/wav', lastModified: Date.now() });
      console.log('提取音频添加到表单');
      formData.append('file', audioFile as File);
      // 使用Blob地址,
      const blobUrl = URL.createObjectURL(blob);
      promptAudioBlobUrl.value = blobUrl;
    });
  } else {
    if (file.file) {
      const blobUrl = URL.createObjectURL(file!.file);
      promptAudioBlobUrl.value = blobUrl;
    }

    // const audioCtx = new AudioContext();
    // const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
    // await audioCtx.decodeAudioData(buffer, audioBuffer => {
    //   duration = audioBuffer.duration;
    // });

    formData.append('file', file.file as File);
  }

  // const audio = new Audio(promptAudioBlobUrl);
  // console.log('duration', duration);
  // if (duration > 5) {
  //   // message.error('参考音频最好5秒内，不要超过7秒');

  //   // 只提示，不阻止
  //   // promptAudioBlobUrl.value = '';
  //   // uploadLoading.value = false;
  //   // if (uploadRef.value) {
  //   //   uploadRef.value.clear();
  //   // }
  //   // return;
  // }

  tmpPromptAudioBlobUrl.value = promptAudioBlobUrl.value;

  initWaveSurfer();
  fileName.value = file.name;
  uploadRef.value?.clear();
};
/**
 * 将浮点数截断到指定的小数位数
 *
 * @param num 输入的浮点数
 * @param decimalPlaces 要保留的小数位数
 * @returns 截断后的浮点数
 */
function truncateFloat(num: number, decimalPlaces: number): number {
  const factor = 10 ** decimalPlaces;
  return Math.floor(num * factor) / factor;
}
// 开始裁剪
const clip = async (): Promise<string> => {
  console.log('clip', [truncateFloat(audioStartTime.value, 3), truncateFloat(audioEndTime.value, 3)]);
  const tmpAudioBuffer = await blobUrlToAudioBuffer(promptAudioBlobUrl.value);
  const newAudioBuffer = await sliceAudio(
    tmpAudioBuffer,
    truncateFloat(audioStartTime.value, 3),
    truncateFloat(audioEndTime.value, 3)
  );

  return new Promise((resolve, reject) => {
    try {
      // 音频文件生成file
      const blob = bufferToWave(newAudioBuffer, newAudioBuffer.sampleRate * newAudioBuffer.duration);

      blobToBase64(blob).then(res => {
        resolve(res);
      });
    } catch (error) {
      reject(error);
      message.error('裁剪出错，请调整裁剪区间');
    }
  });
};
const extract = async () => {
  try {
    const b64 = await clip();
    // console.log(b64);
    extract_loading.value = true;
    // if (!model.prompt_audio) {
    //   message.error('未上传音频');
    //   return;
    // }
    if (!b64) {
      message.error('音频转化失败');
      return;
    }
    const params: Api.Media.Subtitle = {
      file_path: '',
      prompt_audio_b64: b64,
      model: 'Gpt-4o mini',
      format: 'text',
      gamecode: '默认',
      translate: ''
    };
    model.prompt_text = '';
    postExtractSubtitle(params)
      .then(res => {
        if (res.data) {
          model.prompt_text = res.data.text;
        }
      })
      .finally(() => {
        extract_loading.value = false;
      });
  } catch (error) {
    console.log(error);
    message.error('音频转化失败,请上传音频文件');
  }
};
function initWaveSurfer() {
  // 先销毁原来的
  wavesurfer?.destroy();
  const regions = RegionsPlugin.create();
  // const zooms = ZoomPlugin.create({ scale: 0.2 });

  let activeRegion: Region | null;
  regions.on('region-in', region => {
    activeRegion = region;
  });
  regions.on('region-out', region => {
    console.log('region-out', region);
    console.log('activeRegion', activeRegion);
    // if (activeRegion === region) {
    //   console.log('same region');
    //   region.play();
    // }
    region.play();
  });
  regions.on('region-clicked', (region, e) => {
    console.log('region-clicked');
    e.stopPropagation(); // prevent triggering a click on the waveform
    // if (!playing.value) {
    //   region.play();
    // }
    region.play();
  });
  // regions.on('region-out', region => {
  //   region.play();
  // });
  regions.on('region-updated', region => {
    console.log(region);
    audioStartTime.value = region.start;
    audioEndTime.value = region.end;
    // audioStartTime.value = Number.parseInt(`${region.start}`, 10);
    // audioEndTime.value = Math.ceil(region.end);
    console.log('区间', region.start, region.end);
  });
  wavesurfer = WaveSurfer.create({
    container: '#waveform', // 绑定容器
    url: promptAudioBlobUrl.value,
    autoCenter: false, // 自动播放
    waveColor: themeStore.themeColor,
    progressColor: '#383351',
    barHeight: 0.7,
    barWidth: 3,
    barRadius: 3,
    cursorWidth: 1,
    barGap: 3,
    hideScrollbar: true,
    dragToSeek: true,
    plugins: [regions]
  });

  wavesurfer.on('decode', () => {});
  /** When the audio is both decoded and can play */
  wavesurfer.on('ready', duration => {
    console.log('Ready', `${duration}s`);
    audioEndTime.value = duration;
    playing.value = false;

    // Regions
    regions.addRegion({
      start: audioStartTime.value,
      end: audioEndTime.value,
      content: '',
      // color: randomColor(),
      color: 'rgba(13, 208, 61, 0.5)',
      drag: true,
      resize: true,
      minLength: 2
    });
  });

  /** When visible waveform is drawn */
  wavesurfer.on('redrawcomplete', () => {
    console.log('Redraw began');
  });

  /** When all audio channel chunks of the waveform have drawn */
  wavesurfer.on('redrawcomplete', () => {
    console.log('Redraw complete');
  });

  /** When the audio starts playing */
  wavesurfer.on('play', () => {
    console.log('Play');
    playing.value = true;
  });

  /** When the audio pauses */
  wavesurfer.on('pause', () => {
    console.log('Pause');
    playing.value = false;
  });
  wavesurfer.on('interaction', () => {
    activeRegion = null;
    wavesurfer?.play();
  });
}

const removeAudio = async () => {
  audioStartTime.value = 0;
  audioEndTime.value = 0;
  promptAudioBlobUrl.value = '';
  tmpPromptAudioBlobUrl.value = '';
  wavesurfer?.destroy();
};
const triggerPlayPause = async () => {
  if (playing.value) {
    wavesurfer?.pause();
  } else {
    wavesurfer?.setTime(audioStartTime.value);
    wavesurfer?.play();
  }
};

const make = async () => {
  console.log('make');

  if (model.reference_id) {
    model.prompt_audio_b64 = '';
  } else {
    if (!promptAudioBlobUrl.value) {
      message.error('请上传音频文件');
      return;
    }
    const b64 = await clip();
    model.prompt_audio_b64 = b64;
  }

  loading.value = true;
  ttsAudioBlobUrl.value = '';

  postSoVITSZeroShotTast(model)
    .then(({ error, data }) => {
      if (!error) {
        ttsAudioBlobUrl.value = data.b64;
        getUserModels().then(response => {
          const usermodel = response.data;
          if (Array.isArray(usermodel)) {
            model_options.value = usermodel.map(item => ({ label: item, value: item }));
          }
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => model.reference_id,
  newVal => {
    if (newVal) {
      model.save_model = false;
      removeAudio();
    }
  }
);

const disabledSelect = ref(false);

watch(hasAudioFile, newValue => {
  if (newValue) {
    model.reference_id = null; // 清空选择的模型
    disabledSelect.value = true; // 禁用选择器
  } else {
    disabledSelect.value = false; // 启用选择器
  }
});

const resultAudio = ref<HTMLAudioElement>();

// 下载音频
const downloadAudio = (url: string, type: string) => {
  if (!url) {
    message.error('暂无音频文件');
    return;
  }
  const fn = fileName.value.replace(/\.[^/.]+$/, '');
  downloadFile(url, `${fn}_${type}.wav`);
};

// 设置播放速率
const setPlaybackRate = (rate: number, type: string) => {
  if (type === 'result' && resultAudio.value) {
    resultAudio.value.playbackRate = rate;
  }
};
</script>

<template>
  <NScrollbar class="h-full">
    <div class="h-[calc(100vh-13em)] flex gap-16px">
      <!-- 左侧操作区域 -->
      <div class="panel-container shrink-0">
        <NCard class="h-full">
          <div class="max-w-full w-250">
            <NGrid x-gap="20">
              <NGi span="24">
                <div class="mt-2 flex justify-center">
                  <NUpload
                    v-show="!hasAudioFile"
                    ref="uploadRef"
                    class="relative"
                    action=""
                    :custom-request="customRequest"
                    accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
                    :show-file-list="false"
                  >
                    <NUploadDragger class="min-h-128px">
                      <NText class="text-lg">点击或者拖动文件到该区域来上传</NText>
                      <NP depth="3" class="mt-3">音频为纯净人声（无背景音、特效音等）、不否包含多人音色</NP>
                    </NUploadDragger>
                  </NUpload>
                </div>
                <div class="n-upload-trigger h-full flex flex-col">
                  <div v-show="hasAudioFile" id="waveform" class="min-h-128px"></div>

                  <div class="flex items-center justify-between p-2">
                    <template v-if="isSmallScreen">
                      <NMarquee class="mr-5 w-50">从音频两端可以拉到选框前裁剪音频</NMarquee>
                    </template>
                    <template v-else>
                      <NText class="flex-1">从音频两端可以拉到选框前裁剪音频&nbsp;&nbsp;</NText>
                    </template>
                    <NButton strong secondary circle size="large" class="h-35px w-35px" @click="triggerPlayPause">
                      <SvgIcon
                        :icon="playing ? 'zondicons:pause-outline' : 'zondicons:play-outline'"
                        class="h-35px w-35px text-blue-600"
                      ></SvgIcon>
                    </NButton>
                    <div class="flex-1">
                      <NTag
                        :closable="hasAudioFile"
                        round
                        :bordered="false"
                        size="large"
                        class="mx-2 h-35px"
                        :on-close="removeAudio"
                      >
                        <template v-if="hasAudioFile">{{ fileName }}</template>
                        <template v-else>未选择音频</template>
                      </NTag>
                    </div>
                  </div>
                </div>
              </NGi>
              <NGi span="24">
                <div class="my-2">
                  <NInput
                    v-model:value="model.prompt_text"
                    type="textarea"
                    class="custom-input h-32"
                    clearable
                    :rows="3"
                    placeholder="示例文本"
                  >
                    <template #suffix>
                      <div class="mb-2 h-7 w-full flex items-center justify-end">
                        <NButton :loading="extract_loading" size="small" @click.stop="extract">从音频提取样本</NButton>
                      </div>
                    </template>
                  </NInput>
                </div>
                <NFormItem label="样本语种" label-placement="left">
                  <NSelect v-model:value="model.prompt_language" :options="languageOption" class="w-1/2" />
                </NFormItem>
              </NGi>
            </NGrid>
            <div class="noneStyle mt-5">
              <NGrid x-gap="20">
                <NFormItemGi span="24" path="text" label="输入待录制文本" class="mb-3">
                  <NInput
                    ref="textInput"
                    v-model:value="model.text"
                    type="textarea"
                    placeholder="输入要转换的文字"
                    class="custom-input min-h-10"
                  >
                    <template #suffix>
                      <div class="mb-2 h-7 w-full flex items-center justify-end">
                        <NSelect
                          v-model:value="model.text_language"
                          size="small"
                          :options="languageOption"
                          class="mx-2 h-10 w-20"
                        />
                      </div>
                    </template>
                  </NInput>
                </NFormItemGi>
                <NFormItemGi span="6" label="语速" class="mb-5">
                  <NSlider v-model:value="model.speed" :step="0.5" :min="0" :max="2" />
                </NFormItemGi>
                <NFormItemGi span="6" label="波动性" class="mb-5">
                  <NSlider v-model:value="model.temperature" :step="0.1" :min="0" :max="1" />
                </NFormItemGi>
                <NFormItemGi span="6" label="Top P 相关性" class="mb-5">
                  <NSlider v-model:value="model.top_p" :step="0.1" :min="0.1" :max="0.9" />
                </NFormItemGi>
                <NFormItemGi span="6" label="Top K 相似性" class="mb-5">
                  <NSlider v-model:value="model.top_k" :step="1" :min="1" :max="20" />
                </NFormItemGi>
              </NGrid>
            </div>
            <div class="w-full">
              <NSpace justify="space-between" align="center">
                <NSpace vertical>
                  <div class="w-20">
                    <NText class="mr-1 text-3.7">模型选择</NText>
                    <NTooltip trigger="hover">
                      <template #trigger>
                        <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                      </template>
                      请选择自定义模型
                      <br />
                      如您需要使用该功能请取消当前上传的音频文件
                    </NTooltip>
                  </div>
                  <NSelect
                    v-model:value="model.reference_id"
                    clearable
                    class="w-80"
                    :options="model_options"
                    :disabled="disabledSelect"
                  />
                </NSpace>
                <NSpace class="w-60">
                  <div>
                    <NText class="mr-1 text-3.7">模型保存</NText>
                    <NTooltip trigger="hover">
                      <template #trigger>
                        <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                      </template>
                      您可以选择将本次结果保存为自定义模型
                      <br />
                      请注意如需要保存模型，本次推理的"样本文字"建议在50个字以内。
                    </NTooltip>
                  </div>
                  <NCheckbox v-model:checked="model.save_model" :disabled="!!model.reference_id" />
                </NSpace>
              </NSpace>
            </div>
            <NSpace justify="center">
              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton type="info" :loading="loading" class="mt-5 h-55px w-70 p-5" @click="make">生成</NButton>
                </template>
                如本次生成效果存在杂音，您可以选择重新生成。
              </NTooltip>
            </NSpace>
          </div>
        </NCard>
      </div>

      <!-- 右侧结果展示区域 -->
      <div class="panel-container">
        <NCard class="h-full">
          <div class="flex flex-col gap-2">
            <NSpin :show="loading">
              <audio ref="resultAudio" :src="ttsAudioBlobUrl" controls class="mt-2 w-full"></audio>
            </NSpin>
            <div class="flex justify-center">
              <NButtonGroup>
                <NButton size="small" @click="downloadAudio(ttsAudioBlobUrl, 'audio')">
                  <template #icon>
                    <SvgIcon icon="mdi:download" />
                  </template>
                  下载
                </NButton>
                <NButton size="small" @click="setPlaybackRate(0.5, 'result')">0.5x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.0, 'result')">1.0x</NButton>
                <NButton size="small" @click="setPlaybackRate(1.5, 'result')">1.5x</NButton>
              </NButtonGroup>
            </div>
          </div>
        </NCard>
      </div>
    </div>
  </NScrollbar>
</template>

<style scoped lang="scss">
:deep(.custom-input) {
  .n-input-wrapper {
    display: flex;
    flex-direction: column;
  }
}

.noneStyle :deep(.n-form-item-feedback-wrapper) {
  display: none;
}

// 左右均等布局样式
.panel-container {
  width: 50%;
  max-width: 820px;
  padding: 0 8px;

  &:first-child {
    padding-left: 0;
  }

  &:last-child {
    padding-right: 0;
  }

  @media screen and (max-width: 1200px) {
    max-width: 600px;
  }
}

:deep(.n-card) {
  height: 100%;
  display: flex;
  flex-direction: column;

  .n-card-header {
    flex-shrink: 0;
  }

  .n-card__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
}

//:deep(.n-scrollbar-rail) {
//  right: 2px !important;
//}

:deep(.n-scrollbar-content) {
  padding-right: 14px;
}
</style>
