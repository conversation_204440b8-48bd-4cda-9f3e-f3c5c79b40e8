<script setup lang="ts">
import { ref } from 'vue';
import MusicGenerate from './modules/music-generate.vue';
import MusicContinues from './modules/music-continues.vue';
//

const tabModel = ref<string>('generate');
</script>

<template>
  <div class="flex-row-stretch gap-16px">
    <NTabs v-model:value="tabModel" type="line" animated>
      <NTabPane name="generate" tab="音乐创作">
        <MusicGenerate />
      </NTabPane>
      <NTabPane name="senior" tab="音乐续写">
        <MusicContinues />
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped lang="scss"></style>
