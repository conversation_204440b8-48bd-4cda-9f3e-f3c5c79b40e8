import { request } from '@/service/request';

export enum ShareType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO'
}

export type ShareInfo = {
  share_key: string;
  share_type: ShareType;
  params: Record<string, string | number | Array<string>>;
  result: Record<string, string | number>;
};

export async function fetchShare(share_key: string) {
  const sp = new URLSearchParams({ key: share_key });
  const res = await request<ShareInfo>({
    url: `/share/records?${sp.toString()}`,
    method: 'get'
  });
  return res.data;
}

export type CreateShare = {
  share_type: ShareType;
  params: Record<string, string | number | string[]>;
  result: Record<string, string | number>;
};

export async function createShare(args: CreateShare) {
  const res = await request<string>({
    url: '/share/create',
    method: 'post',
    data: args
  });
  const share_key = res.data;
  return `${location.origin}/share-page?key=${share_key}`;
}
