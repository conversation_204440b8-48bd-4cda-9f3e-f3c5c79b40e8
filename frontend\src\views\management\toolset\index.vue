<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NButton, NCheckbox, NEllipsis, NImage, NPopconfirm, NRate, NScrollbar, NTag } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchAddTool, fetchDelTool, fetchToolSearch, fetchToolset, fetchUpdataTool } from '@/service/api';
import ToolOperateDrawer from './modules/tool-operate-drawer.vue';
import ToolSearch from './modules/tool-search.vue';

const appStore = useAppStore();
// const isOpen = ref<boolean>(false);

interface ToolData extends NaiveUI.TableData {
  name: string;
  type: string;
  description?: string;
  main_function?: string;
  url?: string;
  recommendation_rating?: number;
  is_paid?: boolean;
  is_available_in_china?: boolean;
  origin_country?: string;
  image_url?: string;
  document?: string;
}

// 获取游戏管理信息
const fetchToolsetTyped: NaiveUI.TableApiFn<ToolData, Api.SystemManage.CommonSearchParams> = async params => {
  const response = await fetchToolset(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<ToolData>>;
};

const searchParams = ref<Api.SystemManage.CommonSearchParams>({
  current: 1,
  size: 10,
  tool_type: '',
  name: ''
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchToolsetTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'name',
      title: 'AI工具',
      align: 'center',
      minWidth: 40
    },
    {
      key: 'type',
      title: '类型',
      align: 'center',
      minWidth: 40,
      render: row => (
        <NTag round type="info">
          {row.type}
        </NTag>
      )
    },
    {
      key: 'description',
      title: '工具介绍',
      align: 'left',
      minWidth: 200,
      render: row => (
        <NScrollbar style="max-height: 10em">
          <div style="white-space: pre-wrap;">
            <NEllipsis line-clamp={3} expand-trigger="click" tooltip={false}>
              {row.main_function}
            </NEllipsis>
          </div>
        </NScrollbar>
      )
    },
    {
      key: 'main_function',
      title: '主要功能',
      align: 'left',
      minwidth: 200,
      render: row => (
        <NScrollbar style="max-height: 10em">
          <div style="white-space: pre-wrap;">
            <NEllipsis line-clamp={3} expand-trigger="click" tooltip={false}>
              {row.main_function}
            </NEllipsis>
          </div>
        </NScrollbar>
      )
    },
    {
      key: 'url',
      title: '使用地址',
      align: 'center',
      minwidth: 60,
      render: row => (
        <div style="white-space: pre-wrap;">
          <NEllipsis>{row.url}</NEllipsis>
        </div>
      )
    },
    {
      key: 'recommendation_rating',
      title: '推荐指数',
      align: 'center',
      minWidth: 80,
      render: row => <NRate value={row.recommendation_rating} readonly />
    },
    {
      key: 'is_paid',
      title: '是否收费',
      align: 'center',
      maxwidth: 50,
      render: row => <NCheckbox checked={row.is_paid} />
    },
    {
      key: 'is_available_in_china',
      title: '国内可用',
      align: 'center',
      maxwidth: 50,
      render: row => <NCheckbox checked={row.is_available_in_china} />
    },
    {
      key: 'origin_country',
      title: '来源',
      align: 'center',
      width: 60
    },
    {
      key: 'image_url',
      title: '展示图片',
      align: 'center',
      maxwidth: 50,
      render: row => (
        <NImage src={row.image_url} style="width: 100px; height: 100px; object-fit: cover; object-position: center;" />
      )
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.name)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.name)}>
            {{
              default: () => '确认删除？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, handleAdd, handleEdit, checkedRowKeys, onDeleted } = useTableOperate(data, getData);

// 删除
async function handleDelete(name: string) {
  try {
    // console.log(gamecode);
    await fetchDelTool(name);

    // 如果请求成功，调用 onDeleted
    onDeleted();
  } catch {}
}

// 编辑信息表单数据
const editData = ref<any>(null);
function edit(name: string) {
  const rowData = data.value.find(item => item.name === name);
  if (rowData) {
    editData.value = rowData;
  }
  handleEdit(name as any);
}

// 查询
async function handleSearch(params: Api.SystemManage.CommonSearchParams) {
  searchParams.value = params;
  // const newParams = { ...searchParams.value };
  const response = await fetchToolSearch(searchParams.value);
  data.value = response.data.records; // 更新表格数据
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

// 添加新游戏信息
async function handleSubmit(toolData: any) {
  try {
    if (operateType.value === 'add') {
      await fetchAddTool(toolData);
    } else if (operateType.value === 'edit') {
      await fetchUpdataTool(toolData);
    }
    getData();
  } finally {
    drawerVisible.value = false;
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ToolSearch v-model:model="searchParams" @search="handleSearch" />
    <NCard title="游戏管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns as DataTableColumns "
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.gamecode"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <ToolOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editData"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
