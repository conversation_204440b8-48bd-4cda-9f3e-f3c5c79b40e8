.markdown-body {
  background-color: transparent;
  font-size: 1rem;

  p {
    white-space: pre-wrap;
  }

  ol {
    list-style-type: decimal;
    padding-left: 2em;
    margin: 1em 0;
  }

  ul {
    list-style-type: disc;
    padding-left: 2em;
    margin: 1em 0;
  }

  pre code,
  pre tt {
    line-height: 1.65;
  }

  // 改进标题样式
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 1.5em;
    margin-bottom: 1em;
    font-weight: 600;
    line-height: 1.25;
  }

  h1 {
    font-size: 1.6em;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.4em;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.25em;
  }

  // 改进图片样式
  img {
    max-width: 100%;
    border-radius: 4px;
    margin: 1em 0;
  }

  // 改进表格样式
  table {
    border-collapse: collapse;
    margin: 1em 0;
    overflow-x: auto;
    width: 100%;

    th,
    td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
    }

    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  // 改进链接样式
  a {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .highlight pre,
  pre {
    background-color: #fff;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.45;
    overflow: auto;
    padding: 16px;
    margin: 1em 0;
  }

  code.hljs {
    padding: 0;
    background: transparent;
  }

  // 行内代码样式优化
  code:not(.hljs) {
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    font-size: 85%;
    margin: 0;
    padding: 0.2em 0.4em;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 40px;
      margin: 1em 0;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &-header {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 40px;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f1f1f1;
      border-bottom: 1px solid #e1e4e8;
      border-radius: 6px 6px 0 0;

      &__lang {
        font-size: 0.85em;
        font-weight: 600;
        color: #444;
      }

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        padding: 4px 8px;
        font-size: 0.85em;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        user-select: none;
        transition: all 0.2s ease;

        &:hover {
          color: #65a665;
          background: rgba(101, 166, 101, 0.1);
        }
      }
    }

    &-body {
      margin: 0;
      padding: 1em;
    }
  }

  &.markdown-body-generate > dd:last-child:after,
  &.markdown-body-generate > dl:last-child:after,
  &.markdown-body-generate > dt:last-child:after,
  &.markdown-body-generate > h1:last-child:after,
  &.markdown-body-generate > h2:last-child:after,
  &.markdown-body-generate > h3:last-child:after,
  &.markdown-body-generate > h4:last-child:after,
  &.markdown-body-generate > h5:last-child:after,
  &.markdown-body-generate > h6:last-child:after,
  &.markdown-body-generate > li:last-child:after,
  &.markdown-body-generate > ol:last-child li:last-child:after,
  &.markdown-body-generate > p:last-child:after,
  &.markdown-body-generate > pre:last-child code:after,
  &.markdown-body-generate > td:last-child:after,
  &.markdown-body-generate > ul:last-child li:last-child:after {
    animation: blink 1s steps(5, start) infinite;
    color: #000;
    content: '_';
    font-weight: 700;
    margin-left: 3px;
    vertical-align: baseline;
  }

  @keyframes blink {
    to {
      visibility: hidden;
    }
  }

  // 增强 KaTeX 样式
  .katex-display {
    display: block;
    margin: 1em 0;
    text-align: center;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0;

    > .katex {
      display: inline-block;
      text-align: left;
      max-width: 100%;
    }

    > .katex > .katex-html {
      display: block;
      position: relative;
      overflow-x: auto;
      overflow-y: hidden;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .katex {
    font:
      normal 1.1em KaTeX_Main,
      Times New Roman,
      serif;
    line-height: 1.2;
    white-space: normal;
    text-indent: 0;

    .katex-html {
      white-space: normal;

      .base {
        margin: 0.25em 0;
      }
    }

    .mathit {
      font-family: KaTeX_Math;
      font-style: italic;
    }
  }

  .katexmath-block {
    margin: 1em 0;
    padding: 1em;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow-x: auto;
    overflow-y: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

    &::-webkit-scrollbar {
      height: 3px;
    }
  }

  // 优化 think 思考块样式
  .think-block {
    position: relative;
    margin: 0.5em 0;
    padding: 0.5em 1em;
    border-left: 4px solid #65a665;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    font-size: 0.9em;
    background: rgba(101, 166, 101, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    p:first-child {
      margin-top: 0;
    }

    p:last-child {
      margin-bottom: 0;
    }
  }
}

// 暗黑模式适配
html.dark {
  .markdown-body {
    color: #e5e7eb;

    h1,
    h2 {
      border-bottom-color: #333;
    }

    a {
      color: #58a6ff;
    }

    code:not(.hljs) {
      background-color: rgba(255, 255, 255, 0.1);
    }

    table {
      th,
      td {
        border-color: #333;
      }

      th {
        background-color: #1c1c1c;
      }

      tr:nth-child(even) {
        background-color: #1c1c1c;
      }
    }

    .highlight pre,
    pre {
      background-color: #282c34;
    }

    .code-block-header {
      background: #2d313a;
      border-bottom-color: #333;

      &__lang {
        color: #aaa;
      }

      &__copy {
        background: rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(101, 166, 101, 0.2);
        }
      }
    }

    &.markdown-body-generate > dd:last-child:after,
    &.markdown-body-generate > dl:last-child:after,
    &.markdown-body-generate > dt:last-child:after,
    &.markdown-body-generate > h1:last-child:after,
    &.markdown-body-generate > h2:last-child:after,
    &.markdown-body-generate > h3:last-child:after,
    &.markdown-body-generate > h4:last-child:after,
    &.markdown-body-generate > h5:last-child:after,
    &.markdown-body-generate > h6:last-child:after,
    &.markdown-body-generate > li:last-child:after,
    &.markdown-body-generate > ol:last-child li:last-child:after,
    &.markdown-body-generate > p:last-child:after,
    &.markdown-body-generate > pre:last-child code:after,
    &.markdown-body-generate > td:last-child:after,
    &.markdown-body-generate > ul:last-child li:last-child:after {
      color: #65a665;
    }

    .katexmath-block {
      background: rgba(255, 255, 255, 0.05);
    }

    .katex {
      color: #e5e7eb;
    }
  }

  .message-reply {
    .whitespace-pre-wrap {
      white-space: pre-wrap;
      color: var(--n-text-color);
    }
  }
}

/* 添加打字机效果的样式 */
//.markdown-body-generate {
//  position: relative;
//}
//
//.markdown-body-generate::after {
//  content: '|';
//  position: relative;
//  display: inline-block;
//  animation: cursor-blink 0.8s infinite;
//  font-weight: bold;
//  color: #65a665;
//}

@keyframes cursor-blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@media screen and (max-width: 533px) {
  .markdown-body .code-block-wrapper {
    padding-top: 36px;

    .code-block-header {
      height: 36px;
    }

    code {
      padding: 12px;
      font-size: 13px;
    }
  }
}
