<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { WorksTask } from '@/service/api/midjourney';
import { getWorksTasks } from '@/service/api/midjourney';

const router = useRouter();
const message = useMessage();
const ossBaseUrl = import.meta.env.VITE_OSS_BASE_URL;

const goToMj = () => {
  router.push({ name: 'portrait_midjourney' });
};

const tasks = ref<WorksTask[]>([]);
const totalPage = ref(1);

const filteredTasks = computed(() => {
  return tasks.value.filter(task => task.image_url && task.image_url.includes(ossBaseUrl));
});

const formatPrompt = (prompt: string) => {
  const vIndex = prompt.indexOf('--v');
  const nijiIndex = prompt.indexOf('--niji');

  let index: number;

  if (vIndex !== -1 && nijiIndex !== -1) {
    index = Math.min(vIndex, nijiIndex);
  } else if (vIndex !== -1) {
    index = vIndex;
  } else if (nijiIndex !== -1) {
    index = nijiIndex;
  } else {
    index = -1;
  }

  return index !== -1 ? prompt.substring(0, index).trim() : prompt;
};

const copyPromptToClipboard = (prompt: string) => {
  const formattedPrompt = formatPrompt(prompt);
  navigator.clipboard
    .writeText(formattedPrompt)
    .then(() => {
      message.success('提示词已复制');
    })
    .catch(err => {
      console.error('Failed to copy prompt:', err);
    });
};

const isloading = ref<boolean>(false);

// 瀑布流布局的实现
// const performLayout = (container: HTMLElement, items: HTMLElement[]) => {
//   const containerWidth = container.clientWidth;
//   const desiredColumns = 7; // 列数
//   const minItemWidth = 150; // 最小的图片宽度
//   const maxItemWidth = 300; // 最大的图片宽度
//   const gutter = 7; // 图片之间的间距

//   let itemWidth = Math.floor((containerWidth - (desiredColumns - 1) * gutter) / desiredColumns);
//   itemWidth = Math.max(minItemWidth, Math.min(maxItemWidth, itemWidth));

//   const columns = Math.floor((containerWidth + gutter) / (itemWidth + gutter));

//   const totalWidth = columns * itemWidth + (columns - 1) * gutter;
//   const leftOffset = Math.max(0, (containerWidth - totalWidth) / 2);

//   const columnHeights = Array(columns).fill(0);

//   items.forEach(item => {
//     const minIndex = columnHeights.indexOf(Math.min(...columnHeights));

//     item.style.position = 'absolute';
//     item.style.top = `${columnHeights[minIndex]}px`;
//     item.style.left = `${leftOffset + minIndex * (itemWidth + gutter)}px`;
//     item.style.width = `${itemWidth}px`;

//     columnHeights[minIndex] += item.offsetHeight + gutter;
//   });

//   container.style.height = `${Math.max(...columnHeights)}px`;
// };

// const arrangeMasonryLayout = () => {
//   const container = document.getElementById('waterfall-container');
//   if (!container) return;

//   const items = Array.from(container.children) as HTMLElement[];
//   const itemPromises = items.map(item => {
//     return new Promise<void>(resolve => {
//       const img = item.querySelector('img');
//       if (img && !img.complete) {
//         img.onload = () => resolve();
//         img.onerror = () => resolve();
//       } else {
//         resolve();
//       }
//     });
//   });

//   Promise.all(itemPromises).then(() => {
//     performLayout(container, items);
//   });
// };

const fetchTasks = async (page: number = 1, page_size: number = 32) => {
  try {
    isloading.value = true;
    const response = await getWorksTasks(page, page_size);

    if (response.data) {
      tasks.value = response.data.tasks || [];
      totalPage.value = response.data.total_page || 1;
    } else {
      tasks.value = [];
      totalPage.value = 1;
    }

    // nextTick(() => {
    //   arrangeMasonryLayout();
    // });

    // 延迟1.5秒钟后关闭loading
    setTimeout(() => {
      isloading.value = false;
    }, 1500);
  } catch (error) {
    isloading.value = false;
    console.error('获取任务列表失败:', error);
  }
};

// const debouncedArrangeMasonryLayout = debounce(arrangeMasonryLayout, 200);

onMounted(() => {
  fetchTasks();
  // window.addEventListener('resize', debouncedArrangeMasonryLayout);
});

// onUnmounted(() => {
//   window.removeEventListener('resize', debouncedArrangeMasonryLayout);
// });

// const imageLoaded = () => {
//   nextTick(() => {
//     arrangeMasonryLayout();
//   });
// };
</script>

<template>
  <main class="h-screen flex flex-col gap-2">
    <NScrollbar v-if="isloading" class="loadingbox mb-1 h-185">
      <NButton class="mb-3 w-20" type="success" @click="goToMj">
        <SvgIcon icon="icon-park:return" class="mr-0.5" />
        返回
      </NButton>
      <div class="h-160 flex content-center items-center justify-center">
        <NSpace vertical>
          <div class="loading">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <NText>加载中...</NText>
        </NSpace>
      </div>
    </NScrollbar>
    <NScrollbar v-else class="mb-1 max-h-185">
      <NButton class="mb-3 w-20" type="success" @click="goToMj">
        <SvgIcon icon="icon-park:return" class="mr-0.5" />
        返回
      </NButton>

      <div id="waterfall-container" class="relative w-full flex flex-wrap gap-2">
        <div v-for="(task, index) in filteredTasks" :key="index">
          <NImage
            :src="task.image_url + '?imageView2/1/h/250' || undefined"
            :preview-src="task.image_url || undefined"
            class="imagebox w-full rounded"
            object-fit="contain"
            @click="copyPromptToClipboard(task.prompt)"
          ></NImage>
        </div>
      </div>
    </NScrollbar>
    <NSpace justify="center">
      <NPagination :page-count="totalPage" @update:page="fetchTasks" />
    </NSpace>
  </main>
</template>

<style scoped>
.imagebox {
  background: transparent;
  text-transform: uppercase;
  outline: none;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease;
  cursor: pointer;
  /* height: 150px; */
  min-width: 100px;
}

.imagebox:hover {
  transform: scale(1.1); /* 鼠标悬停时放大 */
}

.imagebox:after {
  content: '';
  display: block;
  position: absolute;
  top: -36px;
  left: -100px;
  background: white;
  width: 50px;
  height: 125px;
  opacity: 20%;
  transform: rotate(-45deg);
}

.imagebox:hover:after {
  left: 120%;
  transition: all 600ms cubic-bezier(0.5, 1, 0.2, 1);
  -webkit-transition: all 600ms cubic-bezier(0.5, 1, 0.2, 1);
}

.imagecard {
  overflow: hidden;
  position: absolute;
  transition: transform 0.3s ease;
}

.loading,
.loading > div {
  position: relative;
  box-sizing: border-box;
}

.loading {
  display: block;
  font-size: 0;
  color: #c384d3ff;
}

.loading.la-dark {
  color: #333;
}

.loading > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.loading {
  width: 42px;
  height: 32px;
}

.loading > div:nth-child(1) {
  position: absolute;
  bottom: 32%;
  left: 18%;
  width: 14px;
  height: 14px;
  border-radius: 100%;
  transform-origin: center bottom;
  animation: ball-climbing-dot-jump 0.6s ease-in-out infinite;
}

.loading > div:not(:nth-child(1)) {
  position: absolute;
  top: 0;
  right: 0;
  width: 14px;
  height: 2px;
  border-radius: 0;
  transform: translate(60%, 0);
  animation: ball-climbing-dot-steps 1.8s linear infinite;
}

.loading > div:not(:nth-child(1)):nth-child(2) {
  animation-delay: 0ms;
}

.loading > div:not(:nth-child(1)):nth-child(3) {
  animation-delay: -600ms;
}

.loading > div:not(:nth-child(1)):nth-child(4) {
  animation-delay: -1200ms;
}

@keyframes ball-climbing-dot-jump {
  0% {
    transform: scale(1, 0.7);
  }

  20% {
    transform: scale(0.7, 1.2);
  }

  40% {
    transform: scale(1, 1);
  }

  50% {
    bottom: 125%;
  }

  46% {
    transform: scale(1, 1);
  }

  80% {
    transform: scale(0.7, 1.2);
  }

  90% {
    transform: scale(0.7, 1.2);
  }

  100% {
    transform: scale(1, 0.7);
  }
}

@keyframes ball-climbing-dot-steps {
  0% {
    top: 0;
    right: 0;
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    top: 100%;
    right: 100%;
    opacity: 0;
  }
}
</style>
