from sqlalchemy import Column, Integer, String, BigInteger
from utils.database import Base
import hashlib


class Admin(Base):
    __tablename__ = "admin"

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    username = Column(String(20), unique=True, nullable=True, default='', comment='用户名')
    nickname = Column(String(50), nullable=True, default='', comment='昵称')
    password = Column(String(32), nullable=True, default='', comment='密码')
    salt = Column(String(30), nullable=True, default='', comment='密码盐')
    avater = Column(String(255), nullable=True, default='', comment='头像')
    email = Column(String(100), nullable=True, default='', comment='电子邮箱')
    mobile = Column(String(11), nullable=True, default='', comment='手机号码')
    loginfailure = Column(Integer, nullable=False, default=0, comment='失败次数')
    logintime = Column(BigInteger, nullable=True, comment='登录时间')
    loginip = Column(String(50), nullable=True, comment='登录IP')
    createtime = Column(BigInteger, nullable=True, comment='创建时间')
    updatetime = Column(BigInteger, nullable=True, comment='更新时间')
    token = Column(String(59), nullable=True, default='', comment='Session标识')
    status = Column(String(30), nullable=False, default='normal', comment='状态')

    def __repr__(self):
      return f"<Admin(id={self.id}, username='{self.username}', nickname='{self.nickname}', email='{self.email}')>"

    def set_password(self, password, salt):
      self.password = hashlib.md5((password + salt).encode('utf-8')).hexdigest()

    def check_password(self, password, salt):
      return self.password == hashlib.md5((password + salt).encode('utf-8')).hexdigest()
