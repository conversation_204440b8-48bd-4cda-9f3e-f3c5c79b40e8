import logging

from fastapi import APIRouter, Depends, Query, Body, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func,delete
from utils.database import get_db
from pydantic import BaseModel
from typing import List
import datetime
from models.users import User, get_roles_checker
from models.roles import Role
from models.user_role import UserRole
from models.menu import Menu
from models.role_menu import RoleMenu
from utils.exceptions import ClientVisibleException

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
logger = logging.getLogger(__name__)


class RoleRes(BaseModel):
  id: int
  roleCode: str
  roleName: str
  roleDesc: str
  createBy: str
  updateBy: str
  status: int
  createTime: datetime.datetime
  updateTime: datetime.datetime

  class Config:
    from_attributes = True

class SaveRoleRequest(BaseModel):
  id: int = 0
  roleCode: str =''
  roleName: str =''
  roleDesc: str =''
  status: int =1

class SaveRoleMenuRequest(BaseModel):
  role_id: int = 0
  menu:list[int]
class SaveRoleUserRequest(BaseModel):
  role_id: int = 0
  user:list[int]
class SaveMenuRoleRequest(BaseModel):
  menu_id: int = 0
  role:list[int]


class PaginatedData(BaseModel):
  records: List[RoleRes]
  current: int
  size: int
  total: int


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()


@router.get("/get_role", response_model=PaginatedResponse, tags=["system"])
async def get_role(
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  roleCode: str = Query(''),
  roleName: str = Query(''),
  status: int = Query(''),
  db: AsyncSession = Depends(get_db)
):
  """
  获取角色管理数据
  """
  where=[]
  if roleCode:
    where.append(Role.roleCode.like(f'%{roleCode}%'))
  if roleName:
    where.append(Role.roleName.like(f'%{roleName}%'))
  if status:
    where.append(Role.status == status)
  async with db as session:
    # 获取分页数据
    result = await session.execute(
      select(Role).filter(*where).offset((page - 1) * size).limit(size)
    )
    records = result.scalars().all()

    # 获取总数
    count_res = await session.execute(select(func.count(Role.id)).filter(*where))
    total = count_res.scalar()

    records_out = [RoleRes.from_orm(record) for record in records]

    return PaginatedResponse(
      data=PaginatedData(
        records=records_out,
        current=page,
        size=size,
        total=total
      ),
      code="0000"
    )

@router.get("/get_role_menu",tags=["system"])
async def get_role_menu(
  id: int = Query(0),
  db: AsyncSession = Depends(get_db)
):
  """
  获取角色菜单
  """
  async with db as session:
    result = await session.execute(
      select(Menu)
      .join(RoleMenu,RoleMenu.menu_id == Menu.id,isouter=True)
      .where(RoleMenu.role_id == id)
    )
    records = result.scalars().all()
    # tree=generate_tree(records,0)
    return {
      "code":"0000",
      "data":{
        "records":records
      },
    }

@router.get("/get_role_user",tags=["system"])
async def get_role_user(role_id: int = Query(0),db: AsyncSession = Depends(get_db)):
  """
  读取角色下的用户
  """
  async with db as session:
    result = await session.execute(
      select(User)
      .join(UserRole,User.id == UserRole.user_id,isouter=True)
      .where(UserRole.role_id == role_id)
    )
    records = result.scalars().all()
    return {
      "code":"0000",
      "data":{
        "records":records
      },
    }
@router.post("/set_role_user",tags=["system"])
async def set_role_user(
  save_data: SaveRoleUserRequest,
  db: AsyncSession = Depends(get_db)
):
  """
  设置角色用户
  """
  async with db as session:
    if save_data.role_id>0 :
      # 删除原来数据
      await session.execute( delete(UserRole).where(UserRole.role_id == save_data.role_id))
      role_user = [UserRole(user_id=user_id, role_id=save_data.role_id) for user_id in save_data.user]
      session.add_all(role_user)
    await session.commit()
    return {"code": "0000", "msg": "提交成功"}

@router.get("/get_menu_role",tags=["system"])
async def get_menu_role(menu_id: int = Query(0),db: AsyncSession = Depends(get_db)):
  """
  读取菜单角色
  """
  async with db as session:
    result = await session.execute(
      select(Role)
      .join(RoleMenu,RoleMenu.role_id == Role.id,isouter=True)
      .where(RoleMenu.menu_id == menu_id)
    )
    records = result.scalars().all()
    return {
      "code":"0000",
      "data":{
        "records":records
      },
    }
@router.post("/set_menu_role",tags=["system"])
async def set_menu_role(
  save_data: SaveMenuRoleRequest,
  db: AsyncSession = Depends(get_db)
):
  """
  设置角色用户
  """
  async with db as session:
    if save_data.menu_id>0 :
      # 删除原来数据
      await session.execute( delete(RoleMenu).where(RoleMenu.menu_id == save_data.menu_id))
      role_menu = [RoleMenu(role_id=role_id, menu_id=save_data.menu_id) for role_id in save_data.role]
      session.add_all(role_menu)
    await session.commit()
    return {"code": "0000", "msg": "提交成功"}

@router.post("/set_role_menu",tags=["system"])
async def set_role_menu(
  save_data: SaveRoleMenuRequest,
  db: AsyncSession = Depends(get_db)
):
  """
  设置角色菜单
  """
  async with db as session:
    if save_data.role_id>0 :
      await session.execute( delete(RoleMenu).where(RoleMenu.role_id == save_data.role_id))
      role_menu = [RoleMenu(menu_id=menu_id, role_id=save_data.role_id) for menu_id in save_data.menu]
      session.add_all(role_menu)
    await session.commit()
    return {"code": "0000", "msg": "提交成功"}



@router.post("/save_role", tags=["system"])
async def save_role(
  save_role_data: SaveRoleRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
):
  """
    更新角色管理数据
  """
  try:
    async with db as session:

      if save_role_data.id > 0:
        logger.debug("update user")
        # 更新角色信息
        result = await session.execute( select(Role).where(Role.id == save_role_data.id) )
        role_in_db = result.scalars().first()
        if not role_in_db:
          raise ClientVisibleException("角色不存在")

        role_in_db.roleCode   = save_role_data.roleCode
        role_in_db.roleName   = save_role_data.roleName
        role_in_db.roleDesc   = save_role_data.roleDesc
        role_in_db.status     = save_role_data.status
        role_in_db.updateBy   = request.state.user.username
        role_in_db.updateTime = datetime.datetime.now()

      else:
        # 创建新角色
        new_role = Role (
          roleCode=save_role_data.roleCode,
          roleName=save_role_data.roleName,
          roleDesc=save_role_data.roleDesc,
          status=save_role_data.status,
          createBy=request.state.user.username,
          updateBy=request.state.user.username,
          createTime=datetime.datetime.now(),
          updateTime=datetime.datetime.now(),
        )
        session.add(new_role)

      await session.commit()

      return {"code": "0000", "msg": "提交成功"}

  except Exception as e:
    logger.error(f"Failed to update role: {e}")
    raise ClientVisibleException("提交失败") from e

@router.post("/del_role", tags=["system"])
async def del_role(
  id: int = Body(0,alias='id',embed=True),
  db: AsyncSession = Depends(get_db)
):
  """
  删除角色数据
  """
  async with db as session:
    await session.execute( delete(Role).where(Role.id == id))
    await session.execute( delete(UserRole).where(UserRole.role_id == id))
    await session.execute( delete(RoleMenu).where(RoleMenu.role_id == id))
    await session.commit()

    return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})
