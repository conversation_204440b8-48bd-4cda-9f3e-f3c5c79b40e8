-bash-4.2$ sudo cat ai-admin.gdsre.cn.conf
server {
        server_name ai-admin.gdsre.cn ai-admin2.gdsre.cn;
        listen 80;
        include /etc/nginx/conf.d/common/whlist.conf;
        #rewrite ^(.*) https://ai-admin.gdsre.cn$1 permanent;
        access_log /data/logs/ai-admin-web.gdsre.cn_acess.log;

	root /data/web/ai-admin/;

        # Block docs and redoc paths with 404
        location ~ ^/(docs|redoc)(/.*)?$ {
            return 404;
        }

        location ~ ^/api/ {
            rewrite ^/api/(.*) /$1 break;

            proxy_pass http://*************:5002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_buffering off;
    	}


        location / {
                proxy_pass http://********:9527;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_buffering off;
        }

        location ^~ /pumpkinaigc/ {
                proxy_pass https://api.pumpkinaigc.online/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_ssl_server_name on;
                proxy_buffering off;
        }
        
        # 
        client_max_body_size 500M;


	#location / {
	#	rewrite ^(.*) https://ai-admin.gdsre.cn$1 permanent;
	#}

}

server {
	#listen 80;
	server_name ai-admin.gdsre.cn ai-admin2.gdsre.cn; 
	client_max_body_size 500M;

	include /etc/nginx/conf.d/common/gdsre.cn-ssl.conf;
	include /etc/nginx/conf.d/common/whlist.conf;


	#fastcgi_connect_timeout 900;
	#fastcgi_send_timeout 900;
	#fastcgi_read_timeout 900;

	proxy_send_timeout 300;
	proxy_read_timeout 300;
	proxy_connect_timeout 300;

	root /data/web/ai-admin/;

	location /stablediffusion {
        	rewrite ^(.*) http://ai-admin.gdsre.cn$1 permanent;
	}

        # Block docs and redoc paths with 404
        location ~ ^/(docs|redoc)(/.*)?$ {
            return 404;
        }

        location ~ ^/api/ {
            rewrite ^/api/(.*) /$1 break;

            proxy_pass http://*************:5002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_buffering off;
        }



	location / {
		proxy_pass http://********:9527;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_set_header Host $host;
#		proxy_cache_bypass $http_upgrade;
		proxy_buffering off;
	}


        location ^~ /pumpkinaigc/ {
                proxy_pass https://api.pumpkinaigc.online/;  
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_ssl_server_name on;
                proxy_buffering off;
        }


	error_log /data/logs/ai-admin-web.gdsre.cn_error.log debug;
    access_log /data/logs/ai-admin-web.gdsre.cn_acess.log;
}
