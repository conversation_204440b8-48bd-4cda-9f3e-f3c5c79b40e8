# coding=utf-8
# https://www.volcengine.com/product/tts
# https://www.volcengine.com/docs/6561/1257543
# error code
# 3000	请求正确	正常合成	正常处理
# 3001	无效的请求	一些参数的值非法，比如operation配置错误	检查参数
# 3003	并发超限	超过在线设置的并发阈值	重试；使用sdk的情况下切换离线
# 3005	后端服务忙	后端服务器负载高	重试；使用sdk的情况下切换离线
# 3006	服务中断	请求已完成/失败之后，相同reqid再次请求	检查参数
# 3010	文本长度超限	单次请求超过设置的文本长度阈值	检查参数
# 3011	无效文本	参数有误或者文本为空、文本与语种不匹配、文本只含标点	检查参数
# 3030	处理超时	单次请求超过服务最长时间限制	重试或检查文本
# 3031	处理错误	后端出现异常	重试；使用sdk的情况下切换离线
# 3032	等待获取音频超时	后端网络异常	重试；使用sdk的情况下切换离线
# 3040	后端链路连接错误	后端网络异常	重试
# 3050	音色不存在	检查使用的voice_type代号	检查参数

# 3001 extract request resource id: get resource id: access denied 是音色没有权限
'''
requires Python 3.6 or later
pip install requests
'''
import logging
import base64
import json
from fastapi import APIRouter, BackgroundTasks, Depends
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.assets import AssetType
from models.users import User, get_request_user
from service.file import save_audio
from utils.asset_storage import store_asset_directly
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.redis import redis
import httpx
import uuid
from typing import Optional
from sse_starlette.sse import EventSourceResponse
import asyncio
import random
from utils.lang import contains_chinese

router = APIRouter()

logger = logging.getLogger(__name__)

VOLCENGINE_API_HOST = app_settings.volcengine_api_host
VOLCENGINE_APPID = app_settings.volcengine_appid
VOLCENGINE_ACCESS_TOKEN = app_settings.volcengine_access_token
VOLCENGINE_CLUSTER = app_settings.volcengine_cluster

# voice_type = "BV001_streaming"
voice_type = "BV700_V2_streaming"
api_url = f"{VOLCENGINE_API_HOST}/api/v1/tts"

header = {"Authorization": f"Bearer;{VOLCENGINE_ACCESS_TOKEN}"}


class VocanoTTSRequest(BaseModel):
    text: str
    voice_type: str
    encoding: str = 'mp3'
    language: str = ''
    emotion: str = ''
    speed_ratio: float = 1.0
    volume_ratio: float = 1.0
    pitch_ratio: float = 1.0


@router.post("/tts", tags=["volcano"])
async def tts(
        req: VocanoTTSRequest,
        background_task: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
        save_sample: Optional[bool] = None,
):
    async with httpx.AsyncClient(timeout=300) as client:
        uid = str(uuid.uuid4())
        reqid = str(uuid.uuid4())
        # 处理中英互译的场景
        language = req.language
        if req.language == 'cn|en':
            if contains_chinese(req.text):
                # 包含中文，那就用中文发音
                language = 'cn'
            else:
                # 否则就是英文发音
                language = 'en'

        try:
            request_json = {
                "app": {
                    "appid": VOLCENGINE_APPID,
                    "token": VOLCENGINE_ACCESS_TOKEN,
                    "cluster": VOLCENGINE_CLUSTER
                },
                "user": {
                    "uid": uid
                },
                "audio": {
                    "voice_type": req.voice_type,
                    "encoding": req.encoding,
                    "speed_ratio": req.speed_ratio,
                    "volume_ratio": req.volume_ratio,
                    "pitch_ratio": req.pitch_ratio,
                    "emotion": req.emotion,
                    "language": language,
                },
                "request": {
                    "reqid": reqid,
                    "text": req.text,
                    "text_type": "plain",
                    "operation": "query",
                    # "with_frontend": 1,
                    # "frontend_type": "unitTson"
                }
            }
            logger.debug(json.dumps(request_json, sort_keys=True, indent=4, ensure_ascii=False))

            # return request_json
            response = await client.post(url=f'{api_url}', json=request_json, headers=header)
            response.raise_for_status()
            resp_json = response.json()
            if 3000 != resp_json["code"]:
                return {"code": resp_json["code"], "msg": resp_json["message"]}

            if data := resp_json.get("data", ""):
                # 将 Base64 格式的音频上传到 OSS
                fn = f'{uid}_{reqid}.{req.encoding}'
                url = save_audio(data, fn)
                resp_json["data"] = url
                background_task.add_task(
                    store_asset_directly,
                    db=db,
                    asset_type=AssetType.AUDIO,
                    url=url,
                    user_id=user.id,
                    biz_id='tts_volcano',
                    parameter=req.model_dump(exclude_none=True),
                )
            return {"code": "0000", "data": resp_json}
        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时，请重试") from e
        except httpx.HTTPStatusError as e:
            logger.error(f"Error {e.response.status_code}: {e.response.reason_phrase}")
            if e.response.status_code == 400:
                logger.error("Error 400: Bad Request")
                resp_json = e.response.json()
                if 3011 == resp_json["code"]:
                    raise ClientVisibleException("无效文本或语种不匹配") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("生成失败，请重试") from e


list_key = "tts_tasks"
rcount = 60


async def get_random_number(b, e):
    return random.randint(b, e)


async def process_task(reqid: str):
    rkey = "task:" + reqid
    r_res_bytes = await redis.get(rkey)
    if r_res_bytes:
        r_res_str = r_res_bytes.decode('utf-8')  # 解码为字符串
        r_res = json.loads(r_res_str)
        if r_res['status'] == 1:
            return
        else:
            # print(f"Starting Task {reqid}")
            result = await get_random_number(1, 20)
            if result == 2:
                # print("Task completed")
                await redis.set(f"task:{reqid}", json.dumps({"status": 1, "task": reqid, "result": result}), 180)
                return


async def event_generator(reqid: str):
    rkey = "task:" + reqid
    for i in range(rcount):
        # first_element_b = await redis.lindex(list_key, 0) # 取队列第一个任务
        first_element_b = await redis.exec("lindex", list_key, 0)
        first_element = first_element_b.decode('utf-8')  # 将字节字符串转换为字符串
        # print(first_element,reqid)
        if first_element == reqid:
            asyncio.create_task(process_task(reqid))  # 执行后台任务
        r_res_bytes = await redis.get(rkey)
        if r_res_bytes:
            r_res_str = r_res_bytes.decode('utf-8')  # 解码为字符串
            r_res = json.loads(r_res_str)
            if r_res['status'] == 1:
                # await redis.lrem(list_key, 1, reqid) # 移除队列任务
                await redis.exec("lrem", list_key, 1, reqid)
                data = json.dumps({"progress": rcount, "total": rcount, "data": r_res})
                yield {"data": data}
                break
        data = json.dumps({"progress": i, "total": rcount, "data": r_res})
        if i >= rcount - 1:
            # await redis.lrem(list_key, 1, reqid)
            await redis.exec("lrem", list_key, 1, reqid)
            data = json.dumps({"progress": i, "total": rcount, "data": '系统超时'})
        yield {"data": data}
        await asyncio.sleep(1)


@router.get("/ttstest", tags=["volcano"])
async def send_notification(q: str | None = None):
    reqid = str(uuid.uuid4())
    message = base64.b64encode(q.encode('utf-8'))
    msg_str = message.decode('utf-8')
    rkey = "task:" + reqid
    await redis.set(rkey, json.dumps({"status": 0, "task": msg_str, "result": ""}), 380)
    # await redis.rpush(list_key,reqid)
    await redis.exec("rpush", list_key, reqid)
    return EventSourceResponse(event_generator(reqid))


@router.get("/ttype2", tags=["volcano"])
async def send_notification(q: str | None = None):
    rst = await get_random_number(10, 99)
    rkey = "task: 扣类型2 视频积分:" + str(rst)
    raise ClientVisibleException("扣除积分")


@router.get("/ttype3", tags=["volcano"])
async def send_notification(q: str | None = None):
    rst = await get_random_number(1, 2)
    if rst == 1:
        raise ValueError("系统错误")
    else:
        raise ClientVisibleException("系统错误，请重试")
