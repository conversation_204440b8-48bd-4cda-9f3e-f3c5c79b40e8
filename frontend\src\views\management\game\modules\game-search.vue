<script setup lang="ts">
import { computed, reactive } from 'vue';
import {
  // useFormRules,  输入格式规则验证
  useNaiveForm
} from '@/hooks/common/form';

defineOptions({
  name: 'GameSearch'
});

interface Emits {
  (e: 'search', params: Api.SystemManage.CommonSearchParams): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  gamecode: '',
  gamename: '',
  lang: [] as string[]
});

// type RuleKey = 'gamecode' | 'gamename';

const rules = computed(() => {
  // const { patternRules } = useFormRules();

  return {
    // gamecode: patternRules,
    // gamename: patternRules
    // lang: patternRules.lang
  };
});

// async function reset() {
//   await restoreValidation();
//   emit('reset');
// }

async function search() {
  await validate();
  // console.log('gamecode:', model);
  const searchParams = {
    gamecode: model.gamecode,
    gamename: model.gamename,
    lang: model.lang.join(','), // 将 lang 数组转换为逗号分隔的字符串
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}

// 多选下拉框的选项
const langOptions = [
  { label: '中文', value: 'zh' },
  { label: '英文', value: 'en' }
  // 可以根据需要添加更多选项
];
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="游戏编号" path="gamecode" class="pr-24px">
          <NInput v-model:value="model.gamecode" placeholder="请输入游戏编号" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="游戏名称" path="gamename" class="pr-24px">
          <NInput v-model:value="model.gamename" placeholder="请输入游戏名称" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="语言" path="lang" class="pr-24px">
          <NSelect v-model:value="model.lang" multiple :options="langOptions" placeholder="请选择语言" />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
