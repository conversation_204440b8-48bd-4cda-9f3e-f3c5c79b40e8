from sqlalchemy import Column, Integer, String, JSON, TIMESTAMP, func
from utils.database import Base
from pydantic import BaseModel


class GameTranslation(Base):
    __tablename__ = "gametranslations"
    id = Column(Integer, primary_key=True, index=True)
    gamecode = Column(String(12), nullable=False, comment='游戏编号，唯一标识每个游戏')
    zh = Column(String(255), nullable=True, comment='中文翻译')
    en = Column(String(255), nullable=True, comment='英文翻译')
    update_time = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    editor = Column(String(12), nullable=True, comment='修改者，记录最后修改该数据的用户')
    company = Column(String(32), nullable=True, comment='游戏所属的公司')


class GameTranslationOut(BaseModel):
    id: int
    gamecode: str
    translations: dict
    zh: str
    en: str
    updatetime: str
    editor: str
    company: str

    class Config:
        from_attributes = True
