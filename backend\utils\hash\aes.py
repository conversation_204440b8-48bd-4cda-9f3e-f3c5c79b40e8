from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from base64 import b64encode, b64decode
from os import urandom

from config import app_settings

"""
# 示例
# key = urandom(32)  # 生成一个256位的随机密钥
# plaintext = "这是一个测试文本"

# encrypted = encrypt(key, plaintext)
# print("加密后的密文:", encrypted)

# decrypted = decrypt(key, encrypted)
# print("解密后的明文:", decrypted.decode())
"""

def encrypt(plaintext, key=None):
    if not key:
        key = b64decode(app_settings.aes_key)
    # 生成随机的初始化向量IV
    iv = urandom(16)
    # 创建加密器
    cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    # 加密数据
    ciphertext = encryptor.update(plaintext.encode()) + encryptor.finalize()
    # 将IV和密文用Base64编码后返回
    return b64encode(iv + ciphertext).decode()


def decrypt(b64_encoded_ciphertext, key=None):
    if not key:
        key = b64decode(app_settings.aes_key)
    # 解码Base64获取原始的二进制数据
    encrypted_data = b64decode(b64_encoded_ciphertext)
    # 分离IV和密文
    iv = encrypted_data[:16]
    ciphertext = encrypted_data[16:]
    # 创建解密器
    cipher = Cipher(algorithms.AES(key), modes.CFB(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    # 解密数据并返回
    data = decryptor.update(ciphertext) + decryptor.finalize()
    return data.decode()
