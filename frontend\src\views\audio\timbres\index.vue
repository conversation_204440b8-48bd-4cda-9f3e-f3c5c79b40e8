<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { useDialog, useMessage } from 'naive-ui';
import type { Lang, ModifyToneReq, SaveTone, TonesListParams } from '@/service/api/audio';
import { deleteTone, getTonesList, updateTone } from '@/service/api/audio';
import TimbresTable from './modules/timbres-table.vue';
import TimbresOperate from './modules/timbres-operate.vue';

// 响应式数据
const loading = ref(false);
const tableRef = ref<InstanceType<typeof TimbresTable>>();
const dialog = useDialog();
const message = useMessage();

// Tab管理
const activeTab = ref('all'); // 'all' 表示我的音色库, 'favorite' 表示收藏音色

// 模态框状态管理
const showOperateModal = ref(false);
const operateType = ref<'add' | 'edit'>('add');
const editingTimbre = ref<any>(null);

// 音频播放状态管理
const currentPlayingId = ref<number | null>(null);
const audioElement = ref<HTMLAudioElement | null>(null);

// 获取音色数据的函数
async function fetchTimbresData(filters?: { gender?: string; language?: string }) {
  loading.value = true;
  try {
    // 根据当前tab设置不同的查询参数
    const params: TonesListParams = {
      size: 10,
      current: 1
    };

    // 根据当前激活的tab添加is_favorite参数
    if (activeTab.value === 'favorite') {
      params.is_favorite = 1; // 只获取收藏的音色
    }
    // 如果是'all'，不添加is_favorite参数，获取所有音色

    // 添加筛选参数
    if (filters) {
      if (filters.gender) {
        // 将显示文本转换为API需要的数值
        if (filters.gender === '男声') {
          params.gender = 1;
        } else if (filters.gender === '女声') {
          params.gender = 0;
        }
      }

      if (filters.language) {
        // 直接使用传入的语言枚举键
        params.lang = filters.language as any;
      }
    }

    const response = await getTonesList(params);

    // 检查响应数据是否存在
    if (!response.data) {
      return {
        data: {
          records: [],
          total: 0,
          page: 1,
          pageSize: 10
        }
      };
    }

    // 将API返回的SaveTone数据转换为表格所需的格式
    const convertedData =
      response.data.records?.map((tone: SaveTone) => {
        // 处理性别字段转换
        let genderText = '';
        if (tone.gender === 1) {
          genderText = '男声';
        } else if (tone.gender === 0) {
          genderText = '女声';
        }

        return {
          id: tone.id,
          name: tone.name,
          gender: genderText,
          language: tone.lang || '',
          description: tone.description || '',
          addTime: tone.create_time,
          isCollect: tone.is_favorite,
          audioUrl: tone.audio_url,
          is_reduction: tone.is_reduction
        };
      }) || [];

    return {
      data: {
        records: convertedData,
        total: response.data.total || 0,
        page: response.data.current || 1,
        pageSize: response.data.size || 10
      }
    };
  } catch (error) {
    console.error('获取音色数据失败:', error);
    // 返回空数据结构以避免错误
    return {
      data: {
        records: [],
        total: 0,
        page: 1,
        pageSize: 10
      }
    };
  } finally {
    loading.value = false;
  }
}

// 监听tab变化，重新获取数据
watch(activeTab, () => {
  // 停止当前播放的音频
  stopCurrentAudio();
  // 刷新表格数据
  tableRef.value?.refreshData();
});

// 停止当前播放的音频
function stopCurrentAudio() {
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value.currentTime = 0;
    audioElement.value = null;
  }
  currentPlayingId.value = null;
}

// 处理收藏切换
async function handleToggleCollect(timbre: any) {
  try {
    // 构建更新请求参数，is_favorite为布尔值（传递取反后的值）
    const updateData: ModifyToneReq = {
      name: timbre.name,
      gender: timbre.gender === '男声' ? 1 : 0,
      lang: timbre.language && timbre.language.trim() ? (timbre.language as Lang) : null,
      description: timbre.description || '',
      audio_url: timbre.audioUrl,
      is_favorite: !timbre.isCollect // 修复：传递取反后的收藏状态
    };

    // 调用API更新音色
    await updateTone(timbre.id, updateData);

    // 更新本地状态
    timbre.isCollect = !timbre.isCollect;

    // 显示成功消息
    message.success(timbre.isCollect ? '已添加到收藏' : '已取消收藏');

    // 如果当前在收藏页面且取消了收藏，需要刷新表格
    if (activeTab.value === 'favorite' && !timbre.isCollect) {
      tableRef.value?.refreshData();
    }
  } catch (error) {
    console.error('更新收藏状态失败:', error);
    message.error('更新收藏状态失败');
  }
}

// 处理预览音频播放
function handlePreview(timbre: any) {
  console.log('预览音色:', timbre);

  // 如果当前音色正在播放，则暂停
  if (currentPlayingId.value === timbre.id) {
    stopCurrentAudio();
    return;
  }

  // 停止其他正在播放的音频
  stopCurrentAudio();

  // 检查音频URL是否存在
  if (!timbre.audioUrl) {
    console.warn('音频URL不存在:', timbre);
    return;
  }

  // 创建新的音频元素并播放
  const audio = new Audio(timbre.audioUrl);
  audioElement.value = audio;
  currentPlayingId.value = timbre.id;

  // 监听音频事件
  audio.addEventListener('ended', () => {
    if (currentPlayingId.value === timbre.id) {
      currentPlayingId.value = null;
      audioElement.value = null;
    }
  });

  audio.addEventListener('error', e => {
    console.error('音频播放失败:', e);
    if (currentPlayingId.value === timbre.id) {
      currentPlayingId.value = null;
      audioElement.value = null;
    }
  });

  // 开始播放
  audio.play().catch(error => {
    console.error('音频播放失败:', error);
    currentPlayingId.value = null;
    audioElement.value = null;
  });
}

// 处理编辑
function handleEdit(timbre: any) {
  console.log('编辑音色:', timbre);
  editingTimbre.value = timbre;
  operateType.value = 'edit';
  showOperateModal.value = true;
}

// 处理删除
function handleDelete(timbre: any) {
  dialog.warning({
    title: '删除',
    content: `确认是否删除？删除后将无法还原。`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await deleteTone(timbre.id);
        message.success('删除成功');
        tableRef.value?.refreshData();
      } catch (error) {
        message.error('删除失败');
        console.error('删除音色失败:', error);
      }
    }
  });
}

// 处理新增音色
function handleAdd() {
  console.log('新增音色');
  operateType.value = 'add';
  editingTimbre.value = null;
  showOperateModal.value = true;
}

// 处理模态框提交成功
function handleSubmitted() {
  console.log('音色添加成功，刷新表格数据');
  // 刷新表格数据
  tableRef.value?.refreshData();
  // message.success('音色已更新');
}

onMounted(() => {
  // 页面加载时可以进行一些初始化操作
});
</script>

<template>
  <div class="timbres-container">
    <NTabs v-model:value="activeTab" type="line" size="large" @update:value="() => {}">
      <NTabPane name="all" tab="我的音色库"></NTabPane>
      <NTabPane name="favorite" tab="收藏音色"></NTabPane>
    </NTabs>
    <TimbresTable
      ref="tableRef"
      :fetch-data="fetchTimbresData"
      :loading="loading"
      :current-playing-id="currentPlayingId"
      @toggle-collect="handleToggleCollect"
      @preview="handlePreview"
      @edit="handleEdit"
      @delete="handleDelete"
      @add="handleAdd"
    />

    <!-- 音色操作模态框 -->
    <TimbresOperate
      v-model:visible="showOperateModal"
      :operate-type="operateType"
      :row-data="editingTimbre"
      @submitted="handleSubmitted"
    />
  </div>
</template>

<style scoped lang="scss">
.timbres-container {
  padding: 16px;
  height: 100%;
}
</style>
