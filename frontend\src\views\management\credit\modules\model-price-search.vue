<script setup lang="ts">
import { computed, reactive } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'ModelPriceSearch'
});

type CapacityOptions = { label: string; value: string }[];

defineProps<{
  capacityOptions: CapacityOptions;
}>();

interface Emits {
  (e: 'search', params: Api.SystemManage.ModelPriceSearch): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

const model = reactive({
  capacity: null,
  model: ''
});

const rules = computed(() => {
  return {};
});

async function search() {
  await validate();
  // console.log('gamecode:', model);
  const searchParams = {
    capacity: model.capacity || '',
    model: model.model,
    current: 1,
    size: 10
  };
  emit('search', searchParams);
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80" class="mt-4">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:6" label="AI 能力" path="gamename" class="pr-24px">
          <NSelect v-model:value="model.capacity" :options="capacityOptions" clearable placeholder="请选择能力" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" label="模型" path="model" class="pr-24px">
          <NInput v-model:value="model.model" placeholder="请输入模型" clearable />
        </NFormItemGi>
        <NFormItemGi>
          <NSpace class="w-full" justify="end">
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              搜索
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
