<script lang="ts" setup>
import { h, onMounted, ref } from 'vue';
import { NButton, useMessage } from 'naive-ui';
import type { DataTableColumns, UploadFileInfo } from 'naive-ui';
import { getNews, postUpload } from '@/service/api';

interface TimelineItem {
  title: string;
  url: string;
  save_file?: string;
  modification_time: string;
  size: string;
}

const message = useMessage();
const isOpenUpload = ref(false);
const timelineItems = ref<TimelineItem[]>([]);
const fileList = ref<UploadFileInfo[]>([]);
const loading = ref(false);

const onPositiveClick = async () => {
  try {
    const formData = new FormData();
    formData.append('file', fileList.value[0].file as File);

    loading.value = true;
    const response = await postUpload(formData);
    if (response.data) {
      message.success('上传成功');
      // 添加新数据到表格的最上方
      timelineItems.value.unshift({
        title: response.data.filename,
        url: response.data.url,
        save_file: response.data.save_file,
        modification_time: response.data.modification_time,
        size: response.data.size
      });
    } else {
      message.error('上传失败');
    }
  } catch {
    message.error('上传失败');
  } finally {
    loading.value = false;
  }
};

const onNegativeClick = async () => {};

const fetchNews = async () => {
  try {
    const response = await getNews();
    if (response.data) {
      timelineItems.value = response.data.map((item: any) => ({
        title: item.filename,
        url: item.url,
        save_file: item.save_file,
        modification_time: item.modification_time,
        size: item.size
      }));

      // 根据修改日期降序排序
      timelineItems.value.sort(
        (a, b) => new Date(b.modification_time).getTime() - new Date(a.modification_time).getTime()
      );
    }
  } catch (error) {
    message.error('获取文件列表失败');
  }
};

onMounted(fetchNews);

const copyToClipboard = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      message.success('链接已复制到剪贴板');
    })
    .catch(() => {
      message.error('复制链接失败');
    });
};

const columns: DataTableColumns<TimelineItem> = [
  { title: '文件名', key: 'save_file', align: 'center' },
  { title: '大小', key: 'size', align: 'center' },
  {
    title: '修改日期',
    key: 'modification_time',
    align: 'center',
    sorter: (a, b) => {
      const dateA = new Date(a.modification_time);
      const dateB = new Date(b.modification_time);
      return dateA.getTime() - dateB.getTime(); // 升序排序
    }
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row: TimelineItem) {
      return [
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: 'small',
            onClick: () => window.open(row.url)
          },
          { default: () => '下载' }
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            size: 'small',
            onClick: () => copyToClipboard(row.url)
          },
          { default: () => '复制链接' }
        )
      ];
    }
  }
];
</script>

<template>
  <main>
    <NSpace class="mt-5 px-25" vertical>
      <NSpace class="mb-5" justify="end">
        <NButton strong secondary type="info" @click="isOpenUpload = true">上传文件</NButton>
      </NSpace>
      <!--
      <NTimeline>
        <NTimelineItem v-for="(item, index) in timelineItems" :key="index" type="info" :title="item.title">
          <NCard hoverable :bordered="false">
            <NSpace justify="end">
              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton text @click="copyToClipboard(item.url)">
                    <SvgIcon icon="bitcoin-icons:share-outline" class="text-2xl"></SvgIcon>
                  </NButton>
                </template>
                复制链接
              </NTooltip>
            </NSpace>
            <NTooltip trigger="hover">
              <template #trigger>
                <a :href="item.url" target="_blank" rel="noopener noreferrer">这是一个链接</a>
              </template>
              点击下载文件
            </NTooltip>
          </NCard>
        </NTimelineItem>
      </NTimeline>
      -->
      <NDataTable :columns="columns" :data="timelineItems" :pagination="false" :bordered="false" />
    </NSpace>

    <NModal
      v-model:show="isOpenUpload"
      :mask-closable="false"
      preset="dialog"
      title="上传文件"
      positive-text="确认"
      negative-text="取消"
      :loading="loading"
      @positive-click="onPositiveClick"
      @negative-click="onNegativeClick"
    >
      <NCard>
        <NSpace align="center" justify="center">
          <NUpload v-model:file-list="fileList" :multiple="false" :max="1" class="">
            <NUploadDragger>
              <div class="mt-3 flex justify-center">
                <SvgIcon icon="icon-park:upload-web" class="text-6xl"></SvgIcon>
              </div>
              <NText class="text-base">点击或者拖动文件到该区域来上传</NText>
              <NP depth="3">请不要上传敏感数据</NP>
            </NUploadDragger>
          </NUpload>
        </NSpace>
      </NCard>
    </NModal>
  </main>
</template>

<style scoped></style>
