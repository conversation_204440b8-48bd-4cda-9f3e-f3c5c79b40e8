data = {
  "ref":"refs\/heads\/master",
  "before":"c7363acf4235e8e992d786adee238f230d3c9714",
  "after":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "compare_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/compare\/c7363acf4235e8e992d786adee238f230d3c9714...2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "commits":[{"id":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "message":"test\n",
  "url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/commit\/2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "author":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "committer":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "verification":None,"timestamp":"2024-06-21T14:43:52+08:00",
  "added":["backend\/test\/test.py"],"removed":[],"modified":[]}],
  "total_commits":1,
  "head_commit":{"id":"2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "message":"test\n","url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin\/commit\/2a13f2c837d0709f07a29b74fa5dcd95114c8074",
  "author":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "committer":{"name":"\u5434\u534e\u7ae0","email":"<EMAIL>","username":""},
  "verification":None,"timestamp":"2024-06-21T14:43:52+08:00",
  "added":["backend\/test\/test.py"],
  "removed":[],"modified":[]},
  "repository":{
    "id":1187,
    "owner":{
      "id":98,"login":"isystem","login_name":"","full_name":"","email":"",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/e0d5dd2ef1b1ac62337fc245f5587039",
      "language":"","is_admin":False,"last_login":"0001-01-01T00:00:00Z",
      "created":"2024-04-15T15:54:27+08:00","restricted":False,"active":False,
      "prohibit_login":False,"location":"","pronouns":"","website":"","description":"",
      "visibility":"private","followers_count":0,"following_count":0,"starred_repos_count":0,
      "username":"isystem"
    },
      "name":"ai-admin","full_name":"isystem\/ai-admin","description":"",
      "empty":False,"private":True,"fork":False,"template":False,"parent":None,"mirror":False,
      "size":862,"language":"","languages_url":"https:\/\/git.gdsre.cn\/api\/v1\/repos\/isystem\/ai-admin\/languages",
      "html_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin","url":"https:\/\/git.gdsre.cn\/api\/v1\/repos\/isystem\/ai-admin",
      "link":"","ssh_url":"<EMAIL>:isystem\/ai-admin.git","clone_url":"https:\/\/git.gdsre.cn\/isystem\/ai-admin.git",
      "original_url":"","website":"","stars_count":0,"forks_count":0,"watchers_count":7,"open_issues_count":0,"open_pr_counter":0,
      "release_counter":0,"default_branch":"master","archived":False,"created_at":"2024-06-21T10:13:24+08:00",
      "updated_at":"2024-06-21T14:36:48+08:00","archived_at":"1970-01-01T08:00:00+08:00","permissions":{"admin":True,"push":True,"pull":True},
      "has_issues":True,
      "internal_tracker":{"enable_time_tracker":True,"allow_only_contributors_to_track_time":True,"enable_issue_dependencies":True},
      "has_wiki":True,"wiki_branch":"master","has_pull_requests":True,
      "has_projects":True,"has_releases":True,"has_packages":True,
      "has_actions":True,"ignore_whitespace_conflicts":False,"allow_merge_commits":True,
      "allow_rebase":True,"allow_rebase_explicit":True,"allow_squash_merge":True,"allow_fast_forward_only_merge":True,
      "allow_rebase_update":True,"default_delete_branch_after_merge":False,"default_merge_style":"merge",
      "default_allow_maintainer_edit":False,"avatar_url":"","internal":False,"mirror_interval":"","object_format_name":"sha1",
      "mirror_updated":"0001-01-01T00:00:00Z","repo_transfer":None
    },
      "pusher":{
        "id":94,"login":"wuhuazhang","login_name":"","full_name":"\u5434\u534e\u7ae0",
      "email":"<EMAIL>,noreply.heyyogame.com,noreply.originmood.com",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/72a9d33dbfb67e314d183eb336ddcf6b",
      "language":"","is_admin":False,"last_login":"0001-01-01T00:00:00Z","created":"2024-04-11T17:33:22+08:00",
      "restricted":False,"active":False,"prohibit_login":False,"location":"","pronouns":"","website":"","description":"",
      "visibility":"limited","followers_count":0,"following_count":0,"starred_repos_count":0,"username":"wuhuazhang"
    },
      "sender":{
        "id":94,"login":"wuhuazhang","login_name":"","full_name":"\u5434\u534e\u7ae0",
      "email":"<EMAIL>,noreply.heyyogame.com,noreply.originmood.com",
      "avatar_url":"https:\/\/git.gdsre.cn\/avatars\/72a9d33dbfb67e314d183eb336ddcf6b","language":"","is_admin":False,
      "last_login":"0001-01-01T00:00:00Z","created":"2024-04-11T17:33:22+08:00","restricted":False,"active":False,
      "prohibit_login":False,"location":"","pronouns":"","website":"","description":"","visibility":"limited","followers_count":0,
      "following_count":0,"starred_repos_count":0,"username":"wuhuazhang"
      }
    }
if 'commits' in data and len( data['commits'] ):
  commit = data['commits'][0]
  print(commit)
  with open('./git.log', 'r') as f:
    data = f.readlines()
    if len( data ):
      last_line = data[-1]
      if 'Already up-to-date.' not in last_line:
        print("ai-admin发布:\nPublisher: %s\nMessage: %s\nLog: %s"%(commit['committer']['name'],commit['message'],last_line))
