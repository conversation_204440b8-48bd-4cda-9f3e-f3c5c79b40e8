import os
from fastapi import APIRouter, File, UploadFile
from datetime import datetime
import math

from utils.exceptions import ClientVisibleException

router = APIRouter()

BASE_URL = "https://ai-admin.gdsre.cn/proxy-default/"
UPLOAD_DIR = './upload/ainews/'


def convert_size(size_bytes):
  if size_bytes == 0:
    return "0B"
  size_units = ("B", "KB", "MB", "GB", "TB")
  i = int(math.log(size_bytes, 1024)) if size_bytes else 0
  size = round(size_bytes / (1024 ** i), 2)
  return f"{size} {size_units[i]}"


def get_file_info(file_path, base_url):
  relative_path = os.path.relpath(file_path, start=UPLOAD_DIR).replace("\\", "/")
  modification_time = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
  file_size = convert_size(os.path.getsize(file_path))
  file_url = f"{base_url}upload/ainews/{relative_path}"

  return {
    "filename": os.path.basename(file_path),
    "url": file_url,
    "save_file": os.path.dirname(relative_path),
    "modification_time": modification_time,
    "size": file_size
  }


# 上传文件接口
@router.post("/newsUpload", tags=["ainews"])
async def upload(file: UploadFile = File(...)):
  save_dir = os.path.join(UPLOAD_DIR, file.filename.rsplit('.', 1)[0])
  os.makedirs(save_dir, exist_ok=True)
  save_file = os.path.join(save_dir, file.filename)

  with open(save_file, 'wb') as f:
    f.write(await file.read())

  file_info = get_file_info(save_file, BASE_URL)
  return {
    "code": "0000",
    "data": file_info
  }


# 获取所有上传文件的接口
@router.get("/all_news", tags=["ainews"])
async def get_all_news():
  if not os.path.exists(UPLOAD_DIR):
    raise ClientVisibleException("Upload directory not found")

  all_files = [
    get_file_info(os.path.join(root, file), BASE_URL)
    for root, _, files in os.walk(UPLOAD_DIR)
    for file in files
  ]

  return {
    "code": "0000",
    "data": all_files,
    "msg": '获取成功',
  }
