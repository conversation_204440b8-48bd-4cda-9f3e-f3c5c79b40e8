<script setup lang="ts">
import { computed } from 'vue';
import { capacityOp } from '@/utils/capacity';

const props = defineProps<{ capacity: number }>();
const texts = computed(() => {
  return capacityOp.toText(props.capacity);
});
</script>

<template>
  <NFlex justify="center">
    <NTag v-for="text in texts" :row-key="text" type="info">{{ text }}</NTag>
  </NFlex>
</template>

<style scoped></style>
