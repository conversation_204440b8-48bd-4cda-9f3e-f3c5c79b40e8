import logging

from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from utils.database import get_db
from langchain_openai import ChatOpenAI
from models.copywriting_records import CopywritingRecord
from models.users import User, get_request_user
from pydantic import BaseModel
from typing import List
import datetime
import time
from sqlalchemy.future import select
from models.game_management import GameManagement
from langchain.schema import SystemMessage, HumanMessage, AIMessage
from sse_starlette.sse import EventSourceResponse
from service.openai.chat import get_token, get_base_url
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)

OPENAI_API_KEY = get_token()
API_BASE_URL = get_base_url()


class GenerateCopywritingPayload(BaseModel):
  game: str
  style: str
  duration: str
  gender: str
  age: str
  region: str
  tags: List[str]
  emotion: str
  scene: str
  description: str
  event: str
  reference: str


class CopywritingRecordOut(BaseModel):
  game: str
  style: str
  duration: str
  gender: str
  age: str
  region: str
  tags: list
  emotion: str
  scene: str
  description: str
  event: str
  reference: str
  generated_copy: str
  generated_time: datetime.datetime


def modify_string(vars_dict):
  current_number = 1
  adjusted_lines = []

  for var_name, var_value in vars_dict.items():
    # 如果变量不为空，则添加带有序号的行
    if var_value:
      adjusted_lines.append(f"{current_number}.{var_value}")
      current_number += 1

  # 将调整后的行连接成一个字符串，并用换行符分隔
  adjusted_string = "\n".join(adjusted_lines)

  return adjusted_string


class GetMyHistoryResponse(BaseModel):
  code: str = "0000"
  data: List[CopywritingRecordOut]


ad_form = """"
一、剧情广告
    1.情节引导：
        用户调研适配：结合市场调研，根据不同受众设计有吸引力的情节，如英雄救美、逆袭成功等故事，确保玩家能有代入感。
        情节故事化：制作完整的故事情节，包含起因、经过、结果，使用蒙太奇手法增强故事的吸引力。

    2.代入感：
        交互设计：加入剧情分支让玩家做出选择，增强互动性，提高用户沉浸感和参与度。
        角色代入：设计让玩家能够感同身受的场景，运用感官刺激（音效、特效等）增强情感共鸣。

    3.玩法融合：

        选择展示：在剧情广告中选择性地展示少量关键的游戏玩法和特色，让这些玩法与剧情发展自然融合，使广告的节奏更加连贯。
        剧情主导：保持剧情为主导，通过有限的游戏玩法展示，避免剧情广告因过多的玩法介绍而失去情节的连贯性和观赏性。
        可视化展示：使用视觉效果简要展示玩法，使玩家能够轻松理解游戏的核心要素，而不需要繁琐的详细解说。
        自然过渡：在剧情发展的关键时刻，自然地展示少量的游戏特色或玩法，让这些展示成为情节的一部分，而不是强制切入玩法细节。

二、玩法展示
    1.核心玩法展示：
        直接演示：通过游戏实录视频或动画场景展示操作，强调游戏机制和特色玩法，让用户第一时间了解游戏乐趣。
        快捷体验：展示快速上手和获得成就的过程，让玩家感到游戏上手容易并有成就感。

    2.技能演示：
        高光时刻：剪辑角色释放技能或必杀技的高光瞬间，强化视觉冲击力，吸引对战斗画面有诉求的玩家。
        技能组合：展示不同技能连招的炫酷效果和战略乐趣，吸引喜欢深度操作和策略的玩家。

三、对比广告
    1.成功与失败对比：
        操作对比：展示新手与高手操作的不同效果，凸显技巧差异和游戏挑战性。
        成败对比：通过不同选择的结果，对比成功和失败的截然不同结局，激发用户尝试和挑战的欲望。

    2.低级与高级对比：
        成长路径：展示角色从低级进化到高级的视觉变化，强调成长和进步的乐趣。
        奖励对比：通过对比低级和高级角色所能获得的奖励和特权，激发用户升级动力。

四、玩家推荐

    1.用户评价：
        真实回馈：展示玩家的真实评价和反馈视频，增强广告的可信度和说服力。
        对比展示：通过对比新旧玩家的使用感受，展现游戏给玩家带来的正面改变。

    2.名人效应：
        KOL推荐：邀请知名游戏主播或KOL试玩并推荐游戏，借助其影响力增加广告的覆盖面和影响力。
        网红互动：设计与网红互动的故事情节，让大众对游戏产生好奇和兴趣。

五、互动广告

    1.选择式广告：
        互动机制：设计广告内容让用户进行选择，如剧情发展、对话选项等，提升用户参与感。
        多结局设计：通过多种选择结果展示不同的游戏结局，增加广告的新鲜感。

    2.试玩广告：
        试玩环节：在广告中嵌入小型试玩环节，让用户体验游戏核心玩法，增加下载转化率。
        即时体验：展示游戏的即时操作和反馈，引发用户兴趣和下载欲望。

六、视觉特效

    1.高品质画面：
        清晰画质：采用高清画质和流畅动画，展示游戏的高品质视觉效果。
        场景特效：通过精致的游戏场景和特效展示，让广告视觉冲击效果更强。

    2.特效展示：
        华丽特效：展示各种华丽的战斗特效、爆炸效果等，强化视觉刺激。
        环境效果：结合游戏的特殊环境效果（如天气变化、场景破坏等），增加游戏的动态感和真实性。

七、解说广告

    1.介绍型解说：
        专业解说：用专业的解说方式，详细讲解游戏的背景故事、玩法技巧和特色内容。
        分步演示：通过分步骤演示游戏操作和玩法，帮助新玩家快速上手。

    2.趣味性解说：
        幽默解说：采用幽默风趣的解说风格，使广告内容更加轻松愉快，吸引用户注意。
        角色代入：由游戏角色进行自我介绍和玩法讲解，增加广告的趣味性和亲和力。

八、用户心理学七宗罪

    1. 傲慢

        给玩家带来我玩游戏能装逼的快感。

        展现形式：

        通过展示游戏中的荣誉系统、成就系统，凸显玩家在游戏中的地位和荣耀。
        使用向外界展示玩家在游戏中战绩、排行等内容的玩法，让玩家享受尊崇感。

    2. 嫉妒

        激发玩家的竞争心理，以及对他人进度的关注和比较。

        展现形式：

        显示游戏中的排行榜、竞技场，展示顶级玩家的成就和装备，刺激其他玩家的追赶欲望。
        强调游戏中的稀有道具和高级装备，激起玩家对这些道具的渴望。

    3. 暴怒

        利用玩家对失败或挑战的愤怒情绪，驱动其努力进步和持续投入。

        展现形式：

        展示游戏中刺激惊险的战斗场面以及失败的后果，让玩家感受到挑战的紧迫感。
        创建强力敌人或关卡，引导玩家为了复仇或战胜困难不断尝试。

    4. 懒惰

        满足玩家追求轻松和高效的心理。

        展现形式：

        强调游戏的自动挂机、轻松升级等内置系统，减少玩家的游戏负担。
        展示游戏简单易上手的特点，让玩家感到无压力并且易于接受。

    5. 贪婪

        利用玩家获得更多资源和奖励的欲望，增加游戏吸引力。

        展现形式：

        通过展示丰厚的奖励和成就感，吸引玩家进行长时间和高投入的游戏。
        强调游戏内的限时活动、大奖和丰厚福利，激发玩家的参与度。

    6. 虛榮

        让玩家想要超越他人，追求游戏中的辉煌成就。

        展现形式：

        表现出游戏中角色的成长和进步历程，通过对比展示低级和高级角色的差异。
        让玩家看到他人的炫酷装扮、美丽皮肤和强大技能，激发其追求和攀比心理。

    7. 饕餮

        满足玩家对内容的贪欲和求新心理。

        展现形式：

        展示游戏丰富的内容、独特的玩法和不断更新的活动，保持玩家的新鲜感和持续兴趣。
        强调游戏中的各种稀有道具、神秘宝箱和隐藏任务，激发玩家探索和收集的欲望。
"""


# 提示词
def generate_question(tags_text, target_gender, target_age, region, language_style, video_duration,
                      emotional_depth, specific_scenes, additional_notes, reference_examples, game_name, event):
  theme = "**文案主题**: " + tags_text
  audience = "**目标受众**: " + target_age + " 岁来自 " + region + " 地区的 " + target_gender + " 玩家（需要针对这一群体进行内容设计）。"
  style = "**语言风格**: 请使用 " + language_style + "的语言风格，使文案富有感染力。"
  event_info = "**包含以下信息**: " + event
  duration = "**视频时长**: " + video_duration + "左右。"
  content_integration = "**请在文案中巧妙地结合文案主题和玩家需求、游戏内容等，使内容连贯，吸引观众，引导游戏的精准用户下载。"
  localization = "**本地化要求**: 当目标用户来自于欧美地区，对白或旁白这些内容则用英文，港澳台地区用中文"
  if emotional_depth:
    emotion = "**引导情感深度**: " + emotional_depth
  else:
    emotion = ""
  if specific_scenes:
    scenes = "**描述具体场景**: " + specific_scenes
  else:
    scenes = ""
  if additional_notes:
    notes = "**补充说明**: " + additional_notes
  else:
    notes = ""
  if reference_examples:
    examples = "**案例参考**:\n" + reference_examples
  else:
    examples = ""

  format_requirements = """**格式要求**:
  *【音乐起】和【音乐结束】用来标记音乐的开始和结束；
  *【画面：描述内容】用来具体描述视频画面内容；
  *如果有对话或旁白请使用【对白：对话内容】来呈现角色的台词或解说和语调；
  *【中文翻译】若对白或旁白是中文以外的语言则把对白或旁白的内容翻译成中文方便设计师了解其中含义，如是中文则不需要；
  * 视频中不必每个画面都配备对白或旁白，旁白或对白用于推动剧情或介绍游戏；
  """

  key_points = "**重点**：这是一个游戏买量广告，让我们一步一步的思考然后创作出能够通过视觉元素来呈现且吸引人的视频广告。"

  question_parts = [
    theme,
    audience,
    style,
    event_info,
    duration,
    content_integration,
    localization,
    emotion,
    scenes,
    notes,
    examples,
    format_requirements,
    key_points
  ]

  lc_question = "\n".join(part for part in question_parts if part)
  question = "请根据以下要求，为游戏“" + game_name + "”创作一个视频广告文案，文案需要符合以下条件：\n" + lc_question

  return question


# 用于中止生成的标志
generation_flags = {}


@router.post("/stop_generation", tags=["text"])
async def stop_generation(user: User = Depends(get_request_user)):
  generation_flags[user.username] = True
  return JSONResponse(content={"code": "0000", "msg": "Generation stopped"})


@router.post("/generate_copywriting", tags=["text"])
async def generate_copywriting(
  payload: GenerateCopywritingPayload,
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user)
):
  """
  生成文案接口
  :param payload:
  :param db:
  :param user:
  :return:
  """
  try:
    # 查询游戏信息
    result = await db.execute(select(GameManagement).where(GameManagement.gamename == payload.game))
    game_management = result.scalar_one_or_none()

    if not game_management:
      raise ClientVisibleException("Game not found")

    game_info = game_management.info if game_management else "无"

    # 根据地区选择game_name
    if payload.region == "欧美":
      game_name = game_management.english_name
    else:
      game_name = payload.game

    # 初始化LangChain模型
    if API_BASE_URL:
      llm = ChatOpenAI(api_key=OPENAI_API_KEY, model="gpt-4o", temperature=0.7, base_url=API_BASE_URL)
    else:
      llm = ChatOpenAI(api_key=OPENAI_API_KEY, model="gpt-4o", temperature=0.7)

    # 构建游戏信息内容
    game_content = f"""
    游戏：{game_name}
    游戏信息：{game_info}
    """

    # 构建创作需求内容
    question = generate_question(
      tags_text=', '.join(payload.tags),
      target_gender=payload.gender,
      target_age=payload.age,
      region=payload.region,
      language_style=payload.style,
      video_duration=payload.duration,
      emotional_depth=payload.emotion,
      specific_scenes=payload.scene,
      additional_notes=payload.description,
      reference_examples=payload.reference,
      game_name=game_name,
      event=payload.event
    )

    # print(f"event: {payload.event}")

    # 构建多轮对话提示词
    messages = [
      SystemMessage(
        content="你是一位资深的游戏发行公司广告专家，具备丰富的游戏买量视频广告剧本策划经验。你擅长撰写多种形式的广告文案，包括解说、真人出演、剧情叙述等，能够精准抓住受众的兴趣点，打造引人入胜的广告内容。"
      ),
      AIMessage(
        content="您好，我是您的游戏发行广告专家。为了更好地完成视频文案的创作，我需要您帮助我理解您的游戏产品以及买量广告的创作形式。首先，请您提供游戏的相关信息。"
      ),
      HumanMessage(content=game_content),
      AIMessage(
        content="明白，您的游戏信息已经收到并充分了解。接下来，我需要学习买量视频文案的写作方式，请提供关于游戏买量视频广告的创作形式"
      ),
      HumanMessage(content=ad_form),
      AIMessage(
        content="好的，感谢您提供的游戏买量视频广告创作形式，我将结合您提供的游戏信息，为您创作一则买量视频广告文案。"),
      HumanMessage(content=question)
    ]

    # 打印提示词内容
    # for idx, message in enumerate(messages):
    #   print(f"Message {idx}: {message.content}")

    # 记录调用开始时间
    start_time = time.time()

    # 生成文案
    # response = await llm.agenerate([messages])
    # # print(f"response: {response}")
    # generated_copy = response.generations[0][0].text  # 提取文本内容

    # 生成文案
    async def generate():
      response_chunks = []
      async for chunk in llm.astream(messages):
        if generation_flags.get(user.username, False):
          yield {"event": "message", "data": "Generation stopped by user"}
          break
        response_chunks.append(chunk)
        yield {"event": "message", "data": chunk.content}

      # 组合完整的文案
      generated_copy = "".join([chunk.content for chunk in response_chunks])

      # 记录调用结束时间并计算生成时间
      end_time = time.time()
      time_taken = end_time - start_time

      # 将生成文案数据插入数据库
      new_record = CopywritingRecord(
        username=user.username,
        game=payload.game,
        style=payload.style,
        duration=payload.duration,
        gender=payload.gender,
        age=payload.age,
        region=payload.region,
        tags=payload.tags,
        emotion=payload.emotion,
        scene=payload.scene,
        description=payload.description,
        event=payload.event,
        reference=payload.reference,
        generated_copy=generated_copy,
        time_taken=time_taken,  # 记录生成文案的时间
        created_at=datetime.datetime.now(),
        updated_at=datetime.datetime.now()
      )

      db.add(new_record)
      await db.commit()
      generation_flags.pop(user.username, None)

    return EventSourceResponse(generate())

  except SQLAlchemyError as e:
    logger.error(f"Database error: {e}")
    await db.rollback()
    raise ClientVisibleException("数据库错误") from e
  except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise ClientVisibleException() from e


@router.get("/get_my_history", tags=["text"])
async def get_history(
  page: int = Query(1, description="Page number"),
  page_size: int = Query(10, description="Page size"),
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user)
):
  """"
  获取用户历史生成记录
  """
  try:
    offset = (page - 1) * page_size
    result = await db.execute(
      select(CopywritingRecord)
      .filter(CopywritingRecord.username == user.username)
      .order_by(CopywritingRecord.generated_time.desc())
      .limit(page_size)
      .offset(offset)
    )
    records = result.scalars().all()

    response_data = [
      CopywritingRecordOut(
        game=record.game,
        style=record.style,
        duration=record.duration,
        gender=record.gender,
        age=record.age,
        region=record.region,
        tags=record.tags,
        emotion=record.emotion,
        scene=record.scene,
        description=record.description,
        event=record.event,
        reference=record.reference,
        generated_copy=record.generated_copy,
        generated_time=record.generated_time,
      ) for record in records
    ]

    return GetMyHistoryResponse(data=response_data)

  except SQLAlchemyError as e:
    logger.error(f"Database error: {e}")
    raise ClientVisibleException("获取生成记录失败") from e


@router.get("/get_company_games", tags=["game"])
async def get_company_games(
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user)
):
  """
    获取与用户所属公司相关的游戏名称列表
    """
  try:
    # 获取用户所属公司
    company = user.company
    if not company:
      raise ClientVisibleException("用户公司信息不存在")

    # 查询GameManagement表中与该公司一致的游戏名称
    result = await db.execute(
      select(GameManagement.gamename).filter(GameManagement.company == company)
    )
    gamenames = result.scalars().all()

    return JSONResponse(content={
      "code": "0000",
      "data": {"company": company, "games": gamenames},
      "msg": "成功获取游戏列表"
    })

  except SQLAlchemyError as e:
    logger.error(f"Database error: {e}")
    raise ClientVisibleException("获取游戏名称失败") from e
