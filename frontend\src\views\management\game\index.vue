<script setup lang="tsx">
import { ref, watch } from 'vue';
import { <PERSON><PERSON>utt<PERSON>, NElli<PERSON>, NPopconfirm, NScrollbar } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchAddGame, fetchDelGame, fetchGameManagement, fetchGameSearch, fetchUpdataGame } from '@/service/api';
import GameOperateDrawer from './modules/game-operate-drawer.vue';
import GameSearch from './modules/game-search.vue';

const appStore = useAppStore();
// const isOpen = ref<boolean>(false);

interface GameData extends NaiveUI.TableData {
  gamecode: string;
  gamename: string;
  lang: string;
  info: string;
  editor: string;
  uptime: string;
  company: string;
}

// 获取游戏管理信息
const fetchGameManagementTyped: NaiveUI.TableApiFn<GameData, Api.SystemManage.CommonSearchParams> = async params => {
  const response = await fetchGameManagement(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<GameData>>;
};

const searchParams = ref<Api.SystemManage.CommonSearchParams>({
  current: 1,
  size: 10,
  gamecode: '',
  gamename: '',
  lang: ''
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchGameManagementTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'gamecode',
      title: '游戏编号',
      align: 'center',
      minWidth: 40
    },
    {
      key: 'gamename',
      title: '游戏名称',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'english_name',
      title: '英文名称',
      align: 'center',
      minWidth: 85
    },
    {
      key: 'lang',
      title: '语言',
      align: 'center',
      minwidth: 50
    },
    {
      key: 'info',
      title: '游戏资料',
      align: 'left',
      minwidth: 280,
      render: row => (
        <NScrollbar style="max-height: 10em">
          <div style="white-space: pre-wrap;">
            <NEllipsis line-clamp={3} expand-trigger="click" tooltip={false}>
              {row.info}
            </NEllipsis>
          </div>
        </NScrollbar>
      )
    },
    {
      key: 'editor',
      title: '修改者',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'company',
      title: '所属公司',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'uptime',
      title: '更新时间',
      align: 'center',
      width: 220,
      render: row => row.uptime.replace('T', ' ')
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.gamecode)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.gamecode)}>
            {{
              default: () => '确认删除？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  // editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  // onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

// 批量删除
// async function handleBatchDelete() {
//   // console.log(checkedRowKeys.value);
//   try {
//     await fetchDelGame(checkedRowKeys.value);
//     onBatchDeleted();
//   } catch {}
// }

// 删除
async function handleDelete(gamecode: string) {
  try {
    // console.log(gamecode);
    await fetchDelGame([gamecode]);

    // 如果请求成功，调用 onDeleted
    onDeleted();
  } catch {}
}

// 编辑信息表单数据
const editData = ref<any>(null);
function edit(gamecode: string) {
  const rowData = data.value.find(item => item.gamecode === gamecode);
  if (rowData) {
    editData.value = rowData;
  }
  handleEdit(gamecode as any);
}

// onMounted(() => {
//   getData();
// });

// 查询
async function handleSearch(params: Api.SystemManage.CommonSearchParams) {
  searchParams.value = params;
  // const newParams = { ...searchParams.value };
  const response = await fetchGameSearch(searchParams.value);
  data.value = response.data.records; // 更新表格数据
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

// 添加新游戏信息
async function handleSubmit(gameData: any) {
  try {
    if (operateType.value === 'add') {
      await fetchAddGame(gameData);
    } else if (operateType.value === 'edit') {
      await fetchUpdataGame(gameData);
    }
    getData();
  } finally {
    drawerVisible.value = false;
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <GameSearch v-model:model="searchParams" @search="handleSearch" />
    <NCard title="游戏管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <!--
 <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        /> 
-->
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns as DataTableColumns "
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.gamecode"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <GameOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editData"
        @submitted="handleSubmit"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
