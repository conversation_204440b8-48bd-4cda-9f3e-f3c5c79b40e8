<script setup lang="ts">
import { defineModel } from 'vue';
import { useTable } from '@/hooks/common/table';
import { fetchTaskAnalyzeDetail } from '@/service/api';
// interface Props {
//   path: string;
//   searchParams: Record<string, any>;
// }

// const props = defineProps<Props>();

const show = defineModel<boolean>('show');
const { columns, data, updateSearchParams, getData, loading } = useTable({
  apiFn: fetchTaskAnalyzeDetail,
  showTotal: true,
  immediate: false,
  columns: () => [
    {
      key: 'nickname',
      title: '用户名',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'count',
      title: '调用次数',
      align: 'center',
      minWidth: 120
    }
  ]
});

function getTableData(searchParams: any, api_name: string) {
  updateSearchParams({
    api_name,
    ...searchParams
  });
  getData();
}
defineExpose({ getTableData });
</script>

<template>
  <NModal v-model:show="show" preset="card" class="w-200" title="用户统计">
    <NDataTable :data="data" :columns="columns" :loading="loading" max-height="400"></NDataTable>
  </NModal>
</template>

<style></style>
