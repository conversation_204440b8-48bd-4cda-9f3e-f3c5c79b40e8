from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, func
from sqlalchemy.orm import relationship
from utils.database import Base

class SystemSettingsGroup(Base):
    __tablename__ = 'system_settings_group'
    __table_args__ = {'comment': '系统配置组'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_code = Column(String(64), nullable=False, comment='分组代码')
    group_name = Column(String(128), nullable=False, comment='分组名称')
    description = Column(Text, nullable=True, comment='分组描述')
    seq = Column(Integer, default=0, comment='排序')
    status = Column(Boolean, default=True, comment='状态: 1启用, 0禁用')
    crtime = Column(DateTime, default=func.now(), comment='创建时间')
    edittime = Column(DateTime, default=func.now(), onupdate=func.now(), comment='修改时间')
    edituser = Column(String(32), nullable=True, comment='修改人')


    def __repr__(self):
        return f"<SystemSettingsGroup(id={self.id}, group_code={self.group_code}, group_name={self.group_name})>" 