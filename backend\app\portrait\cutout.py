import logging
import httpx
from fastapi import APIRouter, Header, Request
from pydantic import BaseModel
from typing import List, Optional
from volcengine.visual.VisualService import VisualService
from config import app_settings
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


class CutoutRequest(BaseModel):
  model: str
  output_type: str
  image: str


class FileItem(BaseModel):
  filename: str  # 原始文件名
  data: str  # base64编码的文件数据


class VolcengineCutoutRequest(BaseModel):
  file: List[FileItem]  # 包含文件名和base64数据的列表
  only_mask: Optional[int] = 0  # 0: 返回裁剪出主体区域的BGRA透明图片，默认值
  rgb: Optional[List[int]] = [-1, -1, -1]  # 默认返回透明背景
  refine_mask: Optional[int] = 0  # 边缘增强，默认不增强


@router.post("/get_cutout_img", tags=["cutout"])
async def get_cutout_img(request: CutoutRequest):
  model = request.model
  output_type = request.output_type
  image = request.image

  try:
    # 准备远程请求的payload
    payload = {
      "model": model,
      "output_type": output_type,
      "image": image
    }

    # 获取远程服务的IP地址
    remote_ip = app_settings.ai_server
    remote_url = f"{remote_ip}/cutout/get_cutout_img"
    # remote_url = f"http://************:5003/cutout/get_cutout_img"

    logger.debug(f"url:{remote_url}")

    # 发送请求到远程服务
    async with httpx.AsyncClient(timeout=90) as client: # 超时时间90s
      response = await client.post(remote_url, json=payload)

    # 处理远程服务的响应
    if response.status_code == 200:
      result = response.json()
      if result["code"] == "0000":
        return {"code": "0000", "data": result["data"]}
      else:
        raise ClientVisibleException("图片处理失败，请重试")
    else:
      raise ClientVisibleException("请求失败，请重试")

  except Exception as e:
    logger.error(f"Error processing image with model {model}: {e}")
    raise ClientVisibleException("模型处理错误") from e


@router.post("/volcengine_cutout", tags=["cutout"])
async def volcengine_cutout(request: VolcengineCutoutRequest, req: Request, authorization: str = Header(None)):
    """
    调用火山引擎图像抠图API
    
    Args:
        request: 包含文件和处理参数的请求
        req: 原始请求对象
        authorization: 认证头信息
        
    Returns:
        抠图处理后的图像URL
    """
    # 从环境变量获取认证信息
    access_key_id = app_settings.volcengine_access_key_id
    secret_access_key = app_settings.volcengine_secret_access_key
    
    if not access_key_id or not secret_access_key:
        logger.error("火山引擎认证信息未配置")
        raise ClientVisibleException("认证失败，请配置火山引擎认证信息")
    
    # 处理图片数据
    if not request.file or len(request.file) == 0:
        raise ClientVisibleException("请提供图片")
    
    # 仅处理第一个图像，因为API只支持单张图片
    file_item = request.file[0]
    
    try:
        # 初始化火山引擎VisualService
        visual_service = VisualService()
        
        # 设置AK和SK
        visual_service.set_ak(access_key_id)
        visual_service.set_sk(secret_access_key)
        
        # 处理base64数据，确保移除可能存在的前缀
        image_data = file_item.data
        # 如果数据包含base64前缀，只保留实际的base64部分
        if ',' in image_data:
            image_data = image_data.split(',', 1)[1]
        
        # 验证图片大小
        import base64
        decoded_data = base64.b64decode(image_data)
        image_size_mb = len(decoded_data) / (1024 * 1024)
        if image_size_mb > 4.7:
            raise ClientVisibleException("图片大小超过限制，最大支持 4.7MB")
        
        # 构建请求数据
        form = {
            "req_key": "saliency_seg",  # 固定值
            "binary_data_base64": [image_data],
            "only_mask": request.only_mask,
            "rgb": request.rgb,
            "refine_mask": request.refine_mask
        }
        
        # 添加日志，记录请求前的数据状态
        logger.info(f"正在调用火山引擎抠图SDK，base64数据长度：{len(image_data)}")
        
        # 调用SDK处理图像
        response = visual_service.cv_process(form)
        
        # logger.info(f"火山引擎SDK响应: {response}")
        
        # 解析SDK响应 - 使用原始response对象，不使用截断后的log_response
        if response and 'code' in response and response['code'] == 10000:
            # 检查响应中是否包含data字段
            if 'data' not in response or not isinstance(response['data'], dict):
                logger.error(f"火山引擎SDK响应缺少data字段")
                raise ClientVisibleException("图像处理失败，响应格式异常")
                
            # 检查是否存在binary_data_base64字段
            if 'binary_data_base64' in response['data'] and response['data']['binary_data_base64']:
                binary_data = response['data']['binary_data_base64']
                if isinstance(binary_data, list) and len(binary_data) > 0:
                    # 直接从原始响应中获取数据，确保数据完整性
                    first_binary_data = response['data']['binary_data_base64'][0] if len(response['data']['binary_data_base64']) > 0 else ""
                    
                    # 确保base64数据格式正确
                    if isinstance(first_binary_data, str):
                        # 清理可能导致解码问题的字符，如换行符、空格等
                        first_binary_data = first_binary_data.replace('\n', '').replace('\r', '').replace(' ', '')
                    
                    # 获取第一个二进制数据
                    result_data = {"image_base64": first_binary_data}
                    
                    # 添加日志确认是否有截断
                    full_length = len(first_binary_data) if isinstance(first_binary_data, str) else 0
                    logger.info(f"返回图像数据，完整base64长度：{full_length}")
                    
                    # 添加轮廓点和边界框等信息（如果存在）
                    if 'contours_point' in response['data']:
                        result_data["contours_point"] = response['data']['contours_point']
                    if 'bbox' in response['data']:
                        result_data["bbox"] = response['data']['bbox']
                    
                    logger.info("成功从binary_data_base64中获取图像数据")
                    
                    # 返回前检查结果数据格式
                    if "image_base64" in result_data and isinstance(result_data["image_base64"], str):
                        # 记录base64数据的前50个字符，用于验证格式
                        preview = result_data["image_base64"][:50] + "..." if len(result_data["image_base64"]) > 50 else result_data["image_base64"]
                        logger.info(f"返回base64数据预览: {preview}")
                    
                    return {
                        "code": "0000",
                        "data": result_data
                    }
                else:
                    logger.error(f"火山引擎SDK返回的binary_data_base64格式异常: {type(binary_data)}")
                    raise ClientVisibleException("图像处理失败，返回的数据格式异常")
            else:
                logger.error(f"火山引擎SDK响应中无有效的binary_data_base64")
                raise ClientVisibleException("图像处理失败，响应格式异常")
        else:
            error_code = response.get('code', '未知错误码')
            error_message = response.get('message', '未知错误')
            logger.error(f"火山引擎SDK返回错误码 {error_code}: {error_message}")
            raise ClientVisibleException("图像处理失败")
    
    except Exception as e:
        logger.error(f"调用火山引擎SDK时发生错误: {str(e)}")
        # 尝试解析错误响应
        try:
            if hasattr(e, 'response') and e.response:
                logger.error(f"火山引擎SDK错误详情: {e.response}")
        except:
            pass
        raise ClientVisibleException("处理失败，请重试") from e
