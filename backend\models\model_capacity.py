from enum import IntFlag, auto, CONFORM
from typing import Self


class Capacity(IntFlag, boundary=CONFORM):
  """
  用于表示模型能力的枚举类，每个枚举值对应一个能力。

  注意：这个类不能删除已有字段，也不能改变已有字段的声明顺序。

  >>> Capacity(0b1)  # 可以使用枚举中存在的值来创建 Capacity 对象。
  <Capacity.TEXT: 1>
  >>> Capacity(0b11)  # 也可以使用组合值来创建 Capacity 对象。
  <Capacity.TEXT|Capacity.IMAGE: 3>
  >>> Capacity(0b111111111111111111111)  # 如果组合值超出了枚举的范围，最后的值只会保留有效的部分。
  <Capacity.TEXT|Capacity.IMAGE|Capacity.DRAW: 7>
  """
  TEXT = auto()
  """文本生成能力"""

  IMAGE = auto()
  """图片识别能力"""

  DRAW = auto()
  """图片生成能力"""

  TOOL_CALL = auto()
  """工具调用能力"""

  def add(self, other: Self) -> Self:
    """
    用于添加一个能力，这是一个非原地操作。

    >>> Capacity(0b11).add(Capacity.DRAW)
    <Capacity.TEXT|Capacity.IMAGE|Capacity.DRAW: 7>
    """
    return self | other

  def remove(self, other: Self) -> Self:
    """
    用于移除一个能力，这是一个非原地操作。

    >>> Capacity(0b111).remove(Capacity.IMAGE)
    <Capacity.TEXT|Capacity.DRAW: 5>
    """
    return self ^ other

  def has(self, other: Self) -> bool:
    """
    用于检查是否具有某个能力。

    >>> Capacity(0b11).has(Capacity.IMAGE)
    True
    >>> Capacity(0b11).has(Capacity.DRAW)
    False
    """
    return other in self