from openai import OpenAI
from config import app_settings
from utils.hash.aes import decrypt

templates = {
  "chat": [
    "帮我设计一个健康的一周饮食计划",
    "帮我整理出适合初学者的瑜伽动作",
    "推荐一些适合春季阅读的书籍",
    "如何设置合理的日常运动计划",
    "教我如何制作手工艺品",
    "提供有效的时间管理技巧",
    "分析最近流行的科技产品",
    "给我介绍一些学习外语的有效方法",
    "如何布置一个舒适的学习环境",
    "提供一些有效的压力管理策略",
    "教我如何在家种植蔬菜",
    "推荐一些孩子们会喜欢的科学实验",
    "帮我选择适合夏季的户外活动",
    "推荐一些家庭游戏夜的游戏",
    "如何制定有效的学习计划",
    "帮我分析一下最近的电影趋势",
    "推荐几个有趣的在线课程",
    "教我如何自制自然美容产品",
    "给我一些建议如何改善睡眠质量",
    "提供一些创意的家庭装饰灵感",
    "给我推荐几种提高记忆力的技巧。",
    "解释一下冥想的基本步骤和好处。",
    "为我介绍一些适合初学者的编程语言。",
    "教我如何制作一份简历。",
    "演示如何在家自制面包。",
    "说明如何用自然方法改善睡眠质量。",
    "给我介绍一些有效的时间管理技巧。",
    "教我如何进行基本的自行车维修。",
    "分享一些摄影的基础技巧和构图原则。",
    "给我介绍一些健身房以外的锻炼方法",
    "给我讲解一下如何有效地进行网络安全防护。",
    "教我如何在家制作新鲜的果汁。",
    "解释一下瑜伽中不同体式的好处。",
    "分享一些环球旅行的贴士和建议。",
    "给我介绍一些提升写作能力的方法。",
    "解释一下如何配置一台高效能的个人电脑。",
    "给我推荐一些提升公众演讲技巧的方法。",
    "教我如何打造一个小型花园。",
    "分享一些家庭室内设计的基础知识。",
    "给我介绍一些环保生活的实用建议。",
    "解释如何制作一本个人旅行相册。",
    "分享如何保养和翻新旧家具的技巧。",
    "给我推荐一些学习音乐的入门资源。",
    "教我如何进行有效的家庭预算管理。",
    "解释如何制作一款简单的手机应用。",
    "分享如何准备和进行户外徒步旅行。",
    "给我介绍关于如何养宠物的基本知识。",
    "教我如何用天然材料制作个人护理产品。",
    "分享一些节日主题的手工艺品制作方法。",
    "解释如何优化社交媒体上的个人品牌。",
  ],
  # "image": [
  #     "画一幅月光下的古老城堡",
  #     "画一幅一座桥上的日落景象",
  #     "画一幅森林中的神秘小屋",
  #     "画一幅海边的儿童与沙滩城堡",
  #     "画一幅夜晚星空下的野餐",
  #     "画一幅传统市场的繁忙早晨",
  #     "画一幅雪地中的狼群",
  #     "画一幅古董车在旧时光街道上行驶",
  #     "画一幅航海冒险中的海盗船",
  #     "画一幅花园中的茶会",
  #     "画一幅清晨的雾中，一位老人在乡村小路上散步。",
  #     "画一幅未来城市的夜景，光束从高楼直射天空。",
  #     "画一幅一个孩子在海边的夕阳下放飞风筝。",
  #     "画一幅深林中的秘密小屋，周围是密密的野花和藤蔓。",
  #     "画一幅热带雨林中一群五颜六色的鸟群。",
  #     "画一幅在一个古老图书馆里阅读的女孩。",
  #     "画一幅失落古城的遗迹，覆盖着藤蔓和苔藓。",
  #     "画一幅外太空中的宇航员与远处地球的壮观视图。",
  #     "画一幅一个古老市场的繁忙景象，各种商品琳琅满目。",
  #     "画一幅雪地中的狼群在月光下奔跑。",
  #     "画一幅地中海边的小镇，彩色建筑沿着海岸线排列。",
  #     "画一幅一场中世纪的盛大宴会场景。",
  #     "画一幅悬崖上的灯塔，在暴风雨中指引着方向。",
  #     "画一幅一场花园婚礼，布满鲜花的拱门和幸福的笑容。",
  #     "画一幅反乌托邦世界里的反叛者。",
  #     "画一幅沙漠中的胡杨林在日落的映衬下。",
  #     "画一幅在阴雨天的伦敦街头奏乐的街头艺人。",
  #     "画一幅太空船内部的未来科技控制室。",
  #     "画一幅神秘的北欧森林中的溪流和小桥。",
  #     "画一幅热气球飘浮在峡谷之上的壮观景观。",
  #     "画一幅古董店内的奇妙发现。",
  #     "画一幅梦幻式的下雨天，在彩虹下跳舞的人们。",
  #     "画一幅动物们在非洲草原上的迁徙场景。",
  #     "画一幅舞蹈室里练习芭蕾的女孩。",
  #     "画一幅一个被遗弃的游乐场在月光下的幽静景象。",
  #     "画一幅魔法森林中的小精灵与野生动物们的交流。",
  #     "画一幅宇宙中的星际旅行船。",
  #     "画一幅在古典音乐会中演奏小提琴的音乐家。",
  #     "画一幅日本樱花季节下的传统茶道仪式。",
  #     "画一幅五光十色的海底世界，海豚和色彩斑斓的珊瑚。",
  #     "画一幅冰封的北极，极光下的雪橇犬。",
  #     "画一幅中世纪城堡的骑士和公主。",
  #     "画一幅落日后的篝火晚会，人们围坐共享温暖。",
  #     "画一幅想象中的龙与城堡的对峙场面。",
  #     "画一幅蒸汽朋克风格的机械发明工作室。",
  #     "画一幅在喧闹的爵士酒吧中演奏萨克斯的音乐家。",
  #     "画一幅古老的石桥下流淌的幽静河流。",
  #     "画一幅星光下，一个小孩在后院的帐篷中读书。",
  #     "画一幅在山顶上迎接日出的登山者。",
  #     "画一幅异国的舞蹈节，人们身着传统服装跳舞。",
  #     "画一幅古老神庙的废墟中，考古学家正在发掘。",
  #     "画一幅激烈的航海探险，海浪拍打着古老的帆船。",
  #     "画一幅雨后的公园里，孩子们在彩色的雨伞下玩耍。",
  #     "画一幅在城市楼顶上设的秘密花园。",
  #     "画一幅遥远星系中的宁静星球，星球表面奇异的自然景观。",
  #     "画一幅冬季的小镇，屋顶和街道覆盖着雪。",
  #     "画一幅通过望远镜观望星空的天文学家。",
  #     "画一幅温室内各种奇异植物的世界。",
  #     "画一幅一个虚构的时代，人们与机器人和谐共处的场景。",
  # ],
}

openai_api_key = decrypt(app_settings.openai_api_key)
openai_api_base = app_settings.openai_api_base_url

client = OpenAI(api_key=openai_api_key, base_url=openai_api_base)

META_PROMPT = """
Given a task description or existing prompt, produce a detailed system prompt to guide a language model in completing the task effectively.

# Guidelines

- Understand the Task: Grasp the main objective, goals, requirements, constraints, and expected output.
- Minimal Changes: If an existing prompt is provided, improve it only if it's simple. For complex prompts, enhance clarity and add missing elements without altering the original structure.
- Reasoning Before Conclusions**: Encourage reasoning steps before any conclusions are reached. ATTENTION! If the user provides examples where the reasoning happens afterward, REVERSE the order! NEVER START EXAMPLES WITH CONCLUSIONS!
    - Reasoning Order: Call out reasoning portions of the prompt and conclusion parts (specific fields by name). For each, determine the ORDER in which this is done, and whether it needs to be reversed.
    - Conclusion, classifications, or results should ALWAYS appear last.
- Examples: Include high-quality examples if helpful, using placeholders [in brackets] for complex elements.
   - What kinds of examples may need to be included, how many, and whether they are complex enough to benefit from placeholders.
- Clarity and Conciseness: Use clear, specific language. Avoid unnecessary instructions or bland statements.
- Formatting: Use markdown features for readability. DO NOT USE ``` CODE BLOCKS UNLESS SPECIFICALLY REQUESTED.
- Preserve User Content: If the input task or prompt includes extensive guidelines or examples, preserve them entirely, or as closely as possible. If they are vague, consider breaking down into sub-steps. Keep any details, guidelines, examples, variables, or placeholders provided by the user.
- Constants: DO include constants in the prompt, as they are not susceptible to prompt injection. Such as guides, rubrics, and examples.
- Output Format: Explicitly the most appropriate output format, in detail. This should include length and syntax (e.g. short sentence, paragraph, JSON, etc.)
    - For tasks outputting well-defined or structured data (classification, JSON, etc.) bias toward outputting a JSON.
    - JSON should never be wrapped in code blocks (```) unless explicitly requested.

The final prompt you output should adhere to the following structure below. Do not include any additional commentary, only output the completed system prompt. SPECIFICALLY, do not include any additional messages at the start or end of the prompt. (e.g. no "---")

[Concise instruction describing the task - this should be the first line in the prompt, no section header]

[Additional details as needed.]

[Optional sections with headings or bullet points for detailed steps.]

# Steps [optional]

[optional: a detailed breakdown of the steps necessary to accomplish the task]

# Output Format

[Specifically call out how the output should be formatted, be it response length, structure e.g. JSON, markdown, etc]

# Examples [optional]

[Optional: 1-3 well-defined examples with placeholders if necessary. Clearly mark where examples start and end, and what the input and output are. User placeholders as necessary.]
[If the examples are shorter than what a realistic example is expected to be, make a reference with () explaining how real examples should be longer / shorter / different. AND USE PLACEHOLDERS! ]

# Notes [optional]

[optional: edge cases, details, and an area to call or repeat out specific important considerations]
""".strip()


async def generate_prompt(task_or_prompt: str):
  completion = client.chat.completions.create(
    model="gpt-4o",
    messages=[
      {
        "role": "system",
        "content": META_PROMPT,
      },
      {
        "role": "user",
        "content": "Task, Goal, or Current Prompt:\n" + task_or_prompt,
      },
    ],
  )

  return completion.choices[0].message.content


# 将图片转换为相应的文字描述
IMG_TO_DESC_PROMPT = """
# Default: all regular interaction responses should be in Chinese
image_description_prompt:
  prompt_name: Detailed Image Content and Composition Analysis
  description: >
    This prompt is designed to guide the AI model in performing a comprehensive and detailed content analysis and description of the input image.
    The goal is to generate a piece of text with a clear structure and rich information, covering all core elements of the image, with special attention to any text information present in the image.
  instructions: |
    Based on the image you see, generate a detailed descriptive text. The description should cover the following aspects:

    1.  **Overall Summary:**
        * Firstly, briefly describe the core theme or central scene depicted in the image.

    2.  **Compositional Elements:**
        * **Focal Point:** What is the most attention-grabbing part of the image?
        * **Composition Rules:** Were specific composition techniques used (e.g., Rule of Thirds, Golden Ratio, leading lines, symmetry/asymmetry, framing)? How were they applied?
        * **Depth of Field & Focus:** Is the depth of field large or small? Which parts of the image are sharp, and which are blurred?
        * **Sense of Space:** How does the image create a sense of depth and space (e.g., through the arrangement of foreground, middle ground, and background)?

    3.  **People Description (If there are people in the image):**
        * **Number & Basics:** How many people are there? Estimated age range, gender representation.
        * **Attire & Appearance:** What kind of clothing are they wearing (style, color, material, details)? Hair style, accessories, and other notable appearance features.
        * **Posture & Action:** What posture are the people in? What actions are they performing?
        * **Expression & Emotion:** What are the people's facial expressions? What emotions or states are conveyed?
        * **Relationships:** If there are multiple people, is there a clear relationship or interaction between them?

    4.  **Scene/Environment Description:**
        * **Location Type:** Is it indoors or outdoors? Is it a city, natural landscape, room, street, or other specific location?
        * **Environmental Details:** What key background elements or objects are included in the scene? (e.g., buildings, furniture, vegetation, props, etc.)
        * **Time Clues:** Can the approximate time of day be determined (daytime, twilight, night)? Are there any weather-related clues (sunny, rainy, snowy, etc.)?

    5.  **Text Extraction (If the image contains significant text):**
        * **Extraction & Description:** If the image contains noticeable text information (such as signs, books, screen text, document contents, etc.), please extract these text contents in detail and describe their position and form of appearance in the image.

    6.  **Light & Color:**
        * **Light Source:** Where  does the light come from (natural light, artificial light, mixed light)? Is it a single light source or multiple light sources?
        * **Light Quality:** Is the light soft or harsh? What is the direction of the light (front lighting, side lighting, backlighting, etc.)?
        * **Light & Shadow Effects:** How are the light and shadows distributed? Do they create a special atmosphere or highlight certain elements?
        * **Color Palette:** What is the main color tone of the image (warm tones, cool tones)? What are the color saturation and contrast like? Are there any particularly prominent colors?

    7.  **Atmosphere & Mood:**
        * What kind of feeling or emotion does the image convey overall? (e.g., peaceful, lively, mysterious, melancholic, solemn, nostalgic, etc.)

    8.  **Perspective/Shot Info (If discernible):**
        * **Angle:** Is it eye-level, high-angle, or low-angle?
        * **Shot Size:** Is it a long shot, full shot, medium shot, close-up, or extreme close-up?

  output_requirements:
    * The description should be as objective, detailed, and accurate as possible, based on the image content itself.
    * The structure should be clear and organized by points, but the final output should be presented as fluent natural paragraphs.
    * The language should be natural and fluent, with precise wording.
    * Avoid excessive subjective speculation or associations, unless the image information clearly suggests them.
    * If the image contains significant text, the detailed extraction and description of the text information must be included.
    * Only output the final descriptive text, without including the prompt instructions themselves or other irrelevant information.
""".strip()

IMG_DESC_PROMPT = """
Please consider the following as a picture:\n
```
\n
{desc}
\n
```
\n
According to this picture, complete the following user's request:\n
```
\n
{prompt}
\n
```
\n
## Analyze the visual content as if you are viewing the image directly. Based on this perceived image and the user's request, generate your response. Crucially, do not use phrases like 'according to the image description' or 'derived from the information received'.
""".strip()
