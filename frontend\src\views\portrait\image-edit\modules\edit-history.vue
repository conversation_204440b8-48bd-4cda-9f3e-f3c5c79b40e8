<script lang="ts" setup>
// import { ref } from 'vue';
import { useScreenDetection } from '@/utils/detectionScreen';

const { isLargeScreen } = useScreenDetection(1500);
</script>

<template>
  <NCard>
    <NFlex vertical align="center" justify="center">
      <NFlex class="history_title" align="center" justify="center" :wrap="false">
        <SvgIcon icon="proicons:history" class="mr-1 text-xl" />
        <NText>历史记录</NText>
      </NFlex>

      <NScrollbar :class="isLargeScreen ? 'h-120 max-h-120' : 'h-90 max-h-90'">
        <NFlex vertical>
          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />

          <NImage
            src="https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202507/afcd6349fb12b2481246f5b9f2e92a26_20250725171514.png?imageView2/1/w/120/h/120"
            preview-disabled
            class="h-20 w-20"
          />
        </NFlex>
      </NScrollbar>
    </NFlex>
  </NCard>
</template>

<style scoped>
:deep(.history_title) {
  margin: 0px !important;
  padding: 0px !important;
  gap: 0px !important;
}
</style>
