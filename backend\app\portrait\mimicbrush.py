import logging
import os
import requests
import json
import asyncio
from fastapi import APIRouter
from pydantic import BaseModel
from utils.common import base64_mime_to_file

import httpx
import uuid
from config import app_settings
from utils.exceptions import ClientVisibleException

logger = logging.getLogger(__name__)

CUDA_OM = app_settings.cuda
uploadUrl = f'{CUDA_OM}:8965/upload/image'
taskUrl = f'{CUDA_OM}:8965/prompt'
historyUrl = f'{CUDA_OM}:8965/history'
router = APIRouter()


class MimicBrush(BaseModel):
  source_b64: str = ''
  reference_b64: str = ''
  mask_b64: str = ''
  step: int = 50
  seed: int = -1
  guidance_scale: int = 5
  if_keep_shape: bool = False


UPLOAD_DIR = "upload/tmp"
if not os.path.exists(UPLOAD_DIR):
  os.makedirs(UPLOAD_DIR)


async def uploadMimicBrushFile(type: str, name: str, file_path: str):
  async with httpx.AsyncClient(timeout=50) as client:
    try:
      with open(file_path, 'rb') as file:
        files = {'image': (name, file, 'application/octet-stream')}
        response = await client.post(uploadUrl, files=files)
        response.raise_for_status()
        res = response.json()
        res.update({"type": type})
        return res
    except httpx.HTTPError as error:
      logger.error(f'An error occurred: {error}')


@router.post("/task", tags=["mimicbrush"])
async def task(req: MimicBrush):
  """
  三个文件保存本地，再上传mimic brush 服务器
  添加任务
  返回任务id
  """
  try:
    taskId = str(uuid.uuid4())
    source_file_name = taskId + '_source.png'
    reference_file_name = taskId + '_reference.png'
    mask_file_name = taskId + '_mask.png'

    source_file_path = os.path.join(UPLOAD_DIR, source_file_name)
    reference_file_path = os.path.join(UPLOAD_DIR, reference_file_name)
    mask_file_path = os.path.join(UPLOAD_DIR, mask_file_name)

    base64_mime_to_file(req.source_b64, source_file_path)
    base64_mime_to_file(req.reference_b64, reference_file_path)
    base64_mime_to_file(req.mask_b64, mask_file_path)

    t1 = asyncio.create_task(uploadMimicBrushFile('source', source_file_name, source_file_path))
    t2 = asyncio.create_task(uploadMimicBrushFile('reference', reference_file_name, reference_file_path))
    t3 = asyncio.create_task(uploadMimicBrushFile('mask', mask_file_name, mask_file_path))
    # 等待两个任务的完成
    done, pending = await asyncio.wait([t1, t2, t3])
    for task in done:
      res = task.result()
      # print(f"Task result: {task.result()}")
      # print(res['type'])
      if res["name"] == '':
        raise ClientVisibleException('上传文件失败')

      if res['type'] == 'source':
        source_file_name = res['name']
      elif res['type'] == 'reference':
        reference_file_name = res['name']
      else:
        mask_file_name = res['name']
    data = {
      "prompt": {
        "1": {
          "inputs": {
            "step": req.step,
            "guidance_scale": req.guidance_scale,
            "seed": req.seed,
            "if_keep_shape": req.if_keep_shape,
            "edit_img": [
              "5",
              0
            ],
            "edit_mask": [
              "6",
              0
            ],
            "ref_img": [
              "3",
              0
            ]
          },
          "class_type": "MimicBrushNode",
          "_meta": {
            "title": "MimicBrush Node"
          }
        },
        "3": {
          "inputs": {
            "image": reference_file_name,
            "upload": "image"
          },
          "class_type": "LoadImage",
          "_meta": {
            "title": "加载图像"
          }
        },
        "4": {
          "inputs": {
            "images": [
              "1",
              0
            ]
          },
          "class_type": "PreviewImage",
          "_meta": {
            "title": "预览图像"
          }
        },
        "5": {
          "inputs": {
            "image": source_file_name,
            "upload": "image"
          },
          "class_type": "LoadImage",
          "_meta": {
            "title": "加载图像"
          }
        },
        "6": {
          "inputs": {
            "image": mask_file_name,
            "channel": "alpha",
            "upload": "image"
          },
          "class_type": "LoadImageMask",
          "_meta": {
            "title": "加载图像遮罩"
          }
        }
      }
    }
    logger.debug(json.dumps(data, sort_keys=True, indent=4, ensure_ascii=False))
    async with httpx.AsyncClient(timeout=50) as client:
      response = await client.post(url=taskUrl, json=data)
      response.raise_for_status()
      response_data = response.json()
    # print(taskUrl)
    # {
    #     "prompt_id": "eb343103-e0fe-498d-a735-ad6dd64d1692",
    #     "number": 6,
    #     "node_errors": {}
    # }
    return {"code": "0000", "data": response_data}

  except Exception as e:
    logger.error(f"An error occurred: {e}")
    raise ClientVisibleException() from e


@router.get("/history/{prompt_id}", tags=["mimicbrush"])
async def history(prompt_id: str):
  try:
    response = requests.get(f"{historyUrl}/{prompt_id}")
    return {"code": "0000", "data": response.json()}
  except Exception as e:
    logger.error(f"An error occurred: {e}")
    raise ClientVisibleException() from e
