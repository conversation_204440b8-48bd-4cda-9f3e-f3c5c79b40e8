import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Query, Header
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from utils.database import get_db
from models.app_seting import AppSetting
from pydantic import BaseModel
from typing import List, Optional
import datetime
from models.users import User, get_token_key, get_roles_checker
from utils.exceptions import ClientVisibleException
from utils.redis import redis

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
logger = logging.getLogger(__name__)


class ModelSettingOut(BaseModel):
    id: int
    pid: int
    key_type: Optional[str]
    key_code: str
    value_type: Optional[str]
    key_value: str
    sync_redis: int
    seq: int
    remark: Optional[str]
    crtime: datetime.datetime
    edituser: Optional[str]

    class Config:
        from_attributes = True


class PaginatedData(BaseModel):
    records: List[ModelSettingOut]
    current: int
    size: int
    total: int


class PaginatedResponse(BaseModel):
    data: PaginatedData
    code: str


class DeleteModelRequest(BaseModel):
    id: int


class AddModelModelSettingRequest(BaseModel):
    key_type: Optional[str] = "system"
    key_code: str
    value_type: Optional[str] = "text"
    key_value: Optional[str] = ""
    sync_redis: int = 0
    pid: int
    seq: int
    remark: Optional[str] = ""
    edituser: Optional[str] = ""


async def get_total_count(session: AsyncSession, model):
    result = await session.execute(select(func.count(model.id)))
    return result.scalar()


@router.get("/setting/all", response_model=PaginatedResponse, tags=["manage"])
async def get_all_setting(
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      获取系统字典信息数据
      """
    async with db as session:
        # 获取分页数据
        result = await session.execute(
            select(AppSetting)
            .offset((page - 1) * size)
            .limit(size)
        )
        records = result.scalars().all()

        # 获取总数
        total = await get_total_count(session, AppSetting)

        records_out = [ModelSettingOut.model_validate(record) for record in records]

        return PaginatedResponse(
            data=PaginatedData(
                records=records_out,
                current=page,
                size=size,
                total=total
            ),
            code="0000"
        )


@router.get("/setting/search", response_model=PaginatedResponse, tags=["manage"])
async def search_setting(
        key_type: Optional[str] = Query(None),
        key_code: Optional[str] = Query(None),
        value_type: Optional[str] = Query(None),
        pid: Optional[int] = Query(None),
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      查询系统字典数据
      """
    # 构建查询语句
    query = select(AppSetting)
    if key_type:
        query = query.where(AppSetting.key_type.like(f'%{key_type}%'))
    if key_code:
        query = query.where(AppSetting.key_code.like(f'%{key_code}%'))
    if value_type:
        query = query.where(AppSetting.value_type.like(f'%{value_type}%'))
    if pid:
        query = query.where(AppSetting.pid == pid)

    async with db as session:
        # 获取分页数据
        result = await session.execute(query.offset((page - 1) * size).limit(size))
        records = result.scalars().all()

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()

        records_out = [ModelSettingOut.from_orm(record) for record in records]

        return PaginatedResponse(
            data=PaginatedData(
                records=records_out,
                current=page,
                size=size,
                total=total
            ),
            code="0000"
        )


@router.post("/setting/delete", response_model=PaginatedResponse, tags=["manage"])
async def delete_setting(
        request: DeleteModelRequest,
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      删除系统字典数据
      """
    async with db as session:
        # 查找要删除的数据
        result = await session.execute(select(AppSetting).where(AppSetting.id == request.id))
        record = result.scalars().first()

        if not record:
            raise ClientVisibleException("删除失败")
        recordkey = record.key_code
        recordtype = record.key_type

        # 删除数据
        await session.delete(record)
        await session.commit()

        # 删除redis数据
        key = f'app_setting:{recordtype}:{recordkey}'
        await redis.delete(key)

        return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})


@router.post("/setting/add", response_model=PaginatedResponse, tags=["manage"])
async def add_setting(
        add_data: AddModelModelSettingRequest,
        db: AsyncSession = Depends(get_db),
        authorization: str = Header(None)
):
    """
      添加新的系统字典数据
      """
    credentials_exception = ClientVisibleException("Could not validate credentials")

    try:
        # 从 Authorization 头中提取 token
        token = authorization.split(" ")[1]

        # 验证 token
        token_key = get_token_key(token)
        userid = await redis.get(token_key)

        if userid is None:
            raise credentials_exception

        # 转换 Redis 返回的字节字符串为整数
        userid = int(userid.decode("utf-8"))

        async with db as session:
            # 获取用户信息
            result = await session.execute(select(User).filter(User.id == userid))
            user = result.scalars().first()

            if user is None or user.status != 1:
                raise credentials_exception

            # 验证key是否在数据库中存在
            result = await session.execute(
                select(AppSetting).filter(AppSetting.key_code == add_data.key_code)
            )
            existing_data = result.scalars().first()
            if existing_data:
                raise ClientVisibleException("字典Key已存在")

            # 添加新的模型积分数据
            # print(f'-------------------------------------{add_data}')
            new_data = AppSetting(
                key_type=add_data.key_type,
                key_code=add_data.key_code,
                value_type=add_data.value_type,
                key_value=add_data.key_value,
                edituser=user.username,  # 使用从数据库获取的用户名
                sync_redis=add_data.sync_redis,
                pid=add_data.pid,
                crtime=datetime.datetime.now(),
                remark=add_data.remark,
                seq=add_data.seq
            )
            session.add(new_data)
            await session.commit()
            # print(f'如果同步redis ，则set key==========={add_data.sync_redis}')
            if add_data.sync_redis == 1:
                key = f'app_setting:{add_data.key_type}:{add_data.key_code}'
                await redis.set(key, add_data.key_value)

            return JSONResponse(content={"data": {}, "code": "0000", "msg": "添加成功"})

    except Exception as e:
        logger.error(f"Failed to add game: {e}")
        raise ClientVisibleException("添加字典失败") from e


@router.post("/setting/update", response_model=PaginatedResponse, tags=["manage"])
async def update_setting(
        update_data: ModelSettingOut,
        db: AsyncSession = Depends(get_db),
        authorization: str = Header(None)
):
    """
      更新字典数据
      """
    credentials_exception = ClientVisibleException("Could not validate credentials")

    try:
        # 从 Authorization 头中提取 token
        token = authorization.split(" ")[1]

        # 验证 token
        token_key = get_token_key(token)
        userid = await redis.get(token_key)

        if userid is None:
            raise credentials_exception

        # 转换 Redis 返回的字节字符串为整数
        userid = int(userid.decode("utf-8"))

        async with db as session:
            # 获取用户信息
            result = await session.execute(select(User).filter(User.id == userid))
            user = result.scalars().first()

            if user is None or user.status != 1:
                raise credentials_exception

            # 根据 id 查找对应的模型积分
            result = await session.execute(
                select(AppSetting).filter(AppSetting.id == update_data.id)
            )
            existing_data = result.scalars().first()

            if not existing_data:
                raise ClientVisibleException("字典key不存在")

            # 更新模型积分信息
            # existing_data.id = update_data.id
            existing_data.key_type = update_data.key_type
            existing_data.key_code = update_data.key_code
            existing_data.value_type = update_data.value_type
            existing_data.edituser = user.username
            existing_data.key_value = update_data.key_value
            existing_data.sync_redis = update_data.sync_redis
            existing_data.pid = update_data.pid
            existing_data.remark = update_data.remark
            existing_data.seq = update_data.seq
            # existing_data.crtime=datetime.datetime.now(),

            await session.commit()
            # print(f'如果同步redis ，则set key===================={update_data.sync_redis}')
            if update_data.sync_redis == 1:
                key = f'app_setting:{update_data.key_type}:{update_data.key_code}'
                await redis.set(key, update_data.key_value)
            return JSONResponse(content={"data": {}, "code": "0000", "msg": "更新成功"})


    except Exception as e:
        logger.error(f"Failed to update game: {e}")
        raise ClientVisibleException("更新字典失败") from e
