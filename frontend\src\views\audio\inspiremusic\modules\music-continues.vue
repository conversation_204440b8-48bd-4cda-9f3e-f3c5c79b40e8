<script lang="ts" setup>
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import {
  NEmpty,
  NScrollbar,
  NSpin,
  NText,
  NUpload,
  NUploadDragger,
  type ScrollbarInst,
  type UploadCustomRequestOptions,
  useMessage
} from 'naive-ui';
import WaveSurfer from 'wavesurfer.js';
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js';
import { useThemeStore } from '@/store/modules/theme';
import { getMusicHistory, getMusicTaskStatus, postMusicContinue } from '@/service/api/audio';
import { blobToBase64, blobUrlToAudioBuffer, bufferToWave, fileToArrayBuffer, sliceAudio } from '@/utils/audio';
import { downloadFile } from '@/utils/common';
import MusicInfoCard from './music-infoCard.vue';
import MusicPlayAudio from './music-playAudio.vue';

interface MusicTaskResponse {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_url?: string | null;
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

interface MusicTask {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_data?: { url: string; type: string; duration?: number; sample_rate?: number } | null;
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

const themeStore = useThemeStore();
const message = useMessage();

// Wavesurfer相关变量
let wavesurfer: WaveSurfer | null = null;
const playing = ref(false);
const audioStartTime = ref<number>(0); // 音频开始时间
const audioEndTime = ref<number>(0); // 音频结束时间

// 主题样式计算属性 - 只保留使用的计算属性
const borderColor = computed(() => {
  return !themeStore.darkMode ? 'rgb(239, 239, 245)' : 'rgba(255, 255, 255, 0.09)';
});

const backgroundColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(250, 250, 252, 1)' : 'rgba(24, 24, 28, 1)';
});

const focusBorderColor = computed(() => {
  return !themeStore.darkMode ? 'rgb(26, 126, 251)' : 'rgb(70, 146, 251)';
});

const shadowColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(26, 126, 251, 0.1)' : 'rgba(70, 146, 251, 0.15)';
});

const textColor = computed(() => {
  return !themeStore.darkMode ? 'rgba(31, 34, 37, 1)' : 'rgba(255, 255, 255, 0.9)';
});

// 上传的音频文件
const audioFile = ref<File | null>(null);
const audioBase64 = ref<string>('');
const songDescription = ref(''); // 新增描述文本字段
const fileName = ref('');
const promptAudioBlobUrl = ref(''); // 原始的音频URL
const tmpPromptAudioBlobUrl = ref(''); // 临时的，编辑后的音频URL
const uploadLoading = ref(false);

// 选中的历史音频任务
const selectedAudioTask = ref<MusicTask | null>(null);
const selectedAudioUrl = ref<string>(''); // 选中历史音频的URL

// 模型选择
const selectedModel = ref('InspireMusic-1.5B');
const modelOptions = ref([
  {
    label: '纯音乐',
    value: 'InspireMusic-1.5B'
  },
  {
    label: '歌曲',
    value: 'Song',
    disabled: true
  }
]);

const hasAudioFile = computed(() => {
  // 当有上传的音频文件或选中的历史音频任务时返回true
  return (Boolean(promptAudioBlobUrl.value) && Boolean(fileName.value)) || Boolean(selectedAudioTask.value);
});

// 用户历史音频记录
const userAudioHistory = ref<MusicTask[]>([]);
const isLoadingUserAudio = ref(false);
const userAudioPage = ref(1);
const userAudioPageSize = ref(5);
const hasMoreUserAudio = ref(true);
const isSelectedAudioTask = ref(false);

// 生成的任务记录
const generatedTasks = ref<MusicTask[]>([]);
const pollingIntervals = ref<Map<string, number>>(new Map());
const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoadingHistory = ref(false);
const scrollbarRef = ref<ScrollbarInst | null>(null);

// 播放器相关状态
const currentPlayingTask = ref<MusicTask | null>(null);
const currentAudioUrl = ref<string>('');
const currentAudioTitle = ref<string>('');
const currentPlayingIndex = ref<number>(-1);
const searchTitle = ref<string>('');

// 初始化WaveSurfer
function initWaveSurfer() {
  // 先销毁原来的
  wavesurfer?.destroy();
  const regions = RegionsPlugin.create();

  regions.on('region-out', region => {
    region.play();
  });

  regions.on('region-clicked', (region, e) => {
    e.stopPropagation(); // prevent triggering a click on the waveform
    region.play();
  });

  regions.on('region-updated', region => {
    audioStartTime.value = region.start;
    audioEndTime.value = region.end;
    console.log('区间', region.start, region.end);
  });

  wavesurfer = WaveSurfer.create({
    container: '#waveform',
    url: promptAudioBlobUrl.value,
    autoCenter: false,
    waveColor: themeStore.themeColor,
    progressColor: '#383351',
    barHeight: 0.7,
    barWidth: 3,
    barRadius: 3,
    cursorWidth: 1,
    barGap: 3,
    hideScrollbar: true,
    dragToSeek: true,
    plugins: [regions]
  });

  wavesurfer.on('ready', duration => {
    console.log('Ready', `${duration}s`);
    audioEndTime.value = duration;
    playing.value = false;

    // 添加选择区域
    regions.addRegion({
      start: audioStartTime.value,
      end: audioEndTime.value,
      content: '',
      color: 'rgba(13, 208, 61, 0.5)',
      drag: true,
      resize: true,
      minLength: 1
    });
  });

  wavesurfer.on('play', () => {
    playing.value = true;
  });

  wavesurfer.on('pause', () => {
    playing.value = false;
  });

  wavesurfer.on('interaction', () => {
    wavesurfer?.play();
  });
}

// 裁剪音频功能
const clip = async (): Promise<string> => {
  console.log('clip', [audioStartTime.value, audioEndTime.value]);
  try {
    const tmpAudioBuffer = await blobUrlToAudioBuffer(promptAudioBlobUrl.value);
    const newAudioBuffer = await sliceAudio(tmpAudioBuffer, audioStartTime.value, audioEndTime.value);

    // 生成新的blob并转为base64
    const blob = bufferToWave(newAudioBuffer, newAudioBuffer.sampleRate * newAudioBuffer.duration);
    return await blobToBase64(blob);
  } catch (error) {
    console.error('裁剪出错:', error);
    // message.error('裁剪音频出错，请调整裁剪区间');
    return '';
  }
};

// 播放/暂停波形图
const triggerPlayPause = async () => {
  if (playing.value) {
    wavesurfer?.pause();
  } else {
    wavesurfer?.setTime(audioStartTime.value);
    wavesurfer?.play();
  }
};

// 移除文件的方法
const removeFile = () => {
  // 清除上传的文件
  fileName.value = '';
  promptAudioBlobUrl.value = '';
  tmpPromptAudioBlobUrl.value = '';
  audioFile.value = null;
  audioBase64.value = '';

  // 清除选中的历史任务
  selectedAudioTask.value = null;
  selectedAudioUrl.value = '';

  // 销毁波形图
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
  }

  isSelectedAudioTask.value = false;
};

// 处理音频上传
const customRequest = async ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  try {
    uploadLoading.value = true;

    // 清除选中的历史任务
    selectedAudioTask.value = null;
    selectedAudioUrl.value = '';
    isSelectedAudioTask.value = false;

    // 处理文件预览
    if (file.file) {
      fileName.value = '';
      tmpPromptAudioBlobUrl.value = '';
      promptAudioBlobUrl.value = '';

      // 如果是视频文件(MP4)，提取音频
      if (file.type === 'video/mp4') {
        const buffer: ArrayBuffer = await fileToArrayBuffer(file.file as File);
        const audioCtx = new AudioContext();
        await audioCtx.decodeAudioData(buffer, audioBuffer => {
          const blob = bufferToWave(audioBuffer, audioBuffer.sampleRate * audioBuffer.duration);
          const uuid = Date.now();
          const newFileName = `${uuid}.wav`;
          const audioFileObj = new File([blob], newFileName, { type: 'audio/wav', lastModified: Date.now() });
          const blobUrl = URL.createObjectURL(blob);
          promptAudioBlobUrl.value = blobUrl;
          audioFile.value = audioFileObj;
        });
      } else {
        // 普通音频文件
        const blobUrl = URL.createObjectURL(file.file);
        promptAudioBlobUrl.value = blobUrl;
        audioFile.value = file.file as File;
      }

      // 初始化波形图
      tmpPromptAudioBlobUrl.value = promptAudioBlobUrl.value;
      initWaveSurfer();
      fileName.value = file.name;

      // 转换为base64以便后续API请求使用(会在提交时裁剪获取)
      message.success('音频上传成功');
      onFinish();
    }
  } catch (error) {
    console.error('上传音频时出错:', error);
    message.error('上传音频失败，请重试');
    onError();
  } finally {
    uploadLoading.value = false;
  }
};

// 获取用户历史音频记录
const fetchUserAudioHistory = async (page: number) => {
  if (isLoadingUserAudio.value) return;
  isLoadingUserAudio.value = true;
  try {
    const response = await getMusicHistory(page, userAudioPageSize.value);
    if (response.data) {
      if (page === 1) {
        userAudioHistory.value = response.data;
      } else {
        userAudioHistory.value.push(...response.data);
      }
      hasMoreUserAudio.value = response.data.length === userAudioPageSize.value;
    } else if (page > 1) {
      userAudioPage.value--;
    }
  } catch (error) {
    console.error('获取历史音频记录失败:', error);
  } finally {
    isLoadingUserAudio.value = false;
  }
};

// 处理用户音频历史滚动事件
const handleUserAudioScroll = (e: Event) => {
  const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLElement;
  const threshold = 20; // 距离底部20像素时触发加载

  // 检查是否滚动到底部附近、没有正在加载、且还有更多数据
  if (scrollTop + clientHeight >= scrollHeight - threshold && !isLoadingUserAudio.value && hasMoreUserAudio.value) {
    console.log('用户音频历史滚动到底部，加载下一页...');
    userAudioPage.value++;
    fetchUserAudioHistory(userAudioPage.value);
  }
};

// 选择历史音频作为输入
const selectAudioFromHistory = async (task: MusicTask) => {
  // 只有成功生成的任务才能选择
  if (task.status !== 'SUCCESS' || !task.audio_data?.url) {
    message.warning('只能选择生成成功的音频');
    return;
  }

  isSelectedAudioTask.value = true;

  // 如果选中了当前已选中的任务，则取消选中
  if (selectedAudioTask.value?.id === task.id) {
    selectedAudioTask.value = null;
    selectedAudioUrl.value = '';
    isSelectedAudioTask.value = false;
    message.info('已取消选择');
    return;
  }

  // 设置选中的任务和URL
  selectedAudioTask.value = task;
  selectedAudioUrl.value = task.audio_data.url;

  // 如果有prompt，则填充到描述字段
  if (task.prompt) {
    songDescription.value = task.prompt;
  } else {
    songDescription.value = `续写"${task.music_title}"`;
  }

  // 清空上传的文件状态
  promptAudioBlobUrl.value = '';
  tmpPromptAudioBlobUrl.value = '';
  audioFile.value = null;
  audioBase64.value = '';

  // 设置fileName为选中音频的标题，以便在UI中显示
  fileName.value = task.music_title;

  // 销毁波形图
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
  }

  message.success(`已选中音频: ${task.music_title}`);
};

// 停止轮询
const stopPolling = (taskId: string) => {
  const intervalId = pollingIntervals.value.get(taskId);
  if (intervalId) {
    window.clearInterval(intervalId);
    pollingIntervals.value.delete(taskId);
    console.log(`停止轮询任务: ${taskId}`);
  }
};

// 开始轮询
const startPolling = (taskId: string) => {
  if (!taskId || pollingIntervals.value.has(taskId)) {
    return;
  }

  console.log(`开始轮询任务: ${taskId}`);
  const intervalId = window.setInterval(async () => {
    try {
      console.log(`正在轮询任务 ${taskId} 状态...`);
      const response = await getMusicTaskStatus(taskId);

      if (!response.data) {
        stopPolling(taskId);
        return;
      }

      const taskIndex = generatedTasks.value.findIndex(t => t.taskid === taskId);
      if (taskIndex === -1) {
        console.warn(`轮询时未在列表中找到任务 ${taskId}，停止轮询。`);
        stopPolling(taskId);
        return;
      }

      const apiResponse = response.data as MusicTaskResponse;
      const updatedTask: MusicTask = { ...generatedTasks.value[taskIndex] };

      Object.assign(updatedTask, apiResponse);

      if (apiResponse.audio_url) {
        try {
          updatedTask.audio_data = JSON.parse(apiResponse.audio_url);
        } catch (error) {
          console.error('解析audio_url失败:', error);
        }
      }

      generatedTasks.value[taskIndex] = updatedTask;
      console.log(`任务 ${taskId} 状态更新: ${apiResponse.status}`);

      if (apiResponse.status === 'SUCCESS' || apiResponse.status === 'FAILURE') {
        stopPolling(taskId);
      }
    } catch (error) {
      console.error(`轮询任务 ${taskId} 出错:`, error);
      stopPolling(taskId);
    }
  }, 5000);

  pollingIntervals.value.set(taskId, intervalId);
};

// 生成续写音乐
const continueMusicGeneration = async () => {
  if (!hasAudioFile.value) {
    message.error('请上传音频文件或选择历史音频');
    return;
  }

  try {
    let audioData = '';

    // 根据音频来源获取数据
    if (selectedAudioTask.value && selectedAudioUrl.value) {
      // 从选中的历史任务获取音频URL，直接使用URL
      audioData = selectedAudioUrl.value;
    } else if (promptAudioBlobUrl.value) {
      // 从上传的音频裁剪获取base64
      const clippedBase64 = await clip();
      if (!clippedBase64) {
        return;
      }
      audioData = clippedBase64;
    } else {
      return;
    }

    // 设置请求参数
    const params = {
      text: songDescription.value, // 只使用描述文本
      audio_base64: audioData, // a音频数据（可能是URL或base64）
      model_name: selectedModel.value, // 使用选择的模型名称
      chorus: 'intro',
      output_sample_rate: 48000,
      max_generate_audio_seconds: 30.0
    };

    // 发送请求
    const response = await postMusicContinue(params);

    if (response.data) {
      const newTask = response.data as MusicTaskResponse;
      const processedTask = { ...newTask } as unknown as MusicTask;

      if (newTask.audio_url) {
        try {
          processedTask.audio_data = JSON.parse(newTask.audio_url);
        } catch (error) {
          console.error('解析新生成任务的audio_url失败:', error);
        }
      }

      generatedTasks.value.unshift(processedTask);
      message.success('音乐续写任务已提交');
      startPolling(processedTask.taskid);
    }
  } catch (error) {
    console.error('提交任务出错:', error);
  }
};

// 获取历史生成记录
const fetchHistory = async (pageNum: number) => {
  if (isLoadingHistory.value) return;
  isLoadingHistory.value = true;
  try {
    const trimmedTitle = searchTitle.value ? searchTitle.value.trim() : searchTitle.value;
    const response = await getMusicHistory(pageNum, pageSize.value, trimmedTitle);
    if (response.data) {
      const newTasks = response.data;

      const processedTasks: MusicTask[] = newTasks.map((task: MusicTaskResponse) => {
        const processedTask = { ...task } as unknown as MusicTask;

        if (task.audio_url) {
          try {
            processedTask.audio_data = JSON.parse(task.audio_url);
          } catch (error) {
            console.error('解析历史记录中的audio_url失败:', error);
          }
        }

        return processedTask;
      });

      if (pageNum === 1) {
        generatedTasks.value = processedTasks;
      } else {
        generatedTasks.value.push(...processedTasks);
      }

      hasMore.value = newTasks.length === pageSize.value;
      processedTasks.forEach(task => {
        if (task.taskid && (task.status === 'SUBMITTED' || task.status === 'IN_PROGRESS')) {
          startPolling(task.taskid);
        }
      });
    } else if (pageNum > 1) {
      currentPage.value--;
    }
  } catch (error) {
    if (pageNum > 1) {
      currentPage.value--;
    }
    console.error('获取历史记录出错:', error);
  } finally {
    isLoadingHistory.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  currentPage.value = 1;
  hasMore.value = true;
  generatedTasks.value = [];
  fetchHistory(1);
};

// 处理回车键搜索
const handleSearchKeyup = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    handleSearch();
  }
};

// 播放功能
const handlePlay = (task: MusicTask) => {
  if (task.audio_data?.url) {
    try {
      const taskIndex = generatedTasks.value.findIndex(t => t.taskid === task.taskid);
      if (taskIndex !== -1) {
        currentPlayingIndex.value = taskIndex;
      }

      currentPlayingTask.value = task;
      currentAudioUrl.value = task.audio_data.url;
      currentAudioTitle.value = task.music_title;
      message.info(`已选中: ${task.music_title}, 点击播放按钮开始播放`);
    } catch (error) {
      console.error('设置播放任务时出错:', error);
      message.error('播放失败，请重试');
    }
  }
};

// 处理切换到上一首歌曲
const handlePreviousTrack = () => {
  if (currentPlayingIndex.value <= 0 || generatedTasks.value.length === 0) {
    message.warning('已经是第一首歌曲');
    return;
  }

  const prevIndex = currentPlayingIndex.value - 1;
  const prevTask = generatedTasks.value[prevIndex];

  if (prevTask && prevTask.status === 'SUCCESS' && prevTask.audio_data?.url) {
    currentPlayingIndex.value = prevIndex;
    currentPlayingTask.value = prevTask;
    currentAudioUrl.value = prevTask.audio_data.url;
    currentAudioTitle.value = prevTask.music_title;
  } else {
    message.warning('上一首歌曲无法播放，可能正在生成中或已失败');
    // 尝试继续向前查找可播放的歌曲
    let foundPlayable = false;
    for (let i = prevIndex - 1; i >= 0; i--) {
      const task = generatedTasks.value[i];
      if (task.status === 'SUCCESS' && task.audio_data?.url) {
        currentPlayingIndex.value = i;
        currentPlayingTask.value = task;
        currentAudioUrl.value = task.audio_data.url;
        currentAudioTitle.value = task.music_title;
        foundPlayable = true;
        break;
      }
    }

    if (!foundPlayable) {
      message.warning('没有找到可播放的上一首歌曲');
    }
  }
};

// 处理切换到下一首歌曲
const handleNextTrack = () => {
  if (
    currentPlayingIndex.value === -1 ||
    currentPlayingIndex.value >= generatedTasks.value.length - 1 ||
    generatedTasks.value.length === 0
  ) {
    message.warning('已经是最后一首歌曲');
    return;
  }

  const nextIndex = currentPlayingIndex.value + 1;
  const nextTask = generatedTasks.value[nextIndex];

  if (nextTask && nextTask.status === 'SUCCESS' && nextTask.audio_data?.url) {
    currentPlayingIndex.value = nextIndex;
    currentPlayingTask.value = nextTask;
    currentAudioUrl.value = nextTask.audio_data.url;
    currentAudioTitle.value = nextTask.music_title;
  } else {
    message.warning('下一首歌曲无法播放，可能正在生成中或已失败');
    // 尝试继续向后查找可播放的歌曲
    let foundPlayable = false;
    for (let i = nextIndex + 1; i < generatedTasks.value.length; i++) {
      const task = generatedTasks.value[i];
      if (task.status === 'SUCCESS' && task.audio_data?.url) {
        currentPlayingIndex.value = i;
        currentPlayingTask.value = task;
        currentAudioUrl.value = task.audio_data.url;
        currentAudioTitle.value = task.music_title;
        foundPlayable = true;
        break;
      }
    }

    if (!foundPlayable) {
      message.warning('没有找到可播放的下一首歌曲');
    }
  }
};

// 歌曲播放完成后的处理
const handleTrackEnded = () => {
  // 自动播放下一首
  // handleNextTrack();
};

// 下载音频
const handleDownload = (task: MusicTask) => {
  if (task.audio_data?.url) {
    downloadFile(task.audio_data.url, `${task.music_title || 'music'}.${task.audio_data.type || 'mp3'}`).catch(
      error => {
        console.error('下载音频时出错:', error);
      }
    );
  }
};

// 滚动事件处理函数
const handleScroll = (e: Event) => {
  const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLElement;
  const threshold = 50;

  if (scrollTop + clientHeight >= scrollHeight - threshold && !isLoadingHistory.value && hasMore.value) {
    console.log('滚动到底部，加载下一页...');
    currentPage.value++;
    fetchHistory(currentPage.value);
  }
};

onMounted(() => {
  fetchUserAudioHistory(1);
  fetchHistory(1);
});

onBeforeUnmount(() => {
  pollingIntervals.value.forEach((intervalId, _taskId) => {
    window.clearInterval(intervalId);
  });
  pollingIntervals.value.clear();
  wavesurfer?.destroy();
  console.log('清除所有音乐任务轮询计时器');
});
</script>

<template>
  <NFlex :wrap="false">
    <NCard class="h-full max-w-[550px] w-1/2 flex flex-col px-2">
      <NSelect v-model:value="selectedModel" class="mb-3" :options="modelOptions"></NSelect>

      <div>
        <NText>根据参考音乐延长续写</NText>
      </div>

      <div class="mt-4 max-h-[30em]">
        <!-- 上传音频组件 -->
        <NSpin :show="uploadLoading">
          <NUpload
            v-show="!(hasAudioFile && !isSelectedAudioTask)"
            :disabled="isSelectedAudioTask"
            :custom-request="customRequest"
            accept=".flac,.mp3,.mp4,.mpeg,.mpga,.m4a,.ogg,.wav,.webm"
            :show-file-list="false"
            class=""
          >
            <NUploadDragger>
              <NText>点击或者拖动文件到该区域来上传</NText>
              <NP depth="3">支持MP3、WAV等音频格式</NP>
            </NUploadDragger>
          </NUpload>

          <!-- 音频波形显示 -->
          <div class="n-upload-trigger max-h-[165px] flex flex-col">
            <div v-show="hasAudioFile && !isSelectedAudioTask" id="waveform"></div>

            <div class="my-3 flex items-center justify-between">
              <NMarquee class="mr-3 w-1/3">从音频两端可以拉到选框前裁剪音频</NMarquee>
              <NButton strong secondary circle size="large" class="h-35px w-35px" @click="triggerPlayPause">
                <SvgIcon
                  :icon="playing ? 'zondicons:pause-outline' : 'zondicons:play-outline'"
                  class="h-35px w-35px text-blue-600"
                />
              </NButton>
              <div class="flex-1">
                <NTag
                  :closable="hasAudioFile"
                  round
                  :bordered="false"
                  size="large"
                  class="mx-2 h-35px"
                  :on-close="removeFile"
                >
                  <template v-if="hasAudioFile">{{ fileName.substring(0, 20) }}</template>
                  <template v-else>未选择音频</template>
                </NTag>
              </div>
            </div>
          </div>
        </NSpin>

        <!-- 我的音频历史记录 -->
        <div class="mt-6">
          <NText class="mb-2 block">我的音频</NText>
          <NScrollbar class="max-h-[200px]" @scroll="handleUserAudioScroll">
            <NFlex vertical>
              <div
                v-for="task in userAudioHistory"
                :key="task.taskid"
                class="audio-history-item"
                :class="{
                  'audio-history-item-selected': selectedAudioTask?.id === task.id
                }"
                @click="selectAudioFromHistory(task)"
              >
                {{ task.music_title }}
                <span
                  v-if="task.status === 'SUCCESS' && selectedAudioTask?.id !== task.id"
                  class="ml-2 text-xs text-green-500"
                >
                  可用
                </span>
                <span v-else-if="selectedAudioTask?.id === task.id" class="ml-2 text-xs text-blue-500">已选中</span>
                <span v-else class="ml-2 text-xs text-gray-400">{{ task.status }}</span>
              </div>

              <NFlex v-if="isLoadingUserAudio" justify="center" class="py-2">
                <NSpin size="small" />
                <NText depth="3" class="ml-2">加载中...</NText>
              </NFlex>

              <NFlex v-if="!hasMoreUserAudio && userAudioHistory.length > 0" justify="center" class="py-2">
                <NText depth="3">没有更多数据了</NText>
              </NFlex>

              <NEmpty
                v-if="userAudioHistory.length === 0 && !isLoadingUserAudio"
                description="暂无音频记录"
                class="mt-2"
              />
            </NFlex>
          </NScrollbar>
        </div>
      </div>

      <!-- 描述输入 -->
      <NInput
        v-model:value="songDescription"
        type="textarea"
        :rows="5"
        placeholder="输入音乐描述"
        class="mt-6"
        clearable
        :maxlength="1000"
        show-count
      />

      <!-- 生成按钮 -->
      <NButton class="mt-3 w-full" type="info" :disabled="!hasAudioFile" @click="continueMusicGeneration">生成</NButton>
    </NCard>

    <!-- 右侧历史记录 -->
    <NFlex class="h-full flex flex-col flex-1 px-2">
      <!-- 搜索框 -->
      <NButtonGroup class="w-full">
        <NInput v-model:value="searchTitle" placeholder="输入歌曲标题" @keyup="handleSearchKeyup"></NInput>
        <NButton type="info" @click="handleSearch">
          <SvgIcon icon="dashicons:search" class="mr-1 text-[18px]" />
          查看
        </NButton>
      </NButtonGroup>

      <!-- 历史记录 -->
      <NScrollbar ref="scrollbarRef" class="h-[40em] max-h-[40em] flex flex-col" @scroll="handleScroll">
        <MusicInfoCard
          v-for="task in generatedTasks"
          :key="task.taskid"
          :task="task"
          class="mb-3"
          @play="handlePlay"
          @download="handleDownload"
        />
        <NEmpty v-if="generatedTasks.length === 0 && !isLoadingHistory" description="暂无生成任务" class="mt-10" />

        <!-- 加载 -->
        <NFlex v-if="isLoadingHistory" justify="center" class="py-4">
          <NSpin size="small" />
          <NText depth="3" class="ml-2">加载中...</NText>
        </NFlex>

        <!-- 没有更多数据 -->
        <NFlex v-if="!hasMore && generatedTasks.length > 0" justify="center" class="py-4">
          <NText depth="3">没有更多数据了</NText>
        </NFlex>
      </NScrollbar>

      <!-- 音频播放组件 -->
      <MusicPlayAudio
        :audio-url="currentAudioUrl"
        :audio-title="currentAudioTitle"
        @previous-track="handlePreviousTrack"
        @next-track="handleNextTrack"
        @ended="handleTrackEnded"
      />
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
.borderStyle {
  border: 1px solid v-bind('borderColor');
}

.audio-history-item {
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 6px;
  background-color: v-bind('backgroundColor');
  border: 1px solid v-bind('borderColor');
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: v-bind('textColor');

  &:hover {
    border-color: v-bind('focusBorderColor');
    box-shadow: 0 0 0 1px v-bind('shadowColor');
  }
}

.audio-history-item-selected {
  border-color: v-bind('focusBorderColor');
  box-shadow: 0 0 0 1px v-bind('shadowColor');
  background-color: rgba(70, 146, 251, 0.05);
}

#waveform {
  margin-top: 10px;
  margin-bottom: 10px;
  min-height: 100px;
  border-radius: 6px;
  overflow: hidden;
}
</style>
