from sqlalchemy import Column, String, Text, TIMESTAMP, Integer
from utils.database import Base
import datetime


class AudioEducation(Base):
  __tablename__ = "audio_education"

  id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='自增主键')
  user = Column(String(32), nullable=False, comment='用户')
  taskid = Column(String(255), nullable=False, comment='降噪任务ID')
  status = Column(String(50), nullable=False, comment='任务状态')
  create_time = Column(TIMESTAMP, default=datetime.datetime.now, nullable=True, comment='数据创建时间')
  uptime = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=True,
                  comment='数据更新时间，自动更新为当前时间')
  audio_url = Column(Text, nullable=True, comment='降噪音频OSS地址')
