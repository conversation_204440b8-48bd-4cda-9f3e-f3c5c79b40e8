<script setup lang="ts">
import { computed, ref } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { fetchVolcengineCutout } from '@/service/api/portrait';
import type { VolcengineCutoutRequest } from '@/service/api/portrait';

// 消息提示
const message = useMessage();

// 图片上传预览数据
const originalImage = ref<{ url: string; name: string } | null>(null);
const processedImage = ref<string>('');
const isBase64Result = ref<boolean>(false);

// 处理参数
const cutoutMode = ref<number>(0); // 0: 背景透明(前景图), 1: 蒙版
const refineEdge = ref<number>(0); // 边缘优化：0-不优化，1-轻度优化，2-强度优化
const isLoading = ref<boolean>(false);

// 上传限制
const beforeUpload = (data: { file: UploadFileInfo }): boolean => {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 4.7 * 1024 * 1024; // 4.7MB

  if (!allowedImageTypes.includes(data.file.file?.type || '')) {
    message.error('请上传正确的图片格式（JPG、PNG、GIF、WEBP）');
    return false;
  }

  if (data.file.file && data.file.file.size > maxSize) {
    message.error('图片大小不能超过4.7MB');
    return false;
  }

  // 清除之前的处理结果
  processedImage.value = '';

  return true;
};

// 自定义上传请求
const customRequest = ({ file, onFinish }: { file: UploadFileInfo; onFinish: () => void }) => {
  const reader = new FileReader();
  reader.onload = e => {
    if (e.target && e.target.result && file.file) {
      originalImage.value = {
        url: e.target.result as string,
        name: file.file.name
      };
    }
    onFinish();
  };

  if (file.file) {
    reader.readAsDataURL(file.file);
  } else {
    message.error('图片上传失败');
    onFinish();
  }
};

// 移除已上传图片
const handleRemove = () => {
  originalImage.value = null;
  processedImage.value = '';
};

// 模式选项
const modeOptions = [
  { label: '背景透明', value: 0 },
  { label: '蒙版', value: 1 }
];

// 边缘优化选项
const edgeOptions = [
  { label: '不优化', value: 0 },
  { label: '轻度优化', value: 1 },
  { label: '强度优化', value: 2 }
];

// 生成处理
const handleGenerate = async () => {
  if (!originalImage.value) {
    message.error('请先上传图片');
    return;
  }

  try {
    isLoading.value = true;

    // 构建请求数据
    const requestData: VolcengineCutoutRequest = {
      file: [
        {
          filename: originalImage.value.name,
          data: originalImage.value.url
        }
      ],
      only_mask: cutoutMode.value,
      rgb: [-1, -1, -1], // 默认透明背景
      refine_mask: refineEdge.value
    };

    // 调用API
    const response = await fetchVolcengineCutout(requestData);

    if (response.data) {
      if (
        'image_base64' in response.data &&
        typeof response.data.image_base64 === 'string' &&
        response.data.image_base64
      ) {
        // 如果返回了Base64数据
        // 确保base64字符串不包含特殊字符，清理掉可能导致问题的字符
        const cleanBase64 = response.data.image_base64.replace(/\s/g, '');
        processedImage.value = `data:image/png;base64,${cleanBase64}`;
        isBase64Result.value = true;
        message.success('图片处理成功');
      } else if (
        'image_url' in response.data &&
        typeof response.data.image_url === 'string' &&
        response.data.image_url
      ) {
        // 兼容旧版可能返回URL的情况
        processedImage.value = response.data.image_url;
        isBase64Result.value = false;
        message.success('图片处理成功');
      } else {
        throw new Error('处理失败，返回数据格式不正确');
      }
    } else {
      throw new Error('处理失败，未获取到有效数据');
    }
  } catch (error) {
    console.error('处理错误:', error);
    // message.error('图片处理失败，请重试');
  } finally {
    isLoading.value = false;
  }
};

// 辅助函数：下载图片
const downloadImage = (blobUrl: string) => {
  // 创建下载链接
  const link = document.createElement('a');
  link.href = blobUrl;
  // 根据模式设置文件名
  const filePrefix = cutoutMode.value === 0 ? 'foreground_' : 'mask_';
  link.download = `${filePrefix}${originalImage.value?.name || 'image.png'}`;
  link.style.display = 'none';
  document.body.appendChild(link);

  // 触发下载
  link.click();

  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(blobUrl);
};

// 处理下载
const handleDownload = () => {
  if (!processedImage.value) {
    message.error('没有可下载的图片');
    return;
  }

  // 显示加载状态
  isLoading.value = true;

  try {
    if (isBase64Result.value) {
      // 对于Base64数据，使用安全的方式解码
      // 确保去除data URL前缀
      const base64Data = processedImage.value.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');

      // 使用Fetch API和base64 URL安全地创建blob
      fetch(`data:image/png;base64,${base64Data}`)
        .then(res => res.blob())
        .then(blob => {
          const blobUrl = URL.createObjectURL(blob);
          downloadImage(blobUrl);
        })
        .catch(error => {
          console.error('Base64解码错误:', error);
          message.error('下载失败，图像数据格式异常');
        })
        .finally(() => {
          isLoading.value = false;
        });
    } else {
      // 对于URL，使用fetch获取图片内容
      fetch(processedImage.value)
        .then(response => response.blob())
        .then(blob => {
          const blobUrl = URL.createObjectURL(blob);
          downloadImage(blobUrl);
        })
        .catch(error => {
          console.error('下载错误:', error);
          message.error('下载失败，请重试');
        })
        .finally(() => {
          isLoading.value = false;
        });
    }
  } catch (error) {
    console.error('下载错误:', error);
    message.error('下载失败，请重试');
  } finally {
    isLoading.value = false;
  }
};

// 是否显示结果图片
const showResult = computed(() => {
  return Boolean(processedImage.value);
});

// 是否启用处理按钮
const canProcess = computed(() => {
  return Boolean(originalImage.value) && !isLoading.value;
});

// 是否启用下载按钮
const canDownload = computed(() => {
  return Boolean(processedImage.value) && !isLoading.value;
});

// 模式名称
const modeName = computed(() => {
  return cutoutMode.value === 0 ? '图片' : '蒙版';
});
</script>

<template>
  <div class="image-cutout-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <NCard title="图像抠图选项" class="options-card">
        <NForm label-placement="left" label-width="100">
          <NFormItem label="输出模式">
            <NSelect v-model:value="cutoutMode" :options="modeOptions" />
          </NFormItem>

          <NFormItem label="边缘优化">
            <NSelect v-model:value="refineEdge" :options="edgeOptions" />
          </NFormItem>

          <NFormItem>
            <NButton type="primary" :disabled="!canProcess" :loading="isLoading" block @click="handleGenerate">
              开始生成
            </NButton>
          </NFormItem>

          <NFormItem>
            <NButton :disabled="!canDownload" block @click="handleDownload">下载{{ modeName }}</NButton>
          </NFormItem>
        </NForm>
      </NCard>
    </div>

    <!-- 图片上传和结果预览区域 -->
    <div class="preview-section">
      <div v-if="!showResult" class="upload-area">
        <NUpload
          :custom-request="customRequest"
          accept="image/jpeg,image/png,image/gif,image/webp"
          :show-file-list="false"
          @before-upload="beforeUpload"
        >
          <NUploadDragger>
            <div class="upload-content">
              <div v-if="originalImage" class="image-preview">
                <NImage :src="originalImage.url" object-fit="contain" preview-disabled :width="300" />
                <div class="remove-btn">
                  <NButton text type="error" @click.stop="handleRemove">
                    <template #icon>
                      <icon-ic-round-delete class="text-icon" />
                    </template>
                    删除
                  </NButton>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <SvgIcon icon="mdi-cloud-upload" class="text-[6em] text-[#bdbbbb]" />
                <div class="upload-text">
                  <NText>上传图片</NText>
                  <br />
                  <NText class="upload-hint">可拖入文件或者点击上传</NText>
                  <br />
                  <NText class="upload-hint">支持JPG、PNG等格式，最大4.7MB</NText>
                </div>
              </div>
            </div>
          </NUploadDragger>
        </NUpload>
      </div>

      <!-- 结果预览 -->
      <div v-if="showResult" class="result-view">
        <div class="result-container">
          <!-- 当NImage组件无法显示时，使用原生img标签作为备选 -->
          <template v-if="isBase64Result">
            <img :src="processedImage" alt="处理结果" class="result-image" />
          </template>
          <template v-else>
            <NImage :src="processedImage" object-fit="contain" :preview-disabled="false" class="result-image" />
          </template>
        </div>
        <div class="result-actions">
          <NButton size="small" type="info" @click="handleRemove">重新上传</NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.image-cutout-container {
  display: flex;
  gap: 24px;

  .preview-section {
    flex: 1;
    min-height: 500px;
    border-radius: 8px;
    overflow: hidden;
    // background-color: #f5f5f5;

    .upload-area {
      height: 100%;
      min-height: 500px;

      :deep(.n-upload) {
        height: 100%;
      }

      :deep(.n-upload-trigger) {
        height: 100%;
      }

      :deep(.n-upload-dragger) {
        height: 100%;
        min-height: 500px;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        .image-preview {
          position: relative;
          .remove-btn {
            position: absolute;
            bottom: 5px;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            padding: 4px;
          }
        }

        .upload-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;

          .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
          }

          .upload-text {
            text-align: center;

            p {
              margin: 4px 0;
            }

            .upload-hint {
              font-size: 12px;
            }
          }
        }
      }
    }

    .result-view {
      height: 100%;
      min-height: 500px;
      position: relative;
      overflow: hidden;
      background-color: #f5f5f5;
      background-image: linear-gradient(45deg, #ddd 25%, transparent 25%),
        linear-gradient(-45deg, #ddd 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ddd 75%),
        linear-gradient(-45deg, transparent 75%, #ddd 75%);
      background-size: 20px 20px;
      background-position:
        0 0,
        0 10px,
        10px -10px,
        -10px 0px;

      .result-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;

        .result-image {
          max-height: 90%;
          max-width: 90%;
          object-fit: contain;
          display: block;
          width: auto;
          height: auto;
        }
      }

      .result-actions {
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 10;
      }
    }
  }

  .control-panel {
    width: 300px;

    .options-card {
      height: 100%;
    }
  }
}
</style>
