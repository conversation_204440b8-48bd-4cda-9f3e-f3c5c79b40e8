import type { AxiosResponse } from 'axios';
import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import { localStg } from '@/utils/storage';
import { getServiceBaseURL } from '@/utils/service';
import { checkAndHandleSSEAuth, getCurrentToken } from '@/utils/auth';
import { handleRefreshToken, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';
// sseClient.ts

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL,
    headers: {
      apifoxToken: 'XL299LiMEDZ0H5h3A29PxwQXdMJqWyY2'
    },
    timeout: 900000 // 请求超时时间设置为13分钟
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // set token
      const token = localStg.get('token');
      const Authorization = token ? `Bearer ${token}` : null;
      Object.assign(headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "0000"(default), it means the request is success
      // to change this logic by yourself, you can modify the `VITE_SERVICE_SUCCESS_CODE` in `.env` file
      return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
    },
    async onBackendFail(response, instance) {
      const authStore = useAuthStore();

      function handleLogout() {
        authStore.resetStore();
      }

      function logoutAndCleanup() {
        handleLogout();
        window.removeEventListener('beforeunload', handleLogout);

        request.state.errMsgStack = request.state.errMsgStack.filter(msg => msg !== response.data.msg);
      }

      // when the backend response code is in `logoutCodes`, it means the user will be logged out and redirected to login page
      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
      if (logoutCodes.includes(response.data.code)) {
        handleLogout();
        return null;
      }

      // when the backend response code is in `modalLogoutCodes`, it means the user will be logged out by displaying a modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(response.data.code) && !request.state.errMsgStack?.includes(response.data.msg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), response.data.msg];

        // prevent the user from refreshing the page
        window.addEventListener('beforeunload', handleLogout);

        window.$dialog?.error({
          title: 'Error',
          content: response.data.msg,
          positiveText: $t('common.confirm'),
          maskClosable: false,
          closeOnEsc: false,
          onPositiveClick() {
            logoutAndCleanup();
          },
          onClose() {
            logoutAndCleanup();
          }
        });

        return null;
      }

      // when the backend response code is in `expiredTokenCodes`, it means the token is expired, and refresh token
      // the api `refreshToken` can not return error code in `expiredTokenCodes`, otherwise it will be a dead loop, should return `logoutCodes` or `modalLogoutCodes`
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(response.data.code) && !request.state.isRefreshingToken) {
        request.state.isRefreshingToken = true;

        const refreshConfig = await handleRefreshToken(response.config);

        request.state.isRefreshingToken = false;

        if (refreshConfig) {
          return instance.request(refreshConfig) as Promise<AxiosResponse>;
        }
      }

      return null;
    },
    transformBackendResponse(response) {
      return response.data.data; // 格式化返回的内容
    },
    onError(error) {
      // console.log('on error', error);
      let message = error.message;
      let backendErrorCode = '';
      // console.log('=======errr', request.state, '=', error.response?.data.code, '=', error.response?.data?.msg);

      // 如果返回错误
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.msg || message;
        backendErrorCode = error.response?.data?.code || '';
      }
      // 如果返回402积分不足错误
      if (error.response?.data.code === '402') {
        message = error.response?.data?.msg || '积分不足';
      }

      // the error message is displayed in the modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return;
      }

      // when the token is expired, refresh token and retry request, so no need to show error message
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(backendErrorCode)) {
        return;
      }

      showErrorMsg(request.state, message);
    }
  }
);

export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // set token
      const token = localStg.get('token');
      const Authorization = token ? `Bearer ${token}` : null;
      Object.assign(headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "200", it means the request is success
      // you can change this logic by yourself
      return response.data.status === '200';
    },
    async onBackendFail(_response) {
      // when the backend response code is not "200", it means the request is fail
      // for example: the token is expired, refresh token and retry request
    },
    transformBackendResponse(response) {
      return response.data.result;
    },
    onError(error) {
      // when the request is fail, you can show error message

      let message = error.message;

      // show backend error message
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message;
      }

      window.$message?.error(message);
    }
  }
);

interface SSEOptions {
  url: string;
  payload: any;
  onMessage: (message: string) => void;
  onError?: (error: any) => void;
  onComplete?: () => void; // 添加完成回调
  onStart?: () => void;
  isFormData?: boolean; // 添加是否为FormData参数
  headers?: Record<string, string>;
}

export async function createSSEClient(options: SSEOptions) {
  const { url, payload, onMessage, onError, onComplete, onStart, headers } = options;
  const token = getCurrentToken();

  // 在发起请求前检查token是否存在
  if (!token) {
    const authError = new Error('认证失败，请重新登录');
    if (onError) {
      onError(authError);
    }
    checkAndHandleSSEAuth({ status: 401 } as Response);
    return { cancel: () => {} };
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(payload)
    });

    // 检查认证状态
    if (checkAndHandleSSEAuth(response)) {
      return { cancel: () => {} };
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 请求成功后立即调用 onStart 回调
    if (onStart) onStart();

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    function read() {
      reader
        ?.read()
        .then(({ done, value }) => {
          if (done) {
            if (onComplete) onComplete();
            return;
          }

          const chunk = decoder.decode(value, { stream: true });
          const messages = chunk.split('\n');

          messages.forEach(message => {
            if (message.startsWith('data: ')) {
              const data = message.slice(6).trim();
              if (data !== '') {
                onMessage(data);
              }
            }
          });

          read();
        })
        .catch(error => {
          if (onError) {
            onError(error);
          } else {
            console.error('Stream reading error:', error);
          }
        });
    }

    read();

    return {
      cancel: () => reader?.cancel()
    };
  } catch (error) {
    if (onError) {
      onError(error);
    } else {
      console.error('Fetch error:', error);
    }
    return {
      cancel: () => {}
    };
  }
}

export async function chatSSEClient(options: SSEOptions) {
  const { url, payload, onMessage, onError, onComplete, onStart, isFormData, headers } = options;
  const token = getCurrentToken();

  // 在发起请求前检查token是否存在
  if (!token) {
    const authError = new Error('认证失败，请重新登录');
    if (onError) {
      onError(authError);
    }
    checkAndHandleSSEAuth({ status: 401 } as Response);
    return { cancel: () => {} };
  }

  let isCompleted = false;
  let reader: ReadableStreamDefaultReader<Uint8Array>;
  let lastProcessTime = 0;
  const PROCESS_INTERVAL = 10; // 处理间隔10ms，提高响应速度
  let messageQueue: string[] = []; // 消息队列，用于平滑处理
  let isProcessingQueue = false; // 是否正在处理队列的标志

  // 处理消息队列的函数
  async function processMessageQueue() {
    if (isProcessingQueue || messageQueue.length === 0) return;

    isProcessingQueue = true;

    // 每次只处理一条消息，避免阻塞
    const message = messageQueue.shift();
    if (message) {
      onMessage(message);
    }

    // 使用requestAnimationFrame安排下一次处理，提高渲染性能
    isProcessingQueue = false;
    if (messageQueue.length > 0) {
      requestAnimationFrame(processMessageQueue);
    }
  }

  // 添加消息到队列
  function addToMessageQueue(message: string) {
    messageQueue.push(message);
    if (!isProcessingQueue) {
      processMessageQueue();
    }
  }

  function processLine(line: string) {
    if (!line.trim()) return;

    try {
      let jsonString = line;
      if (line.startsWith('data: ')) {
        jsonString = line.substring(5).trim();
      }
      if (jsonString === 'data: ' || jsonString === '') {
        return;
      }

      const data = JSON.parse(jsonString);

      // 使用队列处理消息，实现平滑显示
      addToMessageQueue(JSON.stringify(data));

      // 检查完成状态
      if (data.done || data.isEnd) {
        if (!isCompleted && onComplete) {
          isCompleted = true;
          onComplete();
        }
      }
    } catch (e) {
      try {
        let cleanedLine = line;
        if (cleanedLine.startsWith('data: ')) {
          cleanedLine = cleanedLine.substring(5).trim();
        }
        const firstBrace = cleanedLine.indexOf('{');
        if (firstBrace > 0) {
          cleanedLine = cleanedLine.substring(firstBrace);
        }
        const data = JSON.parse(cleanedLine);
        addToMessageQueue(JSON.stringify(data));
      } catch (e2) {
        if (line.trim()) {
          addToMessageQueue(
            JSON.stringify({
              data: {
                text: line,
                isPlainText: true
              }
            })
          );
        }
      }
    }
  }

  async function processStream(response: Response) {
    const decoder = new TextDecoder();
    let buffer = '';
    reader = response.body!.getReader();

    async function readNext() {
      if (isCompleted) return;

      try {
        const { done, value } = await reader.read();

        if (done) {
          if (buffer.trim()) {
            processLine(buffer);
          }

          if (!isCompleted && onComplete) {
            isCompleted = true;
            onComplete();
          }
          return;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 更细粒度的行处理，提高响应速度
        const now = Date.now();
        if (now - lastProcessTime >= PROCESS_INTERVAL || buffer.length > 100) {
          // 降低缓冲区大小阈值，更快地处理数据
          lastProcessTime = now;

          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          // 逐行处理，不使用await以避免阻塞
          for (const line of lines) {
            processLine(line);
          }

          // 如果有多行，让出主线程一次而不是每行都让
          if (lines.length > 0) {
            setTimeout(() => {}, 0);
          }

          // 使用requestAnimationFrame安排下一次读取，提高渲染性能
          requestAnimationFrame(() => readNext());
        } else {
          // 如果间隔太短，直接安排下一次读取
          readNext();
        }
      } catch (error) {
        if (!isCompleted && onError) {
          onError(error);
        }
      }
    }

    return readNext();
  }

  try {
    // 立即调用onStart回调
    if (onStart) onStart();

    // 设置请求头
    const reqHeaders: HeadersInit = {
      ...headers,
      Authorization: `Bearer ${token}`
    };

    // 根据是否为FormData设置不同的请求选项
    let requestOptions: RequestInit;

    if (isFormData) {
      // FormData不需要设置Content-Type，浏览器会自动设置
      requestOptions = {
        method: 'POST',
        headers: reqHeaders,
        body: payload // 直接使用FormData对象
      };
    } else {
      reqHeaders['Content-Type'] = 'application/json';
      requestOptions = {
        method: 'POST',
        headers: reqHeaders,
        body: JSON.stringify(payload)
      };
    }

    const response = await fetch(url, requestOptions);

    // 检查认证状态
    if (checkAndHandleSSEAuth(response)) {
      return { cancel: () => {} };
    }

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    await processStream(response);

    return {
      cancel: () => {
        if (reader) {
          reader.cancel();
        }
        // 清空消息队列
        messageQueue = [];
        isProcessingQueue = false;
      }
    };
  } catch (error) {
    if (!isCompleted && onError) onError(error);
    return { cancel: () => {} };
  }
}
