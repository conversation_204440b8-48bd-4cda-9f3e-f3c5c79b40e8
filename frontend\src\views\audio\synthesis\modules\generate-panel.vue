<script lang="ts" setup>
// 定义当前生成任务的数据结构
interface CurrentGeneratingTask {
  id: number;
  status: 'SUBMITTED' | 'IN_PROGRESS' | 'SUCCESS' | 'FAILURE';
  text: string;
  model: string;
  duration: string;
  createTime: string;
  url: string;
  isPlaying?: boolean;
}

// 定义props
const props = defineProps<{
  loading?: boolean;
  currentGeneratingTasks?: CurrentGeneratingTask[];
}>();

// 定义emit事件
const emit = defineEmits<{
  (e: 'generateAudio'): void;
  (e: 'playAudio', task: CurrentGeneratingTask): void;
  (e: 'downloadAudio', task: CurrentGeneratingTask): void;
}>();

// 获取状态对应的文本
const getStatusText = (status: CurrentGeneratingTask['status']) => {
  switch (status) {
    case 'SUCCESS':
      return '';
    case 'FAILURE':
      return '生成失败！';
    case 'SUBMITTED':
      return '排队中...';
    case 'IN_PROGRESS':
      return '生成中..';
    default:
      return '';
  }
};

// 处理播放音频
const handlePlayAudio = (task: CurrentGeneratingTask) => {
  emit('playAudio', task);
};

// 处理下载音频
const handleDownloadAudio = (task: CurrentGeneratingTask) => {
  emit('downloadAudio', task);
};

// 处理生成音频按钮点击
const handleGenerateAudio = () => {
  emit('generateAudio');
};
</script>

<template>
  <NFlex class="w-full p-4">
    <!-- 生成中任务展示 -->
    <NFlex
      v-if="props.currentGeneratingTasks && props.currentGeneratingTasks.length > 0"
      class="w-full"
      justify="space-between"
      align="center"
    >
      <NCard
        v-for="task in props.currentGeneratingTasks"
        :key="task.id"
        class="audio_generate_task_card flex-1"
        :class="{ 'mr-3': props.currentGeneratingTasks.length > 1 }"
      >
        <NFlex justify="space-between" align="center" :wrap="false">
          <!-- 播放/状态按钮 -->
          <NButton v-if="task.status === 'SUCCESS'" text @click="handlePlayAudio(task)">
            <SvgIcon :icon="task.isPlaying ? 'gridicons:pause' : 'lsicon:play-filled'" class="text-4xl" />
          </NButton>

          <SvgIcon v-else-if="task.status === 'FAILURE'" icon="lets-icons:info-fill" class="text-4xl" />

          <SvgIcon v-else icon="eos-icons:loading" class="text-4xl" />

          <!-- 任务信息 -->
          <NFlex vertical class="ml-3">
            <NEllipsis :line-clamp="1">
              {{ task.text }}
              <template #tooltip>
                <div class="max-w-100 text-center">{{ task.text }}</div>
              </template>
            </NEllipsis>

            <NFlex v-if="task.status === 'SUCCESS'" align="center" class="functional_information">
              <NText class="text-xs">{{ task.model }}</NText>
              <!--
 <NDivider vertical />
              <NText class="text-xs">{{ task.duration }}</NText> 
-->
            </NFlex>

            <NFlex v-else align="center" class="functional_information">
              <NText class="text-xs">{{ getStatusText(task.status) }}</NText>
            </NFlex>
          </NFlex>
        </NFlex>

        <!-- 任务结束显示 下载 -->
        <NButton v-if="task.status === 'SUCCESS'" text class="ml-3" @click="handleDownloadAudio(task)">
          <SvgIcon icon="material-symbols-light:download-rounded" class="text-3xl" />
        </NButton>
      </NCard>
    </NFlex>

    <NFlex class="mt-3 w-full" justify="end" align="end">
      <!-- 语言选择 -->
      <!--
 <NPopselect
        v-model:value="language"
        :options="languageOptions"
        trigger="click"
        placement="top"
        class="audio_language_select"
      >
        <NTag size="large" class="w-40 cursor-pointer">
          <NButton text class="flex text-base">
            <SvgIcon icon="pepicons-pencil:internet" class="mr-1" />
            <NText class="w-26 text-left">
              {{ languageOptions.find(item => item.value === language)?.label }}
            </NText>
            <SvgIcon icon="material-symbols-light:expand-all-rounded" class="ml-2" />
          </NButton>
        </NTag>
      </NPopselect>
-->

      <!-- 提交任务 -->
      <NButton
        type="primary"
        class="w-30"
        :loading="props.loading"
        :disabled="props.loading"
        @click="handleGenerateAudio"
      >
        生成音频
      </NButton>
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
.audio_generate_task_card > :deep(.n-card__content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.functional_information) {
  gap: 8px 5px !important ;
}
</style>

<style>
.audio_language_select {
  width: 11em !important;
}
</style>
