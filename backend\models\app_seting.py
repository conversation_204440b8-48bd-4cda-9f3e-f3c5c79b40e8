from utils.database import Base
from sqlalchemy import Column, Integer,String,JSON,DateTime,Text,Enum,TIMESTAMP
from sqlalchemy.dialects.mysql import TINYINT
from enum import Enum as PyEnum
import datetime

# class ValueType(PyEnum):
#     TYPE_TEXT = "text"
#     TYPE_JSON = "json"

class AppSetting(Base):
    __tablename__ = "app_setting"
    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    key_type = Column(String(32), nullable=False, comment='键类型')
    key_code = Column(String(128), nullable=True, comment='键名')
    # value_type = Column(Enum(ValueType), nullable=False, comment='类型 text json')
    value_type = Column(String(8), nullable=False, comment='类型 text json')
    key_value = Column(Text, default=1, nullable=False, comment='值')
    pid = Column(Integer, nullable=True, comment='父ID')
    remark = Column(Text, nullable=True, comment='备注')
    crtime = Column(TIMESTAMP, nullable=True, comment='创建时间')
    edituser = Column(String(32), nullable=True, comment='编辑人')
    seq = Column(Integer, nullable=True, comment='排序')
    sync_redis = Column(TINYINT, nullable=True,default=0,comment='是否同步redis 0-否 1-是')




