<script lang="ts" setup>
// import { ref } from 'vue';

import SpeechEnhancement from './modules/speech-enhancement.vue';
import SpeechSeparation from './modules/speech-separation.vue';
</script>

<template>
  <NTabs type="line" animated>
    <NTabPane name="enhancement" tab="语音增强">
      <SpeechEnhancement />
    </NTabPane>
    <NTabPane name="separation" tab="语音分离">
      <SpeechSeparation />
    </NTabPane>
  </NTabs>
</template>

<style scoped></style>
