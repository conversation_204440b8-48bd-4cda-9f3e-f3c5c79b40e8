from sqlalchemy import Column, Integer, String, ForeignKey, TIMESTAMP, Text, SmallInteger
from sqlalchemy.orm import relationship
from utils.database import Base


class MJChannel(Base):
  __tablename__ = "mj_channels"

  id = Column(Integer, primary_key=True, index=True, autoincrement=True)
  channel_name = Column(String(24), nullable=False, comment="渠道名称")
  permission = Column(String(24), nullable=False, comment="权限标识")
  creator = Column(Integer, nullable=True, comment="创建者ID")
  creator_team = Column(Integer, nullable=True, comment="创建者团队ID")
  create_time = Column(TIMESTAMP, default="CURRENT_TIMESTAMP", nullable=True, comment="创建时间")
  update_time = Column(TIMESTAMP, default="CURRENT_TIMESTAMP", onupdate="CURRENT_TIMESTAMP", nullable=True,
                       comment="更新时间")
  modifier = Column(Integer, nullable=True, comment="修改者ID")
  status = Column(SmallInteger, default=1, nullable=True, comment="状态（1：启用，0：禁用）")

  def __repr__(self):
    return f"<MJChannel(id={self.id}, channel_name={self.channel_name}, permission={self.permission})>"
