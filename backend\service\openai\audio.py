import logging
from openai import OpenAI

from config import app_settings

logger = logging.getLogger(__name__)


def get_token():
    return app_settings.openai_api_key


client = OpenAI(api_key=get_token())

models = ["tts-1", "tts-1-hd"]
speeds = {"min": 0.25, "max": 4}
response_formats = ["mp3", "opus", "aac", "flac", "wav", "pcm"]
voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]


def create_speech(
    text: str,
    voice: str,
    model: str = "tts-1",
    speed: float = 1.0,
    response_format: str = "aac",
):
    if voice not in voices:
        logger.error(f"不支持的音色: {voice}")
        raise ValueError(f"不支持的音色: {voice}")
    if model not in models:
        logger.error(f"不支持的模型: {model}")
        raise ValueError(f"不支持的模型: {model}")
    if not (speeds["min"] <= speed <= speeds["max"]):
        logger.error(f"语速必须在 {speeds['min']} 和 {speeds['max']} 之间")
        raise ValueError(f"语速必须在 {speeds['min']} 和 {speeds['max']} 之间")

    # 构建请求负载
    data = {
        "model": model,
        "input": text,
        "voice": voice,
        "speed": speed,
        "response_format": response_format,
    }

    # 发起API请求
    try:
        response = client.audio.speech.create(**data)
        return response
    except Exception as e:
        error_message = f"Failed to create audio: {str(e)}"
        logger.error(error_message)
        raise Exception(error_message)
