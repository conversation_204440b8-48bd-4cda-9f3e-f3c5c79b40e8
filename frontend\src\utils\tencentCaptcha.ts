class TencentCaptchaWrapper {
  constructor(private readonly appid: string) {
    if (!window.TencentCaptcha) {
      throw new Error('TencentCaptcha is not loaded');
    }
  }

  async show<S>(bizState?: S) {
    return new Promise<CallbackRes<S>>(resolve => {
      const captchaLanguage = navigator.language === 'en-US' ? 'en' : 'zh-cn';
      const captcha = new window.TencentCaptcha!(
        this.appid,
        res => {
          resolve(res);
        },
        {
          userLanguage: captchaLanguage,
          bizState
        }
      );
      captcha.show();
    });
  }
}

const tencentCaptcha = new TencentCaptchaWrapper(import.meta.env.VITE_TENCENT_CAPTCHA_APPID);
export default tencentCaptcha;
