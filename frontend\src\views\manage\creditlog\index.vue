<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchCreditLog, fetchCreditLogSearch } from '@/service/api';
import { AiCapacity } from '@/utils/AiCapacity';
import CreditLogSearch from './modules/creditlog-search.vue';

const appStore = useAppStore();

const capacityOptions = [
  {
    label: '图像生成',
    value: AiCapacity.IMAGE_GENERATION
  },
  {
    label: '视频生成',
    value: AiCapacity.VIDEO_GENERATION
  }
];

interface CreditLogData extends NaiveUI.TableData {
  id: number;
  user_id: number;
  username: string;
  credit: number;
  after_credit: number;
  capacity: string;
  model: string;
  matter: string;
  detail: string;
  editor: string;
  ip: string;
  createtime: string;
}

// 获取积分日志
const fetchLogTyped: NaiveUI.TableApiFn<CreditLogData, Api.SystemManage.CreditLogSearchParams> = async params => {
  const response = await fetchCreditLog(params.current, params.size);
  return response as NaiveUI.FlatResponseData<Api.Common.PaginatingQueryRecord<CreditLogData>>;
};

const searchParams = ref<Api.SystemManage.CreditLogSearchParams>({
  current: 1,
  size: 10
});

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchLogTyped,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'id',
      title: '序号',
      align: 'center',
      minWidth: 10
    },
    {
      key: 'user_id',
      title: '用户 ID',
      align: 'center',
      minWidth: 40
    },
    {
      key: 'username',
      title: '用户名',
      align: 'center',
      minWidth: 80
    },
    {
      key: 'credit',
      title: '本次变更积分',
      align: 'right',
      minWidth: 30,
      render(row) {
        const credit = row.credit;
        if (credit > 0) {
          return `+${credit}`;
        }
        return credit.toString();
      }
    },
    {
      key: 'after_credit',
      title: '变更后积分',
      align: 'right',
      minWidth: 30
    },
    {
      key: 'capacity',
      title: 'AI 能力',
      align: 'center',
      minWidth: 100,
      render(row) {
        const value = row.capacity;
        if (!value) {
          return value;
        }
        for (const c of capacityOptions) {
          if (c.value === value) {
            return c.label;
          }
        }
        return '未知能力';
      }
    },
    {
      key: 'model',
      title: '模型',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'matter',
      title: '事项',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'detail',
      title: '备注',
      align: 'center',
      minWidth: 200,
      maxWidth: 400,
      ellipsis: {
        tooltip: {
          contentClass: 'max-w-48'
        }
      }
    },
    {
      key: 'editor',
      title: '操作人',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'createtime',
      title: '时间',
      align: 'center',
      width: 200,
      render: row => row.createtime.replace('T', ' ')
    }
  ]
});

const { checkedRowKeys } = useTableOperate(data, getData);
// const {
//   handleAdd, // 新增操作
//   handleEdit, // 编辑操作
//   drawerVisible,
//   operateType,
//   editingData, // 当前正在编辑的行数据
//   checkedRowKeys, // 已勾选的行
//   onDeleted // 单条删除后回调
// } = useTableOperate<ChannelItem>(data, getData);

// 查询
async function handleSearch(params: Api.SystemManage.CreditLogSearchParams) {
  searchParams.value = params;
  // const newParams = { ...searchParams.value };
  const response = await fetchCreditLogSearch(searchParams.value);
  data.value = response.data.records; // 更新表格数据
}

watch(
  // 页码改变时更新查询的页码值
  () => searchParams.value.current,
  () => {
    getData();
  }
);

type Row = {
  id: string;
};
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <CreditLogSearch v-model:model="searchParams" :capacity-options="capacityOptions" @search="handleSearch" />
    <NCard title="积分日志" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :hidden-btn="['add']"
          :loading="loading"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="1700"
        :loading="loading"
        remote
        :row-key="(row: Row) => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
