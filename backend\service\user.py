import uuid
import random
import string
from utils.hash.md5 import md5
from models.users import User


def create_token():
    return uuid.uuid4().hex


def save_token(user: User, token):
    pass
def getRandomString(length=10):
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def getEncryptPassword(password: str, salt: str = "") -> str:
    return md5(md5(password) + salt)


def is_admin(user: User) -> bool:
    return user.group_id == 2
