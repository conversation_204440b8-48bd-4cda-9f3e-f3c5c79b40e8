from sqlalchemy import Column, Integer, String, TIMESTAMP
from sqlalchemy.dialects.mysql import TINYINT
from utils.database import Base
import datetime

class Role(Base):
    __tablename__ = "roles"
    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    roleCode = Column(String(50), nullable=False, comment='角色代码')
    roleName = Column(String(50), nullable=False, comment='角色名称')
    roleDesc = Column(String(255), nullable=True, default='', comment='角色描述')
    status = Column(TINYINT(unsigned=True), default=1, nullable=False, comment='状态（1：启用，2：禁用）')
    createBy = Column(String(50), nullable=True, default='', comment='由哪一位管理员创建')
    createTime = Column(TIMESTAMP, default=datetime.datetime.now, nullable=False, comment='创建时间')
    updateBy = Column(String(50), nullable=True, default='', comment='由哪一位管理员更新')
    updateTime = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False, comment='更新时间')
