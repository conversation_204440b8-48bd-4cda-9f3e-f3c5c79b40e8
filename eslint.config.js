const { defineConfig } = require('@soybeanjs/eslint-config');

module.exports = defineConfig(
  { vue: true, unocss: true },
  {
    rules: {
      'vue/multi-word-component-names': [
        'warn',
        {
          ignores: ['index', 'App', 'Register', '[id]', '[url]']
        }
      ],
      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        {
          registeredComponentsOnly: false,
          ignores: ['/^icon-/']
        }
      ],
      'unocss/order-attributify': 'off',
      'no-console': 'off',
      'no-bitwise': 'off',
      'no-plusplus': 'off',
      'max-params': ['error', 5]
    }
  }
);