<script setup lang="ts">
interface Tool {
  id: number;
  name: string;
  type: string;
  description: string;
  main_function: string;
  url: string;
  recommendation_rating: number;
  is_paid: boolean;
  is_available_in_china: boolean;
  origin_country: string;
  image_url: string | null;
  document: string | null;
}

const props = defineProps<{
  tool: Tool;
}>();

// 解构props
const { tool } = props;

const handleClick = () => {
  if (tool.url) {
    window.open(tool.url, '_blank', 'noopener,noreferrer');
  }
};
</script>

<template>
  <div class="cursor-pointer" @click="handleClick">
    <div class="flex flex-1 items-center gap-x-1">
      <div class="image-container flex-1">
        <NImage :src="tool.image_url || 'https://i.postimg.cc/52YJ745k/AI-2048.png'" preview-disabled />
      </div>
      <div class="flex-2 ml-2 flex flex-col items-start gap-y-2">
        <NText class="text-base font-bold">{{ tool.name }}</NText>
        <div class="flex items-center gap-x-1">
          <p class="whitespace-nowrap">官网：</p>
          <NEllipsis :line-clamp="1">
            <p class="url_style">{{ tool.url }}</p>
          </NEllipsis>
        </div>
        <div class="mt-1 flex items-center">
          <NText class="whitespace-nowrap text-base">推荐：</NText>
          <NRate :value="tool.recommendation_rating" readonly />
        </div>
        <div v-if="tool.document" class="flex items-center gap-x-1">
          <p class="whitespace-nowrap">文档：</p>
          <NButton
            text
            tag="a"
            :href="tool.document"
            type="primary"
            target="_blank"
            rel="noopener noreferrer"
            @click.stop
          >
            使用说明
          </NButton>
        </div>
      </div>
    </div>

    <NEllipsis :line-clamp="3" class="mt-2">
      {{ tool.description }}
      <template #tooltip>
        <div class="custom-tooltip">
          {{ tool.description }}
        </div>
      </template>
    </NEllipsis>
  </div>
</template>

<style scoped>
.image-container {
  max-width: 100px;
  max-height: 100px;
  min-width: 100px;
  min-height: 100px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.custom-tooltip {
  max-width: 300px;
  white-space: normal;
}

.url_style {
  max-width: 11.5em;
  word-wrap: break-word;
}

.cursor-pointer:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease-in-out;
}

:deep(.n-image img) {
  width: 100%;
  height: 100%;
}
</style>
