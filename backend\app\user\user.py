import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from utils.database import get_db
from pydantic import BaseModel
import datetime
import pydash
from sqlalchemy.sql import and_
from models.users import User,get_request_user
from models.user_role import UserRole
from models.user_credit import UserCredit, UserCreditLog
from models.roles import Role
from service.user import getEncryptPassword,getRandomString
from utils.exceptions import ClientVisibleException
from utils.hash.aes import decrypt


router = APIRouter()
logger = logging.getLogger(__name__)


class UserInfo(BaseModel):
  id: int
  group_id: int
  role_id: int
  role: list[int]=[]
  username: str
  nickname: str
  company: str
  email: str
  avatar: str
  gender: int
  status: int
  updatetime: datetime.datetime
  class Config:
    from_attributes = True


class SaveUserRequest(BaseModel):
  # id: int = 0
  # group_id: int =0
  # role_id: int =0
  # role: list[int]=[]
  # username: str =''
  nickname: str =''
  password: str =''
  oldPassword: str =''
  # company: str =''
  email: str =''
  # avatar: str =''
  gender: int =0
  # status: int =1

class UserInfoResponse(BaseModel):
  data: UserInfo
  code: str
  msg: str

class AddUserCreditRequest(BaseModel):
  user_id: int = 0
  credit: int = 0

async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()


@router.get("/get_user_info", tags=["user"])
async def get_user_info(request: Request):
  """
  获取用户数据
  """
  # return request.state.user

  # UserInfo.model_validate(request.state.user)
  return UserInfoResponse(
    data=UserInfo.model_validate(request.state.user),
    code="0000",
    msg="请求成功!"
  )


@router.post("/set_user_info", tags=["user"])
async def set_user_info(
  save_user_data: SaveUserRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
):
  """
    更新用户管理数据
  """
  try:
    async with db as session:
      result = await session.execute(select(User).where(User.id == request.state.user.id))
      user_in_db = result.scalars().first()

      if not user_in_db:
        raise ClientVisibleException("用户不存在")

      if save_user_data.password:
        try:
          # 验证原密码
          if not save_user_data.oldPassword:
            raise ClientVisibleException("请输入原密码")

          # 解密原密码
          decrypted_old_password = decrypt(save_user_data.oldPassword)
          # 验证原密码是否正确
          old_password_hash = getEncryptPassword(decrypted_old_password, user_in_db.salt)
          if old_password_hash != user_in_db.password:
            raise ClientVisibleException("原密码错误")

          # 解密新密码并更新
          decrypted_password = decrypt(save_user_data.password)
          salt:str = getRandomString(20)
          password:str = getEncryptPassword(decrypted_password, salt)
          user_in_db.password = password
          user_in_db.salt = salt

        except Exception as e:
          logger.error(f"密码处理失败: {str(e)}")
          raise ClientVisibleException("密码格式错误") from e

      # 处理其他字段更新...
      if save_user_data.nickname:
        user_in_db.nickname = save_user_data.nickname

      user_in_db.updateBy = request.state.user.username
      user_in_db.updatetime = datetime.datetime.now()

      await session.commit()
      return {"code": "0000", "msg": "提交成功"}

  except Exception as e:
    logger.error(f"Failed to update user: {e}")
    raise ClientVisibleException("提交失败") from e

@router.post("/add_user_credit", tags=["user"])
async def add_user_credit(
  credit_data: AddUserCreditRequest,
  request: Request,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
    增加用户积分
  """
  ip = pydash.get(request.state,"client_ip",'')
  try:
    #  权限验证，是否有权限修改用户信息
    process_user=user.id
    async with db as session:
      result = await session.execute(
        select(Role.roleCode)
        .select_from(UserRole)
        .join(Role, UserRole.role_id == Role.id)
        .where(UserRole.user_id == process_user)
      )
      roles = result.scalars().all()
      if 'super_admin' in roles:
        result = await session.execute(
          select(UserCredit).where(and_(UserCredit.user_id == credit_data.user_id))
        )
        user_credit_record = result.scalars().first()
        if not user_credit_record:
          raise ClientVisibleException("用户不存在")
        else:
          currency_credit =  user_credit_record.credit
          user_credit_record.credit = user_credit_record.credit + credit_data.credit
          newLog = UserCreditLog(
                    user_id              = credit_data.user_id,
                    credit               = credit_data.credit,
                    after_credit         = currency_credit + credit_data.credit,
                    matter               = '系统赠送',
                    editor               = user.username or '',
                    ip                   = ip,
                    createtime           = datetime.datetime.now()
                  )
          session.add(newLog)
          await session.commit()
          return JSONResponse(content={"data": '成功', "code": "0000", "msg": "Success"})
      else:
        return JSONResponse(content={"data": {}, "code": "0000", "msg": "无权操作"})

  except Exception as e:
      logger.error(f"Failed to update user: {e}")
      raise ClientVisibleException("提交失败") from e

@router.post("/add_new_user_credit", tags=["user"])
async def add_user_credit(
  credit_data: AddUserCreditRequest,
  request: Request,
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db),
):
  """
    在表user_credit 没用户，增加用户和积分
  """
  ip = pydash.get(request.state,"client_ip",'')
  try:
    #  权限验证，是否有权限修改用户信息
    process_user=user.id
    async with db as session:
      result = await session.execute(
        select(Role.roleCode)
        .select_from(UserRole)
        .join(Role, UserRole.role_id == Role.id)
        .where(UserRole.user_id == process_user)
      )
      roles = result.scalars().all()
      if 'super_admin' in roles:
        result = await session.execute(
          select(UserCredit).where(and_(UserCredit.user_id == credit_data.user_id))
        )
        user_credit_record = result.scalars().first()

        resultuid = await session.execute(
          select(User).where(User.id == credit_data.user_id)
        )
        user_record = resultuid.scalars().first()

        if user_credit_record:
          raise ClientVisibleException("用户已存在")
        elif not user_record:
          raise ClientVisibleException("用户Userid不存在,请先注册")
        else:
          newUserCredit = UserCredit(
              user_id         = credit_data.user_id,
              credit          = credit_data.credit,
              updatetime      = datetime.datetime.now()
          )
          newLog = UserCreditLog(
                    user_id              = credit_data.user_id,
                    credit               = credit_data.credit,
                    after_credit         = credit_data.credit,
                    matter               = '新用户赠送',
                    editor               = user.username or '',
                    ip                   = ip,
                    createtime           = datetime.datetime.now()
                  )
          session.add(newLog)
          session.add(newUserCredit)
          await session.commit()
          return JSONResponse(content={"data": '成功', "code": "0000", "msg": "Success"})
      else:
        return JSONResponse(content={"data": {}, "code": "0000", "msg": "无权操作"})

  except Exception as e:
      logger.error(f"Failed to add user_credit: {e}")
      raise ClientVisibleException("提交失败") from e
