import logging
from collections.abc import Sequence
from typing import Annotated

from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field, AfterValidator
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from models.model import Model
from models.model_capacity import Capacity
from models.users import get_roles_checker
from utils.database import get_db
from service.file import save_image_b64data
from service.ai_model import remove_available_model
from config import app_settings
from utils.exceptions import ClientVisibleException

logger = logging.getLogger(__name__)

router = APIRouter(dependencies=[Depends(get_roles_checker('super_admin'))])
TENCENT_COS_HOST = app_settings.tencent_cos_host


class ModelItem(BaseModel):
    id: int
    name: str
    label: str
    icon: str
    description: str
    publishers: str
    capacity: Capacity
    status: bool


class ModelsPagination(BaseModel):
    records: list[ModelItem]
    size: int
    current: int
    total: int


class ModelsRes(BaseModel):
    code: str = '0000'
    msg: str = ''
    data: ModelsPagination | None = None


class ModelRes(BaseModel):
    code: str = '0000'
    msg: str = ''
    data: ModelItem | None = None


@router.get('', response_model=ModelsRes)
async def get_models(
        db: AsyncSession = Depends(get_db),
        size: int = Query(10, ge=1),
        current: int = Query(1, ge=1),
):
    res = ModelsRes()
    async with db as session:
        data_stmt = select(Model).order_by(Model.status.desc(), Model.create_time.desc()).limit(size).offset(
            (current - 1) * size)
        cnt_stmt = select(func.count()).select_from(Model)
        cnt_res = await session.execute(cnt_stmt)
        data_res = await session.execute(data_stmt)
        cnt = cnt_res.scalar()
        res.data = ModelsPagination(
            records=[],
            size=size,
            current=current,
            total=cnt,
        )
        models: Sequence[Model] = data_res.scalars().all()
        for model in models:
            res.data.records.append(ModelItem(
                id=model.id,
                name=model.name,
                label=model.label,
                icon=model.icon,
                description=model.description,
                publishers=model.publishers,
                capacity=Capacity(model.capacity),
                status=model.status
            ))
    return res


@router.get('/{model_id}', response_model=ModelRes)
async def get_model(
        model_id: int,
        db: AsyncSession = Depends(get_db)
):
    async with db as session:
        stmt = select(Model).filter(Model.id == model_id)
        result = await session.execute(stmt)
        model: Model | None = result.scalars().first()
        if model is None:
            logger.info(f"Model {model_id} not found")
            raise ClientVisibleException("模型不存在")
        else:
            res = ModelRes()
            res.data = ModelItem(
                id=model.id,
                name=model.name,
                label=model.label,
                icon=model.icon,
                description=model.description,
                publishers=model.publishers,
                capacity=Capacity(model.capacity),
                status=model.status
            )
    return res


def icon_validator(data: str):
    if data.startswith('http'):
        if not data.startswith(TENCENT_COS_HOST):
            raise ValueError('Invalid image url')
        return data
    if not data.startswith('data:image/') or ';base64,' not in data:
        raise ValueError('Invalid base64 string')
    return data.split(',', 1)[1]  # 只取逗号后面的 Payload 部分


class ModelCreateReq(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    label: str = Field(max_length=256)
    icon: Annotated[str, Field(min_length=1), AfterValidator(icon_validator)]
    description: str
    publishers: str = Field(min_length=1, max_length=64)
    capacity: Capacity
    status: bool


def process_icon(name: str, icon: str, db: AsyncSession):
    if icon.startswith('http'):
        return icon
    else:
        try:
            uploaded_image_url = save_image_b64data(
                icon,
                f"{name}_{icon[:10]}.png"
            )
            return uploaded_image_url
        except Exception as e:
            logger.error(f"Failed to upload image: {e}")
            raise ClientVisibleException("上传图片失败") from e


@router.post('', response_model=ModelRes)
async def create_model(
        payload: ModelCreateReq,
        db: AsyncSession = Depends(get_db)
):
    res = ModelRes()
    icon_url = process_icon(payload.name, payload.icon, db)

    async with db as session:
        model = Model(
            name=payload.name,
            label=payload.label,
            icon=icon_url,
            description=payload.description,
            publishers=payload.publishers,
            capacity=payload.capacity,
            status=payload.status
        )
        session.begin()
        session.add(model)
        await session.commit()
        res.data = ModelItem(
            id=model.id,
            name=model.name,
            label=model.label,
            icon=model.icon,
            description=model.description,
            publishers=model.publishers,
            capacity=Capacity(model.capacity),
            status=model.status
        )
    return res


class ModelUpdateReq(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    label: str = Field(max_length=256)
    icon: Annotated[str, Field(min_length=1), AfterValidator(icon_validator)]
    description: str
    publishers: str = Field(min_length=1, max_length=64)
    capacity: Capacity
    status: bool


@router.put('/{model_id}', response_model=ModelRes)
async def update_model(
        model_id: int,
        payload: ModelCreateReq,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db)
):
    icon_url = process_icon(payload.name, payload.icon, db)
    async with db as session:
        stmt = select(Model).filter(Model.id == model_id)
        result = await session.execute(stmt)
        model: Model | None = result.scalars().first()
        if model is None:
            logger.info(f"Model {model_id} not found")
            raise ClientVisibleException("模型不存在")
        else:
            res = ModelRes()
            model.name = payload.name
            model.label = payload.label
            model.icon = icon_url
            model.description = payload.description
            model.publishers = payload.publishers
            model.capacity = payload.capacity
            model.status = payload.status
            await session.commit()
            res.data = ModelItem(
                id=model.id,
                name=model.name,
                label=model.label,
                icon=model.icon,
                description=model.description,
                publishers=model.publishers,
                capacity=Capacity(model.capacity),
                status=model.status
            )
            if not model.status:
                background_tasks.add_task(remove_available_model, db, model_id)
        return res


@router.delete('/{model_id}', response_model=ModelRes)
async def delete_model(
        model_id: int,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db)
):
    async with db as session:
        stmt = select(Model).filter(Model.id == model_id)
        result = await session.execute(stmt)
        model: Model | None = result.scalars().first()
        if model is None:
            logger.info(f"Model {model_id} not found")
            raise ClientVisibleException("模型不存在")
        else:
            res = ModelRes()
            await session.delete(model)
            await session.commit()
            background_tasks.add_task(remove_available_model, db, model_id)
        return res
