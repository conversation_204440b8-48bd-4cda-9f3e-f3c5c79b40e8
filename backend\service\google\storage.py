from google.cloud import storage
import os
from io import BytesIO

from config import app_settings


def get_bucket():
    storage_client = storage.Client()
    return storage_client.bucket(app_settings.google_storage_bucket)


def upload_blob(
    destination_blob_name, source_file_name: str = None, source_file_data: str = None
) -> str:
    """Uploads a file to the bucket."""
    # The ID of your GCS bucket
    # bucket_name = "your-bucket-name"
    # The path to your file to upload
    # source_file_name = "local/path/to/file"
    # The ID of your GCS object
    # destination_blob_name = "storage-object-name"

    if source_file_name == None and source_file_data == None:
        raise Exception("source_file_name or source_file_data must be set")

    if source_file_name != None and os.path.exists(source_file_name) == False:
        raise Exception("source_file_name not exists")
    bucket = get_bucket()
    blob = bucket.blob(destination_blob_name)

    if blob.exists():
        return blob.uri
    # Optional: set a generation-match precondition to avoid potential race conditions
    # and data corruptions. The request to upload is aborted if the object's
    # generation number does not match your precondition. For a destination
    # object that does not yet exist, set the if_generation_match precondition to 0.
    # If the destination object already exists in your bucket, set instead a
    # generation-match precondition using its generation number.
    generation_match_precondition = 0

    blob.upload_from_filename(
        source_file_name, if_generation_match=generation_match_precondition
    )

    return blob.uri


def create_gsuri(uri: str) -> str:
    bucket_name = app_settings.google_storage_bucket

    return f"gs://{bucket_name}/{uri.lstrip('/')}"


def download_blob_stream(blob_uri: str):

    blob_uri = blob_uri.lstrip("/")
    bucket = get_bucket()
    blob = bucket.blob(blob_uri)

    def iter_file():
        file_obj = BytesIO()
        file_obj.seek(0)
        yield from file_obj
        if blob.exists() == True:
            blob.download_to_file(file_obj)

    return iter_file
