from datetime import datetime

from sqlalchemy import Column, String, BOOLEAN, DateTime
from sqlalchemy.dialects.mysql import INTEGER, TINYINT

from utils.database import Base


class Model(Base):
    __tablename__ = "ai_models"

    id = Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True, comment='模型 ID')
    name  = Column('model_name', String(128), unique=True, nullable=False, comment='模型名称，用于唯一标识一个模型')
    label = Column(String(256), nullable=False, default='', comment='模型标签，用于前端展示')
    icon = Column(String(256), nullable=False, comment='模型图标')
    description = Column('model_description', String(2048), nullable=False, default='', comment='模型描述')
    publishers = Column(String(64), nullable=False, comment='模型厂商')
    capacity = Column(TINYINT, nullable=False, default=0, comment='模型能力，比特位标记')
    status = Column(BOOLEAN, nullable=False, default=False, comment='模型状态')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    def __repr__(self):
      return f"<Model(id={self.id}, name='{self.name}')>"
