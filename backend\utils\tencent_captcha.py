import asyncio
import json
import logging

from tencentcloud.captcha.v20190722.captcha_client import Captcha<PERSON><PERSON>
from tencentcloud.captcha.v20190722.models import DescribeCaptchaResultRequest, DescribeCaptchaResultResponse
from tencentcloud.common.exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile

from config import app_settings

logger = logging.getLogger(__name__)


http_profile = HttpProfile(endpoint="captcha.tencentcloudapi.com")
client_profile = ClientProfile(httpProfile=http_profile)


async def verify_tencent_captcha(ip: str, randstr: str, ticket: str) -> bool:
    client = CaptchaClient(app_settings.tencent_cloud_credential, "", client_profile)
    req = DescribeCaptchaResultRequest()
    req.from_json_string(json.dumps({
        "CaptchaType": 9,
        "Ticket": ticket,
        "UserIp": ip,
        "Randstr": randstr,
        "CaptchaAppId": app_settings.tencent_captcha_appid,
        "AppSecretKey": app_settings.tencent_captcha_secretkey,
    }))
    try:
        res: DescribeCaptchaResultResponse = await asyncio.to_thread(client.DescribeCaptchaResult, req)
        match res.CaptchaCode:
            case 1:
                logger.info(f"验证码校验通过，ip: {ip}")
                return True
            case 7:
                logger.info(f"7 captcha no match 传入的 Randstr 不合法: {randstr}")
            case 8:
                logger.info("8 ticket expired 传入的 Ticket 已过期（Ticket 有效期5分钟）")
            case 9:
                logger.info(f"9 ticket reused 传入的 Ticket 被重复使用")
            case 15:
                logger.info(f"15 decrypt fail 传入的 Ticket 不合法")
            case 16:
                logger.info(f"16 appid-ticket mismatch 传入的 CaptchaAppId 错误: {app_settings.tencent_captcha_appid}")
            case 21:
                logger.info("21 diff 票据校验异常")
            case 100:
                logger.info("appid-secretkey-ticket mismatch 参数校验错误")
        return False
    except TencentCloudSDKException as e:
        logger.error(f"Tencent Cloud Captcha SDK Exception: {e}")
        return False
