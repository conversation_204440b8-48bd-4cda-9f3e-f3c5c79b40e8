<script setup lang="ts">
import { ref } from 'vue';
import FilterPanel from './modules/filter-panel.vue';
import StatisticsOverview from './modules/statistics-overview.vue';
import AssetsTable from './modules/assets-table.vue';

// 定义StatisticsOverview组件的类型
interface StatisticsOverviewInstance {
  updateStatistics: (params: any) => void;
}

// 定义AssetsTable组件的类型
interface AssetsTableInstance {
  updateTable: (params: any) => void;
}

// 获取组件实例
const statisticsRef = ref<StatisticsOverviewInstance | null>(null);
const assetsTableRef = ref<AssetsTableInstance | null>(null);

// 处理搜索事件
function handleSearch(params: any) {
  // 更新统计数据
  if (statisticsRef.value) {
    statisticsRef.value.updateStatistics(params);
  }

  // 更新表格数据
  if (assetsTableRef.value) {
    assetsTableRef.value.updateTable(params);
  }
}
</script>

<template>
  <div class="flex flex-col gap-4">
    <FilterPanel @search="handleSearch" />

    <StatisticsOverview ref="statisticsRef" />

    <AssetsTable ref="assetsTableRef" />
  </div>
</template>

<style scoped lang="scss"></style>
