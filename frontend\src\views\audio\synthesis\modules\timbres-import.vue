<script lang="ts" setup>
import { computed, ref, useAttrs } from 'vue';
import TimbresOperate from '@/views/audio/timbres/modules/timbres-operate.vue';
import type { SaveTone } from '@/service/api/audio';

defineOptions({
  name: 'TimbresImport',
  inheritAttrs: false
});

// Props接口定义
interface Props {
  buttonText?: string;
  buttonClass?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  buttonText: '导入音色',
  buttonClass: 'ml-5 h-10 flex-1',
  disabled: false
});

// Emits接口定义
interface Emits {
  (e: 'timbre-imported', toneData: SaveTone): void;
}

const emit = defineEmits<Emits>();

// 获取传递的属性
const attrs = useAttrs();

// 计算最终的 class
const finalButtonClass = computed(() => {
  const classes = [props.buttonClass];
  if (attrs.class) {
    classes.push(String(attrs.class));
  }
  return classes.join(' ');
});

// 状态管理
const showTimbresOperate = ref(false);

// 打开音色导入弹窗
const handleImportTimbre = () => {
  showTimbresOperate.value = true;
};

// 处理音色导入成功
const handleTimbreSubmitted = (toneData: SaveTone) => {
  showTimbresOperate.value = false;

  // 通过emit向外传递导入成功的音色数据
  emit('timbre-imported', toneData);
};
</script>

<template>
  <NButton :class="finalButtonClass" :disabled="props.disabled" @click="handleImportTimbre">
    <NText class="text-base">{{ props.buttonText }}</NText>
  </NButton>

  <!-- 音色导入弹窗 -->
  <TimbresOperate v-model:visible="showTimbresOperate" @submitted="handleTimbreSubmitted" />
</template>
