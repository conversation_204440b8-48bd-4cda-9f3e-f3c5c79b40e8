<script setup lang="ts">
import { getCurrentInstance, onBeforeUnmount, onMounted, ref } from 'vue';
import * as echarts from 'echarts';
import { NBadge, NButton, useMessage } from 'naive-ui';
import { debounce } from 'lodash';
import { getMemory, getSdStatus, opensd, stopSdService } from '@/service/api/sdwebui';
import { DEFAULT_COOKIE_OPTIONS } from '@/utils/cookie';

// 定义接口
interface ServerInfo {
  stu: number; // 服务状态：1、2、3等
  user: string; // 用户名称
  freetime: number; // 空闲时间
}

interface SdStatusResponse {
  [key: string]: ServerInfo; // 键为服务名称，值为服务信息
}

interface CardData {
  name: string;
  stu: number;
  user: string;
  freetime: number;
}

const serverPorts: Record<string, number> = {
  '#1': 7903,
  '#2': 7904,
  '#3': 7901,
  '#4': 7902
};

const url = ref('');
const message = useMessage();
const cardData = ref<CardData[]>([]);

const chartInstance = ref<echarts.ECharts | null>(null);
const memoryData = ref<number[]>([]);
const totalMemory = ref<number>(0);
const freeMemoryData = ref<number[]>([]);
const intervalId = ref<ReturnType<typeof setInterval> | null>(null);

const active = ref<Record<string, boolean>>({
  '#1': false,
  '#2': false,
  '#3': false,
  '#4': false
});

const loading = ref<Record<string, boolean>>({
  '#1': false,
  '#2': false,
  '#3': false,
  '#4': false
});

//  Cookie 操作函数
const setCookie = (name: string, value: string, options: any = {}) => {
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
  if (options.path) {
    cookieString += `; path=${options.path}`;
  }
  if (options.domain) {
    cookieString += `; domain=${options.domain}`;
  }
  if (options.expires) {
    cookieString += `; expires=${options.expires.toUTCString()}`;
  }
  document.cookie = cookieString;
};

const getCookie = (name: string): string | null => {
  const nameEQ = `${encodeURIComponent(name)}=`;
  const ca = document.cookie.split(';');
  for (let c of ca) {
    c = c.trim();
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length));
    }
  }
  return null;
};

const deleteCookie = (name: string, options: any = {}) => {
  setCookie(name, '', { ...options, expires: new Date(0) });
};

const fetchServerStatus = async () => {
  try {
    const response = await getSdStatus();
    const data: SdStatusResponse = response.data;
    cardData.value = Object.entries(data).map(([__name, info], index) => ({
      name: `#${index + 1}`, // 修改服务名称为 #1, #2, ...
      stu: info.stu,
      user: info.user || '未知用户',
      freetime: info.freetime
    }));

    // 初始化 active，只有服务状态空闲（stu == 2）时为 true
    cardData.value.forEach(item => {
      active.value[item.name] = item.stu === 2;
    });

    // 更新 loading 状态为 false
    Object.keys(loading.value).forEach(key => {
      loading.value[key] = false;
    });

    // 关联 Cookie 中的数据
    const sessionStu = getCookie('stu');
    const sessionServiceName = getCookie('serviceName');
    if (sessionServiceName && sessionStu) {
      const currentService = cardData.value.find(item => item.name === sessionServiceName);
      if (currentService && currentService.stu !== 2) {
        const cookieOptions = { path: '/', domain: '.gdsre.cn' };
        deleteCookie('url', cookieOptions);
        deleteCookie('stu', cookieOptions);
        deleteCookie('serviceName', cookieOptions);
      }
    }
  } catch (error) {
    message.error('获取服务器状态失败');
  }
};

const { proxy } = getCurrentInstance()!;
const $refs = proxy!.$refs;

// 启动服务，防抖处理函数
const debouncedHandleUpdateValue = debounce(async (value: boolean, name: string) => {
  const originalName = `server${name.replace('#', '')}`;
  loading.value[originalName] = true;
  if (value) {
    try {
      const memoryResponse = await getMemory();
      const freeMemory = memoryResponse.data.free_memory;
      if (freeMemory < 4) {
        message.info('可用显存少于4G，请稍后尝试');
        loading.value[name] = false;
        return;
      }

      const port = serverPorts[name as keyof typeof serverPorts];
      const response: any = await opensd(port);
      url.value = response.data.redirect_url;
      const status = response.data.stu;
      const user = response.data.user;

      message.success(`${name} 启动成功`);

      // 使用 Cookie 存储数据
      const cookieOptions = { path: '/', domain: '.gdsre.cn' };
      setCookie('url', url.value, cookieOptions);
      setCookie('stu', status.toString(), cookieOptions);
      setCookie('serviceName', name, cookieOptions);

      // window.open(`http://ai-admin.gdsre.cn/stablediffusion`, '_blank');
      window.open(url.value, '_blank');

      const server = cardData.value.find(item => item.name === name);
      if (server) {
        server.stu = status;
        server.user = user;
        server.freetime = 0;
      }

      active.value[name] = true;
    } catch (error) {
      // message.error(`${name} 启动失败`);
    } finally {
      loading.value[name] = false;
    }
  } else {
    // 当 NSwitch 为 true 时，点击会触发 n-popconfirm，此处不需要处理
    loading.value[name] = false;
  }
}, 100);

const handleUpdateValue = (value: boolean, name: string) => {
  if (!value) {
    const refName = `popconfirm-${name}`;
    const popconfirm = $refs[refName] as any;
    if (popconfirm && popconfirm.show) {
      popconfirm.show();
    }
  } else {
    // 当 NSwitch 打开时，调用启动服务的逻辑
    // message.info('正在启动服务，请稍后...');
    debouncedHandleUpdateValue(value, name);
  }
};

const initChart = () => {
  const chartDom = document.getElementById('memory-chart');
  if (!chartDom) return;

  chartInstance.value = echarts.init(chartDom);
  chartInstance.value.setOption({
    title: {
      text: '显存使用情况',
      left: 'center'
    },
    legend: {
      top: '95%'
    },
    tooltip: {
      trigger: 'axis',
      showContent: false
    },
    dataset: {
      source: [
        ['Metric', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
        ['已用显存', ...memoryData.value],
        ['可用显存', ...freeMemoryData.value]
      ]
    },
    xAxis: {
      type: 'category',
      gridIndex: 0
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 24,
      interval: 4,
      gridIndex: 0
    },
    grid: {
      left: '55%',
      right: '5%',
      top: '10%',
      bottom: '10%'
    },
    series: [
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
        xAxisIndex: 0,
        yAxisIndex: 0
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' },
        xAxisIndex: 0,
        yAxisIndex: 0
      },
      {
        type: 'pie',
        id: 'pie',
        radius: '55%',
        center: ['25%', '50%'],
        emphasis: {
          focus: 'self'
        },
        label: {
          formatter: '{b}: {@1} ({d}%)'
        },
        encode: {
          itemName: 'Metric',
          value: '1',
          tooltip: '1'
        }
      }
    ]
  });
};

const updateChart = async () => {
  try {
    const response = await getMemory();
    const memoryInfo = response.data;

    totalMemory.value = memoryInfo.total_memory;
    memoryData.value.push(memoryInfo.used_memory);
    freeMemoryData.value.push(memoryInfo.free_memory);

    if (memoryData.value.length > 10) {
      memoryData.value.shift();
      freeMemoryData.value.shift();
    }

    if (chartInstance.value) {
      chartInstance.value.setOption({
        dataset: {
          source: [
            ['Metric', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            ['已用显存', ...memoryData.value],
            ['可用显存', ...freeMemoryData.value]
          ]
        }
      });
    }
  } catch (error) {
    message.error('获取内存数据失败');
  }
};

const getSwitchDisabled = (server: CardData) => {
  const serverIndex = Number.parseInt(server.name.replace('#', ''), 10);
  if (serverIndex === 3 || serverIndex === 4) {
    // 检查服务1和服务2的NSwitch是否均为true（stu === 2表示正在使用）
    const server1Running = cardData.value.some(item => item.name === '#1' && item.stu === 2);
    const server2Running = cardData.value.some(item => item.name === '#2' && item.stu === 2);
    // 当服务1和服务2均运行时，才返回false（即不禁用），否则返回true（禁用）
    return !(server1Running && server2Running);
  }
  // 对于服务1和服务2，当状态不是1（空闲）时禁用NSwitch
  return server.stu !== 1;
};

onMounted(() => {
  fetchServerStatus();
  initChart();
  updateChart();
  intervalId.value = setInterval(updateChart, 2000);
  window.setInterval(() => fetchServerStatus(), 30000);
});

onBeforeUnmount(() => {
  if (intervalId.value) clearInterval(intervalId.value);
  if (chartInstance.value) chartInstance.value.dispose();
});

const handleStopService = async (name: string) => {
  loading.value[name] = true;
  try {
    const port = serverPorts[name as keyof typeof serverPorts];
    await stopSdService(port);

    // 更新服务器状态
    const server = cardData.value.find(item => item.name === name);
    if (server) {
      server.stu = 1; // 停止后状态设为 1
      server.user = '未知用户';
      server.freetime = 0;
    }

    // 删除对应的 Cookie 数据
    deleteCookie('url', DEFAULT_COOKIE_OPTIONS);
    deleteCookie('stu', DEFAULT_COOKIE_OPTIONS);
    deleteCookie('serviceName', DEFAULT_COOKIE_OPTIONS);

    active.value[name] = false;
    // message.success(`${name} 已停止服务`);
  } catch (error) {
    // message.error(`${name} 停止服务失败`);
  } finally {
    loading.value[name] = false;
  }
};

const openStablediffusion = () => {
  // window.open('http://ai-admin.gdsre.cn/stablediffusion', '_blank');
  if (getCookie('url')) {
    url.value = getCookie('url')!;
    window.open(url.value, '_blank');
  } else {
    message.info('请先启动服务');
  }
};
</script>

<template>
  <main class="container">
    <NGrid class="mt-2 w-full" x-gap="12" :cols="4">
      <NGi v-for="server in cardData" :key="server.name">
        <NCard class="ServerCard h-45 flex">
          <NSpace justify="space-between">
            <div>
              <!-- 服务名称 -->
              <NText class="mr-1 cursor-pointer text-4.5" @click.prevent="openStablediffusion">{{ server.name }}</NText>
              <!-- 状态标记 -->
              <NBadge
                :dot="true"
                :type="server.stu === 1 ? 'success' : server.stu === 2 ? 'warning' : 'error'"
                :color="(server.name === '#3' || server.name === '#4') && server.stu === 1 ? 'grey' : undefined"
                class="pb-4 text-3"
              />
            </div>

            <!-- 当 NSwitch 为 true 时，添加 n-popconfirm -->
            <NPopconfirm
              v-if="active[server.name]"
              :ref="'popconfirm-' + server.name"
              @positive-click="() => handleStopService(server.name)"
            >
              <template #trigger>
                <NSwitch
                  :rubber-band="false"
                  :value="active[server.name]"
                  :loading="loading[server.name]"
                  size="large"
                  @update:value="value => handleUpdateValue(value, server.name)"
                />
              </template>
              确定停止服务？
            </NPopconfirm>

            <!-- 当 NSwitch 为 false 时，正常显示 -->
            <div v-else>
              <NTooltip trigger="hover">
                <template #trigger>
                  <NSwitch
                    :rubber-band="false"
                    :value="active[server.name]"
                    :loading="loading[server.name]"
                    size="large"
                    :disabled="getSwitchDisabled(server)"
                    @update:value="value => debouncedHandleUpdateValue(value, server.name)"
                  />
                </template>
                启动服务成功后可通过点击"{{ server.name }}"跳转到服务页面
              </NTooltip>
            </div>
          </NSpace>

          <NSpace class="mt-5 w-full" justify="space-between">
            <NTooltip trigger="hover">
              <template #trigger>
                <NButton text><SvgIcon icon="ri:time-line" class="text-6"></SvgIcon></NButton>
              </template>
              当前使用情况，空闲时间10分钟自动结束服务
            </NTooltip>
            <!-- 创作中 / 闲置 -->
            <div class="w-20 flex justify-end">
              <NText class="self-center text-3.5">
                {{ server.stu === 2 && server.freetime === 0 ? '创作中' : '闲置' }}
              </NText>
            </div>
          </NSpace>

          <NSpace class="mt-5 w-full" justify="space-between" align="center">
            <NTooltip trigger="hover">
              <template #trigger>
                <NButton text><SvgIcon icon="mdi:user" class="text-6"></SvgIcon></NButton>
              </template>
              当前使用服务的用户
            </NTooltip>
            <!-- 用户名称 -->
            <div class="w-25 flex justify-end">
              <NText class="text-3.5">{{ server.user }}</NText>
            </div>
          </NSpace>
        </NCard>
      </NGi>
    </NGrid>

    <NCard id="memory-chart" class="memory-chart"></NCard>
  </main>
</template>

<style scoped>
.container {
  width: 85%;
  margin: 0 auto;
}

#memory-chart {
  margin-top: 2em;
  width: 100%;
}

.memory-chart {
  width: 100%;
  height: 400px;
  margin-top: 2em;
  border-radius: 8px;
}
</style>
