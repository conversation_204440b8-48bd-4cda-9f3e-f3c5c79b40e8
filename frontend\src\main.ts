import { createApp } from 'vue';
import './plugins/assets';
// import loading from '@/directives/loading';
import directives from '@/directives';
import { setupAppVersionNotification, setupDayjs, setupIconifyOffline, setupLoading, setupNProgress } from './plugins';
import { setupStore } from './store';
import { setupRouter } from './router';
import { setupI18n } from './locales';
// import { startReportTask } from './utils/timepiece';
import App from './App.vue';
async function setupApp() {
  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);

  setupStore(app);

  await setupRouter(app);

  setupI18n(app);

  setupAppVersionNotification();
  app.use(directives);
  // startReportTask(15000);
  app.mount('#app');
}

setupApp();
