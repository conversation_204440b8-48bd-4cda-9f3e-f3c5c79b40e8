<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { NInput } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import type { AddPresetsPayload } from '@/service/api/text';

defineOptions({
  name: 'PresetEditModal'
});

interface Props {
  /** the type of operation */
  operateType: 'add' | 'edit';
  /** the edit row data */
  rowData?: any | null;
  gameCode: string;
  onUploadPresets: (parms: AddPresetsPayload) => Promise<void>;
}

const props = defineProps<Props>();

// type SubmitModel = {
//   gamecode: string;
//   en: string;
//   zh: string;
// };

// interface Emits {
//   (e: 'submitted', model: SubmitModel): void;
// }

// const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  return props.operateType === 'add' ? '新增预设' : '编辑预设';
});

type Model = {
  gamecode: string;
  en: string;
  zh: string;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    gamecode: props.gameCode,
    en: '',
    zh: ''
  };
}

type RuleKey = 'gamecode' | 'en' | 'zh';

const rules: Record<RuleKey, any> = {
  gamecode: defaultRequiredRule,
  en: defaultRequiredRule,
  zh: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  // console.log('Model after assigning default values:', props.rowData);

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
    // console.log('Model after assigning rowData:', model);
  }
}

function closeModal() {
  visible.value = false;
}

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});

async function handleSubmit() {
  try {
    const parms: AddPresetsPayload = {
      gamecode: model.gamecode,
      zh: model.zh,
      en: model.en
    };

    if (props.operateType === 'add') {
      await props.onUploadPresets(parms);
    } else if (props.operateType === 'edit') {
      const updatedData = { ...model, id: props.rowData.id };
      props.onUploadPresets(updatedData);
    }
  } catch (error) {
  } finally {
    visible.value = false;
  }
}
</script>

<template>
  <NModal v-model:show="visible">
    <NCard :title="title" :bordered="false" size="huge" role="dialog" aria-modal="true" class="centered-modal">
      <template #default>
        <NForm ref="formRef" :model="model" :rules="rules">
          <!--
          <NFormItem label="游戏编号" path="gamecode">
            <NInput v-model:value="model.gamecode" disabled />
          </NFormItem>
          -->
          <NFormItem label="中文" path="zh">
            <NInput v-model:value="model.zh" type="textarea" placeholder="请输入原文" show-count :maxlength="50" />
          </NFormItem>
          <NFormItem label="英文" path="en">
            <NInput v-model:value="model.en" type="textarea" placeholder="请请输入翻译" show-count :maxlength="50" />
          </NFormItem>
        </NForm>
      </template>

      <template #footer>
        <NSpace justify="end">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="handleSubmit">{{ props.operateType === 'add' ? '确认' : '保存' }}</NButton>
        </NSpace>
      </template>
    </NCard>
  </NModal>
</template>

<style scoped>
.buttonStyle {
  margin-bottom: 1em;
}

@media (min-width: 700px) {
  .centered-modal {
    position: fixed;
    right: 17%;
    bottom: 15%;
  }
}

@media (min-width: 1460px) {
  .centered-modal {
    position: fixed;
    right: 8%;
    bottom: 19%;
  }
}
</style>
