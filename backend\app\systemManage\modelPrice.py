import logging

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from utils.database import get_db
from models.model_price import ModelPrice
from pydantic import BaseModel
from typing import List, Optional
import datetime
from sqlalchemy.sql import and_
from models.users import User, get_request_user, get_roles_checker
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


class ModelPriceOut(BaseModel):
    id: int
    capacity: str
    model: Optional[str]
    api_type: int
    unit: int
    credit: int
    updatetime: datetime.datetime
    editor: Optional[str]

    class Config:
        from_attributes = True


class PaginatedData(BaseModel):
    records: List[ModelPriceOut]
    current: int
    size: int
    total: int


class PaginatedResponse(BaseModel):
    data: PaginatedData
    code: str


class ModelPriceItemResponse(BaseModel):
    data: ModelPriceOut | None
    code: str


class DeleteModelPriceRequest(BaseModel):
    id: int


class AddModelPriceRequest(BaseModel):
    capacity: str
    model: Optional[str] = ""
    api_type: int
    unit: Optional[int] = 1
    credit: int


async def get_total_count(session: AsyncSession, model):
    result = await session.execute(select(func.count(model.id)))
    return result.scalar()


@router.get(
    "/all",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def get_all_model_price_config(
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      获取模型积分管理数据
      """
    async with db as session:
        # 获取分页数据
        result = await session.execute(
            select(ModelPrice)
            .offset((page - 1) * size)
            .limit(size)
        )
        records = result.scalars().all()

        # 获取总数
        total = await get_total_count(session, ModelPrice)

        records_out = [ModelPriceOut.model_validate(record) for record in records]

        return PaginatedResponse(
            data=PaginatedData(
                records=records_out,
                current=page,
                size=size,
                total=total
            ),
            code="0000"
        )


@router.get(
    "/search",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def search_modelprice_management(
        capacity: Optional[str] = Query(None),
        model: Optional[str] = Query(None),
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      查询模型积分数据
      """
    # 构建查询语句
    query = select(ModelPrice)
    if capacity:
        query = query.where(ModelPrice.capacity.like(f'%{capacity}%'))
    if model:
        query = query.where(ModelPrice.model.like(f'%{model}%'))

    async with db as session:
        # 获取分页数据
        result = await session.execute(query.offset((page - 1) * size).limit(size))
        records = result.scalars().all()

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar()

        records_out = [ModelPriceOut.model_validate(record) for record in records]

        return PaginatedResponse(
            data=PaginatedData(
                records=records_out,
                current=page,
                size=size,
                total=total
            ),
            code="0000"
        )


@router.get("/item", response_model=ModelPriceItemResponse, tags=["manage"])
async def get_model_price(
        capacity: str = Query(''),
        model: str = Query(''),
        db: AsyncSession = Depends(get_db)
):
    """
    获取模型积分数据
    """
    # 构建查询语句
    query = select(ModelPrice).where(ModelPrice.capacity == capacity).where(ModelPrice.model == model)

    async with db as session:
        # 获取分页数据
        result = await session.execute(query)
        record = result.scalars().one_or_none()
        if record is None:
            return ModelPriceItemResponse(
                data=None,
                code="0000"
            )

        return ModelPriceItemResponse(
            data=ModelPriceOut.model_validate(record),
            code="0000"
        )


@router.post(
    "/delete",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def delete_game_management(
        request: DeleteModelPriceRequest,
        page: int = Query(1, alias="current"),
        size: int = Query(10),
        db: AsyncSession = Depends(get_db)
):
    """
      删除模型积分数据
      """
    async with db as session:
        # 查找要删除的数据
        result = await session.execute(select(ModelPrice).where(ModelPrice.id == request.id))
        record = result.scalars().first()

        if not record:
            raise ClientVisibleException("删除失败")

        # 删除数据
        await session.delete(record)
        await session.commit()

        return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})


@router.post(
    "/add",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def add_model_price_management(
        add_data: AddModelPriceRequest,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user)
):
    """
    添加新的模型积分数据
    """
    try:
        async with db as session:
            result = await session.execute(
                select(ModelPrice).filter(
                    and_(
                        ModelPrice.capacity == add_data.capacity,
                        ModelPrice.model == add_data.model
                    )
                )
            )
            existing_data = result.scalars().first()
            if existing_data:
                raise ClientVisibleException("模型Api_path已存在")

            # 添加新的模型积分数据
            new_data = ModelPrice(
                capacity=add_data.capacity,
                model=add_data.model,
                editor=user.username,
                api_type=add_data.api_type,
                updatetime=datetime.datetime.now(),
                unit=add_data.unit,
                credit=add_data.credit
            )
            session.add(new_data)
            await session.commit()

            return JSONResponse(content={"data": {}, "code": "0000", "msg": "添加成功"})

    except Exception as e:
        logger.error(f"Failed to add game: {e}")
        raise ClientVisibleException("添加模型积分失败") from e


@router.post(
    "/update",
    response_model=PaginatedResponse,
    dependencies=[Depends(get_roles_checker('super_admin'))],
    tags=["manage"]
)
async def update_game_management(
        update_data: ModelPriceOut,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user)
):
    """
      更新模型积分数据
    """
    try:
        async with db as session:
            # 根据 id 查找对应的模型积分
            result = await session.execute(
                select(ModelPrice).filter(ModelPrice.id == update_data.id)
            )
            existing_data = result.scalars().first()

            if not existing_data:
                raise ClientVisibleException("模型积分不存在")

            # 更新模型积分信息
            existing_data.capacity = update_data.capacity
            existing_data.model = update_data.model
            existing_data.editor = user.username
            existing_data.api_type = update_data.api_type
            existing_data.unit = update_data.unit
            existing_data.credit = update_data.credit

            await session.commit()
            return JSONResponse(content={"data": {}, "code": "0000", "msg": "更新成功"})


    except Exception as e:
        logger.error(f"Failed to update game: {e}")
        raise ClientVisibleException("更新模型积分失败") from e
