{"1": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "4": {"inputs": {"unet_name": "flux1-dev-kontext_fp8_scaled.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "5": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "6": {"inputs": {"trans_switch": true, "trans_text": "女孩，miku，双马尾，中国旗袍，日系动漫画风", "translator": "<PERSON>", "source_language": "auto", "target_language": "English(en)", "Show_Hide_button": "button_show"}, "class_type": "Text_Translation_V2", "_meta": {"title": "Text Translation V2"}}, "8": {"inputs": {"guidance": 3, "conditioning": ["13", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "9": {"inputs": {"conditioning": ["13", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "12": {"inputs": {"separator": "", "text1": ["6", 0]}, "class_type": "CR Text Concatenate", "_meta": {"title": "🔤 CR Text Concatenate"}}, "13": {"inputs": {"text": ["12", 0], "clip": ["5", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "14": {"inputs": {"seed": 262261235248688, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["4", 0], "positive": ["8", 0], "negative": ["9", 0], "latent_image": ["15", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "15": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "16": {"inputs": {"samples": ["14", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "17": {"inputs": {"filename_prefix": "ComfyUI", "images": ["16", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "18": {"inputs": {"upscale_model": ["19", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "使用模型放大图像"}}, "19": {"inputs": {"model_name": "RealESRGAN_x4plus.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "加载放大模型"}}}