import logging
import time
import json
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
import httpx

from config import app_settings
from models.tasks import Task, TaskStatus, TaskType
from utils.redis import redis


logger = logging.getLogger(__name__)


# Redis队列前缀，用于不同类型的任务
TASK_QUEUE_PREFIX = "task:queue:"

class TaskManager:
    """通用任务管理器"""

    @staticmethod
    async def generate_taskid() -> str:
        """
        生成唯一的任务ID

        返回:
        - 任务ID字符串
        """
        # 基于时间戳和UUID生成唯一的任务ID
        timestamp = int(time.time() * 1000)
        unique_part = str(uuid.uuid4()).replace('-', '')[:8]
        return f"{timestamp}-{unique_part}"

    @staticmethod
    async def create_task(
        db: AsyncSession,
        username: str,
        task_type: str,
        action: str,
        task_params: Dict[str, Any],
        prompt_media_url: Optional[str] = None,
        pid: Optional[int] = None,
        max_pending_tasks: int = 5,
        model: Optional[str] = None,  # 新增参数，允许直接传入model值
    ) -> Tuple[Optional[Task], Optional[str]]:
        """
        创建新任务并加入队列

        参数:
        - db: 数据库会话
        - username: 用户名
        - task_type: 任务类型(music, image, video等)
        - action: 任务动作
        - task_params: 任务参数
        - prompt_media_url: 提示词相关的媒体URL(可选)
        - pid: 父任务ID(可选)
        - max_pending_tasks: 用户未完成任务数量限制
        - model: 模型类型(可选)，如果提供则优先使用该值，否则从task_params中获取

        返回:
        - Tuple[task, error_message]: 创建的任务对象和错误信息(如果有)
        """
        # 检查任务类型是否有效
        try:
            task_type_enum = TaskType(task_type)
        except ValueError:
            logger.error(f"无效的任务类型: {task_type}")
            return None, f"无效的任务类型: {task_type}"

        # 检查用户未完成的任务数量
        stmt = select(func.count(Task.id)).select_from(Task).filter(
            Task.username == username,
            Task.task_type == task_type,
            Task.status.in_([TaskStatus.NOT_START, TaskStatus.SUBMITTED, TaskStatus.IN_PROGRESS])
        )

        pending_tasks_count = await db.scalar(stmt)

        # 限制用户任务数量
        if pending_tasks_count >= max_pending_tasks:
            logger.warning(f"用户 {username} 的 {task_type} 类型任务已达到上限({max_pending_tasks}个)")
            return None, f"排队任务超过{max_pending_tasks}个，请等待现有任务完成"

        # 生成任务ID
        taskid = await TaskManager.generate_taskid()

        # 创建任务记录
        task = Task(
            taskid=taskid,
            username=username,
            task_type=task_type,
            action=action,
            status=TaskStatus.NOT_START,
            pid=pid,
            queue_position=0,  # 初始化为0，后续由AI服务器更新
            submit_time=datetime.now(),
            uptime=datetime.now(),
            task_params=task_params,
            prompt_media_url=prompt_media_url,
            model=model if model is not None else task_params.get("model", "framepack")  # 优先使用传入的model参数
        )

        # 添加到数据库
        db.add(task)
        await db.commit()
        await db.refresh(task)

        logger.info(f"任务 {taskid} (DB ID: {task.id}) 已成功创建，准备提交到AI服务器")

        try:
            # 提交任务到AI服务器
            task.task_params["total_second_length"] = int(task.task_params["total_second_length"] + 1)
            res, error = await TaskManager.submit_task_to_ai_server(db, task)
            if res is None:
                # 如果提交失败，将任务状态更新为失败
                task.status = TaskStatus.FAILURE
                task.fail_reason = error
                task.uptime = datetime.now()
                logger.error(f"提交任务 {taskid} (DB ID: {task.id}) 到AI服务器失败: {error}")# 刷新任务对象
                db.add(task)
                await db.commit()
                return None, error
            else:
                # 更新任务状态为已提交
                status = res.get('status', '')
                # 这个判断主要是为了尊重 AiServer 侧给出的状态，避免那边逻辑改变这里无法适配
                if status in TaskStatus:
                    logger.warning(f"任务已经成功提交，status: {status}，task_id：{task.id}")
                    task.status = status
                else:
                    logger.warning(f"任务已经成功提交，但任务 status 无效: {status}，task_id：{task.id}，默认使用 SUBMITTED")
                    task.status = TaskStatus.SUBMITTED
                task.uptime = datetime.now()
                task.queue_position = res.get('queue_position', 0)
                logger.info(f"任务 {task.taskid} (DB ID: {task.id}) 已成功提交到AI服务器")
                db.add(task)
                await db.commit()
                # 刷新任务对象
                await db.refresh(task)
                return task, error
        except Exception as e:
            logger.error(f"提交任务 {taskid} (DB ID: {task.id}) 到AI服务器时出错: {str(e)}")
            # 更新任务状态为失败
            task.status = TaskStatus.FAILURE
            task.fail_reason = str(e)
            task.uptime = datetime.now()
            await db.commit()
            return None, f"提交任务失败: {str(e)}"

    @staticmethod
    async def get_task_status(
        db: AsyncSession,
        taskid: str,
        username: str
    ) -> Optional[Task]:
        """
        查询任务状态

        参数:
        - db: 数据库会话
        - taskid: 任务ID
        - username: 用户名

        返回:
        - 任务对象
        """
        # 查询任务
        stmt = select(Task).filter(
            Task.username == username,
            Task.taskid == taskid
        )
        result = await db.execute(stmt)
        task = result.scalars().first()

        return task

    @staticmethod
    async def get_user_tasks(
        db: AsyncSession,
        username: str,
        task_type: Optional[str] = None,
        page: int = 1,
        size: int = 10,
        filter_params: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Task], int]:
        """
        获取用户任务列表(分页)

        参数:
        - db: 数据库会话
        - username: 用户名
        - task_type: 任务类型(可选)
        - page: 页码
        - size: 每页大小
        - filter_params: 其他过滤条件

        返回:
        - 任务列表和总数
        """
        # 构建基础查询
        query = select(Task).filter(Task.username == username)
        count_query = select(func.count(Task.id)).select_from(Task).filter(Task.username == username)

        # 添加任务类型过滤
        if task_type:
            query = query.filter(Task.task_type == task_type)
            count_query = count_query.filter(Task.task_type == task_type)

        # 添加其他过滤条件
        if filter_params:
            for key, value in filter_params.items():
                if hasattr(Task, key):
                    # 处理特殊条件，如模糊搜索
                    if key == 'task_params' and isinstance(value, dict):
                        # 处理JSON字段过滤，根据实际情况调整
                        pass
                    else:
                        query = query.filter(getattr(Task, key) == value)
                        count_query = count_query.filter(getattr(Task, key) == value)

        # 计算总数
        total_count = await db.scalar(count_query)

        # 分页
        offset = (page - 1) * size
        query = query.order_by(Task.submit_time.desc()).offset(offset).limit(size)

        # 执行查询
        result = await db.execute(query)
        tasks = result.scalars().all()

        return tasks, total_count

    @staticmethod
    async def update_task_status(
        db: AsyncSession,
        task_id: int,
        status: TaskStatus,
        result: Optional[Dict[str, Any]] = None,
        resource_url: Optional[str] = None,
        fail_reason: Optional[str] = None
    ) -> Optional[Task]:
        """
        更新任务状态

        参数:
        - db: 数据库会话
        - task_id: 任务ID(数据库ID)
        - status: 新状态
        - result: 任务结果
        - resource_url: 资源URL
        - fail_reason: 失败原因

        返回:
        - 更新后的任务对象
        """
        # 查询任务
        stmt = select(Task).filter(Task.id == task_id)
        result_query = await db.execute(stmt)
        task = result_query.scalars().first()

        if not task:
            logger.error(f"未找到任务 ID: {task_id}")
            return None

        # 更新状态
        task.status = status
        task.uptime = datetime.now()

        # 根据状态设置其他字段
        if status == TaskStatus.IN_PROGRESS:
            task.start_time = datetime.now()

        if status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]:
            task.finish_time = datetime.now()

        if result:
            task.task_result = result

        if resource_url:
            task.resource_url = resource_url

        if fail_reason:
            task.fail_reason = fail_reason

        await db.commit()
        await db.refresh(task)

        return task

    @staticmethod
    async def update_task_pos(
        db: AsyncSession,
        task_id: int,
        pos: int,
    ) -> Optional[Task]:
        """
        更新任务位置

        参数:
        - db: 数据库会话
        - task_id: 任务ID(数据库ID)
        - status: 位置信息

        返回:
        - 更新后的任务对象
        """
        # 查询任务
        stmt = select(Task).filter(Task.id == task_id)
        result_query = await db.execute(stmt)
        task = result_query.scalars().first()

        if not task:
            logger.error(f"未找到任务 ID: {task_id}")
            return None

        # 更新状态
        task.queue_position = pos
        task.uptime = datetime.now()

        await db.commit()
        await db.refresh(task)

        return task

    @staticmethod
    async def update_task_result(
        db: AsyncSession,
        task_id: int,
        result: Optional[Dict[str, Any]] = None,
        resource_url: Optional[str] = None,
        update_status: bool = True  # 添加新参数，控制是否更新状态
    ) -> Optional[Task]:
        """
        更新任务结果

        参数:
        - db: 数据库会话
        - task_id: 任务ID(数据库ID)
        - result: 任务结果
        - resource_url: 资源URL
        - update_status: 是否更新任务状态为SUCCESS (默认为True)

        返回:
        - 更新后的任务对象
        """
        # 查询任务
        stmt = select(Task).filter(Task.id == task_id)
        result_query = await db.execute(stmt)
        task = result_query.scalars().first()

        if not task:
            logger.error(f"未找到任务 ID: {task_id}")
            return None

        # 如果任务已取消且需要更新状态，则记录信息并返回
        if task.status == TaskStatus.CANCELED and update_status:
            logger.info(f"任务 {task.taskid} 已取消，不更新结果")
            return task

        # 如果任务已取消但不需要更新状态，只更新结果信息，不修改任何状态相关字段
        if task.status == TaskStatus.CANCELED and not update_status:
            logger.info(f"任务 {task.taskid} 已取消，仅更新结果信息，不修改状态")
            # 仅更新结果和资源URL，不更新状态和完成时间
            if result:
                task.task_result = result
            if resource_url:
                task.resource_url = resource_url

            # 更新修改时间
            task.uptime = datetime.now()

            await db.commit()
            await db.refresh(task)
            return task

        # 以下处理非取消状态的任务
        # 更新时间
        task.uptime = datetime.now()

        # 更新结果
        if result:
            task.task_result = result

        # 更新资源URL
        if resource_url:
            task.resource_url = resource_url

        # 如果需要更新状态为成功，且当前状态不是已取消
        if update_status and task.status != TaskStatus.CANCELED:
            task.status = TaskStatus.SUCCESS
            task.finish_time = datetime.now()

        await db.commit()
        await db.refresh(task)

        return task

    @staticmethod
    async def remove_task_from_queue(
        db: AsyncSession,
        taskid: str,
        task_type: str,
        update_db: bool = True  # 新增参数控制是否更新数据库
    ) -> Tuple[bool, List[Task], Optional[str]]:
        """
        从队列中移除任务并更新受影响任务的队列位置

        参数:
        - db: 数据库会话
        - taskid: 任务ID
        - task_type: 任务类型
        - update_db: 是否更新数据库中的队列位置状态(默认为True)

        返回:
        - Tuple[success, affected_tasks, error_message]: 操作是否成功、受影响的任务列表和错误信息
        """
        try:
            queue_key = f"{TASK_QUEUE_PREFIX}{task_type}"

            # 获取队列所有任务
            queue_items = await redis.exec('lrange', queue_key, 0, -1)

            # 记录任务是否在Redis队列中被找到和移除
            task_removed_from_redis = False
            target_index = -1

            if not queue_items:
                logger.info(f"队列 {queue_key} 为空，无法从Redis中移除任务，但仍将尝试更新数据库中的队列位置")
            else:
                # 记录原始队列中的所有taskid，用于后续比较
                original_task_ids = []
                task_id_to_db_id = {}  # 映射taskid到db_task_id

                for item in queue_items:
                    try:
                        item_data = json.loads(item)
                        tid = item_data.get("taskid")
                        if tid:
                            original_task_ids.append(tid)
                            db_id = item_data.get("db_task_id")
                            if db_id:
                                task_id_to_db_id[tid] = db_id
                    except json.JSONDecodeError:
                        continue

                # 检查要移除的任务是否在队列中
                task_in_queue = taskid in original_task_ids

                # 如果任务在队列中，则移除任务
                if task_in_queue:
                    target_index = original_task_ids.index(taskid)

                    # 获取要移除的任务项
                    task_to_remove = queue_items[target_index]

                    # 从队列中移除任务
                    removed_count = await redis.exec('lrem', queue_key, 1, task_to_remove)
                    if removed_count == 0:
                        logger.warning(f"任务 {taskid} 无法从队列 {queue_key} 中移除")
                    else:
                        task_removed_from_redis = True
                        logger.info(f"已从队列 {queue_key} 中移除任务 {taskid}, 原队列位置: {target_index}")
                else:
                    # 任务不在队列中（可能是正在处理的任务）
                    logger.info(f"任务 {taskid} 不在队列 {queue_key} 中，可能是正在处理中的任务")

            # 如果不需要更新数据库，直接返回成功
            if not update_db:
                return True, [], None

            # 获取移除后的队列状态，用于比较和更新
            updated_queue_items = await redis.exec('lrange', queue_key, 0, -1)
            updated_task_ids = []

            for item in updated_queue_items:
                try:
                    item_data = json.loads(item)
                    tid = item_data.get("taskid")
                    if tid:
                        updated_task_ids.append(tid)
                except json.JSONDecodeError:
                    continue

            # 获取所有待更新队列位置的任务
            affected_tasks = []

            # 方法2：更新所有相关任务的队列位置（不管任务是否在Redis队列中被移除）
            # 获取数据库中该类型的所有未完成任务
            tasks_stmt = select(Task).filter(
                Task.task_type == task_type,
                Task.status.in_([TaskStatus.SUBMITTED, TaskStatus.NOT_START])
            ).order_by(Task.submit_time.asc())

            tasks_result = await db.execute(tasks_stmt)
            all_pending_tasks = tasks_result.scalars().all()

            logger.info(f"重新计算队列位置: 找到 {len(all_pending_tasks)} 个待处理任务")

            # 重新计算所有待处理任务的队列位置
            for i, task in enumerate(all_pending_tasks):
                # 跳过要取消的任务
                if task.taskid == taskid:
                    continue

                if task.taskid in updated_task_ids:
                    # 任务在Redis队列中，使用Redis队列的位置
                    new_position = updated_task_ids.index(task.taskid)
                else:
                    # 任务不在Redis队列中，基于在待处理任务列表中的位置计算
                    # 但是要除去当前任务，所以如果这个任务在列表中的索引大于当前任务，需要减1
                    current_task_index = next((idx for idx, t in enumerate(all_pending_tasks) if t.taskid == taskid), -1)
                    new_position = i
                    if current_task_index != -1 and i > current_task_index:
                        new_position = i - 1

                old_position = task.queue_position

                # 只有当位置需要更新时才更新
                if new_position != old_position:
                    task.queue_position = new_position
                    task.uptime = datetime.now()
                    affected_tasks.append(task)
                    logger.info(f"更新任务 {task.taskid} (DB ID: {task.id}) 的队列位置: {old_position} -> {new_position}")

            # 提交数据库更改
            if affected_tasks:
                await db.commit()
                logger.info(f"更新了 {len(affected_tasks)} 个任务的队列位置")
            else:
                logger.info("没有任务的队列位置需要更新")

            return True, affected_tasks, None

        except Exception as e:
            logger.error(f"从队列中移除任务 {taskid} 时出错: {e}", exc_info=True)
            await db.rollback()
            return False, [], f"从队列中移除任务时出错: {e}"

    @staticmethod
    async def get_task_by_taskid(
        db: AsyncSession,
        taskid: str
    ) -> Optional[Task]:
        """
        通过taskid查询任务

        参数:
        - db: 数据库会话
        - taskid: 任务ID

        返回:
        - 任务对象
        """
        # 查询任务
        stmt = select(Task).filter(Task.taskid == taskid)
        result = await db.execute(stmt)
        task = result.scalars().first()

        return task

    @staticmethod
    async def submit_task_to_ai_server(
        db: AsyncSession,
        task: Task,
        callback_url: str = None
    ) -> Tuple[dict[str, Any] | None, Optional[str]]:
        """
        提交任务到AI服务器

        参数:
        - db: 数据库会话
        - task: 任务对象
        - callback_url: 回调URL，用于接收任务状态更新（可选，有默认值）

        返回:
        - Tuple[success, error_message]: 是否成功和错误信息
        """
        # 获取AI服务器地址
        ai_server = app_settings.ai_server
        if not ai_server:
            logger.error("未设置AI-SERVER环境变量")
            return None, "未设置AI服务器地址"

        # 如果未提供回调URL，使用默认值
        if not callback_url:
            # 获取后端服务地址（用于回调）
            callback_url = app_settings.backend_server

        # 准备请求数据
        request_data = {
            "username": task.username,
            "task_type": task.task_type,
            "action": task.action,
            "params": task.task_params,
            "callback_url": callback_url,
            "taskid": task.taskid,
            "model": task.model
        }

        logger.info(f"提交任务到AI服务器: {request_data}")

        try:
            # 发送请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{ai_server}/task/submit",
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                )

                # 检查响应
                if response.status_code == 200:
                    return response.json(), None
                else:
                    error_message = f"提交任务到AI服务器失败: HTTP {response.status_code}, {response.text}"
                    logger.error(error_message)
                    return None, error_message

        except Exception as e:
            error_message = f"提交任务到AI服务器时出错: {str(e)}"
            logger.error(error_message)
            return None, error_message

    @staticmethod
    async def notify_task_canceling_to_ai_server(
        task: Task,
        callback_url: str = None
    ) -> bool:
        """
        通知 AI 资源机取消任务
        """
        # 获取AI服务器地址
        ai_server = app_settings.ai_server
        if not ai_server:
            logger.error("未设置AI-SERVER环境变量")
            return False

        # 如果未提供回调URL，使用默认值
        if not callback_url:
            # 获取后端服务地址（用于回调）
            callback_url = app_settings.backend_server

        try:
            # 发送请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{ai_server}/task/cancel",
                    headers={"Content-Type": "application/json"},
                    json={
                        "taskid": task.taskid,
                    }
                )

                # 检查响应
                if response.status_code == 200:
                    return True
                else:
                    error_message = f"通知 AI 服务器取消任务时出错: HTTP {response.status_code}, {response.text}"
                    logger.error(error_message)
                    return False

        except Exception as e:
            error_message = f"通知 AI 服务器取消任务时出错 taskid:{task.taskid}, : {str(e)}"
            logger.error(error_message)
            return False

    @staticmethod
    async def delete_task(
        db: AsyncSession,
        taskid: str,
        username: str
    ) -> Tuple[bool, Optional[str]]:
        """
        删除任务记录及其相关资产

        参数:
        - db: 数据库会话
        - taskid: 任务ID
        - username: 用户名

        返回:
        - Tuple[success, error_message]: 操作是否成功和错误信息
        """
        try:
            # 查询任务
            task = await TaskManager.get_task_status(
                db=db,
                taskid=taskid,
                username=username
            )

            if not task:
                return False, "任务不存在"

            # 检查任务状态是否允许删除（只有SUCCESS、FAILURE、CANCELED状态可以删除）
            allowed_statuses = [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.CANCELED]
            if task.status not in allowed_statuses:
                return False, f"无法删除状态为 {task.status} 的任务"

            # 删除相关的资产数据
            try:
                from utils.asset_storage import delete_assets_by_taskid_from_manager
                
                # 根据taskid和用户ID删除相关资产（添加用户ID作为额外的安全检查）
                asset_success, deleted_asset_count, asset_error = await delete_assets_by_taskid_from_manager(
                    db=db,
                    taskid=taskid,
                    user_id=task.user_id if hasattr(task, 'user_id') else None
                )
                
                if not asset_success:
                    logger.error(f"删除任务 {taskid} 的资产失败: {asset_error}")
                    await db.rollback()
                    return False, f"删除任务相关资产失败: {asset_error}"
                
                if deleted_asset_count > 0:
                    logger.info(f"任务 {taskid} 关联的 {deleted_asset_count} 个资产已删除")
                else:
                    logger.info(f"任务 {taskid} 没有关联的资产数据")
                    
            except Exception as e:
                logger.error(f"删除任务 {taskid} 的资产时出错: {str(e)}")
                await db.rollback()
                return False, f"删除任务相关资产时出错: {str(e)}"

            # 从数据库中删除任务记录
            await db.delete(task)
            await db.commit()

            logger.info(f"任务 {taskid} (DB ID: {task.id}) 及其关联资产已成功删除，用户: {username}")
            return True, None

        except Exception as e:
            logger.error(f"删除任务 {taskid} 失败，用户 {username}: {e}", exc_info=True)
            await db.rollback()
            return False, f"删除任务时发生内部错误"
