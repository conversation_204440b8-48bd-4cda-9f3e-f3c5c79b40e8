<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { updateConversationTitle } from '@/service/api/chat';

const props = defineProps<{
  chatList: Array<{ id: number; uuid: number; title: string; create_time: string; isEdit: boolean }>;
  selectedChatId: number | null;
  selectedChatTitle: string;
  isMobile?: boolean;
}>();

const emit = defineEmits<{
  (event: 'update:selectedChatId', value: number | null): void;
  (event: 'chatAdded'): void;
  (event: 'update:selectedChatTitle', value: string): void;
  (event: 'chatDelete', value: number): void;
}>();

// 聊天记录
const chatList = ref(props.chatList);

// 选中聊天记录
const selectedChatId = ref<number | null>(props.selectedChatId);

watch(
  () => props.chatList,
  newChatList => {
    chatList.value = newChatList;
    // console.log('当前聊天列表:', chatList.value);
  },
  { deep: true, immediate: true }
);

// 监听selectedChatTitle的变化
watch(
  () => props.selectedChatTitle,
  newTitle => {
    // 找到当前选中的聊天并更新标题
    const selectedChat = chatList.value.find(chat => chat.uuid === selectedChatId.value);
    if (selectedChat && !selectedChat.isEdit) {
      selectedChat.title = newTitle;
    }
  }
);

function selectTitle(id: number) {
  const selectedChat = chatList.value.find(chat => chat.uuid === id);
  if (selectedChat) {
    emit('update:selectedChatTitle', selectedChat.title);
  }
}

function selectChat(id: number) {
  selectedChatId.value = id;
  emit('update:selectedChatId', selectedChatId.value);
  // console.log('选中的聊天ID:', selectedChatId.value);

  // 更新选中的聊天标题
  const selectedChat = chatList.value.find(chat => chat.uuid === id);
  if (selectedChat) {
    // console.log('选中的聊天标题:', selectedChat.title);
    emit('update:selectedChatTitle', selectedChat.title);
  }
}

watch(
  () => props.selectedChatId,
  newSelectedChatId => {
    selectedChatId.value = newSelectedChatId;
  },
  { immediate: true }
);

function isActive(id: number) {
  return selectedChatId.value === id;
}

function emitadd() {
  emit('chatAdded');
}

const oldTitle = ref('');

function editChat(id: number) {
  const targetChat = chatList.value.find(chat => chat.uuid === id);
  if (targetChat) {
    targetChat.isEdit = true;
    oldTitle.value = targetChat.title;
  }
}

async function saveChat(id: number) {
  const targetChat = chatList.value.find(chat => chat.uuid === id);
  if (targetChat) {
    if (!targetChat.title.trim()) {
      // 如果标题为空，不进行保存操作
      targetChat.title = oldTitle.value;
    }
    targetChat.isEdit = false;
    // 调用保存API
    await updateConversationTitle(id, targetChat.title);

    // 如果保存的是当前选中的聊天，更新header的title
    if (id === selectedChatId.value) {
      emit('update:selectedChatTitle', targetChat.title);
    }
  }
}

async function deleteChat(id: number) {
  emit('chatDelete', id);
}

function handleEnter(chat: any, event: KeyboardEvent) {
  if (event.key === 'Enter') {
    saveChat(chat.id);
  }
}

function formatTime(time: Date) {
  const now = new Date();
  const diffInMs = now.getTime() - time.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInMonths = (now.getFullYear() - time.getFullYear()) * 12 + now.getMonth() - time.getMonth();

  if (diffInDays === 0) return '今天';
  if (diffInDays === 1) return '昨天';
  if (diffInDays <= 7) return `${diffInDays}天前`;

  // 如果是本月
  if (time.getMonth() === now.getMonth() && time.getFullYear() === now.getFullYear()) return '本月';

  // 如果是在12个月内，显示"X个月前"
  if (diffInMonths > 0 && diffInMonths < 12) return `${diffInMonths}个月前`;

  // 如果是超过一年，返回YYYY-MM格式
  const month = time.getMonth() + 1;
  const formattedMonth = month < 10 ? `0${month}` : month;
  return `${time.getFullYear()}-${formattedMonth}`;
}

function groupChatsByDate() {
  const grouped = chatList.value.reduce(
    (acc, chat) => {
      // 将字符串转换为 Date 对象
      const dateKey = formatTime(new Date(chat.create_time));
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(chat);
      return acc;
    },
    {} as Record<string, typeof chatList.value>
  );

  return Object.entries(grouped);
}

onMounted(async () => {
  selectTitle(props.selectedChatId as number);
});

const showDeleteModal = ref(false);
</script>

<template>
  <div class="min-h-0 flex-1 overflow-hidden pb-4">
    <div class="my-2" :class="{ 'px-3': !isMobile }">
      <NButton dashed block @click="emitadd">
        <template #icon>
          <SvgIcon icon="material-symbols:assignment-add" />
        </template>
        {{ $t('page.chat.newChat') }}
      </NButton>
    </div>
    <NInfiniteScroll :class="{ 'px-3': !isMobile }" :distance="10">
      <div class="flex flex-col gap-2 text-sm">
        <div v-for="[date, chats] in groupChatsByDate()" :key="date">
          <div class="px-3 py-2 text-xs text-gray-400">
            <b>{{ date }}</b>
          </div>
          <div v-for="item in chats" :key="item.uuid">
            <div
              class="group relative flex cursor-pointer items-center gap-3 break-all rounded-md px-3 py-2 dark:border-neutral-800 hover:bg-neutral-100 dark:hover:bg-[#24272e]"
              :class="[{ 'dark:bg-[#24272e] bg-neutral-100': isActive(item.uuid) }]"
              @click="selectChat(item.uuid)"
            >
              <div class="relative w-8/9 overflow-hidden text-ellipsis whitespace-nowrap break-all">
                <NInput
                  v-if="item.isEdit"
                  v-model:value="item.title"
                  size="tiny"
                  @keypress="handleEnter(item, $event)"
                  @blur="saveChat(item.uuid)"
                />
                <span v-else>
                  <NText>
                    {{ item.title }}
                  </NText>
                </span>
              </div>
              <div v-if="isActive(item.uuid)" class="visible absolute right-1 z-10 flex">
                <template v-if="item.isEdit">
                  <NButton text class="p-1" @click="saveChat(item.uuid)">
                    <SvgIcon icon="ri:save-line" />
                  </NButton>
                </template>
                <template v-else>
                  <NButton text class="p-1" @click="editChat(item.uuid)">
                    <SvgIcon icon="ri:edit-line" />
                  </NButton>
                  <NPopconfirm v-if="!isMobile" placement="bottom" @positive-click="deleteChat(item.uuid)">
                    <template #trigger>
                      <NButton text class="p-1">
                        <SvgIcon icon="ri:delete-bin-line" />
                      </NButton>
                    </template>
                    确定删除此记录?
                  </NPopconfirm>
                  <NButton v-else text class="p-1" @click="showDeleteModal = true">
                    <SvgIcon icon="ri:delete-bin-line" />
                  </NButton>
                  <NModal
                    v-model:show="showDeleteModal"
                    preset="dialog"
                    title="确认"
                    content="确定删除此记录?"
                    positive-text="确认"
                    negative-text="取消"
                    @positive-click="deleteChat(item.uuid)"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </NInfiniteScroll>
  </div>
</template>

<style scoped></style>
