import asyncio
import json
import logging
import re
import time
import traceback
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from datetime import datetime
from typing import List, Optional, AsyncIterator

import importlib
import openpyxl
import pandas as pd
import tiktoken
from fastapi import APIRouter, Depends, UploadFile, File, Form, Header, Query
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field, SkipValidation
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.sql import func

from service.ai_model import get_models
from models.chat import chatContent
from models.game_management import GameManagement
from models.gametranslations import GameTranslation
from models.users import User, get_token_key, get_request_user
from utils.database import get_db, AsyncSessionLocal
from utils.exceptions import ClientVisibleException
from utils.redis import redis
from utils.lang import contains_chinese

router = APIRouter()
logger = logging.getLogger(__name__)

class DeletePresetsRequest(BaseModel):
  id: int


class GameTranslationOut(BaseModel):
  id: int
  zh: str
  en: str
  gamecode: str
  update_time: SkipValidation[datetime]
  editor: Optional[str] = Field(default='')

  class Config:
    from_attributes = True
    json_encoders = {
      datetime: lambda v: v.strftime('%Y-%m-%d %H:%M:%S'),
    }
    arbitrary_types_allowed = True


class PaginatedData(BaseModel):
  records: List[GameTranslationOut]
  current: int
  size: int
  total: int


class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str
  msg: str


translation_dict = {
    "中英互译": {"language": "Chinese-English", "audio": 1 , "voice_type": "BV421_streaming", "langcode": "cn|en"},
    "英文": {"language": "English", "audio": 1 , "voice_type": "BV421_streaming", "langcode": "en"},
    "简体中文": {"language": "Simplified Chinese", "audio": 1, "voice_type": "BV421_streaming", "langcode": "cn"},
    "繁体中文": {"language": "Traditional Chinese", "audio": 1, "voice_type": "BV704_streaming,BV704_streaming", "langcode": "zh_yueyu,zh_taipu"},
    "西班牙语": {"language": "Spanish", "audio": 0, "voice_type":"", "langcode": ""},
    "法语": {"language": "French", "audio": 0, "voice_type":"", "langcode": ""},
    "德语": {"language": "German", "audio": 0, "voice_type":"", "langcode": ""},
    "俄语": {"language": "Russian", "audio": 0, "voice_type":"", "langcode": ""},
    "日语": {"language": "Japanese", "audio": 1, "voice_type": "BV421_streaming", "langcode": "ja"},
    "韩语": {"language": "Korean", "audio": 0, "voice_type":"", "langcode": ""},
    "阿拉伯语": {"language": "Arabic", "audio": 0, "voice_type":"", "langcode": ""},
    "葡萄牙语": {"language": "Portuguese", "audio": 1, "voice_type": "BV421_streaming", "langcode": "ptbr"},
    "意大利语": {"language": "Italian", "audio": 0, "voice_type":"", "langcode": ""},
    "印地语": {"language": "Hindi", "audio": 0, "voice_type":"", "langcode": ""},
    "孟加拉语": {"language": "Bengali", "audio": 0, "voice_type":"", "langcode": ""},
    "乌尔都语": {"language": "Urdu", "audio": 0, "voice_type":"", "langcode": ""},
    "荷兰语": {"language": "Dutch", "audio": 0, "voice_type":"", "langcode": ""},
    "希腊语": {"language": "Greek", "audio": 0, "voice_type":"", "langcode": ""},
    "泰语": {"language": "Thai", "audio": 1, "voice_type": "BV421_streaming", "langcode": "thth"},
    "越南语": {"language": "Vietnamese", "audio": 1, "voice_type": "BV421_streaming", "langcode": "vivn"},
    "土耳其语": {"language": "Turkish", "audio": 0, "voice_type":"", "langcode": ""},
    "波兰语": {"language": "Polish", "audio": 0, "voice_type":"", "langcode": ""},
    "瑞典语": {"language": "Swedish", "audio": 0, "voice_type":"", "langcode": ""},
    "捷克语": {"language": "Czech", "audio": 0, "voice_type":"", "langcode": ""},
    "匈牙利语": {"language": "Hungarian", "audio": 0, "voice_type":"", "langcode": ""},
    "菲律宾语": {"language": "Filipino", "audio": 0, "voice_type":"", "langcode": ""},
    "希伯来语": {"language": "Hebrew", "audio": 0, "voice_type":"", "langcode": ""},
    "印尼语": {"language": "Indonesian", "audio": 1, "voice_type": "BV421_streaming", "langcode": "id"},
    "马来语": {"language": "Malay", "audio": 0, "voice_type":"", "langcode": ""}
}

# 模型配置字典
models_dict = {
    "DeepSeek": {"label": "DeepSeek", "value": "deepseek-v3", "description": "DeepSeek模型"},
    # "Qwen": {"label": "Qwen", "value": "Qwen", "description": "通义千问大模型"},
    # "Gpt-4o mini": {"label": "Gpt-4o mini", "value": "Gpt-4o mini", "description": "OpenAI GPT-4o mini模型"},
    # "ChatGPT 4o": {"label": "ChatGPT 4o", "value": "gpt-4o", "description": "ChatGPT 4o模型"},
    "ChatGPT 4.1": {"label": "ChatGPT 4.1", "value": "gpt-4.1", "description": "ChatGPT 4.1 模型"},
    # "Qwen-turbo": {"label": "Qwen-turbo", "value": "Qwen-turbo", "description": "通义千问高速模型"},
    
}


def num_tokens_from_string(string: str, model_name: str) -> int:
  """计算tokens确保翻译文本不会过长"""
  encoding = tiktoken.encoding_for_model(model_name)
  num_tokens = len(encoding.encode(string))
  return num_tokens


async def translate_text(db: AsyncSession, text: str, target_language: str, gamecode: str, model_name: str,
                         ext_prompt_text: str = '', use_presets: bool = True, user: str = None) -> str:
  # 在函数开始时预先获取所有翻译记录
  zh_translations = []
  en_translations = []
  
  async with AsyncSessionLocal() as db:
    try:
      logger.info(f"gamecode: {gamecode}")
      logger.info(f"user: {user.username}")
      
      # 根据gamecode构建不同的查询条件
      if gamecode == "Default" and user:
        # 当gamecode为Default时,只查询用户个人的配置
        result = await db.execute(select(GameTranslation).filter(
          GameTranslation.gamecode == gamecode,
          GameTranslation.company == user.username
        ))
      else:
        # 查询指定gamecode的所有配置
        result = await db.execute(select(GameTranslation).filter(GameTranslation.gamecode == gamecode))
      
      translation_records = result.scalars().all()

      # 遍历记录并将其添加到翻译列表中
      for record in translation_records:
        if record.zh:
          zh_translations.append(record.zh)
        if record.en:
          en_translations.append(record.en)
    except Exception as e:
      error_stack = traceback.format_exc()
      logger.error(f"Error fetching translation records: {str(e)}\n堆栈信息: {error_stack}")
      # 继续执行，即使没有找到预设数据
  
  # 数据库连接已关闭，继续处理翻译逻辑
  try:
    # 根据use_presets参数决定是否使用预设
    logger.info(f"use_presets: {use_presets}")
    context = ""
    if use_presets and zh_translations and en_translations:
      logger.info(f"target_language: {target_language}")
      # 预处理target_language，去除空格并转换为小写
      processed_target_language = target_language.strip().lower()
      logger.info(f"processed_target_language: {processed_target_language}")
      
      if processed_target_language == "english":
        logger.info(f"目标语言是英语，使用预设")
        context = "以下是一些特定名词的翻译对照表：\n"
        for zh, en in zip(zh_translations, en_translations):
          context += f"{zh} <---> {en}\n"
      else:
        logger.info(f"目标语言不是英语，不使用预设")
    else:
      logger.info(f"执行了 else  use_presets: {use_presets}")
      
    logger.info(f"context: {context}")

    # 替换文本中的`{` 和 `}` 为 `[` 和 `]`
    sanitized_text = re.sub(r'{', '[', text)
    sanitized_text = re.sub(r'}', ']', sanitized_text)

    prompt_text = f"""
                  By default, all responses must be in {target_language}.

                  # 专业游戏翻译指南\n

                  ## 核心翻译能力\n
                  你是一名专业游戏翻译专家，精通将文本准确翻译成{target_language}。\n

                  ## 额外细节\n
                  {context}\n

                  请将以下文本翻译成`{target_language}`：\n
                  {sanitized_text}\n

                  ## 翻译规范\n
                  - 严格参考提供的特定名词翻译对照表\n
                  - 保持游戏术语的一致性和准确性\n
                  - 保留原文的语气、风格和表达方式\n
                  - 确保翻译自然流畅，符合目标语言的表达习惯\n
                  - 专业处理游戏特有的文化元素和表达方式\n

                  ## 输出要求\n
                  - 仅返回翻译后的文本，不包含任何解释、分析或额外说明\n
                  - 每个词汇或句子只提供一个翻译结果\n
                  - 不要添加"这是翻译的文本"、"返回"、"翻译后"等额外文字\n
                  - 禁止输出原文与译文的混合形式\n
                  - 确保翻译后的段落换行与原文保持一致。\n

                  {ext_prompt_text}
                  """
    logger.info(f"prompt_text: {prompt_text}")

    # 从models_dict获取模型配置
    model_value = ""
    # 直接通过value匹配
    for key, value in models_dict.items():
      if value["value"] == model_name:
        model_value = model_name
        logger.info(f"模型加载成功：{model_value}")
        break
    ai_models = await get_models(db)
    if not model_value or model_value not in ai_models:
      # 默认使用ChatGPT 4o
      logger.info(f"模型加载异常")
      model_value = "gpt-4o"
    
    # 获取模型完整配置
    model_config = ai_models[model_value]
    logger.info(f"使用模型: {model_value}, 提供商: {model_config.publishers}")
    
    # 动态导入相应服务商的聊天模块
    import asyncio
    
    # 构建聊天内容
    contents = [chatContent(role="user", content=sanitized_text, files=[])]
    
    # 在单独的线程中运行chat函数并收集结果
    def run_chat():
      result = ""
      try:
        # 动态加载对应提供商的聊天服务
        chatServer = importlib.import_module(f"service.{model_config.publishers}.chat")
        
        # 调用相应服务的chat函数
        for chunk in chatServer.chat(
          model_config.name,
          contents,
          systemPrompt=prompt_text,
          memory=""
        ):
          if isinstance(chunk, str):
            result += chunk
        return result
      except Exception as e:
        error_stack = traceback.format_exc()
        logger.error(f"调用模型服务失败: {str(e)}\n堆栈信息: {error_stack}")
        raise e
    
    # 使用ThreadPoolExecutor来运行同步函数
    with ThreadPoolExecutor() as executor:
      translation = await asyncio.get_event_loop().run_in_executor(
        executor, run_chat
      )
    
    # 清理并返回翻译结果
    translation = translation.strip()
    return translation
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in translate_text: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("翻译失败，请重试") from e


@router.get("/get_files_presets", tags=["text"])
async def get_files_presets(db: AsyncSession = Depends(get_db), user: User = Depends(get_request_user)):
  """
    获取符合条件的游戏名称列表
    """
  credentials_exception = ClientVisibleException("权限验证失败")

  try:
    async with db as session:
      # 获取用户信息
      result = await session.execute(select(User).filter(User.id == user.id))
      user = result.scalars().first()

      if user is None or user.status != 1:
        raise credentials_exception

      # 获取用户公司对应的游戏管理数据
      result = await session.execute(
        select(GameManagement.gamename, GameManagement.gamecode).filter(GameManagement.company == user.company)
      )
      games = result.all()

      # 构建包含 gamename 和 gamecode 的列表
      game_list = [{"gamename": game.gamename, "gamecode": game.gamecode} for game in games]
      sorted_list = sorted(game_list, key=lambda x: len(x["gamename"]))
      return JSONResponse(content={"code": "0000", "data": sorted_list})

  except Exception as e:
    logger.error(f"Error in get_files_presets: {str(e)}")
    raise ClientVisibleException("获取预设列表失败") from e


@router.post("/read_excel", tags=["text"])
async def read_excel(file: UploadFile = File(...)):
  """
    获取用户上传的Excel需翻译文件内容返回前端
    """
  try:
    # 读取Excel文件
    workbook = openpyxl.load_workbook(file.file)
    sheet = workbook.active

    texts = []
    translations = []

    # Excel的第一列为文本，第二列为翻译
    for row in sheet.iter_rows(min_row=2, values_only=True):  # 从第二行开始读取数据
      text, translation = row
      texts.append(text)
      translations.append(translation)

    response_data = {
      "code": "0000",
      "data": {
        "text": texts,
        "translations": translations
      },
      "msg": "文件读取成功"
    }

    return JSONResponse(content=response_data)
  except Exception as e:
    raise ClientVisibleException("文件读取失败") from e


@router.post("/translate", tags=["text"])
async def translate(
  file: UploadFile = File(None),  # 文件上传
  userinput: str = Form(''),  # 用户输入
  target_language: str = Form(...),
  gamecode: str = Form(...),
  model: str = Form(...),  # 新增model参数
  use_presets: str = Form(...),  # 移除默认值
  user: User = Depends(get_request_user),  # 获取当前用户
  db: AsyncSession = Depends(get_db),
):
  try:
    logger.info(f"传入的use_presets是: {use_presets}")
    logger.info(f"Received use_presets value: {use_presets}")
    logger.info(f"use_presets type: {type(use_presets)}")
    # 将字符串转换为布尔值，处理各种可能的输入
    use_presets_bool = use_presets.lower() in ('true', '1', 'yes', 'on')
    logger.info(f"use_presets_bool: {use_presets_bool}")
    
    if userinput and userinput.strip():
      # 如果用户输入存在并且不为空，则翻译用户输入
      translated_text = await translate_text(db, userinput, target_language, gamecode, model, use_presets=use_presets_bool, user=user)
      
      # 返回翻译结果
      response_data = {
        "code": "0000",
        "data": {
          "translations": translated_text
        },
        "msg": "翻译成功"
      }
      
      return JSONResponse(content=response_data)
    elif file:
      # 首先读取表格内容
      workbook = openpyxl.load_workbook(file.file)
      sheet = workbook.active
      
      # 提取需要翻译的文本
      texts = [cell.value for cell in sheet['A'] if cell.row > 1 and cell.value is not None]
      
      if not texts:
        raise ClientVisibleException("表格中没有找到需要翻译的文本")
      
      # 并行处理所有翻译任务
      tasks = []
      for text_to_translate in texts:
        tasks.append(translate_text(text_to_translate, target_language, gamecode, model, use_presets=use_presets_bool, user=user))
      
      # 等待所有翻译任务完成
      translated_texts = await asyncio.gather(*tasks)
      
      # 返回翻译结果
      response_data = {
        "code": "0000",
        "data": {
          "translations": "\n".join(translated_texts),
          "text": texts
        },
        "msg": "翻译成功"
      }
      
      return JSONResponse(content=response_data)
    else:
      raise ClientVisibleException("请提供文件或输入内容")

  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in translate: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("翻译失败") from e


# 流式翻译函数
async def generate_translation_stream(db: AsyncSession, text: str, target_language: str, gamecode: str, model: str, use_presets: bool, context: str = "", ext_prompt_text: str = "") -> AsyncIterator[str]:
    """生成翻译内容的流式响应"""
    start_time = time.time()
    # 处理中英互译的 target_language
    if target_language.lower() == "Chinese-English".lower():
      if contains_chinese(text):
        processed_target_language = "English"
      else:
        processed_target_language = "Simplified Chinese"
    else:
      processed_target_language = target_language

    logger.info(f"流式翻译开始: 目标语言={processed_target_language}, 模型={model}")
    
    # 发送初始状态
    yield json.dumps({"language": target_language, "text": "", "done": False}) + "\n"
    
    try:
        # 替换文本中的`{` 和 `}` 为 `[` 和 `]`
        sanitized_text = re.sub(r'{', '[', text)
        sanitized_text = re.sub(r'}', ']', sanitized_text)
        
        # 构建提示文本
        prompt_text = f"""
                    By default, all responses must be in {processed_target_language}.

                  # 专业游戏翻译指南\n

                  ## 核心翻译能力\n
                  你是一名专业游戏翻译专家，精通将文本准确翻译成{processed_target_language}。\n

                  ## 额外细节\n
                  {context}\n

                  请将以下文本翻译成`{processed_target_language}`：\n
                  {sanitized_text}\n

                  ## 翻译规范\n
                  - 严格参考提供的特定名词翻译对照表\n
                  - 保持游戏术语的一致性和准确性\n
                  - 保留原文的语气、风格和表达方式\n
                  - 确保翻译自然流畅，符合目标语言的表达习惯\n
                  - 专业处理游戏特有的文化元素和表达方式\n

                  ## 输出要求\n
                  - 仅返回翻译后的文本，不包含任何解释、分析或额外说明\n
                  - 每个词汇或句子只提供一个翻译结果\n
                  - 不要添加"这是翻译的文本"、"返回"、"翻译后"等额外文字\n
                  - 禁止输出原文与译文的混合形式\n
                  - 确保翻译后的段落换行与原文保持一致。\n

                  {ext_prompt_text}
                    """

        # 获取模型配置
        ai_models = await get_models(db)
        model_config = ai_models.get(model, ai_models["gpt-4.1"])
        
        # 构建聊天内容
        contents = [chatContent(role="user", content=sanitized_text, files=[])]
        
        # 使用ThreadPoolExecutor在后台线程中运行同步API调用
        def run_chat_and_process():
            # 动态导入聊天服务模块
            # chatServer = importlib.import_module(f"service.{model_config['publishers']}.chat")
            chatServer = importlib.import_module("service.openai.chat")
            
            # 收集结果
            result_chunks = []
            full_text = ""
            
            try:
                # 调用聊天API并收集结果
                for chunk in chatServer.chat(
                    model_config.name,
                    contents,
                    systemPrompt=prompt_text,
                    memory="",
                    temperature=0.2
                ):
                    if isinstance(chunk, str):
                        full_text += chunk
                        result_chunks.append((full_text, False))
                
                # 添加最终结果
                result_chunks.append((full_text, True))
                return result_chunks
            except Exception as e:
                logger.error(f"模型API调用错误: {str(e)}")
                return [(str(e), True, True)]  # 第三个参数表示错误
        
        # 使用线程池执行同步操作
        with ThreadPoolExecutor() as executor:
            future = executor.submit(run_chat_and_process)
            
            # 等待结果开始生成
            while not future.done():
                await asyncio.sleep(0.1)  # 轻微暂停，避免CPU空转
            
            # 获取所有生成的块
            chunks = future.result()
            
            # 逐个发送结果
            for i, (chunk_text, is_done, *extra) in enumerate(chunks):
                is_error = len(extra) > 0 and extra[0]
                
                if is_error:
                    yield json.dumps({
                        "language": target_language,
                        "error": chunk_text,
                        "done": True
                    }) + "\n"
                else:
                    yield json.dumps({
                        "language": target_language,
                        "text": chunk_text,
                        "done": is_done
                    }) + "\n"
                
                # 在发送每个块后等待一小段时间，模拟流式效果
                if not is_done and i < len(chunks) - 1:
                    await asyncio.sleep(0.05)
        
        logger.info(f"翻译完成，总耗时={time.time() - start_time:.2f}秒")
            
    except Exception as e:
        logger.error(f"流式翻译发生错误: {str(e)}")
        # 发送错误信号
        yield json.dumps({
            "language": target_language,
            "error": str(e),
            "done": True
        }) + "\n"


@router.post("/translate_stream", tags=["text"])
async def translate_stream(
    userinput: str = Form(''),  # 用户输入
    target_language: str = Form(...),
    gamecode: str = Form(...),
    model: str = Form(...),
    use_presets: str = Form(...),
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """流式翻译API"""
    start_time = time.time()
    logger.info(f"translate_stream API 开始处理请求: 目标语言={target_language}, 模型={model}")
    
    try:
        # 将字符串转换为布尔值
        use_presets_bool = use_presets.lower() in ('true', '1', 'yes', 'on')
        
        if not userinput or not userinput.strip():
            raise ClientVisibleException("请提供需要翻译的内容")
        
        logger.info(f"参数验证完成，耗时: {time.time() - start_time:.2f}秒")
        
        # 提前准备翻译上下文，完成所有数据库操作
        context = ""
        if use_presets_bool:
            zh_translations = []
            en_translations = []
            
            # 在返回流式响应前完成所有数据库操作
            db_start = time.time()
            async with AsyncSessionLocal() as db:
                try:
                    logger.info(f"开始数据库查询, gamecode={gamecode}")
                    # 根据gamecode构建不同的查询条件
                    if gamecode == "Default" and user:
                        result = await db.execute(select(GameTranslation).filter(
                            GameTranslation.gamecode == gamecode,
                            GameTranslation.company == user.username
                        ))
                    else:
                        result = await db.execute(select(GameTranslation).filter(
                            GameTranslation.gamecode == gamecode
                        ))
                    
                    translation_records = result.scalars().all()
                    logger.info(f"数据库查询完成，获取到 {len(translation_records)} 条记录，耗时: {time.time() - db_start:.2f}秒")
                    
                    # 预处理翻译记录
                    for record in translation_records:
                        if record.zh:
                            zh_translations.append(record.zh)
                        if record.en:
                            en_translations.append(record.en)
                except Exception as e:
                    error_stack = traceback.format_exc()
                    logger.error(f"数据库查询错误: {str(e)}\n堆栈信息: {error_stack}")
            
            # 构建上下文
            processed_target_language = target_language.strip().lower()
            if processed_target_language == "english" and zh_translations and en_translations:
                context = "以下是一些特定名词的翻译对照表：\n"
                for zh, en in zip(zh_translations, en_translations):
                    context += f"{zh} <---> {en}\n"
                logger.info(f"上下文构建完成，包含 {len(zh_translations)} 对翻译")
            else:
                logger.info(f"不需要构建上下文，目标语言={processed_target_language}")
        
        # 返回流式响应
        logger.info(f"准备启动流式响应，总前置准备耗时: {time.time() - start_time:.2f}秒")
        return StreamingResponse(
            generate_translation_stream(db, userinput, target_language, gamecode, model, use_presets_bool, context, ext_prompt_text=""),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache", 
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # 禁用Nginx缓冲
            }
        )
        
    except Exception as e:
        error_stack = traceback.format_exc()
        logger.error(f"translate_stream API 错误: {str(e)}, 总耗时: {time.time() - start_time:.2f}秒\n堆栈信息: {error_stack}")
        raise ClientVisibleException("翻译失败") from e


@router.get("/get_presets", tags=["text"])
async def get_presets(
  gamecode: str,
  current: int = 1,
  size: int = 10,
  db: AsyncSession = Depends(get_db),
  user: User = Depends(get_request_user)  # 获取当前用户
):
  """
    获取游戏预设翻译
    """
  try:
    # 根据gamecode构建不同的查询条件
    if gamecode == "Default":
      # 当gamecode为Default时,只查询用户个人的配置
      query = select(GameTranslation).where(
        GameTranslation.gamecode == gamecode,
        GameTranslation.company == user.username
      )
    else:
      # 原有逻辑,查询指定gamecode的所有配置
      query = select(GameTranslation).where(GameTranslation.gamecode == gamecode)

    # 查询总记录数
    total_query = await db.execute(select(func.count()).select_from(query.subquery()))
    total = total_query.scalar()

    # 计算偏移量
    offset = (current - 1) * size

    # 查询当前页的数据
    result = await db.execute(
      query.offset(offset)
      .limit(size)
    )
    translation_records = result.scalars().all()

    response_list = []
    for record in translation_records:
      response_list.append({
        "id": record.id,
        "gamecode": record.gamecode,
        "en": record.en,
        "zh": record.zh,
        "update_time": str(record.update_time),
        "editor": record.editor
      })

    response_data = {
      "code": "0000",
      "data": {
        "records": response_list,
        "current": current,
        "size": size,
        "total": total
      },
      "msg": "读取预设成功"
    }

    return JSONResponse(content=response_data)
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in get_presets: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("读取预设失败") from e


class AddPresetsRequest(BaseModel):
  gamecode: str
  en: str
  zh: str
  company: Optional[str] = None


class SavePresetsRequest(BaseModel):
  id: int
  en: str
  zh: str


@router.post("/save_presets", tags=["text"])
async def save_presets(
  request: SavePresetsRequest,
  db: AsyncSession = Depends(get_db),
  Authorization: str = Header(...),
  user: User = Depends(get_request_user)
):
  try:
    # 获取用户信息
    result = await db.execute(select(User).filter(User.id == user.id))
    user = result.scalars().first()

    if user is None or user.status != 1:
      raise ClientVisibleException("账号已停用")

    # 获取当前翻译记录
    result = await db.execute(select(GameTranslation).filter(GameTranslation.id == request.id))
    translation_record = result.scalars().first()

    if not translation_record:
      raise ClientVisibleException("记录不存在")

    # 更新翻译和编辑者
    translation_record.zh = request.zh
    translation_record.en = request.en
    translation_record.editor = user.username  # 使用用户名更新编辑者字段

    # 更新记录
    db.add(translation_record)
    await db.commit()

    response_data = {
      "code": "0000",
      "data": {"zh": request.zh, "en": request.en},
      "msg": "预设修改成功"
    }

    return JSONResponse(content=response_data)
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in save_presets: {str(e)}\n堆栈信息: {error_stack}")
    await db.rollback()
    raise ClientVisibleException("预设修改失败") from e


@router.post("/add_presets", tags=["text"])
async def upload_presets(
  request: AddPresetsRequest,
  db: AsyncSession = Depends(get_db),
  Authorization: str = Header(...),
  user: User = Depends(get_request_user)  # 获取当前用户
):
  """
    用户上传预设
    """
  try:
    # 获取用户信息
    result = await db.execute(select(User).filter(User.id == user.id))
    user = result.scalars().first()

    if user is None or user.status != 1:
      raise ClientVisibleException("该账号已停用")

    # 确保 company 字段被正确设置
    if request.gamecode == "Default":
      request.company = user.username  # 设置公司为用户名
    else:
      request.company = user.company  # 设置公司为用户的公司信息

    # 创建新的翻译记录
    new_translation = GameTranslation(
      gamecode=request.gamecode,
      zh=request.zh,
      en=request.en,
      editor=user.username,  # 使用用户名作为编辑者
      company=request.company  # 确保使用正确的公司信息
    )
    db.add(new_translation)
    await db.commit()

    response_data = {
      "code": "0000",
      "data": {"message": "Presets uploaded successfully."},
      "msg": "预设添加成功"
    }

    return JSONResponse(content=response_data)
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in upload_presets: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("预设添加失败") from e


@router.post("/delete_presets", tags=["text"])
async def delete_presets(
  request: DeletePresetsRequest,
  db: AsyncSession = Depends(get_db),
  Authorization: str = Header(...),
  user: User = Depends(get_request_user)  # 获取当前用户
):
  """
    用户删除预设
    """
  try:
    result = await db.execute(select(GameTranslation).filter(
      GameTranslation.id == request.id,
      GameTranslation.company == user.username  # 确保是用户的配置
    ))
    translation_record = result.scalars().first()

    if not translation_record:
      raise ClientVisibleException("预设不存在")

    await db.delete(translation_record)
    await db.commit()

    return JSONResponse(content={"data": {}, "code": "0000", "msg": "预设删除成功"})
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in delete_presets: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("预设删除失败") from e


@router.get("/search_presets", response_model=PaginatedResponse, tags=["text"])
async def search_presets(
  gamecode: Optional[str] = Query(None),
  zh: Optional[str] = Query(None),
  en: Optional[str] = Query(None),
  page: int = Query(1, alias="current"),
  size: int = Query(10),
  db: AsyncSession = Depends(get_db)
):
  """
    查询翻译预设数据
    """
  query = select(GameTranslation).where(GameTranslation.gamecode == gamecode)

  if zh:
    query = query.where(GameTranslation.zh.like(f"%{zh}%"))
  if en:
    query = query.where(GameTranslation.en.like(f"%{en}%"))

  total_query = select(func.count()).select_from(query.subquery())

  result = await db.execute(query.offset((page - 1) * size).limit(size))
  records = result.scalars().all()

  total_result = await db.execute(total_query)
  total = total_result.scalar()

  records_out = [GameTranslationOut.from_orm(record) for record in records]

  return PaginatedResponse(
    data=PaginatedData(
      records=records_out,
      current=page,
      size=size,
      total=total
    ),
    code="0000",
    msg="搜索成功"
  )


@router.post("/upload_translation_presets", tags=["text"])
async def upload_translation_presets(
  file: UploadFile = File(...),
  gamecode: str = Form(...),
  db: AsyncSession = Depends(get_db),
  Authorization: str = Header(...)
):
  """
    上传翻译预设表格，检查表格格式并添加到数据库中
    """
  try:
    logger.info(f"Received file: {file.filename}, game_code: {gamecode}")

    # 读取表格内容
    if file.content_type == 'text/csv':
      df = pd.read_csv(file.file)
    else:
      df = pd.read_excel(file.file)

    # 检查表格标题
    required_columns = ['原文', '翻译']
    if not all(column in df.columns for column in required_columns):
      raise ClientVisibleException("请规定标题列`原文`，`翻译`")

    # 提取表格内容
    translations = []
    for _, row in df.iterrows():
      translations.append({
        'zh': row['原文'],
        'en': row['翻译']
      })

    # 从Authorization头部提取token
    token = Authorization.split(" ")[1]
    token_key = get_token_key(token)
    userid = await redis.get(token_key)

    if userid is None:
      raise ClientVisibleException("权限不足")

    userid = int(userid.decode("utf-8"))
    result = await db.execute(select(User).filter(User.id == userid))
    user = result.scalars().first()

    if user is None or user.status != 1:
      raise ClientVisibleException("该账号已停用")

    # 确定company字段的值
    # 当gamecode为"Default"时，使用username作为company
    company = user.username if gamecode == "Default" else user.company

    # 将翻译内容添加到数据库
    for translation in translations:
      new_translation = GameTranslation(
        gamecode=gamecode,
        zh=translation['zh'],
        en=translation['en'],
        editor=user.username,
        company=company  # 使用根据条件确定的company值
      )
      db.add(new_translation)

    await db.commit()

    return JSONResponse(content={"data": {"message": "预设上传成功"}, "code": "0000", "msg": "预设上传成功"})
  except Exception as e:
    error_stack = traceback.format_exc()
    logger.error(f"Error in upload_translation_presets: {str(e)}\n堆栈信息: {error_stack}")
    raise ClientVisibleException("预设上传失败") from e


@router.get("/get_supported_languages", tags=["text"])
async def get_supported_languages():
    """
    获取支持的翻译语言列表
    """
    try:
        languages = []
        for key, value in translation_dict.items():
            language_info = {
                "label": key, 
                "value": value["language"], 
                "audio": value["audio"],
                "voice_type": value["voice_type"],
                "langcode": value["langcode"]
            }
            languages.append(language_info)
        return JSONResponse(content={"code": "0000", "data": languages, "msg": "获取语言列表成功"})
    except Exception as e:
        logger.error(f"Error in get_supported_languages: {str(e)}")
        raise ClientVisibleException("获取语言列表失败") from e


@router.get("/get_supported_models", tags=["text"])
async def get_supported_models():
    """
    获取支持的翻译模型列表
    """
    try:
        models_list = [{"label": model_info["label"], "value": model_info["value"]} for model_name, model_info in models_dict.items()]
        return JSONResponse(content={"code": "0000", "data": models_list, "msg": "获取模型列表成功"})
    except Exception as e:
        logger.error(f"Error in get_supported_models: {str(e)}")
        raise ClientVisibleException("获取模型列表失败") from e
