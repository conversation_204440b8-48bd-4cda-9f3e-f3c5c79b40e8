import hmac
import logging
from datetime import timedelta, datetime

from asgi_user_agents import UADetails
from fastapi import Header, Request, Depends
from typing import Optional, Annotated

from fastapi_limiter.depends import RateLimiter

from config import app_settings
from models.user import get_request_user, User, get_token_key
from security import is_non_validate_route
from utils.exceptions import ClientVisibleException
from utils.redis import redis

logger = logging.getLogger(__name__)


async def get_token_header(
    x_token: str = Header(),
    user: User = Depends(get_request_user),
    request: Request = None,
):
    logger.info(
        "[%s](%s) request: %s" % (user.username, request.state.client_ip, request.url)
    )


# 获得客户端IP
async def get_real_ip(request: Request, x_forwarded_for: Optional[str] = Header(None)) -> str:
    # 尝试从 X-Forwarded-For 头部获取 IP
    client_ip = x_forwarded_for.split(",")[0].strip() if x_forwarded_for else None

    # 如果没有 X-Forwarded-For 头部或不可信，则使用 request.client.host
    if not client_ip or not is_trusted_proxy(request.client.host):
        client_ip = request.client.host
    request.state.client_ip = client_ip
    return client_ip


def is_trusted_proxy(ip_address):
    # 这里应该实现一个逻辑来判断 IP 地址是否来自一个可信的代理
    # 这可能涉及检查 IP 地址是否在一个已知的可信代理列表中
    # 示例中简单地返回 True，实际使用中应替换为实际的检查逻辑
    return True


allow_browser_families = {
    "Chrome",  # Chrome 族，包括 Chromium
    "Chrome Mobile",  # Android 端 Chrome
    "Chrome Mobile iOS",  # iPad 端 Chrome
    "Firefox",  # 火狐浏览器
    "Firefox Mobile",  # 火狐浏览器移动端
    "Safari",  # 苹果 Safari 浏览器
    "Mobile Safari",  # iOS 端 Safari 浏览器
    "Opera",  # Opera 浏览器
    "Edge",  # 微软 Edge 浏览器
    "Edge Mobile",  # 微软 Edge 浏览器移动端
    "IE",  # Internet Explorer
    "QQ Browser",  # QQ 浏览器
    "UC Browser",  # UC 浏览器
    "Maxthon",  # 遨游浏览器
    "Sogou Explorer",  # 搜狗浏览器
    "MiuiBrowser",  # 小米浏览器
    "HuaweiBrowser",  # 华为浏览器
}
allow_os_families = {
    "Windows",
    "Linux",
    "Mac OS X",
    "iOS",
    "Android",
    "HarmonyOS",
}


def ua_checker(request: Request, client_ip: Annotated[str, Depends(get_real_ip)]) -> str:
    """
    从 user-agents 中间件中获取 UA 信息，并判断合法性，同时返回 UA 供之后的流程使用
    """
    ua: UADetails = request.scope["ua"]
    if is_non_validate_route(request.url.path):
        return ua.ua_string
    # 错误的原因模糊处理，不要将真正的原因暴露出去
    err = ClientVisibleException("请求失败")
    if not ua.is_provided:
        logger.warning(f'no user-agent provided, IP: {client_ip}')
        raise err
    if ua.is_bot:
        logger.warning(f'bot detected: {ua.ua_string}, IP: {client_ip}')
        raise err
    if ua.browser.family not in allow_browser_families:
        logger.warning(f'unauthorized browser detected: {ua.ua_string}, IP: {client_ip}')
        raise err
    if ua.os.family not in allow_os_families:
        logger.warning(f'unauthorized os detected: {ua.ua_string}, IP: {client_ip}')
        raise err
    return ua.ua_string

def ip_rate_limiter(times: int, duration: timedelta):
    """基于 IP 的限流器"""
    return RateLimiter(times=times, seconds=int(duration.total_seconds()), identifier=_rl_ip_identifier)

def userid_rate_limiter(times: int, duration: timedelta):
    """基于 userid 的限流器"""
    return RateLimiter(times=times, seconds=int(duration.total_seconds()), identifier=_rl_userid_identifier)

async def _rl_ip_identifier(request: Request):
    """限流中中间基于 IP 的标识"""
    client_ip = request.state.client_ip
    if not client_ip:
        raise ClientVisibleException("请求失败")
    path = request.url.path
    return f"{path}:{client_ip}"


async def _rl_userid_identifier(request: Request):
    """限流中中间基于 userid 的标识"""
    err = ClientVisibleException("请求失败")
    authorization = request.headers.get('Authorization')
    if not authorization:
        raise err
    arr = authorization.split()
    if len(arr) != 2 or arr[0].lower() != "bearer":
        raise err
    token = arr[1]
    token_key = get_token_key(token)
    userid = await redis.get(token_key)
    if not userid:
        raise err
    # 这里就不连接数据库校验 userid 是否合法了
    # 因为这里并不负责 userid 的合法性，如果接口需要校验 userid 的合法性，则应当自行引入校验合法性的依赖
    userid_str = userid.decode("utf-8")
    path = request.url.path
    return f'{path}:{userid_str}'


# 签名时间戳的最大时间
SIGN_MAX_TS = datetime.fromisoformat('2099-12-31T23:59:59+08:00').timestamp()

def signature_checker(
    timestamp: Annotated[int, Header(alias="X-Timestamp", gt=0, lt=SIGN_MAX_TS, description="UNIX 时间戳")],
    signature: Annotated[str, Header(alias="X-Signature", min_length=1)]
):
    sign_dt = datetime.fromtimestamp(timestamp)
    now = datetime.now()
    delta = now - sign_dt
    if delta > timedelta(seconds=app_settings.sign_ttl):
        logger.info(f"时间戳校验失败: {timestamp}，差值：{delta.total_seconds()}")
        raise ClientVisibleException("请求失败")
    # 将时间戳转换成字节数据，按小端序排列，总共 8 bytes / 64 bits
    # 使用小端序是因为大端序是很多编程接口的默认值，这里稍稍增加一点门槛
    tb = timestamp.to_bytes(8, "little")
    hmac_obj = hmac.new(app_settings.sign_key.encode("utf-8"), tb, digestmod="md5")
    if hmac_obj.hexdigest() != signature:
        logger.info(f"签名校验失败: {timestamp} {signature}")
        raise ClientVisibleException("请求失败")
    return sign_dt
