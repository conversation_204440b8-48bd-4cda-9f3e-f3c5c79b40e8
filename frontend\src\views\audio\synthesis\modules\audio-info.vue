<script lang="ts" setup>
import { computed } from 'vue';

// 定义 props
const props = defineProps<{
  audioData?: any;
}>();

// 定义 emit
const emit = defineEmits<{
  'go-back': [];
  'play-audio': [item: any];
  'reuse-settings': [audioData: any];
}>();

// 返回历史记录
const handleGoBack = () => {
  emit('go-back');
};

// 播放音频
const handlePlayAudio = () => {
  if (props.audioData) {
    emit('play-audio', props.audioData);
  }
};

// 复用设置
const handleReuseSettings = () => {
  if (props.audioData) {
    emit('reuse-settings', props.audioData);
  }
};

// 计算属性
const audioSegments = computed(() => {
  if (!props.audioData?.params?.segs) return [];
  return props.audioData.params.segs;
});

const audioInfo = computed(() => {
  if (!props.audioData) return null;
  return {
    duration: props.audioData.duration || '00:00',
    createTime: props.audioData.createTime || '',
    status: props.audioData.status || 'UNKNOWN'
  };
});

// 获取模型显示名称
const getModelDisplayName = (modelFunction: string) => {
  switch (modelFunction) {
    case 'fast_clone':
      return 'GPT-SoVITS 极速复刻';
    case 'cosy_pretrained':
      return 'CosyVoice 预训练音色';
    case 'chat_tts':
      return 'Chat TTS 语音合成';
    case 'gpt_sovits_tts':
      return 'GPT-SoVITS 语音合成';
    case 'volcengine':
      return 'Volcengine 语音合成';
    default:
      return modelFunction || '未知模型';
  }
};

// 播放状态
const isPlaying = computed(() => {
  return props.audioData?.isPlaying || false;
});
</script>

<template>
  <NFlex vertical class="h-full w-full p-3">
    <!-- 头部 返回按钮 标题 -->
    <NFlex>
      <NButton text @click="handleGoBack">
        <SvgIcon icon="lets-icons:back" class="mr-3 text-2xl" />
      </NButton>
      <NText class="text-lg">返回队列历史</NText>
    </NFlex>

    <!-- 音频生成信息  单/多段生成  -->
    <div v-if="audioSegments.length > 0">
      <NFlex v-for="(segment, index) in audioSegments" :key="index" class="mt-4">
        <NFlex justify="space-between" align="center" :wrap="false" class="w-full">
          <NText>{{ segment.model_name || `音色 ${index + 1}` }}</NText>
          <NFlex :wrap="false">
            <NText>{{ getModelDisplayName(segment.model_function) }}</NText>
          </NFlex>
        </NFlex>
        <NDivider class="title_info" />
        <NText>
          {{ segment.text || '无文本内容' }}
        </NText>
      </NFlex>
    </div>

    <!-- 如果没有分段信息，显示默认内容 -->
    <NFlex v-else class="mt-4">
      <NFlex justify="space-between" align="center" :wrap="false" class="w-full">
        <NText>音频信息</NText>
        <NFlex :wrap="false">
          <NText>{{ audioData?.model || '未知模型' }}</NText>
        </NFlex>
      </NFlex>
      <NDivider class="title_info" />
      <NText>
        {{ audioData?.text || '无文本内容' }}
      </NText>
    </NFlex>

    <!-- 时长 和  任务生成时间 -->
    <NFlex class="mt-2 w-full" align="center">
      <NText class="text-[0.9em]">{{ audioInfo?.duration || '00:00' }}</NText>
      <NDivider vertical />
      <NText class="text-[0.9em]">{{ audioInfo?.createTime || '未知时间' }}</NText>
    </NFlex>

    <!-- 操作按钮 -->
    <NFlex class="mt-4" :wrap="false" align="center">
      <NButton v-if="audioData?.status === 'SUCCESS'" class="w-35" type="info" @click="handlePlayAudio">
        <SvgIcon :icon="isPlaying ? 'gridicons:pause' : 'lsicon:play-filled'" class="mr-3 text-2xl" />
        {{ isPlaying ? '暂停' : '播放' }}
      </NButton>

      <NButton v-if="audioData?.status === 'SUCCESS'" class="w-35" type="info" @click="handleReuseSettings">
        <SvgIcon icon="ph:link-break" class="text-2xl" />
        复用设置
      </NButton>

      <!-- 如果音频状态不是成功，显示状态信息 -->
      <NText v-else-if="audioData?.status" class="text-gray-500">
        音频状态：{{
          audioData.status === 'FAILURE'
            ? '生成失败'
            : audioData.status === 'IN_PROGRESS'
              ? '生成中'
              : audioData.status === 'SUBMITTED'
                ? '排队中'
                : audioData.status
        }}
      </NText>
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
:deep(.title_info) .n-divider__line {
  height: 3px !important;
}

:deep(.title_info) {
  gap: 0px !important;
  margin: 0px !important;
}
</style>
