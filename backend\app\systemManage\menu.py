import logging

from fastapi import APIRouter, Depends, Body, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, delete,asc

from models.users import get_request_user, User, get_roles_checker
from utils.database import get_db
from pydantic import BaseModel
from typing import List,Optional
import datetime
from models.user_role import UserRole
from models.menu import Menu
from models.roles import Role
from models.role_menu import RoleMenu
import json

from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


# 返回数据
class ItemRes(BaseModel):
  id:int
  parentId:int
  menuName:str
  routeName:str
  menuType:str
  routePath:str
  component:str
  i18nKey:str
  icon:str
  href:str
  activeMenu:str
  iconType:str
  keepAlive:bool
  constant:bool
  order:int
  hideInMenu:bool
  multiTab:bool
  fixedIndexInTab:int
  # query:str
  # buttons:str
  status:str
  # createBy:str
  # createTime:datetime.datetime
  # updateBy:str
  # updateTime:datetime.datetime
  # class Config:
    # from_attributes = True
  children:List['ItemRes']=[]

# 静态路由
class ConstRouteItemRes(BaseModel):
  id:int=0
  name: str=''
  path: str=''
  component: str=''
  children:List['ConstRouteItemRes']=[]

class PaginatedData(BaseModel):
  records: List[ItemRes]

# tre
class TreeData(BaseModel):
  records: List[ItemRes]

class MenuTreeResponse(BaseModel):
  data: TreeData
  code: str

class PaginatedResponse(BaseModel):
  data: PaginatedData
  code: str


class QueryParams(BaseModel):
  key:str
  value:str

class ButtonsParams(BaseModel):
  code:str
  desc:str

class MenuTree(BaseModel):
  id:int
  label:str
  i18nKey:str
  pId:int
  children:Optional[List['MenuTree']]=None

class SaveMenuRequest(BaseModel):
  id: int = 0
  parentId:int
  menuName:str
  routeName:str
  menuType:int
  routePath:str
  component:str
  i18nKey:str
  icon:str
  href:str
  activeMenu:str
  iconType:int
  keepAlive:int
  constant:int
  order:int
  hideInMenu:int
  multiTab:int
  fixedIndexInTab:int
  status:int
  query:list=[]
  buttons:list=[]

# 一个vue参识别的route

class VueRouteMate(BaseModel):
  title:str=''
  i18nKey:str=''
  icon:str=''
  # order:int=0
  href:str=''
  activeMenu:str=''
  # fixedIndexInTab:int
  hideInMenu:bool=False
  keepAlive:bool=False
  multiTab:bool=False

class VueRouteItem(BaseModel):
  id:int=0
  parentId:int=0
  name:str=''
  path:str=''
  component:str=''
  meta:VueRouteMate
  children:List['VueRouteItem']=[]


async def get_total_count(session: AsyncSession, model):
  result = await session.execute(select(func.count(model.id)))
  return result.scalar()
def generate_tree(source,parent,cache=[]):
  tree = []
  for item in source:
    if item.id in cache:
      continue
    if item.parentId == parent:
      item.children = generate_tree(source, item.id,cache)
      tree.append(item)
  return tree

def generate_tree2(source,parent,cache=[]):
  tree = []
  for item in source:
    if item.id in cache:
      continue
    if item.pId == parent:
      children=generate_tree2(source, item.id,cache)
      if len(children)>0:
        item.children = children
      tree.append(item)
  return tree


@router.get("/getAllPages", tags=["system"])
async def getAllPages(db: AsyncSession = Depends(get_db)):
  # 返回所以route name
  return {
    "data": [
      "home",
      "403",
      "404",
      "405",
      "function_multi-tab",
      "function_tab",
      "exception_403",
      "exception_404",
      "exception_500",
      "multi-menu_first_child",
      "multi-menu_second_child_home",
      "manage_user",
      "manage_role",
      "manage_menu",
      "manage_user-detail",
      "about"
    ],
    "code": "0000",
    "msg": "请求成功"
  }

@router.get("/get_menu", response_model=PaginatedResponse,tags=["system"])
async def get_menu(
  db: AsyncSession = Depends(get_db)
):
  """
    获取列表数据
  """
  async with db as session:
    result = await session.execute(
      select(Menu).order_by( asc(Menu.parentId), asc(Menu.order))
    )
    records = result.scalars().all()
    item_res=[]
    for item in records:
      item_res.append(
        ItemRes(
          id=item.id,
          parentId=item.parentId,
          menuName=item.menuName,
          routeName=item.routeName,
          menuType=str(item.menuType),
          routePath=item.routePath,
          component=item.component,
          i18nKey=item.i18nKey,
          icon=item.icon,
          href=item.href,
          activeMenu=item.activeMenu,
          iconType=str(item.iconType),
          keepAlive=bool(item.keepAlive),
          constant=bool(item.constant),
          hideInMenu=bool(item.hideInMenu),
          multiTab=bool(item.multiTab),
          order=item.order,
          fixedIndexInTab=item.fixedIndexInTab,
          status=str(item.status),
        )
      )

    tree=generate_tree(item_res,0)
    return MenuTreeResponse(
      data=TreeData(
        records=tree,
      ),
      code="0000"
    )

@router.get("/get_menu_tree",tags=["system"])
async def get_menu_tree(
  db: AsyncSession = Depends(get_db)
):
  """
    获取可用的菜单树数据
  """
  async with db as session:
    result = await session.execute(
      select(Menu).where(Menu.status==1).order_by( asc(Menu.parentId), asc(Menu.order))
    )
    records = result.scalars().all()
    item_res=[]
    for item in records:
      item_res.append(
        MenuTree(
          id=item.id,
          label=item.menuName,
          i18nKey=item.i18nKey,
          pId=item.parentId,
          children=None
        )
      )
    tree=generate_tree2(item_res,0)
    return {
      "code":"0000",
      "data":tree
    }

@router.get("/get_user_menu",tags=["system"])
async def get_user_menu(
  user: User = Depends(get_request_user),
  db: AsyncSession = Depends(get_db)
):
  """
    获取用户菜单
  """
  user_id = user.id
  async with db as session:
    roleModel = await session.execute(
      select(Role)
      .join(UserRole, Role.id == UserRole.role_id,isouter=True)
      .where(Role.status==1)
      .where(UserRole.user_id==user_id)
    )
    roleList = roleModel.scalars().all()
    if len(roleList) == 0:
      # 没有角色，直达403页面
      return {
      "code":"0000",
        "data":{
          "routes":[],
          "home":'403'
        }
      }
    roleIds:list[int] =  list( map(lambda x: x.id, roleList) )

    # todo 如果是超级管理员，返回所有菜单
    query=select(Menu).where(Menu.status==1).distinct(Menu.id).order_by( asc(Menu.parentId), asc(Menu.order))
    if 1 in roleIds:
      pass
    else:
      # 获取用户角色对应的菜单
      query = query.join(RoleMenu, Menu.id == RoleMenu.menu_id,isouter=True).where(RoleMenu.role_id.in_(roleIds))

    result   = await session.execute(query)
    records  = result.scalars().all()
    item_res = []
    # format
    for item in records:
      item_res.append(
        VueRouteItem (
          id        = item.id,
          parentId  = item.parentId,
          name      = item.routeName,
          path      = item.routePath,
          component = item.component,
          meta=VueRouteMate(
            title           = item.menuName,
            i18nKey         = item.i18nKey,
            icon            = item.icon,
            order           = item.order,
            href            = item.href,
            activeMenu      = item.activeMenu,
            hideInMenu      = bool(item.hideInMenu),
            keepAlive       = bool(item.keepAlive),
            multiTab        = bool(item.multiTab),
            fixedIndexInTab = item.fixedIndexInTab,
          )
          # routeName       = item.routeName,
          # menuType        = str(item.menuType),
          # i18nKey         = item.i18nKey,
          # icon            = item.icon,
          # href            = item.href,
          # iconType        = str(item.iconType),
          # keepAlive       = bool(item.keepAlive),
          # constant        = bool(item.constant),
          # hideInMenu      = bool(item.hideInMenu),
          # multiTab        = bool(item.multiTab),
          # order           = item.order,
          # fixedIndexInTab = item.fixedIndexInTab,
          # status          = str(item.status),
        )
      )

    tree = generate_tree(item_res,0)
    tree.append({
      "name": "user-center",
      "path": "/user-center",
      "component": "layout.base$view.user-center",
      "meta": {
        "title": "个人中心",
        "i18nKey": "route.user-center",
        "icon": "tabler:home-search",
        "href": "",
        "activeMenu": "",
        "hideInMenu": True,
        "keepAlive": False,
        "multiTab": False
      }
    })
    home = [item_res[0]]
    return {
      "code":"0000",
      "data":{
        "routes":tree,
        "home":item_res[0].name
      }
    }

@router.get("/get_const_menu",tags=["system"])
async def get_const_menu(
  db: AsyncSession = Depends(get_db)
):
  """
    获取静态菜单
  """
  async with db as session:
    result   = await session.execute(
      select(Menu)
      .where(Menu.constant==1)
      .order_by( asc(Menu.parentId), asc(Menu.order))
    )
    records  = result.scalars().all()
    item_res = []
    # format
    # for item in records:
    #   item_res.append(
    #     ConstRouteItemRes(
    #       id=item.id,
    #       name=item.routeName,
    #       component=item.component,
    #     )
    #   )
    for number in range(1, 2):
      item_res.append(
        ConstRouteItemRes(
          # id=item.id,
          name='login',
          path='/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
          component='layout.blank$view.login',
        )
      )
    return {
      "code":"0000",
      "data":[
        {
            "name": "403",
            "path": "/403",
            "component": "layout.blank$view.403",
            "meta": {
                "title": "403",
                "i18nKey": "route.403",
                "constant": True,
                "hideInMenu": True
            }
        },
        {
            "name": "404",
            "path": "/404",
            "component": "layout.blank$view.404",
            "meta": {
                "title": "404",
                "i18nKey": "route.404",
                "constant": True,
                "hideInMenu": True
            }
        },
        {
            "name": "500",
            "path": "/500",
            "component": "layout.blank$view.500",
            "meta": {
                "title": "500",
                "i18nKey": "route.500",
                "constant": True,
                "hideInMenu": True
            }
        },
        {
            "name": "iframe-page",
            "path": "/iframe-page/:url",
            "component": "layout.base$view.iframe-page",
            "props": True,
            "meta": {
                "title": "iframe-page",
                "i18nKey": "route.iframe-page",
                "constant": True,
                "hideInMenu": True,
                "keepAlive": True
            }
        },
        {
            "name": "login",
            "path": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
            "component": "layout.blank$view.login",
            "props": True,
            "meta": {
                "title": "login",
                "i18nKey": "route.login",
                "constant": True,
                "hideInMenu": True
            }
        },
        {
            "name": "share",
            "path": "/share-page",
            "component": "layout.blank$view.share-page",
            "props": True,
            "meta": {
                "title": "share",
                "i18nKey": "route.share-page",
                "constant": True,
                "hideInMenu": True
            }
        }
      ],
    }
    # tree=generate_tree(item_res,0)
    # return MenuTreeResponse(
    #   data=TreeData(
    #     records=item_res,
    #   ),
    #   code="0000"
    # )


@router.post("/save_menu", tags=["system"], dependencies=[Depends(get_roles_checker('super_admin'))])
async def save_menu(
  save_data: SaveMenuRequest,
  request: Request,
  db: AsyncSession = Depends(get_db),
):
  """
    保存菜单数据
  """
  # return save_data
  try:
    if save_data.hideInMenu==0:
      save_data.activeMenu=''

    async with db as session:

      if save_data.id > 0 :
        logger.debug("save menu")
        # 更新
        result = await session.execute( select(Menu).where(Menu.id == save_data.id) )
        data_in_db = result.scalars().first()
        if not data_in_db:
          raise ClientVisibleException("菜单不存在")
        # data_in_db.parentId=save_data.parentId,
        data_in_db.menuName=save_data.menuName
        data_in_db.routeName=save_data.routeName
        data_in_db.menuType=save_data.menuType
        data_in_db.routePath=save_data.routePath
        data_in_db.component=save_data.component
        data_in_db.i18nKey=save_data.i18nKey
        data_in_db.icon=save_data.icon
        data_in_db.href=save_data.href
        data_in_db.activeMenu=save_data.activeMenu
        data_in_db.iconType=save_data.iconType
        data_in_db.keepAlive=save_data.keepAlive
        data_in_db.constant=save_data.constant
        data_in_db.order=save_data.order
        data_in_db.hideInMenu=save_data.hideInMenu
        data_in_db.multiTab=save_data.multiTab
        data_in_db.fixedIndexInTab=save_data.fixedIndexInTab
        data_in_db.status=save_data.status
        data_in_db.query=json.dumps(save_data.query)
        data_in_db.buttons=json.dumps(save_data.buttons)
        data_in_db.updateBy=request.state.user.username
        data_in_db.updateTime=datetime.datetime.now()
      else:
        # 创建
        new_data = Menu (
          parentId=save_data.parentId,
          menuName=save_data.menuName,
          routeName=save_data.routeName,
          menuType=save_data.menuType,
          routePath=save_data.routePath,
          component=save_data.component,
          i18nKey=save_data.i18nKey,
          icon=save_data.icon,
          href=save_data.href,
          activeMenu=save_data.activeMenu,
          iconType=save_data.iconType,
          keepAlive=save_data.keepAlive,
          constant=save_data.constant,
          order=save_data.order,
          hideInMenu=save_data.hideInMenu,
          multiTab=save_data.multiTab,
          fixedIndexInTab=save_data.fixedIndexInTab,
          status=save_data.status,
          query=json.dumps(save_data.query),
          buttons=json.dumps(save_data.buttons),
          createBy=request.state.user.username,
          updateBy=request.state.user.username,
          createTime=datetime.datetime.now(),
          updateTime=datetime.datetime.now(),
        )
        session.add(new_data)
        save_data.id=new_data.id

      await session.commit()

      return {"code": "0000", "msg": "提交成功"}

  except Exception as e:
    logger.error(f"Failed to update menu: {e}")
    raise ClientVisibleException("提交失败") from e

@router.post("/del_menu", tags=["system"], dependencies=[Depends(get_roles_checker('super_admin'))])
async def del_menu(
  id: int = Body(0,alias='id',embed=True),
  db: AsyncSession = Depends(get_db)
):
  """
  删除数据
  """
  async with db as session:
    await session.execute( delete(Menu).where(Menu.id == id))
    await session.commit()

    return JSONResponse(content={"data": {}, "code": "0000", "msg": "删除成功"})
