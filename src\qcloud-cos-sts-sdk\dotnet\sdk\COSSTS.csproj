<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net45;netstandard2.0</TargetFrameworks>
    <RootNamespace>COSSTS</RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>COSSTS</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>

    <PackageId>Tencent.QCloud.Cos.Sts.Sdk</PackageId>
    <Version>3.0.5</Version>
    <Authors>Tencent</Authors>
    <Company>Tencent</Company>
    <description>Tencent Cloud COS(Cloud Object Service) STS .Net SDK</description>
    <RepositoryUrl>https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/nodejs</RepositoryUrl>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Release\COSXML.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="TencentCloudSDK" Version="3.0.*" />
  </ItemGroup>

</Project>
