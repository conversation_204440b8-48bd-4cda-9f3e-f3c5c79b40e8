<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { NCard, NSlider } from 'naive-ui';

export default defineComponent({
  name: 'AudioPlayer',
  components: { <PERSON>ard, NSlider },
  props: {
    src: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const audioElement = ref<HTMLAudioElement | null>(null);
    const isPlaying = ref(false);
    const currentTime = ref(0);
    const duration = ref(0);
    const volume = ref(1);

    const togglePlay = () => {
      if (audioElement.value) {
        if (isPlaying.value) {
          audioElement.value.pause();
        } else {
          audioElement.value.play();
        }
        isPlaying.value = !isPlaying.value;
      }
    };

    const updateTime = () => {
      if (audioElement.value) {
        currentTime.value = audioElement.value.currentTime;
      }
    };

    const seek = (time: number) => {
      if (audioElement.value) {
        audioElement.value.currentTime = time;
      }
    };

    const updateVolume = (vol: number) => {
      if (audioElement.value) {
        audioElement.value.volume = vol;
      }
    };

    const loadedMetadata = () => {
      if (audioElement.value) {
        duration.value = audioElement.value.duration;
      }
    };

    const formatTime = (time: number) => {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    const downloadAudio = () => {
      const link = document.createElement('a');
      link.href = props.src;
      link.download = 'audio.mp3';
      link.click();
    };

    // Watch for changes in the src prop
    watch(
      () => props.src,
      newSrc => {
        if (audioElement.value) {
          audioElement.value.src = newSrc;
          audioElement.value.load();
          currentTime.value = 0;
          isPlaying.value = false;
        }
      }
    );

    return {
      audioElement,
      isPlaying,
      currentTime,
      duration,
      volume,
      togglePlay,
      updateTime,
      seek,
      updateVolume,
      loadedMetadata,
      formatTime,
      downloadAudio
    };
  }
});
</script>

<template>
  <NCard title="音频播放器">
    <audio ref="audioElement" :src="src" @timeupdate="updateTime" @loadedmetadata="loadedMetadata">
      Your browser does not support the audio element.
    </audio>
    <div class="controls">
      <button @click="togglePlay">{{ isPlaying ? '暂停' : '播放' }}</button>
      <NSlider v-model:value="currentTime" :max="duration" @update:value="seek" />
      <span>{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
      <NSlider v-model:value="volume" :max="1" :step="0.01" @update:value="updateVolume" />
      <button @click="downloadAudio">下载</button>
    </div>
  </NCard>
</template>

<style scoped>
.controls {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
