import logging

from .chat import client
import re
import asyncio
from openai import AsyncOpenAI
from config import app_settings

logger = logging.getLogger(__name__)


def create_image(prompt: str, model: str, size: str, number: int, image_base64: str):
    # 准备API请求的负载
    image_payload = {"prompt": prompt, "n": number, "model": model, "size": size}

    # 如果有提供图像数据，则添加到请求负载中
    if image_base64:
        image_payload["image"] = image_base64

    # 发起API请求
    try:
        response = client.images.generate(**image_payload)
        # 处理返回的ImagesResponse，转换成指定格式
        formatted_response = {
            "data": [{"url": img.url} for img in response.data],
            "created": response.created,
        }
        return formatted_response
    except Exception as e:
        error_message = f"图像创作失败: {str(e)}"
        logger.error(error_message)
        raise Exception(error_message)
    


# 从 content 中匹配下载链接的正则表达式模式
patterns = (
    re.compile(r"^!\[\S+]\((https://\S+)\)$", re.MULTILINE),
    re.compile(r"^\[点击下载]\((https://\S+)\)$", re.MULTILINE),
)

async def create_image_with_gpt(
        prompt: str, 
        image_urls: list, 
        model: str = "gpt-4o-image-vip", 
        # n: int = 1
    ):
    """
    使用GPT模型创建图像的逆向接口
    
    Args:
        prompt: 提示文本
        image_urls: 图片URL列表，可以包含多张图片
        model: 使用的模型名称
    
    Returns:
        str: 生成的图片URL
    
    Raises:
        ValueError: 当没有返回数据或无法提取图片URL时
        TimeoutError: 当API请求超时时
        Exception: 其他错误
    """
    try:
        # 创建AsyncOpenAI客户端
        openai_api_key = app_settings.openai_image_api_key
        openai_base_url = app_settings.openai_image_api_base_url
        # logger.info(f"openai_api_key: {openai_api_key}")
        logger.info(f"openai_base_url: {openai_base_url}")
        # openai_base_url = "https://api.pumpkinaigc.online/v1"
            
        async_client = AsyncOpenAI(
            api_key=openai_api_key,
            base_url=openai_base_url,
        )
        
        # 构建消息内容
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt,
                    }
                ]
            }
        ]
        
        # 根据图片URL列表添加图片内容
        if isinstance(image_urls, list):
            for img_url in image_urls:
                if img_url:
                    messages[0]["content"].append(
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": img_url,
                            },
                        }
                    )
        
        # 发送请求
        res = await async_client.chat.completions.create(
            model=model,
            messages=messages,
            timeout=600, # 600秒超时
            stream=False,
            # n=n  # 生成图片数量
        )
        
        # 处理响应
        if not res.choices or len(res.choices) == 0:
            error_message = f"没有返回数据, res: {res}"
            logger.error(error_message)
            raise ValueError(error_message)
            
        content = res.choices[0].message.content

        
        # 从内容中提取图片URL
        match = None
        for pattern in patterns:
            match = pattern.search(content)
            if match:
                break
                
        if not match:
            error_message = f"未能从返回内容中提取图片URL, content: {content}"
            logger.error(error_message)
            raise ValueError(error_message)
            
        url = match.group(1)
        return url
        
    except asyncio.CancelledError as e:
        error_message = f"API请求超时: {str(e)}"
        logger.warning(error_message)
        raise TimeoutError(error_message) from e
    except Exception as e:
        error_message = f"图像创建失败"
        logger.error(f"OpenAI图像创建失败: {str(e)}")
        raise Exception(error_message)

