import json
import logging

import aiohttp
import random
import string
import datetime
import re
from config import endpoint_config
from utils.redis import redis
import ffmpeg
from base64 import b64decode


logger = logging.getLogger(__name__)


async def get_url_data(url):
    async def fetch(session, url):
        async with session.get(url) as response:
            return await response.text()

    async with aiohttp.ClientSession() as session:
        html = await fetch(session, url)
        return html


def generate_alphanumeric_random_string(length: int) -> str:
    """
    生成指定长度的随机字母数字字符串。

    Args:
      length: 字符串的长度。

    Returns:
      生成的随机字母数字字符串。
    """
    characters = string.ascii_letters + string.digits  # 包含字母和数字
    random_string = "".join(random.choice(characters) for _ in range(length))
    return random_string


def contains_chinese(string):
    """检查字符串是否包含中文字符"""
    return bool(re.search(r"[\u4e00-\u9fff]+", string))


def uncamelize(camel_caps, separator="_"):
    """
    Convert camel case string to lower case string with a separator.

    Args:
    camel_caps (str): The camel case string to convert.
    separator (str): The separator between words.

    Returns:
    str: Lowercase string with separator.
    """
    # 使用正则表达式分割大写字母，并在前面加上分隔符
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1" + separator + r"\2", camel_caps)
    # 如果有连绌的大写字母，进行类似处理
    result = re.sub("([a-z-9])([A-Z])", r"\1" + separator + r"\2", s1).lower()
    return result


def timestamp_to_string(timestamp: int | None) -> str:
    if not timestamp:
        return None
    if timestamp > 9999999999:
        timestamp = timestamp / 1000
    # 将时间戳转为 datetime 对象
    dt_object = datetime.datetime.fromtimestamp(timestamp)
    # 将 datetime 对象格式化为字符串，格式为 'yyyy-mm-dd hh:mm:ss'
    formatted_time = dt_object.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_time


async def add_task(task_data):
    """
    向 Redis Stream 队列发送任务消息
    """
    # print("开始add_task逻辑")
    task_data_str = {k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) for k, v in task_data.items()}
    await redis.xadd(endpoint_config["queue"]["name"], task_data_str)


async def convert_to_mp3(input_file: str, output_file: str,_ab:string ='192k') -> None:
  try:
    # 使用ffmpeg将转换为mp3
    ffmpeg.input(input_file).output(output_file, format='mp3', acodec='libmp3lame', ab=_ab).run(overwrite_output=True)
    logger.error(f"转换成功：{output_file}")
  except ffmpeg.Error as e:
    logger.error(f"转换失败：{e}")
def seconds_to_hhmmss(seconds):
    """
    将秒数转换为 hh:mm:ss 格式.

    :param seconds: int, 以秒为单位的时间长度
    :return: str, 以 hh:mm:ss 格式表示的时间
    """
    # 利用 timedelta 对象处理秒数
    time_delta = datetime.timedelta(seconds=seconds)
    # 将 timedelta 对象转换为字符串，提取 HOURS:MINUTES:SECONDS 部分
    hh_mm_ss = str(time_delta)

    # 如果是超过24小时，str(time_delta) 会有类似 '1 day, 01:23:45'
    # 我们只需要 HOURS:MINUTES:SECONDS 部分
    if 'day' in hh_mm_ss:
        hh_mm_ss = hh_mm_ss.split(', ')[-1]

    return hh_mm_ss

async def extract_to_mp3(input_file: str, output_file: str,start_time:float=0,end_time:float=0,_ab:string ='192k') -> None:
  """
  使用ffmpeg将音频文件转换为mp3格式，并截取指定时间段的音频。
  """
  # start = seconds_to_hhmmss(start_time)
  # end   = seconds_to_hhmmss(end_time)
  start_time_str = str(start_time)
  end_time_str = str(end_time)
  try:
    # 使用ffmpeg将转换为mp3
    ffmpeg.input(input_file,ss=start_time_str, to=end_time_str).output(output_file, format='mp3', acodec='libmp3lame', ab=_ab).run(overwrite_output=True)
    logger.info(f"提取成功：{output_file}")
  except ffmpeg.Error as e:
    logger.error(f"提取失败：{e}")


def base64_mime_to_file(data_url, output_file_path):
    """
    将含有 MIME 类型的 base64 数据 URL 转换为文件

    :param data_url: str, 含有 MIME 类型的 base64 数据 URL
    :param output_file_path: str, 输出文件的路径
    """
    # 分离 MIME 类型标识和 Base64 数据
    header, base64_str = data_url.split(',', 1)

    # 解码 Base64 字符串
    file_data = b64decode(base64_str)

    # 将解码的二进制数据写入文件
    with open(output_file_path, 'wb') as output_file:
        output_file.write(file_data)
