import { sessionStg } from '@/utils/storage';

/** Framepack form cache key */
const FRAMEPACK_CACHE_KEY = 'formCache_video_framepack';

/**
 * 保存framepack表单数据到sessionStorage
 *
 * @param formData 表单数据
 */
export function saveFramepackFormCache(formData: StorageType.FramepackFormCache): void {
  try {
    const cacheData: StorageType.FramepackFormCache = {
      ...formData,
      cachedAt: Date.now()
    };
    sessionStg.set(FRAMEPACK_CACHE_KEY, cacheData);
    console.log('表单数据已缓存到sessionStorage');
  } catch (error) {
    console.error('保存表单缓存失败:', error);
  }
}

/**
 * 从sessionStorage恢复framepack表单数据
 *
 * @returns 缓存的表单数据，如果没有缓存则返回null
 */
export function restoreFramepackFormCache(): StorageType.FramepackFormCache | null {
  try {
    const cacheData = sessionStg.get(FRAMEPACK_CACHE_KEY);
    if (cacheData) {
      console.log('从sessionStorage恢复表单数据');
      return cacheData;
    }
    return null;
  } catch (error) {
    console.error('恢复表单缓存失败:', error);
    return null;
  }
}

/** 清空framepack表单缓存 */
export function clearFramepackFormCache(): void {
  try {
    sessionStg.remove(FRAMEPACK_CACHE_KEY);
    console.log('表单缓存已清空');
  } catch (error) {
    console.error('清空表单缓存失败:', error);
  }
}

/**
 * 检查是否存在framepack表单缓存
 *
 * @returns 是否存在缓存
 */
export function hasFramepackFormCache(): boolean {
  try {
    const cacheData = sessionStg.get(FRAMEPACK_CACHE_KEY);
    return cacheData !== null;
  } catch (error) {
    console.error('检查表单缓存失败:', error);
    return false;
  }
}

/**
 * 从framepack组件提取表单数据
 *
 * @param formRefs 表单引用对象
 * @returns 提取的表单数据
 */
export function extractFramepackFormData(formRefs: {
  mediaPreview: string | null;
  uploadedUrl: string | null;
  mediaType: 'image' | null;
  videoDescription: string;
  videoSeconds: number;
  videoSeed: string;
  selectedModel: string;
  selectedSubModel: string;
}): StorageType.FramepackFormCache {
  return {
    mediaPreview: formRefs.mediaPreview,
    uploadedUrl: formRefs.uploadedUrl,
    mediaType: formRefs.mediaType,
    videoDescription: formRefs.videoDescription,
    videoSeconds: formRefs.videoSeconds,
    videoSeed: formRefs.videoSeed,
    selectedModel: formRefs.selectedModel,
    selectedSubModel: formRefs.selectedSubModel,
    cachedAt: Date.now()
  };
}

/**
 * 验证缓存数据的有效性
 *
 * @param cacheData 缓存数据
 * @returns 是否有效
 */
export function validateFramepackCacheData(cacheData: any): cacheData is StorageType.FramepackFormCache {
  if (!cacheData || typeof cacheData !== 'object') {
    return false;
  }

  const requiredFields = [
    'videoDescription',
    'videoSeconds',
    'videoSeed',
    'selectedModel',
    'selectedSubModel',
    'cachedAt'
  ];

  return requiredFields.every(field => field in cacheData);
}
