<script setup lang="ts">
// import { $t } from '@/locales';
import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/theme';
import Logo_long from '@/assets/svg-icon/logo_long.png';
import Logo from '@/assets/svg-icon/logo.png';

defineOptions({
  name: 'GlobalLogo'
});

interface Props {
  /** Whether to show the title */
  showTitle?: boolean;
}

withDefaults(defineProps<Props>(), {
  showTitle: true
});

const themeStore = useThemeStore();

// 在亮色主题下应用过滤器
const logoStyle = computed(() => {
  if (!themeStore.darkMode) {
    return {
      filter:
        'brightness(0) saturate(100%) invert(35%) sepia(51%) saturate(2878%) hue-rotate(230deg) brightness(91%) contrast(98%)'
    };
  }
  return {};
});
</script>

<template>
  <RouterLink to="/" class="w-full flex-center nowrap-hidden">
    <!--
 <SystemLogo class="text-32px text-primary" />
    <h2 v-show="showTitle" class="pl-8px text-16px text-primary font-bold transition duration-300 ease-in-out">
      {{ $t('system.title') }}
    </h2>
-->
    <img
      :src="showTitle ? Logo_long : Logo"
      :alt="showTitle ? 'Logo Long' : 'Logo'"
      class="h-33px"
      loading="eager"
      fetchpriority="high"
      :style="logoStyle"
    />
  </RouterLink>
</template>

<style scoped></style>
