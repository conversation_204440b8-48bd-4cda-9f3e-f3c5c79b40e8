<script setup lang="ts">
// import { $t } from '@/locales';
import type { Ref } from 'vue';
import { computed, defineEmits, ref, watch } from 'vue';
// import { useMessage } from 'naive-ui';
// import { metaPrompt } from '@/service/api';
import { $t } from '@/locales';
import { Capacity, capacityOp } from '@/utils/capacity';
import UploadImage from './upload-image.vue';

const imageList = ref<string[]>([]);
const imageInputRef = ref<Ref | null>(null);
const prompt = ref<string>('');

const emit = defineEmits<{
  (event: 'submit', payload: { prompt: string; imageList: string[] }): void;
  (event: 'stop'): void;
  (event: 'changeWeb', payload: { useWeb: boolean }): void;
  (event: 'useSkill', payload: { skill: string | null }): void;
}>();

type Model = {
  label: string;
  value: string;
  desc: string;
  icon: string;
  capacity: number;
};

const props = defineProps<{
  loading?: boolean;
  useWeb: boolean;
  isMobile?: boolean;
  model?: Model;
}>();

function handleSubmit() {
  if (!prompt.value) {
    return;
  }
  emit('submit', { prompt: prompt.value, imageList: imageList.value });
  // 清空输入内容
  prompt.value = '';
  imageList.value = [];
  // 清空图片上传组件状态
  if (imageInputRef.value) {
    imageInputRef.value.clear();
  }
}

function handleStop() {
  emit('stop');
}

function handleClick() {
  if (props.loading) {
    handleStop();
    return;
  }
  handleSubmit();
}

function handleEnter(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    handleSubmit();
  }
}

function handlePaste(event: ClipboardEvent) {
  //   if (loading.value) return;

  // 清除帶data的圖片
  const clipboardData = event.clipboardData;
  // console.log(clipboardData);
  let item;
  let items;
  let types;
  if (clipboardData) {
    items = clipboardData.items;
    if (!items) return;

    // 如果剪贴板中有文字，则不做任何操作
    if (clipboardData.getData('Text') || clipboardData.getData('text/plain')) return;

    // 保存在剪贴板中的数据类型
    types = clipboardData.types || [];
    for (let i = 0; i < types.length; i++) {
      if (types[i] === 'Files') {
        item = items[i];
        break;
      }
    }

    // 判断是否为图片数据
    if (item && item.kind === 'file' && item.type.match(/^image\//i)) {
      // 读取该图片
      const file = item.getAsFile();
      imageInputRef.value.changeFile(file);
    }
  }
}

// const message = useMessage();
// const buttonLoading = ref(false);

// const onPositiveClick = async () => {
//   if (!prompt.value) {
//     message.error('请输入内容');
//     return;
//   }

//   try {
//     buttonLoading.value = true;
//     message.success('提示词优化中...');
//     const response = await metaPrompt(prompt.value);
//     if (response.data) {
//       prompt.value = response.data.meta_prompt;
//       message.success('优化成功');
//     }
//   } catch {
//     message.error('优化失败，请重试');
//   } finally {
//     buttonLoading.value = false;
//   }
// };

const skills: Record<string, string> = {
  'image-generation': '图像生成'
};
const skillPlaceholders: Record<string, string> = {
  'image-generation': $t('page.chat.inputPlaceholderImageGeneration')
};
const getSkillIcon = (skill: string): string => {
  switch (skill) {
    case 'image-generation':
      return 'fluent:image-sparkle-16-regular';
    default:
      return 'fluent:toolbox-16-regular';
  }
};

const skillOptions = computed(() => Object.entries(skills).map(([value, label]) => ({ label, value })));
const selectedSkill = ref<string | null>(null);
const showSkillPopover = ref(false);
const handleSelectSkill = (value: string | null) => {
  showSkillPopover.value = false;
  if (selectedSkill.value === value) {
    selectedSkill.value = null;
    emit('useSkill', { skill: null });
  } else {
    selectedSkill.value = value;
    emit('useSkill', { skill: value });
  }
};

const placeholder = computed(() => {
  if (props.isMobile) {
    return $t('page.chat.inputPlaceholderMobile');
  } else if (selectedSkill.value) {
    return skillPlaceholders[selectedSkill.value] || $t('page.chat.inputPlaceholder');
  }
  return $t('page.chat.inputPlaceholder');
});
watch(
  () => props.model,
  () => {
    // 监听当前使用的模型，如果不支持 Tool Call，则清空技能选项
    if (!capacityOp.has(props.model?.capacity || 0, Capacity.TOOL_CALL)) {
      selectedSkill.value = null;
    }
  }
);
</script>

<template>
  <NInput
    v-model:value="prompt"
    type="textarea"
    :placeholder="placeholder"
    :autosize="isMobile ? { minRows: 2, maxRows: 5 } : { minRows: 3, maxRows: 10 }"
    class="message-input"
    :class="[isMobile ? '' : 'max-w-[1280px] mx-auto']"
    @keypress="handleEnter"
    @paste="handlePaste"
  >
    <template #prefix>
      <div v-show="imageList.length > 0">
        <UploadImage ref="imageInputRef" :is-mobile="isMobile" @change="v => (imageList = v)" />
      </div>
    </template>
    <template #suffix>
      <div class="flex items-center justify-between">
        <div class="flex1 flex gap-3">
          <NButton
            bordered
            round
            size="small"
            :type="useWeb ? 'info' : ''"
            @click="() => emit('changeWeb', { useWeb: !useWeb })"
          >
            <template #icon>
              <SvgIcon icon="streamline-plump:web" class="text-lg" />
            </template>
            {{ $t('page.chat.enableWeb') }}
          </NButton>

          <NPopover v-if="!isMobile" v-model:show="showSkillPopover" trigger="click" :show-arrow="false" class="p-1!">
            <template #trigger>
              <NButton bordered round size="small" :type="selectedSkill ? 'primary' : ''">
                <template #icon>
                  <SvgIcon v-if="selectedSkill" :icon="getSkillIcon(selectedSkill)" class="text-lg" />
                  <SvgIcon v-else icon="fluent:rectangle-landscape-sparkle-16-regular" class="text-lg" />
                </template>
                <span v-if="selectedSkill">{{ skills[selectedSkill] }}</span>
                <span v-else>{{ $t('page.chat.skills') }}</span>
              </NButton>
            </template>
            <NList :bordered="false" clickable hoverable :show-divider="false">
              <NListItem
                v-for="opt of skillOptions"
                :key="opt.value"
                class="px-3! py-2!"
                @click="handleSelectSkill(opt.value)"
              >
                <NText class="flex flex-center" :class="{ 'text-primary': selectedSkill === opt.value }">
                  <SvgIcon :icon="getSkillIcon(opt.value)" class="mr-1 inline" />
                  <span>{{ opt.label }}</span>
                </NText>
              </NListItem>
            </NList>
          </NPopover>

          <NTooltip :disabled="isMobile" trigger="hover">
            <template #trigger>
              <NButton bordered round size="small" @click="imageInputRef?.openFileDialog()">
                <SvgIcon icon="lucide:image-plus" class="text-xl" />
              </NButton>
            </template>
            {{ $t('page.chat.uploadImage') }}
          </NTooltip>
        </div>

        <NButton :type="loading ? 'warning' : 'primary'" :disabled="!prompt" class="send-btn" @click="handleClick">
          <template #icon>
            <span class="dark:text-black">
              <SvgIcon v-if="loading" icon="material-symbols:stop-rounded" />
              <SvgIcon v-else icon="ri:send-plane-fill" />
            </span>
          </template>
        </NButton>
      </div>
    </template>
  </NInput>
</template>

<style scoped>
.message-input :deep(.n-input-wrapper) {
  flex-direction: column;
}

.message-input :deep(.n-input__suffix),
.message-input :deep(.n-input__prefix) {
  display: block;
  margin: 0;
}

.send-btn {
  border-radius: 20px;
  margin-bottom: 0.5em;
}
</style>
