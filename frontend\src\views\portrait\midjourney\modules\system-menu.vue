<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { useRoute } from 'vue-router';
import { getPrompt } from '@/service/api';
import { describe, fluxComfyUIImage } from '@/service/api/midjourney';
import type { ChatMjTask } from '@/service/api/midjourney';
import { ParamsFillBack, decodeParams } from '@/utils/paramsFillBack';
import { AiCapacity } from '@/utils/AiCapacity';
import { useThemeStore } from '@/store/modules/theme';
import PromptLibrary from './prompt-library.vue';
import PromptTag from './prompt-tag.vue';
import ModelSelect from './model-select.vue';
import styleOptions from './style.json';

// 定义emit
const emit = defineEmits<{
  (event: 'generate', formValue: typeof form.value, num: number): void;
  (event: 'imageDescribeTask', task: ChatMjTask, num?: number): void;
  // (event: 'platform-change', platform: string): void;
}>();

const message = useMessage();
const themeStore = useThemeStore();
const isDescribe = ref(false);
const route = useRoute();
const localLoading = ref(false);

// 文件选择器ref
const fileInputRef = ref<HTMLInputElement | null>(null);

// 风格选项
const styleValue = ref(null);
// 选中的风格数组
const selectedStyles = ref<Array<{ label: string; value: string }>>([]);

// 平台风格缓存 - 为每个平台维护独立的风格状态
const platformStylesCache = ref<Record<string, Array<{ label: string; value: string }>>>({
  midjourney: [],
  openai: [],
  flux: []
});

// 当前选择的平台
const currentPlatform = ref('midjourney');

// 添加一个状态变量来控制风格选择Popover的显示状态
const showStylePopover = ref(false);

// 表单数据
const form = ref({
  model: ' --v 6.1', // 默认模型
  prompt: '',
  image: [] as string[],
  selectedPrompts: [] as any[],
  stylize: null,
  quality: null,
  chaos: null,
  seed: null,
  aspect: null as string | null,
  iw: null,
  repeat: null,
  exclude: '',
  parameters: {} as Record<string, string | number>,
  originalPrompt: '',
  platform: 'midjourney', // 添加platform字段
  num: 1 // 生成数量，默认为1
});

// 为prompt添加风格值（适用于所有平台）
const addStylesToPrompt = (inputPrompt: string): string => {
  let result = inputPrompt.trim();
  if (selectedStyles.value.length > 0) {
    const styleValues = selectedStyles.value.map(s => s.value).join(', ');

    // 检查 result 末尾是否包含逗号
    if (result.endsWith(',')) {
      result += ` ${styleValues}`;
    } else {
      result += `, ${styleValues}`;
    }
  }
  return result;
};

// 生成最终的prompt
const generatePrompt = (): string => {
  const { prompt, selectedPrompts, model } = form.value;

  // 统一处理风格值（适用于所有平台）
  const basePrompt = addStylesToPrompt(prompt);

  // 如果是OpenAI平台，使用简化的处理逻辑
  if (currentPlatform.value === 'openai') {
    let openaiPrompt = basePrompt.replace(/["'""'']/g, '');

    // 只添加比例参数
    if (form.value.aspect) {
      openaiPrompt += ` --ar ${form.value.aspect}`;
    }

    return openaiPrompt;
  }

  // 如果是Flux平台，使用简化的处理逻辑
  if (currentPlatform.value === 'flux') {
    const fluxPrompt = basePrompt.replace(/["'""'']/g, '');

    // Flux平台只需要prompt，模型通过API参数传递
    return fluxPrompt;
  }

  // 以下是Midjourney平台的处理逻辑
  // 提取以'--'开头并以','结尾的参数，并放到字符串的末尾
  let modifiedPrompt = basePrompt;
  let extractedParams = '';

  // 正则匹配 -- 开头并以逗号结尾的部分
  const regex = /--\s*\S+\s*\S*,/g;
  let match = regex.exec(modifiedPrompt);

  while (match !== null) {
    extractedParams += ` ${match[0].replace(',', '').trim()}`;
    modifiedPrompt = modifiedPrompt.replace(match[0], '').trim();
    match = regex.exec(modifiedPrompt);
  }

  // 去掉末尾多余的逗号
  modifiedPrompt = modifiedPrompt.replace(/\s*,\s*$/, '');

  // 去掉双引号和单引号，包括中文引号
  modifiedPrompt = modifiedPrompt.replace(/["'""'']/g, '');

  // 提取参数对象，为不同平台准备
  const params: Record<string, string | number> = {};

  // 对于所有平台，都添加比例参数
  if (form.value.aspect) params['--ar '] = form.value.aspect;

  // 如果是Midjourney平台，添加其他特有参数
  if (currentPlatform.value === 'midjourney') {
    if (form.value.quality) params['--q '] = form.value.quality;
    if (form.value.stylize) params['--s '] = form.value.stylize;
    if (form.value.chaos) params['--c '] = form.value.chaos;
    if (form.value.seed) params['--seed '] = form.value.seed;
    if (form.value.iw) params['--iw '] = form.value.iw;
    if (form.value.repeat) params['--r '] = form.value.repeat;
    if (form.value.exclude) params['--no '] = form.value.exclude;

    // 处理 selectedPrompts
    if (selectedPrompts.length > 0) {
      const selectedPromptsStr = selectedPrompts
        .map(sp => (sp.weight === 0 ? sp.param : `${sp.param}::${sp.weight}`))
        .join(', ');

      // 检查 modifiedPrompt 末尾是否包含逗号
      if (modifiedPrompt.endsWith(',')) {
        modifiedPrompt += ` ${selectedPromptsStr}`;
      } else {
        modifiedPrompt += `, ${selectedPromptsStr}`;
      }
    }
  }

  // 处理参数
  if (Object.keys(params).length > 0) {
    const parametersStr = Object.entries(params)
      .map(([key, value]) => `${key} ${value}`)
      .join(' ');
    extractedParams += ` ${parametersStr}`;
  }

  // 检查 modifiedPrompt 中是否包含 model，如果有则去除，最后再加上
  const modelValue = model.trim(); // 确保去掉 model 两侧的空格
  const modelRegex = new RegExp(`${modelValue.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}`, 'i'); // 转义特殊字符
  if (modelRegex.test(modifiedPrompt)) {
    modifiedPrompt = modifiedPrompt.replace(modelRegex, '').trim();
  }

  // 拼接提取的参数和 model，model 始终在最后，避免重复
  return `${`${modifiedPrompt} ${extractedParams}`.trim()} ${modelValue}`;
};

// 处理模型选择变化
const handleModelChange = (value: string) => {
  form.value.model = value;
  console.log('Model changed to:', value);
};

// 平台风格状态管理辅助函数
// 保存当前平台的风格状态到缓存
const savePlatformStyles = () => {
  const currentPlatformKey = currentPlatform.value;
  platformStylesCache.value[currentPlatformKey] = [...selectedStyles.value];
  console.log(`保存 ${currentPlatformKey} 平台风格:`, selectedStyles.value);
};

// 加载指定平台的风格状态
const loadPlatformStyles = (platform: string) => {
  const cachedStyles = platformStylesCache.value[platform] || [];
  selectedStyles.value = [...cachedStyles];
  // 使用 Vue.nextTick 确保在下一个tick中调用，避免函数依赖问题
  nextTick(() => {
    if (selectedStyles.value.length === 0) {
      styleValue.value = null;
    } else {
      styleValue.value = selectedStyles.value[0].value as any;
    }
  });
  console.log(`加载 ${platform} 平台风格:`, cachedStyles);
};

// 清空当前风格选择状态
// const clearCurrentStyles = () => {
//   selectedStyles.value = [];
//   styleValue.value = null;
//   // 内联 cleanPromptStyles 的功能
//   const currentPrompt = form.value.prompt;
//   // 这里需要访问 styleOptions，暂时先不清理 prompt，只清理状态
// };

// 处理平台变化
const handlePlatformChange = (platform: string) => {
  // 保存当前平台的风格状态
  savePlatformStyles();

  // 更新平台信息
  currentPlatform.value = platform;
  form.value.platform = platform; // 更新form中的platform字段

  // 加载目标平台的风格状态
  loadPlatformStyles(platform);

  console.log('Platform changed to:', platform);
};

// 风格管理函数
// 清理 prompt 中的风格内容（移除风格值，但不添加新的）
const cleanPromptStyles = () => {
  let currentPrompt = form.value.prompt;

  // 移除所有现有的风格内容
  styleOptions.forEach(option => {
    currentPrompt = currentPrompt.replace(new RegExp(option.value, 'g'), '').trim();
  });

  // 清理多余的逗号和空格
  currentPrompt = currentPrompt.replace(/,\s*,/g, ',').replace(/^,\s*/, '').replace(/,\s*$/, '').trim();

  form.value.prompt = currentPrompt;
};

// 更新风格选择器状态
const updateStyleSelect = () => {
  if (selectedStyles.value.length === 0) {
    styleValue.value = null;
  } else {
    // 单选逻辑：始终显示唯一选中的风格
    styleValue.value = selectedStyles.value[0].value as any;
  }
};

// 将风格添加到选中数组中
const addStyleToPrompt = (style: { label: string; value: string }) => {
  // 检查是否已经存在
  const exists = selectedStyles.value.some(s => s.value === style.value);
  if (exists) return;

  // 清空现有风格，确保只能选择一个风格
  selectedStyles.value = [];

  // 添加到选中风格数组
  selectedStyles.value.push(style);

  // 同步更新当前平台的缓存
  platformStylesCache.value[currentPlatform.value] = [...selectedStyles.value];

  // 清理输入框中的风格内容
  cleanPromptStyles();

  // 关闭风格选择Popover
  showStylePopover.value = false;
};

// 从选中数组中移除风格
const removeStyleFromPrompt = (removeStyleValue: string) => {
  // 从选中风格数组中移除
  const index = selectedStyles.value.findIndex(s => s.value === removeStyleValue);
  if (index !== -1) {
    selectedStyles.value.splice(index, 1);
  }

  // 同步更新当前平台的缓存
  platformStylesCache.value[currentPlatform.value] = [...selectedStyles.value];

  // 清理输入框中的风格内容，更新选择器状态
  cleanPromptStyles();
  updateStyleSelect();
};

// 主题适配computed
const themeClasses = computed(() => ({
  popover: themeStore.darkMode ? 'none' : 'bg-white border-gray-200',
  styleCard: themeStore.darkMode
    ? 'dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600'
    : 'bg-gray-50 hover:bg-gray-100 border-gray-200',
  selectedCard: themeStore.darkMode ? 'dark:bg-blue-800 dark:border-blue-500' : 'bg-blue-50 border-blue-400'
}));

// 检查风格是否被选中
const isStyleSelected = (styleVal: string) => {
  return selectedStyles.value.some(s => s.value === styleVal);
};

// 解析 prompt 中的风格内容
const parseStylesFromPrompt = (prompt: string) => {
  const foundStyles: Array<{ label: string; value: string }> = [];

  styleOptions.forEach(option => {
    if (prompt.includes(option.value)) {
      foundStyles.push(option);
    }
  });

  return foundStyles;
};

// 更新表单数据的函数
function updateFormParameters() {
  const params: Record<string, string | number> = {};

  if (form.value.aspect) params['--ar '] = form.value.aspect;
  if (form.value.quality) params['--q '] = form.value.quality;
  if (form.value.stylize) params['--s '] = form.value.stylize;
  if (form.value.chaos) params['--c '] = form.value.chaos;
  if (form.value.seed) params['--seed '] = form.value.seed;
  if (form.value.iw) params['--iw '] = form.value.iw;
  if (form.value.repeat) params['--r '] = form.value.repeat;
  if (form.value.exclude) params['--no '] = form.value.exclude;

  form.value.parameters = params;
  console.log('Updated parameters:', form.value.parameters);
}

// 监听表单值的变化
watch(
  () => [
    form.value.aspect,
    form.value.quality,
    form.value.stylize,
    form.value.chaos,
    form.value.seed,
    form.value.iw,
    form.value.repeat,
    form.value.exclude
  ],
  () => {
    updateFormParameters();
    console.log('new from values', form.value);
  }
);

// 监听风格选择器变化
watch(
  () => styleValue.value,
  newValue => {
    if (newValue) {
      const selectedOption = styleOptions.find(option => option.value === newValue);
      if (selectedOption) {
        addStyleToPrompt(selectedOption);
      }
    }
  }
);

// 监听 prompt 变化，解析风格内容（仅在用户手动输入风格时同步）
watch(
  () => form.value.prompt,
  newPrompt => {
    // 只有当prompt中包含风格值且selectedStyles为空时，才进行同步
    // 这避免了在清理风格时的循环触发
    if (selectedStyles.value.length === 0) {
      const foundStyles = parseStylesFromPrompt(newPrompt);

      if (foundStyles.length > 0) {
        selectedStyles.value = foundStyles;
        updateStyleSelect();
      }
    }
  },
  { deep: true }
);

// 定义比例选项
const ratios = ref([
  { label: '1:1', value: '1:1', width: '100%', height: '100%' },
  { label: '4:3', value: '4:3', width: '80%', height: '60%' },
  { label: '3:4', value: '3:4', width: '60%', height: '80%' },
  { label: '16:9', value: '16:9', width: '95%', height: '54%' },
  { label: '9:16', value: '9:16', width: '54%', height: '95%' }
]);

// 处理比例选择
function handleRatioSelect(value: string) {
  if (form.value.aspect === value) {
    form.value.aspect = null; // 如果再次点击已选中的比例，则取消选择
  } else {
    form.value.aspect = value; // 否则选中点击的比例
  }
}

// 图片上传
// interface UploadEvent {
//   file: UploadFileInfo;
//   fileList: UploadFileInfo[];
//   event?: Event;
// }

const fileLists = ref<UploadFileInfo[][]>([[], [], [], []]);

// 监听平台变化，当切换到flux时自动清除非第一张图片
watch(
  () => currentPlatform.value,
  newPlatform => {
    if (newPlatform === 'flux') {
      // 保留第一张图片，清除其他图片
      if (fileLists.value[0].length > 0) {
        // 如果有第一张图片，保留它，清除其他图片
        const firstImage = fileLists.value[0];
        fileLists.value = [firstImage, [], [], []];

        // 更新form.value.image只保留第一张图片
        if (form.value.image.length > 0) {
          form.value.image = [form.value.image[0]];
        }
      }
    }
  },
  { immediate: false }
);

// 清除所有上传的图片
function clearAllImages() {
  // 清空文件列表
  fileLists.value = [[], [], [], []];
  // 清空表单中的图片数据
  form.value.image = [];
  message.success('已清除所有图片');
}

function handleUpdateFileList(index: number, newFileList: UploadFileInfo[]) {
  // 更新对应索引的 fileList
  fileLists.value[index] = newFileList;

  // 将所有 fileList 中的文件转换为 base64
  const imagePromises: Promise<string>[] = fileLists.value.flat().map(item => {
    return new Promise((resolve, reject) => {
      if (item.file) {
        const reader = new FileReader();
        reader.onload = e => {
          if (e.target && e.target.result) {
            const fullBase64 = e.target.result as string;
            item.url = fullBase64; // 同步 item.url 以便删除时保持一致
            resolve(fullBase64);
          } else {
            resolve('');
          }
        };
        reader.onerror = reject;
        reader.readAsDataURL(item.file);
      } else {
        resolve('');
      }
    });
  });

  // 更新 form.value.image 为所有上传框的图片
  Promise.all(imagePromises)
    .then(images => {
      form.value.image = images.filter(img => img !== '');
      console.log('Updated images:', form.value.image);
    })
    .catch(error => {
      console.error('Error processing images:', error);
    });
}

// 读取回填数据
const loadFillBackParams = () => {
  const s = route.query[ParamsFillBack];
  if (!s) {
    return;
  }
  if (Array.isArray(s)) {
    return;
  }
  try {
    const params = decodeParams<{ prompt: string; prompt_img: string[] }>(s);
    // 填充 Prompt
    if (params.prompt) {
      form.value.prompt = params.prompt;
    }
    // 填充图片
    if (params.prompt_img) {
      let imgs = params.prompt_img;
      if (!Array.isArray(imgs)) {
        imgs = [imgs];
      }
      const len = Math.min(fileLists.value.length, imgs.length);
      for (let i = 0; i < len; i++) {
        const img = imgs[i];
        fileLists.value[i] = [{ id: img, name: img, status: 'finished', url: img, thumbnailUrl: img }];
        form.value.image = [...form.value.image, img];
      }
    }
  } catch (e) {
    console.error('无法解析回填参数', e);
  }
};

onMounted(loadFillBackParams);

// 其他官方参数显示
const isSystem = ref<boolean>(false);

const active = ref<boolean>(false);

// 图片权重选择
const iw_options = [
  { value: '0.5', label: '0.5' },
  { value: '0.75', label: '0.75' },
  { value: '1', label: '1' },
  { value: '1.25', label: '1.25' },
  { value: '1.5', label: '1.5' },
  { value: '1.75', label: '1.75' },
  { value: '2', label: '2' }
];

// 处理从子组件接收到的 selectedPrompts
function handleSelectedPromptsUpdate(newSelectedPrompts: any[]) {
  form.value.selectedPrompts = newSelectedPrompts;
}

// 开始生成,发送表单数据到父组件
function handleGenerateClick() {
  if (!form.value.prompt.trim()) {
    message.info('提示词不能为空');
    return;
  }

  // 保存原始提示词
  const originalPrompt = form.value.prompt.trim();

  // 生成最终的prompt（但不覆盖表单中的原始提示词）
  const finalPrompt = generatePrompt();

  // 创建表单数据的副本，用于提交
  const formDataToSubmit = { ...form.value };

  // 在副本中设置最终生成的prompt和原始提示词
  formDataToSubmit.prompt = finalPrompt;
  formDataToSubmit.originalPrompt = originalPrompt;

  // 添加proportion参数
  (formDataToSubmit as any).proportion = form.value.aspect;

  // 根据平台调用不同的API
  if (currentPlatform.value === 'flux') {
    handleFluxGenerate(formDataToSubmit);
  } else {
    // 发送表单数据副本到父组件（MJ和OpenAI使用原有逻辑）
    emit('generate', formDataToSubmit, form.value.num);
  }

  console.log('Form data submitted:', formDataToSubmit);
}

// 处理Flux图片生成
async function handleFluxGenerate(formData: typeof form.value) {
  // if (!formData.image || formData.image.length === 0) {
  //   message.warning('Flux模型需上传参考图片');
  //   return;
  // }

  try {
    localLoading.value = true;

    const payload: any = {
      prompt: formData.prompt,
      model_name: formData.model,
      num: formData.num,
      aspect_ratio: formData.aspect,
      proportion: formData.aspect,
      preserve_history: false
    };

    // 如果有种子值，添加到请求中
    if (formData.seed) {
      payload.seed = formData.seed;
    }

    // 如果有第一张参考图片，添加到请求中
    if (formData.image && formData.image.length > 0) {
      payload.input_image = formData.image[0];
    }

    const response = await fluxComfyUIImage(payload);

    if (response.data && response.data.task) {
      // message.success('Flux图像生成任务已提交');

      // 构造完整的任务对象并发送给父组件
      const task: ChatMjTask = {
        id: response.data.task.id,
        pid: response.data.task.pid,
        username: response.data.task.username,
        taskid: Number(response.data.task.taskid),
        action: response.data.task.action,
        status: response.data.task.status,
        prompt: response.data.task.prompt,
        prompt_en: response.data.task.prompt_en,
        description: response.data.task.description,
        state: response.data.task.state,
        submit_time: response.data.task.submit_time,
        start_time: response.data.task.start_time,
        finish_time: response.data.task.finish_time,
        image_url: response.data.task.image_url,
        progress: response.data.task.progress,
        fail_reason: response.data.task.fail_reason,
        uptime: response.data.task.uptime,
        seed: response.data.task.seed,
        button: response.data.task.button,
        channels: response.data.task.channels || 0,
        like_count: response.data.task.like_count || 0,
        is_like: response.data.task.is_like || 0,
        model: response.data.task.model,
        manufacturer: response.data.task.manufacturer
      };

      // 直接发送任务对象和生成数量给父组件
      emit('imageDescribeTask', task, formData.num);
    } else {
      // message.error('Flux图像生成任务提交失败');
    }
  } catch (error) {
    console.error('Flux图像生成失败:', error);
    // message.error('Flux图像生成失败');
  } finally {
    localLoading.value = false;
  }
}

// 处理标签关闭
function handleTagClose(param: string) {
  const index = form.value.selectedPrompts.findIndex(p => p.param === param);
  if (index !== -1) {
    form.value.selectedPrompts.splice(index, 1);
  }
  // 通知 PromptLibrary 组件更新选中状态
  const promptLibraryRef = ref<InstanceType<typeof PromptLibrary> | null>(null);
  promptLibraryRef.value?.updateSelectedPrompts(form.value.selectedPrompts);
}

// 处理风格标签关闭
function handleStyleTagClose(styleValueToRemove: string) {
  removeStyleFromPrompt(styleValueToRemove);
}

// 接收传递的loading状态
// const props = defineProps<{ loading: boolean; channels: number }>();
defineProps<{ loading: boolean }>();

const isGetPrompt = ref(false);
// 提示词优化
// 优化提示词的函数
const optimizePrompt = async () => {
  // 进入loading状态
  isGetPrompt.value = true;

  try {
    // 调用 getPrompt，传入表单的 prompt 数据
    const response = await getPrompt(form.value.prompt);

    //
    if (response.data) {
      // 更新表单的 prompt 为返回的 mj_prompt
      form.value.prompt = response.data.mj_prompt;
      message.success('提示词优化成功');
    }
  } catch (error) {
    message.error('提示词优化失败');
  } finally {
    // 请求结束，loading 状态取消
    isGetPrompt.value = false;
  }
};

// 触发文件选择器
function handleImageDescribe() {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
}

// 处理文件选择
async function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) {
    return;
  }

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return;
  }

  // 验证文件大小 (限制为10MB)
  if (file.size > 10 * 1024 * 1024) {
    message.error('图片文件大小不能超过10MB');
    return;
  }

  isDescribe.value = true;

  try {
    // 读取文件并转换为base64
    const base64 = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => {
        if (e.target && e.target.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('文件读取失败'));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

    // 调用反推API
    const response = await describe(base64);

    if (response.data && response.data.taskid) {
      message.success('图片反推提示词任务已提交');

      // 构造完整的任务对象
      const task: ChatMjTask = {
        id: response.data.id,
        pid: response.data.pid,
        username: response.data.username,
        taskid: Number(response.data.taskid),
        action: response.data.action,
        status: response.data.status,
        prompt: response.data.prompt,
        prompt_en: response.data.prompt_en,
        description: response.data.description,
        state: response.data.state,
        submit_time: response.data.submit_time,
        start_time: response.data.start_time,
        finish_time: response.data.finish_time,
        image_url: response.data.image_url,
        progress: response.data.progress,
        fail_reason: response.data.fail_reason,
        uptime: response.data.uptime,
        seed: response.data.seed,
        button: response.data.button,
        channels: response.data.channels,
        like_count: response.data.like_count
      };

      // 通过 emit 将任务对象传递给父组件
      emit('imageDescribeTask', task, form.value.num);
    } else {
      message.error('图片反推提示词任务提交失败');
    }
  } catch (error) {
    message.error('图片反推提示词任务失败');
    console.error(error);
  } finally {
    isDescribe.value = false;
    // 清空文件输入，允许重复选择同一文件
    if (target) {
      target.value = '';
    }
  }
}

defineExpose({
  importParams({
    action,
    prompt,
    _description,
    imageUrl,
    promptImg = []
  }: {
    action: string;
    prompt: string;
    _description: string;
    imageUrl: string | null;
    promptImg?: string[] | string;
  }) {
    // 确保promptImg是数组
    let promptImgArray: string[] = [];

    if (promptImg) {
      // 如果promptImg是字符串，尝试解析为数组
      if (typeof promptImg === 'string') {
        try {
          const parsed = JSON.parse(promptImg);
          promptImgArray = Array.isArray(parsed) ? parsed : [promptImg];
        } catch (e) {
          // 如果解析失败，将字符串作为单个元素添加到数组
          promptImgArray = [promptImg];
        }
      } else if (Array.isArray(promptImg)) {
        // 如果已经是数组，直接使用
        promptImgArray = promptImg;
      }
    }

    console.log('importParams接收到参数:', { action, promptLength: promptImgArray.length });

    // 1) 如果是 DESCRIBE：只填第一张图片，其它不动
    if (action === 'DESCRIBE') {
      if (imageUrl) {
        console.log('DESCRIBE模式: 使用单张图片');
        fillImagesToUpload([imageUrl]);
      }
      return;
    }

    // 2) 如果是 IMAGINE：优先使用promptImg数组，如果为空则保持上传框为空
    if (action === 'IMAGINE') {
      if (promptImgArray.length > 0) {
        // 使用传入的promptImg数组
        console.log('IMAGINE模式: 使用promptImg数组填充上传框, 图片数量:', promptImgArray.length);
        fillImagesToUpload(promptImgArray);
      } else {
        // 如果promptImg为空，保持上传框为空
        console.log('IMAGINE模式: promptImg为空，保持上传框为空');
        // 清空上传框
        fileLists.value = [[], [], [], []];
        form.value.image = [];
      }
      // prompt 一律写入 form.value.prompt
      form.value.prompt = prompt;
      return;
    }

    // 3) 其他 action: 仅把 prompt 写进输入框
    console.log('其他模式: 仅设置prompt');
    form.value.prompt = prompt;
  }
});

function fillImagesToUpload(urls: string[] | string) {
  // 确保urls是数组
  let urlsArray: string[] = [];

  if (typeof urls === 'string') {
    try {
      const parsed = JSON.parse(urls);
      urlsArray = Array.isArray(parsed) ? parsed : [urls];
    } catch (e) {
      urlsArray = [urls];
    }
  } else if (Array.isArray(urls)) {
    urlsArray = urls;
  }

  // 先清空
  fileLists.value = [[], [], [], []];

  // 最多放4张
  const limitedUrls = urlsArray.slice(0, 4);
  limitedUrls.forEach((url, index) => {
    console.log(url);
    fileLists.value[index] = [
      {
        id: `${String(Date.now())}-${index}`,
        name: `imported-${index}.png`,
        status: 'finished',
        url,
        thumbnailUrl: url
      }
    ];
  });

  // 同步 form.value.image
  form.value.image = limitedUrls;
}

const creditModel = computed(() => {
  return form.value.model === 'image-1' ? 'gpt-4o-image-vip' : form.value.model;
});
</script>

<template>
  <main class="page-container">
    <NScrollbar class="scrollable-content">
      <NCard class="h-full">
        <NForm>
          <NFormItem label="MJ模型选择" path="model" class="modelBox">
            <ModelSelect
              v-model="form.model"
              @update:model-value="handleModelChange"
              @platform-change="handlePlatformChange"
            />
          </NFormItem>

          <NFormItem path="prompt" class="promptBox">
            <div class="relative w-full flex flex-col">
              <div class="mb-2">
                <NText class="mr-1 text-3.7">主体描述</NText>
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                  </template>
                  可输入中文
                </NTooltip>
              </div>
              <NInput v-model:value="form.prompt" placeholder="请输入提示词" type="textarea">
                <template #suffix>
                  <NFlex class="mb-2 w-full">
                    <div class="flex items-center">
                      <NTooltip v-if="!isGetPrompt" trigger="hover">
                        <template #trigger>
                          <NButton size="small" text @click="optimizePrompt">
                            <SvgIcon icon="icon-park-outline:smart-optimization" class="text-xl" />
                          </NButton>
                        </template>
                        点击该按钮，为您优化当前的提示词描述。
                      </NTooltip>

                      <NSpin v-else size="15"></NSpin>
                    </div>

                    <div v-if="currentPlatform === 'midjourney'" class="flex items-center">
                      <!-- 隐藏的文件输入 -->
                      <input
                        ref="fileInputRef"
                        type="file"
                        accept="image/*"
                        class="hidden"
                        @change="handleFileSelect"
                      />
                      <NTooltip>
                        <template #trigger>
                          <NButton size="small" text :loading="isDescribe" @click="handleImageDescribe">
                            <!-- 图片反推提示词 -->
                            <SvgIcon icon="mingcute:magic-hat-2-fill" class="text-2xl" />
                          </NButton>
                        </template>
                        上传一张图片，根据图片反推出提示词。
                      </NTooltip>
                    </div>

                    <div class="flex items-center">
                      <!-- 提示词库开关 -->
                      <NButton v-if="currentPlatform === 'midjourney'" type="default" text @click="active = true">
                        <SvgIcon icon="material-symbols:palette-outline" class="mr-1 text-2xl" />
                        <!-- <NText>选择风格</NText> -->
                      </NButton>

                      <NPopover
                        v-else
                        v-model:show="showStylePopover"
                        trigger="click"
                        placement="bottom-start"
                        :show-arrow="false"
                      >
                        <template #trigger>
                          <NTooltip>
                            <template #trigger>
                              <NButton text>
                                <SvgIcon icon="material-symbols:palette-outline" class="mr-1 text-2xl" />
                                <!-- <NText>选择风格</NText> -->
                              </NButton>
                            </template>
                            选择风格
                          </NTooltip>
                        </template>
                        <NScrollbar class="max-h-80">
                          <div class="max-h-80 p-2" :class="[themeClasses.popover]">
                            <NGrid cols="1" x-gap="0" y-gap="4">
                              <NGridItem v-for="style in styleOptions" :key="style.value">
                                <div
                                  class="cursor-pointer rounded-lg p-3 transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                                  :class="[isStyleSelected(style.value) ? 'bg-gray-100 dark:bg-gray-700' : '']"
                                  @click="addStyleToPrompt(style)"
                                >
                                  <div class="flex flex-row items-center justify-start space-x-3">
                                    <NImage
                                      :src="style.image"
                                      class="h-10 w-10 flex-shrink-0 rounded-md object-cover"
                                      preview-disabled
                                    />
                                    <NText
                                      class="flex-1 text-sm leading-tight"
                                      :class="themeStore.darkMode ? 'text-gray-200' : 'text-gray-700'"
                                    >
                                      {{ style.label }}
                                    </NText>
                                  </div>
                                </div>
                              </NGridItem>
                            </NGrid>
                          </div>
                        </NScrollbar>
                      </NPopover>
                    </div>
                  </NFlex>
                </template>
              </NInput>

              <div v-if="currentPlatform === 'openai' || currentPlatform === 'flux'" class="absolute right-1 top-2">
                <a
                  :href="
                    currentPlatform === 'flux'
                      ? 'https://www.5bei.cn/after-30-hours-fluxkontext-is-the-most-stable-and.html'
                      : 'https://www.bilibili.com/video/BV1yto9YkEtL/?spm_id_from=333.1387.upload.video_card.click&vd_source=ca69d43963d4f069ea8131c9a63b8983'
                  "
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <NText class="text-blue-500 underline underline-offset-[3px] dark:text-blue-400">精彩案例</NText>
                </a>
              </div>
            </div>
          </NFormItem>
          <!-- 风格标签显示区域 -->
          <div v-if="selectedStyles.length > 0" class="mt-1 flex flex-wrap gap-1">
            <NTag
              v-for="style in selectedStyles"
              :key="style.value"
              closable
              type="info"
              @close="handleStyleTagClose(style.value)"
            >
              {{ style.label }}
            </NTag>
          </div>

          <div v-if="currentPlatform === 'midjourney'" class="mt-1 flex flex-wrap gap-1">
            <PromptTag
              v-for="prompt in form.selectedPrompts"
              :key="prompt.param"
              :category="prompt.category"
              :name="prompt.name"
              :weight="prompt.weight"
              :param="prompt.param"
              @close="handleTagClose"
            />
          </div>
          <NFormItem path="image" class="promptBox mb-3">
            <div class="relative w-full flex flex-col">
              <div class="mb-2 flex items-center justify-between">
                <div class="flex items-center">
                  <NText class="mr-1 text-3.7">参考图片</NText>
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                    </template>
                    上传图片作为生成的参考
                  </NTooltip>
                </div>

                <NFlex>
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <NButton size="small" @click="clearAllImages">
                        <SvgIcon icon="material-symbols:delete-outline" class="text-[1.4em]" />
                      </NButton>
                    </template>
                    点击清除所有已上传的参考图片
                  </NTooltip>
                </NFlex>
              </div>
              <div class="upload grid grid-cols-4 gap-2">
                <NUpload
                  v-for="(__upload, index) in 4"
                  :key="index"
                  list-type="image-card"
                  accept="image/*"
                  :max="1"
                  :file-list="fileLists[index]"
                  :disabled="currentPlatform === 'flux' && index > 0"
                  @update:file-list="(newFileList: UploadFileInfo[]) => handleUpdateFileList(index, newFileList)"
                />
              </div>
            </div>
          </NFormItem>

          <NFormItem v-if="currentPlatform === 'openai'" label="生成数量" class="my-3">
            <NSlider
              v-model:value="form.num"
              :marks="{
                1: '1',
                2: '2',
                3: '3',
                4: '4'
              }"
              :min="1"
              :max="4"
              step="mark"
            />
          </NFormItem>

          <NFormItem v-if="currentPlatform !== 'flux'" label="比例" class="radioBox">
            <div class="ratio-buttons">
              <div
                v-for="ratio in ratios"
                :key="ratio.value"
                class="ratio-option"
                :class="{ 'ratio-option-active': form.aspect === ratio.value }"
                @click="handleRatioSelect(ratio.value)"
              >
                <div class="ratio-preview-container">
                  <div class="ratio-preview" :style="{ width: ratio.width, height: ratio.height }"></div>
                </div>
                <NText>{{ ratio.label }}</NText>
              </div>
            </div>
          </NFormItem>

          <!-- 官方参数 质量 Quality  风格化 Stylize默认显示 -->
          <NGrid v-if="currentPlatform === 'midjourney'" cols="2" responsive="screen" x-gap="5" class="systemBox">
            <NGridItem>
              <NFormItem class="flex-1">
                <NSpace vertical>
                  <div>
                    <NText class="mr-1 text-3.7">质量 Quality</NText>
                    <NTooltip trigger="hover">
                      <template #trigger>
                        <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                      </template>
                      用于控制生成图像的细节程度和分辨率。
                      <br />
                      值越大细节越多,生成速度越慢。
                    </NTooltip>
                  </div>
                  <NInputNumber v-model:value="form.quality" max="2" min="0" step="1" :precision="0" />
                </NSpace>
              </NFormItem>
            </NGridItem>

            <NGridItem>
              <NFormItem class="flex-1">
                <NSpace vertical>
                  <div>
                    <NText class="mr-1 text-3.7">风格化 Stylize</NText>
                    <NTooltip trigger="hover">
                      <template #trigger>
                        <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                      </template>
                      用于控制图像生成时应用的艺术风格
                      <br />
                      可以通过调整风格化参数，使生成的图像更具艺术感或更接近具体描述。
                    </NTooltip>
                  </div>
                  <NInputNumber v-model:value="form.stylize" max="1000" min="0" :precision="0" />
                </NSpace>
              </NFormItem>
            </NGridItem>
          </NGrid>

          <!-- 官方参数默认隐藏参数 -->
          <div v-if="currentPlatform === 'midjourney' && isSystem" class="systemBox">
            <NGrid cols="2" responsive="screen" x-gap="5">
              <NGridItem>
                <NFormItem>
                  <NSpace vertical>
                    <div>
                      <NText class="mr-1 text-3.7">混乱 Chaos</NText>
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                        </template>
                        用于控制生成图像的随机性和创意性
                        <br />
                        值越高生成的图像越具有创意和不可预测性。
                      </NTooltip>
                    </div>
                    <NInputNumber v-model:value="form.chaos" max="2" min="0" step="1" :precision="0" />
                  </NSpace>
                </NFormItem>
              </NGridItem>

              <NGridItem>
                <NFormItem>
                  <NSpace vertical>
                    <div>
                      <NText class="mr-1 text-3.7">种子 Seed</NText>
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                        </template>
                        通过设置相同的种子值，让每次生成的图像保持一致性和相似性
                      </NTooltip>
                    </div>
                    <NInputNumber v-model:value="form.seed" max="4294967295" min="0" :precision="0" />
                  </NSpace>
                </NFormItem>
              </NGridItem>

              <NGridItem>
                <NFormItem>
                  <NSpace vertical>
                    <div>
                      <NText class="mr-1 text-3.7">重复 Repeat</NText>
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                        </template>
                        用于重复上一次生成的图像或动作
                        <br />
                        如想一个提示词一次性多次生成图片可添加该参数。
                      </NTooltip>
                    </div>
                    <NInputNumber v-model:value="form.repeat" />
                  </NSpace>
                </NFormItem>
              </NGridItem>

              <NGridItem>
                <NFormItem>
                  <NSpace vertical class="w-50">
                    <div>
                      <NText class="mr-1 text-3.7">排除 No</NText>
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                        </template>
                        不希望生成的图片中出现的元素
                        <br />
                        如，希望生成一张不树和花的图像，可以写入tree、flowers。
                      </NTooltip>
                    </div>
                    <NInput v-model:value="form.exclude" />
                  </NSpace>
                </NFormItem>
              </NGridItem>

              <NGridItem>
                <NFormItem>
                  <NSpace vertical>
                    <div>
                      <NText class="mr-1 text-3.7">IW 图片权重</NText>
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <NButton text><SvgIcon icon="ph:question-bold" /></NButton>
                        </template>
                        可以影响生成图片时MJ对提供的参考图像、文本的重视程度。
                        <br />
                        如，在图生图中，想要更加遵循文本描述，不希望太过重视参考图片，可以通过该值设置。
                      </NTooltip>
                    </div>
                    <NSelect v-model:value="form.iw" :options="iw_options" class="w-50" />
                  </NSpace>
                </NFormItem>
              </NGridItem>
            </NGrid>
          </div>

          <!-- 官方参数显示隐藏按钮 -->
          <NButton
            v-if="currentPlatform === 'midjourney'"
            type="warning"
            class="mt-5 w-full"
            @click="isSystem = !isSystem"
          >
            <NText v-if="isSystem">隐藏官方参数</NText>
            <NText v-else>其他官方参数</NText>
          </NButton>
        </NForm>
      </NCard>
    </NScrollbar>

    <!-- 固定页面底部按钮 -->
    <NCard class="fixed-bottom" embedded>
      <ModelPrice v-slot="priceProps" :capacity="AiCapacity.IMAGE_GENERATION" :model="creditModel">
        <NButton
          type="info"
          :disabled="!priceProps.creditEnough"
          :loading="loading || localLoading || priceProps.loading"
          @click="handleGenerateClick"
        >
          <PriceIndicator :credit="priceProps.credit" />
          生成
        </NButton>
      </ModelPrice>
    </NCard>

    <NDrawer v-model:show="active" :default-width="835" placement="right" resizable>
      <NDrawerContent title="提示词库">
        <PromptLibrary @update:selected-prompts="handleSelectedPromptsUpdate" />
      </NDrawerContent>
    </NDrawer>
  </main>
</template>

<style scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollable-content {
  flex-grow: 1;
}

.fixed-bottom {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 12%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

:deep(.fixed-bottom) .n-card__content .n-button {
  width: 28.6em;
  height: 3.5em;
}

.ratio-buttons {
  width: 100%;
  display: flex;
  gap: 0.8em;
  justify-content: flex-start;
}

.ratio-option {
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px 10px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  transition: all 0.2s ease;
  border: 1px solid var(--n-border-color);
  width: 20%;
}

.ratio-option:hover:not(.ratio-option-active) {
  background-color: rgba(24, 160, 245, 0.04); /* 使用与选中状态类似但更淡的背景色 */
}

/* 选中效果 */
.ratio-option-active {
  box-shadow: 0 0 0 2px var(--n-color-primary);
  background-color: rgba(24, 160, 245, 0.08);
}

.ratio-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-bottom: 8px; */
  width: 2em;
  height: 2em;
  position: relative;
}

.ratio-preview {
  background-color: var(--n-border-color-hover);
  border: 1px solid var(--n-border-color);
  box-sizing: border-box;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.ratio-option-active .ratio-preview {
  background-color: var(--n-color-primary-light-1);
  border-color: var(--n-color-primary);
}

:deep(.promptBox) .n-form-item-feedback-wrapper {
  display: none;
}

:deep(.modelBox) .n-form-item-feedback-wrapper {
  display: none;
}

:deep(.radioBox) .n-form-item-blank {
  display: flex;
  justify-content: center;
}

.systemBox :deep(.n-form-item-feedback-wrapper),
.radioBox :deep(.n-form-item-feedback-wrapper) {
  display: none;
}

:deep(.modelBox) .n-form-item-blank {
  align-items: flex-start !important;
  flex-direction: column !important;
}

.promptBox :deep(.n-input-wrapper) {
  flex-direction: column !important;
  align-items: flex-end !important;
}

.promptBox :deep(.n-input__suffix) {
  width: 100% !important;
}
</style>
