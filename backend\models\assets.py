from sqlalchemy import Column, Integer, String, Text, JSON, DateTime, Enum
from sqlalchemy.dialects.mysql import TINYINT
from utils.database import Base
from models.pydanticBase import BaseModel
from enum import Enum as EnumType
from datetime import datetime
from typing import Optional, Dict, Any


class AssetType(str, EnumType):
    """资产类型枚举"""
    IMAGE = "IMAGE"
    VIDEO = "VIDEO"
    AUDIO = "AUDIO"


class Assets(Base):
    """资产表模型"""
    __tablename__ = "assets"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID / 资产标识')
    type = Column(Enum(AssetType), nullable=False, comment='资产类型，图片、视频等')
    url = Column(Text, nullable=False, comment='资产对应的资源 URL')
    user_id = Column(Integer, nullable=False, comment='用户标识', index=True)
    create_time = Column(DateTime, default=datetime.now, nullable=True, comment='生成的时间')
    iscollect = Column(TINYINT(1), default=0, nullable=True, comment='是否收藏：0-否，1-是')
    parameter = Column(JSON, nullable=True, comment='生成参数，JSON 格式')
    taskid = Column(String(32), nullable=True, comment='任务标识', index=True)
    biz_id = Column(String(32), nullable=False, default='', comment='业务标识，标记当前资产所属的业务板块', index=True)

    def __repr__(self):
        return f"<Assets(id={self.id}, type='{self.type}', user_id={self.user_id}, url='{self.url[:50]}...')>"


class AssetOut(BaseModel):
    """资产输出模型"""
    # id: int
    type: AssetType
    url: str
    # user_id: int
    create_time: Optional[datetime] = None
    iscollect: int = 0
    parameter: Optional[Dict[str, Any]] = None
    taskid: Optional[str] = None
    
    class Config:
        from_attributes = True
