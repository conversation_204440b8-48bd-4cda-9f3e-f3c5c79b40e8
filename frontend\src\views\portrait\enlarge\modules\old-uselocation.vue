<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { fetchEnlarge } from '@/service/api/portrait';
import type { enlargeRequest } from '@/service/api/portrait';
import { $t } from '@/locales';

const filePreview = ref<Array<{ url: string; name: string }>>([]);
const message = useMessage();

const faceStrong = ref(false);
const isOpenAudio = ref(false);

// 选择放大的倍数
const enlargeValue = ref<number>(4);

interface CustomRequestOptions {
  file: UploadFileInfo;
  onFinish: () => void;
}

const inputType = ref('video'); //  video 默认选中
const input_options = [
  { label: '视频', value: 'video' },
  // { label: '图片', value: 'image' },
  { label: '批量上传', value: 'batch_images' }
];

const model = ref('realesrgan-x4plus-anime');
const model_options = [
  { label: 'realesrgan-x4plus-anime', value: 'realesrgan-x4plus-anime' },
  { label: 'realesrgan-x4plus', value: 'realesrgan-x4plus' },
  { label: 'realesr-animevideov3', value: 'realesr-animevideov3' }
];

// 用于限制下拉框选项的长度
// const renderLabel = (option: SelectOption) => {
//   return h(
//     NEllipsis,
//     {
//       tooltip: true,
//       style: {
//         maxWidth: '128px',
//         display: 'inline-block'
//       }
//     },
//     { default: () => option.label }
//   );
// };

const customRequest = ({ file, onFinish }: CustomRequestOptions) => {
  const reader = new FileReader();
  reader.onload = e => {
    if (e.target && e.target.result && file.file) {
      // const fileData: FileItem = {
      //   filename: file.file.name, // 获取原始文件名
      //   data: e.target.result as string
      // };

      if (inputType.value === 'batch_images') {
        filePreview.value.push({
          url: e.target.result as string,
          name: file.file.name // 存储文件名用于预览（可选）
        });
      } else {
        // 对于单个文件上传（视频或单张图片），直接替换数组内容
        filePreview.value = [
          {
            url: e.target.result as string,
            name: file.file.name // 存储文件名用于预览（可选）
          }
        ];
      }
    }
    onFinish();
  };

  if (file.file) {
    reader.readAsDataURL(file.file);
  } else {
    message.error($t('page.portrait.badUpload'));
    onFinish();
  }
};

// const handleChange = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
//   console.log('Upload changed:', data);
// };

const openlimit = ref<boolean>(true);
watch(openlimit, newValue => {
  filePreview.value = [];
  if (!newValue) {
    message.info('请注意上传的文件过大,会消耗更多时间且效果不好');
  }
});

const beforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): boolean | Promise<boolean> => {
  const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg'];
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif'];

  if (inputType.value === 'video') {
    if (!allowedVideoTypes.includes(data.file.file?.type || '')) {
      message.error($t('page.portrait.uploadVideoError'));
      return false;
    }
    return new Promise(resolve => {
      if (!data.file.file) {
        message.error($t('page.portrait.uploadVideoError'));
        resolve(false);
        return;
      }
      const video = document.createElement('video');
      video.src = URL.createObjectURL(data.file.file as Blob);
      video.onloadedmetadata = () => {
        URL.revokeObjectURL(video.src);
        const duration = video.duration;
        const width = video.videoWidth;
        const height = video.videoHeight;
        if (duration > 60) {
          message.error($t('page.portrait.videoDuration'));
          resolve(false);
        } else if (openlimit.value) {
          if (width * height > 1024 * 1024) {
            message.error($t('page.portrait.videoBig'));
            resolve(false);
          } else {
            resolve(true);
          }
        } else {
          resolve(true);
        }
      };
    });
  }

  if (
    (inputType.value === 'image' || inputType.value === 'batch_images') &&
    !allowedImageTypes.includes(data.file.file?.type || '')
  ) {
    message.error($t('page.portrait.uploadImgerror'));
    return false;
  }

  return true;
};

function remove() {
  filePreview.value = [];
}

watch(inputType, () => {
  filePreview.value = [];
});

const uploadAccept = computed(() => {
  switch (inputType.value) {
    case 'video':
      return 'video/*';
    case 'image':
    case 'batch_images':
      return 'image/*';
    default:
      return '';
  }
});

const isMultiple = computed(() => {
  return inputType.value === 'batch_images';
});

watch(faceStrong, newValue => {
  if (newValue) {
    message.info('此功能只针对脸部模糊的图片效果最佳,开启后生成时间将大幅度延长,如有需要再开启!');
  }
});

// "打开音频"仅在视频模式下启用
const isAudioEnabled = computed(() => {
  return inputType.value === 'video';
});

watch(isAudioEnabled, newValue => {
  if (!newValue) {
    isOpenAudio.value = false;
  }
});

const outputType = ref('');
const outputData = ref('');
const downloadLink = ref('');
const downloadFilename = ref('');
const isloading = ref(false);

// 调用api
const handleGenerate = async () => {
  if (filePreview.value.length === 0) {
    message.error($t('page.portrait.pleaseUpload'));
    return;
  }

  const requestData: enlargeRequest = {
    file: filePreview.value.map(f => ({
      filename: f.name,
      data: f.url
    })),
    inputType: inputType.value,
    model: model.value,
    faceStrong: faceStrong.value,
    isOpenAudio: isOpenAudio.value,
    enlarge: enlargeValue.value
  };

  try {
    outputType.value = '';
    isloading.value = true;
    const response = await fetchEnlarge(requestData);

    if (response.data) {
      outputData.value = '';
      downloadLink.value = '';
      downloadFilename.value = '';
      isloading.value = false;
      outputType.value = response.data.inputType;

      if (outputType.value === 'video' || outputType.value === 'image') {
        // 直接使用返回的URL
        outputData.value = response.data.response_data;
      } else if (outputType.value === 'batch_images') {
        downloadLink.value = response.data.file_path;
        downloadFilename.value = response.data.filename;
      }
    } else {
      throw new Error('Invalid response code');
    }
  } catch (error) {
    // console.error('Generate error', error);
    message.error('生成失败');
  } finally {
    isloading.value = false;
  }
};
// watch(enlargeValue, newValue => {
//   console.log('enlargeValue changed:', newValue);
// });

const isDisabled = ref<boolean>(false);

watch(enlargeValue, newValue => {
  const numericValue = Number(newValue);
  console.log('enlargeValue:', newValue);
  if (numericValue !== 4) {
    model.value = 'realesr-animevideov3';
    isDisabled.value = true;
  } else {
    model.value = 'realesrgan-x4plus-anime';
    isDisabled.value = false;
  }
  console.log('isDisabled:', isDisabled.value);
});
</script>

<template>
  <main>
    <NGrid cols="1 l:2" responsive="screen" x-gap="25" y-gap="10">
      <NGridItem>
        <NCard class="left">
          <NSpace class="mt-5.5" justify="space-between">
            <NInputGroup class="w-45">
              <NButton type="info">模式</NButton>
              <NSelect v-model:value="inputType" clearable :options="input_options" />
            </NInputGroup>

            <NInputGroup class="w-70">
              <NButton type="info">模型</NButton>
              <NSelect v-model:value="model" clearable :options="model_options" :disabled="isDisabled" />
            </NInputGroup>

            <NInputGroup>
              <NButton type="info">放大倍数</NButton>
              <NRadioGroup v-model:value="enlargeValue">
                <NRadioButton key="2" value="2">x2</NRadioButton>
                <NRadioButton key="3" value="3">x3</NRadioButton>
                <NRadioButton key="4" value="4">x4</NRadioButton>
              </NRadioGroup>
            </NInputGroup>
          </NSpace>
          <NSpace class="mt-3" justify="space-between" align="end">
            <NSpace>
              <div class="checkbox">
                <NText class="whitespace-nowrap">脸部加强</NText>
                <NCheckbox v-model:checked="faceStrong"></NCheckbox>
              </div>

              <!--
 <div class="checkbox">
                <NText class="whitespace-nowrap">打开音频</NText>
                <NCheckbox v-model:checked="isOpenAudio" :disabled="!isAudioEnabled"></NCheckbox>
              </div>

              <div class="ml-3 flex gap-1">
                <NText>视频尺寸</NText>
                <NSwitch v-model:value="openlimit">
                  <template #checked>已限制</template>
                  <template #unchecked>解开限制</template>
                </NSwitch>
              </div>
-->
            </NSpace>

            <NButton type="info" @click="remove">
              <template #icon>
                <SvgIcon icon="icons8:cancel" />
              </template>
              取消上传
            </NButton>
          </NSpace>

          <div class="mt-2 h-125">
            <NUpload
              :custom-request="customRequest"
              :default-upload="true"
              :accept="uploadAccept"
              :multiple="isMultiple"
              :show-file-list="false"
              class="upload-component"
              @before-upload="beforeUpload"
            >
              <NUploadDragger>
                <template v-if="filePreview.length > 0">
                  <template v-if="inputType === 'video'">
                    <video :src="filePreview[0].url" controls class="preview-video" width="512" height="512"></video>
                  </template>
                  <template v-else-if="inputType === 'image'">
                    <NImage
                      :src="filePreview[0].url"
                      class="preview-image"
                      width="512"
                      height="512"
                      object-fit="contain"
                      preview-disabled
                    />
                  </template>
                  <template v-else-if="inputType === 'batch_images'">
                    <NScrollbar :style="{ height: `100%` }">
                      <NGrid cols="4" x-gap="12" y-gap="12" responsive="screen">
                        <NGridItem v-for="(file, index) in filePreview" :key="index" class="image-container">
                          <NImage
                            :src="file.url"
                            class="preview-image"
                            width="100%"
                            height="100%"
                            object-fit="cover"
                            preview-disabled
                          />
                        </NGridItem>
                      </NGrid>
                    </NScrollbar>
                  </template>
                </template>
                <div v-else>
                  <div class="flex justify-center">
                    <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
                  </div>
                  <NText>点击或者拖动{{ inputType === 'video' ? '视频' : '图片' }}到该区域来上传</NText>
                  <NP depth="3">
                    请不要上传{{ inputType === 'video' ? '视频' : '图片' }}格式外的文件,如文本或者表格等。
                  </NP>
                </div>
              </NUploadDragger>
            </NUpload>
          </div>

          <div class="mt-3">
            <NButton type="success" class="w-full" @click="handleGenerate">开始生成</NButton>
          </div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard class="right flex justify-center pt-6">
          <NH2>最终输出：</NH2>
          <div v-if="outputType !== 'batch_images'" class="box w-full flex justify-center">
            <template v-if="outputType === 'video'">
              <video :src="outputData" controls class="output-video"></video>
            </template>
            <template v-else-if="outputType === 'image'">
              <NImage class="imgbox h-157 w-4/5 flex justify-center" :src="outputData" />
            </template>
            <template v-else-if="isloading">
              <div class="loading-container">
                <div class="outer-circle">
                  <div class="inner-circle"></div>
                  <span></span>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </template>
          </div>
          <div class="mt-3">
            <NButton
              v-if="outputType === 'batch_images'"
              type="success"
              class="w-full"
              tag="a"
              :href="downloadLink"
              :download="downloadFilename"
            >
              下载压缩包
            </NButton>
          </div>
        </NCard>
      </NGridItem>
    </NGrid>
  </main>
</template>

<style scoped>
.left,
.right {
  width: 100%;
  height: 50em;
}

.upload-component {
  width: 100%;
  height: 100%;
}

:deep(.imgbox) > img {
  object-fit: contain !important;
}

:deep(.upload-component) .n-upload-trigger {
  width: 100%;
  height: 100%;
}

:deep(.upload-component) .n-upload-dragger {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: flex;
  justify-content: center;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

:deep(.right) .n-card__content .box {
  margin-top: 1.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40em;
  border: 1px dashed lightgray;
  border-radius: 10px;
}

/* :deep(.right) .n-card__content .box .imgbox {
  border: 1px dashed lightgray;
} */

.output-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  /* border: 1px dashed lightgray; */
  display: block;
  margin: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.outer-circle {
  position: relative;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
  animation: rotate 1.5s linear infinite;
}

.outer-circle span {
  position: absolute;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
}

.outer-circle span:nth-child(1) {
  filter: blur(5px);
}

.outer-circle span:nth-child(2) {
  filter: blur(10px);
}

.outer-circle span:nth-child(3) {
  filter: blur(25px);
}

.outer-circle span:nth-child(4) {
  filter: blur(150px);
}

.inner-circle {
  height: 180px;
  width: 180px;
  position: absolute;
  background: black;
  top: 10px;
  left: 10px;
  border-radius: 50%;
  z-index: 9;
}

@keyframes rotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}
</style>
