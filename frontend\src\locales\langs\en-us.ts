const local: App.I18n.Schema = {
  system: {
    title: 'Creative',
    updateTitle: 'System Version Update Notification',
    updateContent: 'A new version of the system has been detected. Do you want to refresh the page immediately?',
    updateConfirm: 'Refresh immediately',
    updateCancel: 'Later'
  },
  common: {
    credit: 'User Credit',
    action: 'Action',
    add: 'Add',
    addSuccess: 'Add Success',
    backToHome: 'Back to home',
    backToLogin: 'Back to login',
    batchDelete: 'Batch Delete',
    cancel: 'Cancel',
    close: 'Close',
    check: 'Check',
    expandColumn: 'Expand Column',
    columnSetting: 'Column Setting',
    config: 'Config',
    confirm: 'Confirm',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    confirmDelete: 'Are you sure you want to delete?',
    edit: 'Edit',
    index: 'Index',
    keywordSearch: 'Please enter keyword',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to log out?',
    lookForward: 'Coming soon',
    modify: 'Modify',
    modifySuccess: 'Modify Success',
    noData: 'No Data',
    operate: 'Operate',
    pleaseCheckValue: 'Please check whether the value is valid',
    refresh: 'Refresh',
    reset: 'Reset',
    search: 'Search',
    switch: 'Switch',
    tip: 'Tip',
    trigger: 'Trigger',
    update: 'Update',
    updateSuccess: 'Update Success',
    userCenter: 'User Center',
    yesOrNo: {
      yes: 'Yes',
      no: 'No'
    },
    creditNotEnough: 'Credit not enough'
  },
  request: {
    logout: 'Logout user after request failed',
    logoutMsg: 'User status is invalid, please log in again',
    logoutWithModal: 'Pop up modal after request failed and then log out user',
    logoutWithModalMsg: 'User status is invalid, please log in again',
    refreshToken: 'The requested token has expired, refresh the token',
    tokenExpired: 'The requested token has expired'
  },
  theme: {
    themeSchema: {
      title: 'Theme Schema',
      light: 'Light',
      dark: 'Dark',
      auto: 'Follow System'
    },
    grayscale: 'Grayscale',
    layoutMode: {
      title: 'Layout Mode',
      vertical: 'Vertical Menu Mode',
      horizontal: 'Horizontal Menu Mode',
      'vertical-mix': 'Vertical Mix Menu Mode',
      'horizontal-mix': 'Horizontal Mix menu Mode'
    },
    recommendColor: 'Apply Recommended Color Algorithm',
    recommendColorDesc: 'The recommended color algorithm refers to',
    themeColor: {
      title: 'Theme Color',
      primary: 'Primary',
      info: 'Info',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      followPrimary: 'Follow Primary'
    },
    scrollMode: {
      title: 'Scroll Mode',
      wrapper: 'Wrapper',
      content: 'Content'
    },
    page: {
      animate: 'Page Animate',
      mode: {
        title: 'Page Animate Mode',
        fade: 'Fade',
        'fade-slide': 'Slide',
        'fade-bottom': 'Fade Zoom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'None'
      }
    },
    fixedHeaderAndTab: 'Fixed Header And Tab',
    header: {
      height: 'Header Height',
      breadcrumb: {
        visible: 'Breadcrumb Visible',
        showIcon: 'Breadcrumb Icon Visible'
      }
    },
    tab: {
      visible: 'Tab Visible',
      cache: 'Tab Cache',
      height: 'Tab Height',
      mode: {
        title: 'Tab Mode',
        chrome: 'Chrome',
        button: 'Button'
      }
    },
    sider: {
      inverted: 'Dark Sider',
      width: 'Sider Width',
      collapsedWidth: 'Sider Collapsed Width',
      mixWidth: 'Mix Sider Width',
      mixCollapsedWidth: 'Mix Sider Collapse Width',
      mixChildMenuWidth: 'Mix Child Menu Width'
    },
    footer: {
      visible: 'Footer Visible',
      fixed: 'Fixed Footer',
      height: 'Footer Height',
      right: 'Right Footer'
    },
    themeDrawerTitle: 'Theme Configuration',
    pageFunTitle: 'Page Function',
    configOperation: {
      copyConfig: 'Copy Config',
      copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
      resetConfig: 'Reset Config',
      resetSuccessMsg: 'Reset Success'
    }
  },
  route: {
    login: 'Login',
    403: 'No Permission',
    404: 'Page Not Found',
    500: 'Server Error',
    'iframe-page': 'Iframe',
    home: 'Recommend',
    document: 'Document',
    document_project: 'Project Document',
    'document_project-link': 'Project Document(External Link)',
    document_vue: 'Vue Document',
    document_vite: 'Vite Document',
    document_unocss: 'UnoCSS Document',
    document_naive: 'Naive UI Document',
    document_antd: 'Ant Design Vue Document',
    'user-center': 'User Center',
    about: 'About',
    function: 'System Function',
    function_tab: 'Tab',
    'function_multi-tab': 'Multi Tab',
    'function_hide-child': 'Hide Child',
    'function_hide-child_one': 'Hide Child',
    'function_hide-child_two': 'Two',
    'function_hide-child_three': 'Three',
    function_request: 'Request',
    'function_toggle-auth': 'Toggle Auth',
    'function_super-page': 'Super Admin Visible',
    manage: 'System Manage',
    manage_user: 'User Manage',
    'manage_user-detail': 'User Detail',
    manage_role: 'Role Manage',
    manage_creditlog: 'Credit log',
    'manage_user-credit': 'User Credit',
    manage_settings: 'Dictionary Config',
    'manage_system-config': 'System Config',
    manage_menu: 'Menu Manage',
    manage_workteams: 'Teams Manage',
    'multi-menu': 'Multi Menu',
    'multi-menu_first': 'Menu One',
    'multi-menu_first_child': 'Menu One Child',
    'multi-menu_second': 'Menu Two',
    'multi-menu_second_child': 'Menu Two Child',
    'multi-menu_second_child_home': 'Menu Two Child Home',
    text_translate: 'translate',
    text: 'text',
    text_copywriting: 'copywriting',
    exception: 'Exception',
    exception_403: '403',
    exception_404: '404',
    exception_500: '500',
    management: 'Management',
    management_game: 'Game',
    management_credit: 'Model Credit',
    management_toolset: 'toolset',
    navigation: 'home',
    portrait: 'Image',
    portrait_cutout: 'cutout',
    portrait_subtitle: 'Extract Subtitle',
    audio: 'Audio',
    audio_separate: 'Voice Separate',
    audio_texttospeech: 'Text To Speech',
    audio_cosy: 'Cosy Voice',
    portrait_enlarge: 'Enlarge',
    portrait_aidraw: 'ai Draw',
    portrait_facecopy: 'Face Copy',
    portrait_midjourney: 'AI Draw',
    portrait_mimicbrush: 'Mimic Brush',
    statis: 'Statis',
    statis_taskcall: 'Task',
    statis_page: 'Page',
    audio_volcano: 'volcengine TTS',
    portrait_mjworks: 'Midjourney Works',
    aichat: 'Chat',
    aichat_assistant: 'Chat',
    wiki: 'Wiki',
    wiki_document: 'Use wiki',
    monitor: 'Monitor',
    monitor_process: 'Process',
    text_imgocr: 'Text Extraction',
    audio_reduction: 'Noise Reduction',
    portrait_sdwebui: 'Stable Diffusion',
    wiki_ainews: 'Information Sharing',
    stablediffusion: 'stable diffusion',
    management_channels: 'Channels',
    audio_clearvoice: 'Clear Voice',
    audio_inspiremusic: 'Music Creation',
    video: 'Video',
    video_framepack: 'Video Creation',
    manage_models: 'Models Managemant',
    'manage_system-config_modules': 'Modules',
    'manage_system-config_modules_ai-models-setting': 'AI Models Setting',
    'share-page': 'Share',
    'asset-manage': 'Assets',
    'asset-manage_user-assets': 'My Assets',
    'asset-manage_asset-info': 'Asset Info',
    'asset-manage_asset-report': 'Asset Report',
    'asset-manage_user-report': 'User Report',
    audio_timbres: 'Timbres',
    audio_synthesis: 'Synthesis',
    'portrait_image-edit': 'Image Edit'
  },
  page: {
    mobile: {
      routeName: 'AIHUB',
      chat: {
        routeName: 'Chat'
      }
    },
    chat: {
      inputPlaceholder: 'Let us chat...(Shift + Enter = new line, Ctrl+v to upload an image)',
      inputPlaceholderMobile: 'Let us chat...',
      inputPlaceholderImageGeneration: 'Tell me about your inspiration.',
      uploadImage: 'Upload Image',
      enableWeb: 'Web Search',
      skills: 'Model Skills',
      newChat: 'Create New',
      optimization: 'Prompt Optimization',
      webSearchResult: 'pages',
      webSearchResultTitle: 'Related',
      webSearchingText: 'Web Searching'
    },
    portrait: {
      uploadImgerror: 'only upload image files in png, jpg, jpeg, bmp, webp formats',
      pleaseUpload: 'Please upload the file first and select the model and output type',
      badUpload: 'Unable to read file',
      uploadVideoError: 'Only video files in mp4, webm, ogg formats can be uploaded. Please upload again',
      videoBig: 'Video resolution cannot exceed 1280*720',
      videoDuration: 'Uploaded videos cannot be longer than one minute',
      faceStrongInfo:
        'This function is best for pictures with blurred faces. The generation time will be greatly extended after it is turned on. Turn it on if necessary!'
    },
    text: {
      pleaseChooseGame: 'Please make a Game selection',
      pleaseChoose: 'Please make a selection',
      maxTags: 'You can only add up to five tags',
      requiredValue: 'Please fill in all required fields',
      interrupt: 'Stop copywriting',
      uploadExcel: 'Please upload the form file',
      uploadSuccess: 'Upload success!',
      uploadError: 'File upload failed',
      onlyTranslate: 'Please clear the input box / cancel file upload',
      tsSuccess: 'Successful translation',
      viewPresets: 'Preset data loaded successfully',
      savePresets: 'Saved successfully',
      onlyJson: 'Please upload a JSON file',
      onlyExcel: 'only upload files in Excel or CSV format. Please upload again'
    },
    login: {
      common: {
        loginOrRegister: 'Login / Register',
        userNamePlaceholder: 'Please enter user name',
        phonePlaceholder: 'Please enter phone number',
        codePlaceholder: 'Please enter verification code',
        passwordPlaceholder: 'Please enter password',
        confirmPasswordPlaceholder: 'Please enter password again',
        codeLogin: 'Verification code login',
        confirm: 'Confirm',
        back: 'Back',
        validateSuccess: 'Verification passed',
        loginSuccess: 'Login successfully',
        welcomeBack: 'Welcome back, {userName} !'
      },
      pwdLogin: {
        title: 'Password Login',
        rememberMe: 'Remember me',
        forgetPassword: 'Forget password?',
        register: 'Register',
        otherAccountLogin: 'Other Account Login',
        otherLoginMode: 'Other Login Mode',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        user: 'User'
      },
      codeLogin: {
        title: 'Verification Code Login',
        getCode: 'Get verification code',
        reGetCode: 'Reacquire after {time}s',
        sendCodeSuccess: 'Verification code sent successfully',
        imageCodePlaceholder: 'Please enter image verification code'
      },
      register: {
        title: 'Register',
        agreement: 'I have read and agree to',
        protocol: '《User Agreement》',
        policy: '《Privacy Policy》'
      },
      resetPwd: {
        title: 'Reset Password'
      },
      bindWeChat: {
        title: 'Bind WeChat'
      },
      dingding: {
        title: 'DingDing Login'
      },
      osa: {
        title: 'OSA Login'
      }
    },
    about: {
      title: 'About',
      introduction: `SoybeanAdmin is an elegant and powerful admin template, based on the latest front-end technology stack, including Vue3, Vite5, TypeScript, Pinia and UnoCSS. It has built-in rich theme configuration and components, strict code specifications, and an automated file routing system. In addition, it also uses the online mock data solution based on ApiFox. SoybeanAdmin provides you with a one-stop admin solution, no additional configuration, and out of the box. It is also a best practice for learning cutting-edge technologies quickly.`,
      projectInfo: {
        title: 'Project Info',
        version: 'Version',
        latestBuildTime: 'Latest Build Time',
        githubLink: 'Github Link',
        previewLink: 'Preview Link'
      },
      prdDep: 'Production Dependency',
      devDep: 'Development Dependency'
    },
    home: {
      greeting: 'Good morning, {userName}, today is another day full of vitality!',
      weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
      projectCount: 'Project Count',
      todo: 'Todo',
      message: 'Message',
      downloadCount: 'Download Count',
      registerCount: 'Register Count',
      schedule: 'Work and rest Schedule',
      study: 'Study',
      work: 'Work',
      rest: 'Rest',
      entertainment: 'Entertainment',
      visitCount: 'Visit Count',
      turnover: 'Turnover',
      dealCount: 'Deal Count',
      distinctUserid: 'Distinct Userid',
      count: 'Count',
      data: 'Data',
      projectNews: {
        title: 'Project News',
        moreNews: 'More News',
        desc1: 'Soybean created the open source project soybean-admin on May 28, 2021!',
        desc2: 'Yanbowe submitted a bug to soybean-admin, the multi-tab bar will not adapt.',
        desc3: 'Soybean is ready to do sufficient preparation for the release of soybean-admin!',
        desc4: 'Soybean is busy writing project documentation for soybean-admin!',
        desc5: 'Soybean just wrote some of the workbench pages casually, and it was enough to see!'
      },
      creativity: 'Creativity'
    },
    function: {
      tab: {
        tabOperate: {
          title: 'Tab Operation',
          addTab: 'Add Tab',
          addTabDesc: 'To about page',
          closeTab: 'Close Tab',
          closeCurrentTab: 'Close Current Tab',
          closeAboutTab: 'Close "About" Tab',
          addMultiTab: 'Add Multi Tab',
          addMultiTabDesc1: 'To MultiTab page',
          addMultiTabDesc2: 'To MultiTab page(with query params)'
        },
        tabTitle: {
          title: 'Tab Title',
          changeTitle: 'Change Title',
          change: 'Change',
          resetTitle: 'Reset Title',
          reset: 'Reset'
        }
      },
      multiTab: {
        routeParam: 'Route Param',
        backTab: 'Back function_tab'
      },
      toggleAuth: {
        toggleAccount: 'Toggle Account',
        authHook: 'Auth Hook Function `hasAuth`',
        superAdminVisible: 'Super Admin Visible',
        adminVisible: 'Admin Visible',
        adminOrUserVisible: 'Admin and User Visible'
      },
      request: {
        repeatedErrorOccurOnce: 'Repeated Request Error Occurs Once',
        repeatedError: 'Repeated Request Error',
        repeatedErrorMsg1: 'Custom Request Error 1',
        repeatedErrorMsg2: 'Custom Request Error 2'
      }
    },
    manage: {
      common: {
        status: {
          enable: 'Enable',
          disable: 'Disable'
        }
      },
      role: {
        title: 'Role List',
        roleName: 'Role Name',
        roleCode: 'Role Code',
        roleStatus: 'Role Status',
        roleDesc: 'Role Description',
        menuAuth: 'Menu Auth',
        buttonAuth: 'Button Auth',
        roleUser: 'Role User',
        form: {
          roleName: 'Please enter role name',
          roleCode: 'Please enter role code',
          roleStatus: 'Please select role status',
          roleDesc: 'Please enter role description'
        },
        addRole: 'Add Role',
        editRole: 'Edit Role'
      },
      user: {
        id: 'User ID',
        title: 'User List',
        userName: 'User Name',
        userGender: 'Gender',
        nickName: 'Nick Name',
        userPhone: 'Phone Number',
        userEmail: 'Email',
        company: 'Company',
        userStatus: 'User Status',
        userRole: 'User Role',
        password: 'Password',
        updateTime: 'Update Time',
        loginIp: 'Login IP',
        loginTime: 'Login Time',
        updateBy: 'Updated By',
        form: {
          userName: 'Please enter user name',
          userGender: 'Please select gender',
          nickName: 'Please enter nick name',
          userPhone: 'Please enter phone number',
          userEmail: 'Please enter email',
          userStatus: 'Please select user status',
          userRole: 'Please select user role',
          password: 'Please enter Password',
          repetition_password: 'Password again'
        },
        addUser: 'Add User',
        editUser: 'Edit User',
        gender: {
          unknown: 'unknown',
          male: 'Male',
          female: 'Female'
        },
        creditManagement: 'Credit Management',
        credit: 'Credit'
      },
      menu: {
        home: 'Home',
        title: 'Menu List',
        id: 'ID',
        parentId: 'Parent ID',
        menuType: 'Menu Type',
        menuName: 'Menu Name',
        routeName: 'Route Name',
        routeNameDesc: 'Route name, also used for backend authentication',
        routePath: 'Route Path',
        pathParam: 'Path Param',
        layout: 'Layout Component',
        page: 'Page Component',
        i18nKey: 'I18n Key',
        icon: 'Icon',
        localIcon: 'Local Icon',
        iconTypeTitle: 'Icon Type',
        order: 'Order',
        constant: 'Constant',
        keepAlive: 'Keep Alive',
        href: 'Href',
        hideInMenu: 'Hide In Menu',
        activeMenu: 'Active Menu',
        multiTab: 'Multi Tab',
        fixedIndexInTab: 'Fixed Index In Tab',
        query: 'Query Params',
        button: 'Button',
        buttonCode: 'Button Code',
        buttonDesc: 'Button Desc',
        menuStatus: 'Menu Status',
        form: {
          home: 'Please select home',
          menuType: 'Please select menu type',
          menuName: 'Please enter menu name',
          routeName: 'Please enter route name',
          routePath: 'Please enter route path',
          pathParam: 'Please enter path param',
          page: 'Please select page component',
          layout: 'Please select layout component',
          i18nKey: 'Please enter i18n key',
          icon: 'Please enter iconify name',
          localIcon: 'Please enter local icon name',
          order: 'Please enter order',
          keepAlive: 'Please select whether to cache route',
          href: 'Please enter href',
          hideInMenu: 'Please select whether to hide menu',
          activeMenu: 'Please select route name of the highlighted menu',
          multiTab: 'Please select whether to support multiple tabs',
          fixedInTab: 'Please select whether to fix in the tab',
          fixedIndexInTab: 'Please enter the index fixed in the tab',
          queryKey: 'Please enter route parameter Key',
          queryValue: 'Please enter route parameter Value',
          button: 'Please select whether it is a button',
          buttonCode: 'Please enter button code',
          buttonDesc: 'Please enter button description',
          menuStatus: 'Please select menu status'
        },
        addMenu: 'Add Menu',
        editMenu: 'Edit Menu',
        addChildMenu: 'Add Child Menu',
        addButton: 'Add Button',
        setRole: 'Set Role',
        type: {
          directory: 'Directory',
          menu: 'Menu',
          button: 'Button'
        },
        iconType: {
          iconify: 'Iconify Icon',
          local: 'Local Icon'
        }
      },
      model: {
        title: 'Model List',
        name: 'Name',
        name_col: 'Model',
        label: 'Label',
        description: 'Description',
        publishers: 'Publishers',
        icon: 'Icon',
        capacity: 'Capacity',
        status: 'Status',
        statusText: {
          enabled: 'Enabled',
          disabled: 'Disabled'
        },
        addModel: 'Add Model',
        editModel: 'Edit Model',
        form: {
          name: 'Please enter model identity name',
          label: 'Please enter label, use for display',
          description: 'Please enter description'
        },
        setting: {
          saveBtn: 'Save',
          assistant: {
            title: 'AI Assistant'
          }
        }
      }
    },
    media: {
      subtitle: {
        model: 'Model',
        format: 'Format',
        game: 'Game',
        translate: 'Translate'
      },
      form: {
        text: {
          dndVideoOrAudio: 'Drag and drop audio or video here',
          noFileSelected: 'No file selected',
          originalText: 'Original Text',
          translatedText: 'Translated Text'
        },
        btn: {
          extractSubtitle: 'Extract Subtitle',
          translate: 'Translate',
          download: 'Download',
          del: 'Delete'
        }
      }
    },
    'share-page': {
      prompt: 'Prompt',
      copy: 'Copy',
      createSame: 'Create Same',
      makeVideo: 'Make Video',
      copySuccess: 'Copy successful!',
      copyError: 'Copy error!'
    }
  },
  form: {
    required: 'Cannot be empty',
    userName: {
      required: 'Please enter user name',
      invalid: 'User name format is incorrect'
    },
    phone: {
      required: 'Please enter phone number',
      invalid: 'Phone number format is incorrect'
    },
    pwd: {
      required: 'Please enter password',
      invalid: 'Password must be 6-18 characters and contain both letters and numbers'
    },
    confirmPwd: {
      required: 'Please enter password again',
      invalid: 'The two passwords are inconsistent'
    },
    code: {
      required: 'Please enter verification code',
      invalid: 'Verification code format is incorrect'
    },
    email: {
      required: 'Please enter email',
      invalid: 'Email format is incorrect'
    }
  },
  dropdown: {
    closeCurrent: 'Close Current',
    closeOther: 'Close Other',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeAll: 'Close All'
  },
  icon: {
    themeConfig: 'Theme Configuration',
    themeSchema: 'Theme Schema',
    lang: 'Switch Language',
    fullscreen: 'Fullscreen',
    fullscreenExit: 'Exit Fullscreen',
    reload: 'Reload Page',
    collapse: 'Collapse Menu',
    expand: 'Expand Menu',
    pin: 'Pin',
    unpin: 'Unpin'
  },
  datatable: {
    itemCount: 'Total {total} items'
  }
};

export default local;
