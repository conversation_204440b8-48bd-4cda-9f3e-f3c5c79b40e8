import { request } from '../request';

export interface FileItem {
  filename: string; // 原始文件名
  data: string; // base64编码的文件数据
}

export interface enlargeRequest {
  file: FileItem[]; // 修改为包含文件名和数据的列表
  inputType: string;
  model: string;
  faceStrong: boolean;
  isOpenAudio: boolean;
  enlarge: number | string;
}

export interface LogoInfo {
  add_logo: boolean;
  position?: number;
  language?: number;
  opacity?: number;
  logo_text_content?: string;
}

export interface VolcengineEnlargeRequest {
  file: FileItem[];
  enlarge: string;
  enable_hdr?: boolean;
  enable_wb?: boolean;
  result_format?: number;
  jpg_quality?: number;
  hdr_strength?: number;
  logo_info?: LogoInfo;
}

export interface VolcengineCutoutRequest {
  file: FileItem[];
  only_mask: number; // 0: 前景图(背景透明), 1: 蒙版
  rgb?: number[];
  refine_mask?: number;
}

export interface VolcengineCutoutResponse {
  code: string;
  data:
    | {
        image_url?: string; // 图像URL（原有方式）
        image_base64?: string; // Base64格式的图像数据（新增方式）
        contours_point?: any[]; // 轮廓点数据
        bbox?: number[][]; // 边界框数据
        mask_url?: string; // 蒙版URL（如果有）
      }
    | null
    | Record<string, never>;
  msg?: string;
}

export interface SendPromptRequest {
  imagename: string;
  videoname: string;
  animation_region?: string;
  driving_option?: string;
  driving_multiplier?: number;
}

/** 获取剪切后的图像 */
export function fetchCutoutImg(model: string, outputType: string, image: string) {
  return request({
    url: '/cutout/get_cutout_img',
    method: 'post',
    data: { model, output_type: outputType, image }
  });
}

/** 使用火山引擎API进行图片抠图 */
export function fetchVolcengineCutout(data: VolcengineCutoutRequest) {
  return request<VolcengineCutoutResponse>({
    url: '/cutout/volcengine_cutout',
    method: 'post',
    data
  });
}

/** 获取放大后的文件数据 */
export function fetchEnlarge(data: enlargeRequest) {
  return request({
    url: '/enlarge/get_enlarge_file',
    method: 'post',
    data
  });
}

/** 使用火山引擎API放大图片 */
export function fetchVolcengineEnlarge(data: VolcengineEnlargeRequest) {
  return request({
    url: '/enlarge/volcengine_enlarge',
    method: 'post',
    data
  });
}

/** facecopy */

/** 上传文件到资源机上 */
export function fetchUpload(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/facecopy/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 发送prompt请求 */
export function sendPrompt(data: SendPromptRequest) {
  return request({
    url: '/facecopy/send_prompt',
    method: 'post',
    data
  });
}

/** 获取队列状态 */
export function getQueueStatus() {
  return request({
    url: '/facecopy/queue_status',
    method: 'get'
  });
}

/** 获取历史任务数据 */
export function fetchHistory(promptId: string) {
  return request({
    url: `/facecopy/history/${promptId}`,
    method: 'get'
  });
}

/** 生成结果预览 */
export function viewImage(filename: string) {
  return request({
    url: '/facecopy/view',
    method: 'get',
    params: { filename }
  });
}

/** 预处理文件为视频 */
export function generateVideo(data: SendPromptRequest) {
  return request({
    url: '/facecopy/isvedio',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/** 上传文件到资源机上 不走comfyui */
export function no_comfyui_upload(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/facecopy/Upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 预处理文件为视频 */
export function generateAnimalVideo(data: SendPromptRequest) {
  return request({
    url: '/facecopy/isAnimal',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
