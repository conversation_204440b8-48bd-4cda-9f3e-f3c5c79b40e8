<script setup lang="ts">
import { onMounted, ref } from 'vue';
import SovitsTts from './modules/sovits-tts.vue';
import SovitsShot from './modules/sovits-zeroshot.vue';
import ChatTts from './modules/chat-tts.vue';
const tabModel = ref<string>('model_1');

// 隐藏表单空容器
onMounted(() => {
  // 创建一个观察器实例
  const observer = new MutationObserver(() => {
    const feedbackWrappers = document.querySelectorAll('.n-form-item-feedback-wrapper');
    feedbackWrappers.forEach(wrapper => {
      (wrapper as HTMLElement).style.display = 'none';
    });
  });

  // 配置观察选项
  const config = {
    childList: true,
    subtree: true
  };

  // 开始观察文档中的变化
  observer.observe(document.body, config);
});
</script>

<template>
  <div class="flex-row-stretch gap-16px">
    <NTabs v-model:value="tabModel" type="line" animated>
      <NTabPane name="model_1" tab="SoVITS 语音合成">
        <SovitsTts />
      </NTabPane>
      <NTabPane name="model_2" tab="SoVITS 极速复刻">
        <SovitsShot />
      </NTabPane>
      <NTabPane name="model_3" tab="Chat TTS 语音合成">
        <ChatTts />
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped lang="scss"></style>
