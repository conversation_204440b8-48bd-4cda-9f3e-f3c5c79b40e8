<script setup lang="ts">
import { computed } from 'vue';
import { NButton, NCard, NFlex, NTag, NText } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

interface MusicTask {
  id: number;
  taskid: string;
  status: 'not_start' | 'submitted' | 'in_progress' | 'failure' | 'success';
  prompt?: string | null;
  music_title: string;
  audio_data?: { url: string; type: string; duration?: number; sample_rate?: number } | null;
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

interface Props {
  task: MusicTask;
  isPlaying?: boolean;
}
const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'play', task: MusicTask): void;
  (e: 'pause', task: MusicTask): void;
  (e: 'download', task: MusicTask): void;
}>();

const formattedSubmitTime = computed(() => {
  if (!props.task.submit_time) return '';
  try {
    return new Date(props.task.submit_time).toLocaleString();
  } catch (e) {
    return props.task.submit_time;
  }
});

const handlePlay = () => {
  if (props.task.audio_data?.url) {
    if (props.isPlaying) {
      emit('pause', props.task);
    } else {
      emit('play', props.task);
    }
  }
};

const handleDownload = () => {
  if (props.task.audio_data?.url) {
    emit('download', props.task);
  }
};
</script>

<template>
  <NCard class="music-card" :bordered="false" hoverable>
    <NFlex justify="space-between">
      <!-- 顶部信息栏 -->
      <NFlex align="center" justify="space-between" :wrap="false">
        <NFlex align="center" size="small" :wrap="false">
          <div class="music-thumbnail">
            <img
              src="https://static-cos.mureka.ai/cos-prod/res/image/default/304.png?x-oss-process=image/resize,h_536/format,webp"
              alt="音乐缩略图"
              class="thumbnail-img"
            />
          </div>
          <NFlex vertical>
            <NFlex align="center" size="small" class="mb-1" :wrap="true">
              <NText class="music-title">{{ props.task.music_title }}</NText>
              <!--
 <NTag type="info" size="small" round>
                {{ props.task.audio_data?.type }}
              </NTag>
-->
            </NFlex>
            <NFlex vertical size="small" :wrap="true">
              <NEllipsis depth="3" style="max-width: 240px">{{ props.task.prompt || '无描述' }}</NEllipsis>

              <NText depth="3" class="music-date">{{ formattedSubmitTime }}</NText>
            </NFlex>
          </NFlex>
        </NFlex>
      </NFlex>

      <!-- 状态 -->
      <NFlex v-if="props.task.status === 'submitted' || props.task.status === 'in_progress'" align="center">
        <NTag
          v-if="props.task.status === 'submitted' && props.task.queue_position && props.task.queue_position > 0"
          round
          size="large"
          type="info"
        >
          正在排队，前方还有 {{ props.task.queue_position }} 个任务
          <template #icon>
            <SvgIcon icon="streamline:information-desk" class="ml-1" />
          </template>
        </NTag>
        <NTag v-else-if="props.task.status === 'submitted'" round size="large" type="info">
          正在排队
          <template #icon>
            <SvgIcon icon="streamline:information-desk" class="ml-1" />
          </template>
        </NTag>
        <NTag v-else-if="props.task.status === 'in_progress'" round size="large" type="info">
          正在生成中
          <template #icon>
            <SvgIcon icon="eos-icons:bubble-loading" class="ml-1" />
          </template>
        </NTag>
      </NFlex>

      <!-- 失败状态 -->
      <NFlex v-else-if="props.task.status === 'failure'" align="center">
        <NTag round size="large" type="error">
          {{ props.task.fail_reason || '未知原因' }}
          <template #icon>
            <SvgIcon icon="material-symbols:error-outline" class="ml-1" />
          </template>
        </NTag>
      </NFlex>

      <!-- 成功状态: 播放和下载 -->
      <NFlex v-else-if="props.task.status === 'success'" align="center">
        <NButton round size="large" type="info" :disabled="!props.task.audio_data?.url" @click="handlePlay">
          {{ props.isPlaying ? '暂停' : '播放' }}
          <template #icon>
            <SvgIcon :icon="props.isPlaying ? 'icon-park-outline:pause' : 'icon-park-outline:play'" />
          </template>
        </NButton>

        <NButton round size="large" type="info" :disabled="!props.task.audio_data?.url" @click="handleDownload">
          下载
          <template #icon>
            <SvgIcon icon="material-symbols:download" />
          </template>
        </NButton>
      </NFlex>

      <!-- 其他或未知状态 -->
      <NFlex v-else align="center">
        <NTag round size="large" type="warning">状态未知: {{ props.task.status }}</NTag>
      </NFlex>
    </NFlex>
  </NCard>
</template>

<style scoped lang="scss">
.music-card {
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  }
}

.music-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
  }
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.music-title {
  font-size: 20px;
  font-weight: 600;
  margin-right: 8px;

  @media (max-width: 768px) {
    font-size: 18px;
  }
}

.music-subtitle {
  max-width: 5em;
  font-size: 14px;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.music-date {
  font-size: 14px;
  color: rgba(128, 128, 128, 0.8);

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.action-bar {
  margin-top: 12px;
}

.action-btn {
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.publish-btn {
  padding: 0 32px;
  border-radius: 20px;
  font-weight: 500;
  height: 36px;
  font-size: 15px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  @media (max-width: 768px) {
    padding: 0 24px;
    height: 32px;
    font-size: 14px;
  }
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}
</style>
