import { request } from '../request';

export function getMyAssets(page: number, size: number, type: string, iscollect?: number) {
  return request({
    url: '/asset/get_my_assets',
    method: 'get',
    params: {
      page,
      size,
      type,
      ...(iscollect !== undefined ? { iscollect } : {})
    }
  });
}

export function toggleCollect(taskid: string) {
  return request({
    url: '/asset/toggle_collect',
    method: 'post',
    params: {
      taskid
    }
  });
}

export function getAssetInfoList(params: {
  page: number;
  size: number;
  type: string;
  taskid?: string;
  resetPage?: boolean;
  userid?: number;
}) {
  const { page, size, type, taskid, resetPage = true, userid } = params;
  return request({
    url: '/asset/asset_info_list',
    method: 'get',
    params: {
      page,
      size,
      type,
      ...(taskid ? { taskid } : {}),
      reset_page: resetPage,
      ...(userid !== undefined ? { userid } : {})
    }
  });
}

export function getAssetStatistics(params?: { startDate?: string; endDate?: string; company?: string }) {
  return request({
    url: '/asset/statistics',
    method: 'get',
    params
  });
}

export function getUserAssets(params: {
  page: number;
  pageSize: number;
  startDate?: string;
  endDate?: string;
  company?: string;
  sortField?: string;
  sortOrder?: string;
}) {
  return request({
    url: '/asset/user_assets',
    method: 'get',
    params
  });
}

export function getUserAssetsById(page: number, size: number, type: string, userid: number, iscollect?: number) {
  return request({
    url: '/asset/get_user_assets',
    method: 'get',
    params: {
      page,
      size,
      type,
      userid,
      ...(iscollect !== undefined ? { iscollect } : {})
    }
  });
}
