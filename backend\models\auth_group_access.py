from sqlalchemy import Column, Integer, UniqueConstraint
from utils.database import Base


class AuthGroupAccess(Base):
    __tablename__ = "auth_group_access"

    uid = Column(Integer, primary_key=True, nullable=False, comment='会员ID')
    group_id = Column(Integer, primary_key=True, nullable=False, comment='级别ID')

    __table_args__ = (
      UniqueConstraint('uid', 'group_id', name='uid_group_id'),
    )

    def __repr__(self):
        return f"<AuthGroupAccess(uid={self.uid}, group_id={self.group_id})>"
