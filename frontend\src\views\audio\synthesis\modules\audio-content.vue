<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { ParamSeg, SynthesisTone } from '@/service/api/audio';

// 定义props
const props = defineProps<{
  toneData?: SynthesisTone | null;
}>();

const emit = defineEmits<{
  (e: 'addMore'): void;
  (e: 'remove'): void;
  (e: 'selectTone', toneData?: SynthesisTone | null): void;
}>();

// 文本输入内容
const textContent = ref('');

// 计算显示的音色名称
const modelName = computed(() => {
  if (!props.toneData) {
    return '选择音色';
  }

  // 优先显示音色名称，如果没有则显示音色ID
  return props.toneData.tone_name || `音色 ${props.toneData.tone_id}` || '选择音色';
});

const isMarquee = () => {
  return modelName.value.length > 7;
};

// 获取音色的tooltip信息
const getToneTooltip = computed(() => {
  if (!props.toneData) {
    return '点击选择音色';
  }

  const typeText = props.toneData.tone_type === 'custom' ? '自定义音色' : '预训练音色';
  // const description = props.toneData.description || '无描述信息';

  return `${typeText}`;
});

const handleAddMore = () => {
  emit('addMore');
};

const handleRemove = () => {
  emit('remove');
};

// 处理音色选择
const handleSelectTone = () => {
  // 传递当前的音色数据状态，让父组件决定是打开选择界面还是切换到设置面板
  emit('selectTone', props.toneData);
};

// 获取ParamSeg数据的方法
const getParamSegData = (): ParamSeg | null => {
  if (!props.toneData || !textContent.value.trim()) {
    return null;
  }

  return {
    tone_type: props.toneData.tone_type,
    tone_id: props.toneData.tone_id,
    model_function: props.toneData.model_function,
    text: textContent.value.trim(),
    // speed和pitch将由父组件统一设置
    speed: 1,
    pitch: 0
  };
};

// 暴露方法和属性供父组件调用
defineExpose({
  getParamSegData,
  textContent
});
</script>

<template>
  <NFlex class="w-full p-4" vertical :wrap="false">
    <NFlex justify="space-between" align="start" class="w-full">
      <!-- 音色名称 -->
      <NTooltip :show-arrow="false">
        <template #trigger>
          <NTag
            size="large"
            class="w-35 cursor-pointer"
            :type="toneData ? 'info' : 'default'"
            @click="handleSelectTone"
          >
            <NMarquee v-if="isMarquee()" speed="30" class="max-w-24" :line-clamp="1">{{ modelName }}</NMarquee>
            <NText v-else class="max-w-24" :line-clamp="1">{{ modelName }}</NText>
            <template #icon>
              <SvgIcon icon="mingcute:voice-line" />
            </template>
          </NTag>
        </template>
        <div class="max-w-200px whitespace-pre-line">{{ getToneTooltip }}</div>
      </NTooltip>

      <!-- 取消关闭 -->
      <NButton text @click="handleRemove">
        <SvgIcon icon="material-symbols-light:close" class="text-2xl" />
      </NButton>
    </NFlex>

    <!-- 用户输入文本 -->
    <NInput
      v-model:value="textContent"
      class="text_content"
      type="textarea"
      placeholder="在此处开始输入文字，生成您的个性化音频..."
      :autosize="{ minRows: 3, maxRows: 10 }"
    />

    <!-- 点击添加更多 -->
    <NDivider title-placement="center">
      <NButton text @click="handleAddMore">
        <SvgIcon icon="material-symbols-light:add" class="text-xl" />
        点击添加更多
      </NButton>
    </NDivider>
  </NFlex>
</template>

<style scoped lang="scss">
:deep(.n-input) {
  background-color: transparent !important;
}

:deep(.n-input:not(.n-input--disabled).n-input--focus) {
  background-color: transparent !important;
}

:deep(.n-input__input-el) {
  background-color: transparent !important;
}

:deep(.n-input-wrapper) {
  background-color: transparent !important;
  padding: 0px !important;
}

// 文本输入框提示
// .text_content :deep(.n-input-wrapper) .n-input__input .n-input__placeholder span  {
//   display: none !important;
// }

// 去掉 hover 效果和 focus 效果
:deep(.text_content.n-input) {
  // 去掉边框的 hover 和 focus 效果
  --n-border-hover: transparent !important;
  --n-border-focus: transparent !important;
  --n-box-shadow-hover: none !important;
  --n-box-shadow-focus: none !important;
  --n-border: none !important;

  .n-input-wrapper {
    &:hover {
      border-color: transparent !important;
      box-shadow: none !important;
    }

    &.n-input-wrapper--focus {
      border-color: transparent !important;
      box-shadow: none !important;
    }
  }
}
</style>
