<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { generateAnimalVideo, generateVideo, no_comfyui_upload } from '@/service/api/portrait';
import ExampleCard from './modules/example-card.vue';

const loading = ref(false); // 生成loading状态
const mediaPreview = ref<string | null>(null); // 用于展示图片或视频预览
const imgname = ref<string | null>(null); // 保存图片或视频文件名
const videoname = ref<string | null>(null); // 保存专用视频文件名
const message = useMessage();
const finallyPreview = ref<string | undefined>('');
const mediaType = ref<'image' | 'video' | null>(null); // 新增变量
const currentTab = ref('human'); // 默认选择为人物模型
const refermediaType = ref<'image' | 'video' | null>(null);
const referMediaPreview = ref<string | null>(null);
const finalMediaType = ref<'image' | 'video'>('video'); // 生成结果的媒体类型

interface CustomRequestOptions {
  file: UploadFileInfo;
  onFinish: () => void;
}

// 允许的媒体类型
const allowedMediaTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm', 'video/ogg'];

// 上传逻辑
const uploadFile = async (file: File, isMedia: boolean) => {
  const response = await no_comfyui_upload(file);
  if (response.data) {
    if (isMedia) {
      imgname.value = response.data.filename;
    } else {
      videoname.value = response.data.filename;
    }
    message.success('上传成功');
  }
  // message.success('上传成功');
};

// 参考视频/图片上传逻辑
const beforeReferMediaUpload = (data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}): boolean | Promise<boolean> => {
  const fileType = data.file.file?.type || '';
  if (!allowedMediaTypes.includes(fileType)) {
    message.error('上传的文件格式错误');
    return false;
  }

  refermediaType.value = fileType.startsWith('image') ? 'image' : 'video';
  if (refermediaType.value === 'video') {
    return new Promise(resolve => {
      if (!data.file.file) {
        message.error('上传的视频格式错误');
        resolve(false);
        return;
      }
      const video = document.createElement('video');
      video.src = URL.createObjectURL(data.file.file as Blob);
      video.onloadedmetadata = () => {
        URL.revokeObjectURL(video.src);
        const duration = video.duration;
        if (duration > 60) {
          message.error('视频时长不能超过60秒');
          resolve(false);
        } else {
          resolve(true);
        }
      };
    });
  }

  return true;
};

const customReferMediaRequest = ({ file, onFinish }: CustomRequestOptions) => {
  const reader = new FileReader();
  reader.onload = async e => {
    if (e.target && e.target.result) {
      const binaryData = new Uint8Array(e.target.result as ArrayBuffer);
      const previewUrl = URL.createObjectURL(new Blob([binaryData]));
      referMediaPreview.value = previewUrl;
      await uploadFile(file.file as File, false);
    }
    onFinish();
  };
  if (file.file) {
    reader.readAsArrayBuffer(file.file);
  } else {
    message.error('上传失败');
    onFinish();
  }
};

// 普通媒体上传逻辑
const beforeMediaUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): boolean => {
  const fileType = data.file.file?.type || '';
  if (!allowedMediaTypes.includes(fileType)) {
    message.error('上传的文件格式错误');
    return false;
  }

  mediaType.value = fileType.startsWith('image') ? 'image' : 'video';
  return true;
};

const customMediaRequest = ({ file, onFinish }: CustomRequestOptions) => {
  const reader = new FileReader();
  reader.onload = async e => {
    if (e.target && e.target.result) {
      const binaryData = new Uint8Array(e.target.result as ArrayBuffer);
      const previewUrl = URL.createObjectURL(new Blob([binaryData]));
      mediaPreview.value = previewUrl;
      await uploadFile(file.file as File, true);
    }
    onFinish();
  };
  if (file.file) {
    reader.readAsArrayBuffer(file.file);
  } else {
    message.error('上传失败');
    onFinish();
  }
};

const animationRegion = ref<string>('exp');
const drivingOption = ref<string>('expression-friendly');
const drivingMultiplier = ref<number>(1.0);

// 判断URL文件类型的函数
const getFileTypeFromUrl = (url: string): 'image' | 'video' => {
  const extension = url.split('.').pop()?.toLowerCase() || '';
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  } else if (['mp4', 'webm', 'ogg', 'mov', 'avi'].includes(extension)) {
    return 'video';
  }
  // 默认作为图片处理
  return 'image';
};

// 发送 prompt 请求
const sendPromptRequest = async () => {
  if (!imgname.value || !videoname.value) {
    message.error('请先上传图片与视频');
    return;
  }

  loading.value = true;

  // 官方版本
  let response;

  // 判断当前选中的TabPane
  if (currentTab.value === 'human') {
    // 调用generateVideo
    response = await generateVideo({
      imagename: imgname.value,
      videoname: videoname.value,
      animation_region: animationRegion.value,
      driving_option: drivingOption.value,
      driving_multiplier: drivingMultiplier.value
    });
  } else if (currentTab.value === 'animal') {
    // 调用generateAnimalVideo
    response = await generateAnimalVideo({
      imagename: imgname.value,
      videoname: videoname.value,
      animation_region: animationRegion.value,
      driving_option: drivingOption.value,
      driving_multiplier: drivingMultiplier.value
    });
  }

  if (response?.data?.file) {
    const fileUrl = response.data.file;
    finallyPreview.value = fileUrl;
    finalMediaType.value = getFileTypeFromUrl(fileUrl);
    loading.value = false;
    message.success('生成成功');
  } else {
    loading.value = false;
    message.error('生成失败');
  }
};

watch(currentTab, newValue => {
  // 清空相关数据
  imgname.value = null;
  videoname.value = null;
  mediaPreview.value = null;
  referMediaPreview.value = null;

  console.log(`已切换至${newValue === 'human' ? '人物模型' : '动物模型'}`);
});

// const base64ToFile = (base64: string, filename: string): File => {
//   const arr = base64.split(',');
//   const mime = arr[0].match(/:(.*?);/)?.[1] || '';
//   const bstr = atob(arr[1]);
//   let n = bstr.length;
//   const u8arr = new Uint8Array(n);

//   while (n--) {
//     u8arr[n] = bstr.charCodeAt(n);
//   }

//   return new File([u8arr], filename, { type: mime });
// };

interface TryParams {
  source_b64: string;
  reference_b64: string;
  type: 'image' | 'video' | null;
  tab: string;
  sourceFileName: string;
  referenceFileName: string;
}

const doTry = async (params: TryParams) => {
  nextTick(() => {
    mediaPreview.value = params.source_b64;
    referMediaPreview.value = params.reference_b64;
    imgname.value = params.sourceFileName;
    videoname.value = params.referenceFileName;
    mediaType.value = 'image';
    refermediaType.value = params.type;
    finalMediaType.value = params.type || 'image';
    currentTab.value = params.tab;
  });
};
</script>

<template>
  <main>
    <NGrid cols="2 s:1 m:2" responsive="screen" y-gap="5" x-gap="25">
      <NGridItem>
        <NCard class="left">
          <NTabs v-model:value="currentTab" type="segment">
            <NTabPane name="human" tab="人物模型">
              <!-- 图片和视频上传 -->
              <div class="flex gap-1">
                <NUpload
                  :custom-request="customMediaRequest"
                  :default-upload="true"
                  accept="image/*,video/*"
                  :multiple="false"
                  :show-file-list="false"
                  class="uploadbox flex-1"
                  @before-upload="beforeMediaUpload"
                >
                  <NUploadDragger>
                    <template v-if="mediaPreview">
                      <template v-if="mediaType === 'image'">
                        <NImage
                          :src="mediaPreview"
                          class="preview-image"
                          :width="512"
                          :height="512"
                          object-fit="contain"
                          preview-disabled
                        />
                      </template>
                      <template v-else>
                        <video :src="mediaPreview" controls class="preview-video max-h-full max-w-full"></video>
                      </template>
                    </template>
                    <div v-else>
                      <div class="flex justify-center">
                        <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
                      </div>
                      <NText>点击或者拖动预处理图片或视频到该区域进行上传</NText>
                      <NP depth="3">请不要上传不支持格式的文件。</NP>
                    </div>
                  </NUploadDragger>
                </NUpload>
              </div>

              <!-- 参考视频 / 图片 -->
              <div class="mt-2 flex gap-1">
                <NScrollbar class="menuScrollbar flex-1">
                  <NSpace class="menuBox" vertical>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">部位驱动</NTag></div>
                      <NRadioGroup v-model:value="animationRegion">
                        <NSpace>
                          <NRadio value="exp">表情</NRadio>
                          <NRadio value="pose">姿势</NRadio>
                          <NRadio value="lip">嘴巴</NRadio>
                          <NRadio value="eyes">眼睛</NRadio>
                          <NRadio value="all">全部</NRadio>
                        </NSpace>
                      </NRadioGroup>
                    </NCard>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">驱动模式</NTag></div>
                      <NRadioGroup v-model:value="drivingOption">
                        <NSpace>
                          <NRadio value="expression-friendly">表情优先</NRadio>
                          <NRadio value="pose-friendly">姿势优先</NRadio>
                        </NSpace>
                      </NRadioGroup>
                    </NCard>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">驱动强度</NTag></div>
                      <NInputNumber
                        v-model:value="drivingMultiplier"
                        clearable
                        :precision="2"
                        max="2"
                        min="0"
                        :show-button="false"
                      ></NInputNumber>
                    </NCard>
                  </NSpace>
                </NScrollbar>
                <NUpload
                  :custom-request="customReferMediaRequest"
                  :default-upload="true"
                  accept="video/*,image/*"
                  :multiple="false"
                  :show-file-list="false"
                  class="uploadbox flex-2"
                  @before-upload="beforeReferMediaUpload"
                >
                  <NUploadDragger>
                    <template v-if="referMediaPreview">
                      <template v-if="refermediaType === 'image'">
                        <NImage
                          :src="referMediaPreview"
                          class="preview-image"
                          :width="512"
                          :height="512"
                          object-fit="contain"
                          preview-disabled
                        />
                      </template>
                      <template v-else>
                        <video :src="referMediaPreview" controls class="preview-video max-h-full max-w-full"></video>
                      </template>
                    </template>
                    <div v-else>
                      <div class="flex justify-center">
                        <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
                      </div>
                      <div><NText>点击或者拖动</NText></div>
                      <div><NText>参考视频 / 图片</NText></div>
                      <div><NText>到该区域进行上传</NText></div>
                      <NP depth="3">请不要上传不支持格式的视频或图片。</NP>
                    </div>
                  </NUploadDragger>
                </NUpload>
              </div>

              <!-- 生成按钮 -->
              <NButton
                type="success"
                class="mt-3 w-full"
                :loading="loading"
                :disabled="loading"
                @click="sendPromptRequest"
              >
                生成
              </NButton>
            </NTabPane>
            <NTabPane name="animal" tab="动物模型">
              <!-- 图片和视频上传 -->
              <div class="flex gap-1">
                <NUpload
                  :custom-request="customMediaRequest"
                  :default-upload="true"
                  accept="image/*,video/*"
                  :multiple="false"
                  :show-file-list="false"
                  class="uploadbox flex-1"
                  @before-upload="beforeMediaUpload"
                >
                  <NUploadDragger>
                    <template v-if="mediaPreview">
                      <template v-if="mediaType === 'image'">
                        <NImage
                          :src="mediaPreview"
                          class="preview-image"
                          :width="512"
                          :height="512"
                          object-fit="contain"
                          preview-disabled
                        />
                      </template>
                      <template v-else>
                        <video :src="mediaPreview" controls class="preview-video max-h-full max-w-full"></video>
                      </template>
                    </template>
                    <div v-else>
                      <div class="flex justify-center">
                        <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
                      </div>
                      <NText>点击或者拖动图片或视频到该区域来上传</NText>
                      <NP depth="3">请不要上传不支持格式的文件。</NP>
                    </div>
                  </NUploadDragger>
                </NUpload>
              </div>

              <!-- 参考视频 / 图片 -->
              <div class="mt-2 flex gap-1">
                <NScrollbar class="menuScrollbar flex-1">
                  <NSpace class="menuBox" vertical>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">部位驱动</NTag></div>
                      <NRadioGroup v-model:value="animationRegion">
                        <NSpace>
                          <NRadio value="exp">表情</NRadio>
                          <NRadio value="pose">姿势</NRadio>
                          <NRadio value="lip">嘴巴</NRadio>
                          <NRadio value="eyes">眼睛</NRadio>
                          <NRadio value="all">全部</NRadio>
                        </NSpace>
                      </NRadioGroup>
                    </NCard>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">驱动模式</NTag></div>
                      <NRadioGroup v-model:value="drivingOption">
                        <NSpace>
                          <NRadio value="expression-friendly">表情优先</NRadio>
                          <NRadio value="pose-friendly">姿势优先</NRadio>
                        </NSpace>
                      </NRadioGroup>
                    </NCard>
                    <NCard bordered>
                      <div class="mb-3"><NTag type="info">驱动强度</NTag></div>
                      <NInputNumber
                        v-model:value="drivingMultiplier"
                        clearable
                        :precision="2"
                        max="2"
                        min="0"
                        :show-button="false"
                      ></NInputNumber>
                    </NCard>
                  </NSpace>
                </NScrollbar>
                <NUpload
                  :custom-request="customReferMediaRequest"
                  :default-upload="true"
                  accept="video/*,image/*"
                  :multiple="false"
                  :show-file-list="false"
                  class="uploadbox flex-2"
                  @before-upload="beforeReferMediaUpload"
                >
                  <NUploadDragger>
                    <template v-if="referMediaPreview">
                      <template v-if="refermediaType === 'image'">
                        <NImage
                          :src="referMediaPreview"
                          class="preview-image"
                          :width="512"
                          :height="512"
                          object-fit="contain"
                          preview-disabled
                        />
                      </template>
                      <template v-else>
                        <video :src="referMediaPreview" controls class="preview-video max-h-full max-w-full"></video>
                      </template>
                    </template>
                    <div v-else>
                      <div class="flex justify-center">
                        <SvgIcon icon="icon-park:upload-web" class="text-5xl" />
                      </div>
                      <div><NText>点击或者拖动</NText></div>
                      <div><NText>参考视频 / 图片</NText></div>
                      <div><NText>到该区域进行上传</NText></div>
                      <NP depth="3">请不要上传不支持格式的视频或图片。</NP>
                    </div>
                  </NUploadDragger>
                </NUpload>
              </div>

              <!-- 生成按钮 -->
              <NButton
                type="success"
                class="mt-3 w-full"
                :loading="loading"
                :disabled="loading"
                @click="sendPromptRequest"
              >
                生成
              </NButton>
            </NTabPane>
          </NTabs>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard class="right">
          <div class="h-2/3">
            <div><NH2>最终结果：</NH2></div>

            <!-- loading加载状态 -->
            <div v-if="loading" class="loading-container">
              <div class="outer-circle">
                <div class="inner-circle"></div>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>

            <!-- 结果展示 -->
            <NCard v-else class="videoincard">
              <div v-if="finallyPreview" class="previewBox">
                <video
                  v-if="finalMediaType === 'video'"
                  :src="finallyPreview"
                  controls
                  class="m-h-full m-w-full object-contain"
                ></video>
                <NImage v-else :src="finallyPreview" object-fit="contain" class="m-h-full m-w-full"></NImage>
              </div>
            </NCard>
          </div>
          <div class="mt-2"><NH4>参考示例：</NH4></div>
          <NCard bordered class="refBox h-4/15">
            <ExampleCard class="h-full overflow-auto" @do-try="doTry" />
          </NCard>
        </NCard>
      </NGridItem>
    </NGrid>
  </main>
</template>

<style scoped>
.refBox :deep(.n-card__content) {
  padding: 0 !important;
}

.left,
.right {
  width: 100%;
  height: 55.5em;
}

.right :deep(.n-card__content) {
  height: 90%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  flex-direction: column;
}

.outer-circle {
  position: relative;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
  animation: rotate 1.5s linear infinite;
}

.outer-circle span {
  position: absolute;
  height: 200px;
  width: 200px;
  background: linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
  border-radius: 50%;
}

.outer-circle span:nth-child(1) {
  filter: blur(5px);
}

.outer-circle span:nth-child(2) {
  filter: blur(10px);
}

.outer-circle span:nth-child(3) {
  filter: blur(25px);
}

.outer-circle span:nth-child(4) {
  filter: blur(150px);
}

.inner-circle {
  height: 180px;
  width: 180px;
  position: absolute;
  background: black;
  top: 10px;
  left: 10px;
  border-radius: 50%;
  z-index: 9;
}

@keyframes rotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

:deep(.uploadbox) {
  height: 23em;
  width: 23em;
}

:deep(.uploadbox) .n-upload-trigger,
:deep(.uploadbox) .n-upload-trigger .n-upload-dragger {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.example-image {
  object-fit: cover;
  width: 90%;
  height: 16em;
  max-width: 90%;
  max-height: 90%;
  margin-top: 0.5em;
}

:deep(.videoincard) {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  height: 88%;
}

.videoincard :deep(.previewBox) {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.videoincard :deep(.previewBox) video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  min-width: 100%;
  min-height: 100%;
}

.menuBox :deep(.n-card__content) {
  padding: 1.2em !important;
}

:deep(.menuScrollbar) {
  max-height: 23em;
}
</style>
