<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchNewCreditUserList } from '@/service/api';

defineOptions({
  name: 'NewUserCredit'
});

const options = ref<any[]>([]);

type SubmitModel = {
  user_id: string;
  type: number;
  credit: number;
};

interface Emits {
  (e: 'submitted', model: SubmitModel): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = '增加新积分用户';

type Model = {
  user_id: string;
  type: number;
  credit: number;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    user_id: '',
    type: 1,
    credit: 1
  };
}

type RuleKey = 'user_id' | 'type' | 'credit';

const rules: Record<RuleKey, any> = {
  user_id: defaultRequiredRule,
  type: defaultRequiredRule,
  credit: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());
}

function closeDrawer() {
  visible.value = false;
}

async function handleUpdateValue(
  value: string | number | Array<string | number> | null
  // option: TreeSelectOption | null | Array<TreeSelectOption | null>
) {
  model.user_id = String(value);
  // console.log('model.user_id========', value, model.user_id);
}

async function handleSubmit() {
  await validate();
  const submitModel = {
    ...model
  };
  closeDrawer();
  emit('submitted', submitModel);
}

const fetchData = async () => {
  try {
    const response = await fetchNewCreditUserList();
    options.value = response.data.records;
  } catch (error) {
    console.error('请求数据失败:', error);
  }
};

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
  }
});

onMounted(() => {
  fetchData();
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-600px">
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="用户名" path="user_id">
        <NTreeSelect
          :options="options"
          :default-value="['0']"
          check-strategy="child"
          @update:value="handleUpdateValue"
        />
      </NFormItem>
      <NFormItem label="用户 ID" path="user_id">
        <NInput v-model:value="model.user_id" placeholder="Userid" disabled />
      </NFormItem>
      <NFormItem label="积分" path="credit">
        <NInputNumber
          v-model:value="model.credit"
          type="number"
          placeholder="请输入积分"
          clearable
          class="w-full"
          :min="1"
          :precision="0"
          :step="1"
        />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace>
        <NButton @click="closeDrawer">取消</NButton>
        <NButton type="primary" @click="handleSubmit">确认</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
