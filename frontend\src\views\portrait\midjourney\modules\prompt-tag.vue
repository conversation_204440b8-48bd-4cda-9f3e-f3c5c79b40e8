<script setup lang="ts">
// 定义接收的 props
const props = defineProps<{
  category: string;
  name: string;
  weight: number;
  param: string;
}>();

// 定义 emits，添加 `close` 事件
const emit = defineEmits<{
  (event: 'close', param: string): void;
}>();
</script>

<template>
  <NTag closable type="warning" @close="emit('close', props.param)">
    <NText>{{ props.category }}</NText>
    :
    <NText>{{ props.name }}</NText>
    <NText v-if="props.weight !== 0">(+{{ props.weight }})</NText>
  </NTag>
</template>

<style scoped></style>
