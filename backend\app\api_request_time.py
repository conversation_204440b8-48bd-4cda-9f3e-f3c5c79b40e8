import time
import functools
from typing import Optional, Dict
from fastapi import APIRouter

from utils.exceptions import ClientVisibleException
from utils.redis import redis
from pydantic import BaseModel

router = APIRouter()

class APITimeResponse(BaseModel):
    code: str = "0000"
    data: Dict[str, float]

def record_time(api_name: str):
    """
    装饰器：记录API请求时间

    Args:
        api_name (str): API名称
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 开始计时
            start_time = time.time()

            # 执行原函数
            result = await func(*args, **kwargs)

            # 计算耗时（毫秒）
            execution_time = (time.time() - start_time) * 1000

            # 从Redis获取当前平均时间
            redis_key = f"api_time:{api_name}"
            current_avg = await redis.get(redis_key)

            if current_avg:
                # 计算滚动平均值 (保留最近5次请求的平均值)
                new_avg = (float(current_avg) * 4 + execution_time) / 5
            else:
                new_avg = execution_time

            # 更新Redis中的平均时间
            await redis.set(redis_key, str(new_avg))

            return result
        return wrapper
    return decorator

@router.get("/api/time", response_model=APITimeResponse, tags=["system"])
async def get_api_time(api_name: Optional[str] = None):
    """
    获取API平均响应时间

    Args:
        api_name (Optional[str]): API名称，不传则返回所有API的时间

    Returns:
        Dict: API响应时间统计数据
    """
    try:
        if api_name:
            # 获取指定API的响应时间
            redis_key = f"api_time:{api_name}"
            value = await redis.get(redis_key)
            result = {api_name: float(value) if value else 0}
        else:
            # 获取所有API的响应时间
            keys = await redis.exec("keys", "api_time:*")
            result = {}

            for key in keys:
                key_str = key.decode('utf-8')
                api_name = key_str.split(':')[1]
                value = await redis.get(key_str)
                result[api_name] = float(value) if value else 0

        return {
            "code": "0000",
            "data": result
        }
    except Exception as e:
        raise ClientVisibleException() from e
